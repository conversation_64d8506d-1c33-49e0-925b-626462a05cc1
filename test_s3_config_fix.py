#!/usr/bin/env python3
"""Test script to debug phantom DynamoDB saves."""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging to see debug output
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_phantom_saves():
    """Test and debug phantom DynamoDB saves."""
    
    print("🔍 PHANTOM SAVE DEBUG SESSION STARTED")
    print("=" * 60)
    
    try:
        # Create repository directly without factory complications
        from src.repositories.fb_archive_repository import FBArchiveRepository
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        import logging
        from src.config_models.loader import load_config
        
        # Load configuration
        config = load_config("fb_ads") 
        logger.info("✅ Configuration loaded successfully")
        
        # Create dependencies manually - use simple logger
        logger_instance = logging.getLogger("fb_repo_debug")
        
        # Create and initialize storage
        async with AsyncDynamoDBStorage(config, logger_instance) as storage:
            fb_repo = FBArchiveRepository(storage, logger_instance)
            
            logger.info("✅ FB Archive repository created")
            print(f"\n🏷️  Table name: {fb_repo.table_name}")
            
            # Test 1: Query by PageID and LastUpdated (the problematic query from logs)
            print(f"\n🔍 Test 1: Query PageID=107433804914030, LastUpdated=20250803")
            try:
                results = await fb_repo.query_by_page_id_and_date("107433804914030", "20250803")
                print(f"📊 RESULT: Found {len(results)} records")
                
                if results:
                    print("✅ DATA FOUND - Save operations ARE working!")
                    for i, record in enumerate(results[:3]):
                        print(f"  Record {i+1}: ad_archive_id={record.get('ad_archive_id', 'missing')}")
                else:
                    print("❌ NO DATA FOUND - This confirms phantom saves!")
                    
            except Exception as e:
                print(f"❌ Query failed: {e}")
                import traceback
                traceback.print_exc()
                
            # Test 2: Query just by PageID (broader search)
            print(f"\n🔍 Test 2: Query by PageID=107433804914030 only")
            try:
                results = await fb_repo.query_by_page_id("107433804914030")
                print(f"📊 RESULT: Found {len(results)} records total")
                
                if results:
                    # Check dates in results
                    dates = set(r.get('last_updated', 'missing') for r in results)
                    print(f"  Available dates: {sorted(dates)}")
                    
                    # Check if 20250803 is there
                    date_20250803 = [r for r in results if r.get('last_updated') == '20250803']
                    print(f"  Records with last_updated=20250803: {len(date_20250803)}")
                    
                    if not date_20250803:
                        print("🚨 FOUND ISSUE: Records exist for PageID but NOT for 20250803!")
                        print("    This suggests case conversion or field mapping problems!")
                        
                        # Show a sample record structure
                        sample = results[0]
                        print(f"  Sample record keys: {list(sample.keys())[:10]}")
                        print(f"  Sample last_updated value: '{sample.get('last_updated', 'MISSING')}'")
                        
            except Exception as e:
                print(f"❌ Broader query failed: {e}")
                import traceback
                traceback.print_exc()
                
            # Test 3: Direct storage test with minimal record
            print(f"\n🔍 Test 3: Test minimal record save/retrieve")
            try:
                test_record = {
                    'ad_archive_id': 'TEST_PHANTOM_DEBUG_001',
                    'start_date': '20250804',
                    'page_id': '107433804914030',
                    'last_updated': '20250804',
                    'test_field': 'phantom_debug_test'
                }
                
                print(f"  Saving test record...")
                await fb_repo.put_item(test_record)
                print(f"  ✅ Save reported success")
                
                print(f"  Querying for test record...")
                test_results = await fb_repo.query_by_page_id_and_date("107433804914030", "20250804")
                test_found = [r for r in test_results if r.get('ad_archive_id') == 'TEST_PHANTOM_DEBUG_001']
                
                if test_found:
                    print(f"  ✅ Test record FOUND - Basic save/retrieve works!")
                    # Clean up
                    await fb_repo.delete_record('TEST_PHANTOM_DEBUG_001', '20250804')
                    print(f"  🧹 Test record cleaned up")
                else:
                    print(f"  ❌ Test record NOT FOUND - Confirming phantom save issue!")
                    print(f"     Total results from test query: {len(test_results)}")
                    
            except Exception as e:
                print(f"❌ Test record save/retrieve failed: {e}")
                import traceback
                traceback.print_exc()
                
            print(f"\n🔍 PHANTOM SAVE DEBUG COMPLETE")
            return True
            
    except Exception as e:
        logger.error(f"❌ Debug session failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_phantom_saves())
    exit(0 if success else 1)