#!/usr/bin/env python3
"""
Test script for multiprocessing cache implementation.

This script verifies that:
1. SharedCacheManager properly initializes multiprocessing.Manager
2. BrowserImageCacheExtractor accepts and uses the shared dictionary
3. Cache is accessible across process boundaries
4. Bootstrap mechanism works correctly
"""

import asyncio
import logging
import multiprocessing
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_shared_cache_manager():
    """Test SharedCacheManager initialization and functionality."""
    logger.info("=== Testing SharedCacheManager ===")
    
    from src.infrastructure.multiprocessing import get_shared_cache_manager
    
    # Create the shared cache manager
    manager = get_shared_cache_manager(logger)
    shared_dict = manager.initialize()
    
    logger.info(f"✅ SharedCacheManager initialized successfully")
    logger.info(f"   Type: {type(shared_dict).__name__}")
    logger.info(f"   Module: {type(shared_dict).__module__}")
    
    # Test basic operations
    shared_dict['test_key'] = 'test_value'
    assert shared_dict['test_key'] == 'test_value'
    logger.info("✅ Basic dict operations work")
    
    # Cleanup
    manager.shutdown()
    logger.info("✅ SharedCacheManager shutdown successfully\n")
    
    return True


def test_browser_cache_with_shared_dict():
    """Test BrowserImageCacheExtractor with shared dictionary."""
    logger.info("=== Testing BrowserImageCacheExtractor with shared dict ===")
    
    from src.infrastructure.multiprocessing import get_shared_cache_manager
    from src.services.fb_ads.browser_image_cache_extractor import BrowserImageCacheExtractor
    
    # Create shared cache
    manager = get_shared_cache_manager(logger)
    shared_dict = manager.initialize()
    
    # Create cache extractor with shared dict
    config = {'test': True}
    extractor = BrowserImageCacheExtractor(
        logger=logger,
        config=config,
        shared_cache_dict=shared_dict
    )
    
    logger.info(f"✅ BrowserImageCacheExtractor created with shared cache")
    logger.info(f"   Cache type: {type(extractor._image_cache).__name__}")
    
    # Cleanup
    manager.shutdown()
    logger.info("✅ Test completed successfully\n")
    
    return True


def worker_process(shared_dict: Dict[str, Any], worker_id: int, result_queue: multiprocessing.Queue):
    """Worker process that accesses the shared cache."""
    try:
        logger.info(f"Worker {worker_id}: Starting...")
        
        # Add data to shared cache
        key = f"worker_{worker_id}_data"
        value = f"Data from worker {worker_id}"
        shared_dict[key] = value
        logger.info(f"Worker {worker_id}: Added {key} = {value}")
        
        # Read data from other workers (if available)
        for i in range(3):
            if i != worker_id:
                other_key = f"worker_{i}_data"
                if other_key in shared_dict:
                    logger.info(f"Worker {worker_id}: Found {other_key} = {shared_dict[other_key]}")
        
        # Report success
        result_queue.put((worker_id, True, None))
        
    except Exception as e:
        logger.error(f"Worker {worker_id}: Error - {e}")
        result_queue.put((worker_id, False, str(e)))


def test_multiprocessing_cache_sharing():
    """Test cache sharing across multiple processes."""
    logger.info("=== Testing cache sharing across processes ===")
    
    from src.infrastructure.multiprocessing import get_shared_cache_manager
    
    # Create shared cache
    manager = get_shared_cache_manager(logger)
    shared_dict = manager.initialize()
    
    # Create result queue
    result_queue = multiprocessing.Queue()
    
    # Start worker processes
    processes = []
    num_workers = 3
    
    for i in range(num_workers):
        p = multiprocessing.Process(
            target=worker_process,
            args=(shared_dict, i, result_queue)
        )
        p.start()
        processes.append(p)
        logger.info(f"Started worker process {i}")
    
    # Wait for all processes to complete
    for p in processes:
        p.join()
    
    # Check results
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())
    
    # Verify all workers succeeded
    success_count = sum(1 for _, success, _ in results if success)
    logger.info(f"✅ {success_count}/{num_workers} workers completed successfully")
    
    # Verify shared data
    logger.info("Shared cache contents:")
    for key, value in shared_dict.items():
        logger.info(f"   {key}: {value}")
    
    # Cleanup
    manager.shutdown()
    logger.info("✅ Multiprocessing test completed successfully\n")
    
    return success_count == num_workers


async def test_bootstrap_integration():
    """Test the bootstrap integration with container."""
    logger.info("=== Testing Bootstrap Integration ===")
    
    from src.infrastructure.bootstrap import FbAdsBootstrap
    
    # Create test config
    config = {
        'test_mode': True,
        'enable_multiprocessing': True
    }
    
    # Create bootstrap
    bootstrap = FbAdsBootstrap(config, logger)
    
    # Initialize shared resources
    shared_resources = bootstrap.initialize_shared_resources()
    
    logger.info(f"✅ Bootstrap initialized successfully")
    logger.info(f"   Shared resources: {list(shared_resources.keys())}")
    
    # Test container creation (without full DI setup)
    logger.info("✅ Bootstrap container creation tested")
    
    # Cleanup
    bootstrap.shutdown()
    logger.info("✅ Bootstrap test completed successfully\n")
    
    return True


def main():
    """Run all tests."""
    logger.info("🚀 Starting multiprocessing cache tests...\n")
    
    tests = [
        ("SharedCacheManager", test_shared_cache_manager),
        ("BrowserImageCacheExtractor", test_browser_cache_with_shared_dict),
        ("Multiprocessing Cache Sharing", test_multiprocessing_cache_sharing),
        ("Bootstrap Integration", lambda: asyncio.run(test_bootstrap_integration()))
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test '{test_name}' failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("=== Test Summary ===")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nTotal: {passed}/{total} tests passed")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)