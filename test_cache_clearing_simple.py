#!/usr/bin/env python3
"""
Simple test for per-advertiser cache clearing workflow without external dependencies.
"""

import asyncio
import sys
import os
import logging
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.services.fb_ads.browser_image_cache_extractor import (
        BrowserImageCacheExtractor,
        CachedImageResource
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("This test requires the source code to be available")
    sys.exit(1)


class MockPlaywrightPage:
    """Mock Playwright page for testing"""
    
    def __init__(self):
        self.response_handlers = []
        
    def on(self, event: str, handler):
        """Register event handler"""
        if event == "response":
            self.response_handlers.append(handler)
    
    async def trigger_response(self, url: str, status: int = 200, content_type: str = "image/jpeg", content: bytes = None):
        """Simulate a response for testing"""
        response = MockResponse(url, status, content_type, content or b"fake_image_data")
        for handler in self.response_handlers:
            await handler(response)


class MockResponse:
    """Mock Playwright response"""
    
    def __init__(self, url: str, status: int, content_type: str, content: bytes):
        self.url = url
        self.status = status
        self.headers = {"content-type": content_type}
        self._content = content
        
    async def body(self):
        return self._content


async def test_cache_cleared_between_advertisers():
    """Test that cache is cleared between different advertisers"""
    print("🧪 Testing cache clearing between advertisers...")
    
    # Create cache extractor
    mock_logger = MagicMock(spec=logging.Logger)
    config = {
        'enable_bandwidth_logging': False,
        'data_dir': './test_data'
    }
    
    cache_extractor = BrowserImageCacheExtractor(
        logger=mock_logger,
        config=config,
        max_cache_size_mb=100,
        cache_ttl_minutes=30,
        enable_disk_persistence=False  # Disabled for per-advertiser workflow
    )
    
    # Simulate first advertiser processing
    page1 = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page1)
    
    # Populate cache with first advertiser's images
    await page1.trigger_response("https://facebook.com/advertiser1/image1.jpg", content=b"advertiser1_image1")
    await page1.trigger_response("https://facebook.com/advertiser1/image2.jpg", content=b"advertiser1_image2")
    
    # Verify cache has images from first advertiser
    assert len(cache_extractor._image_cache) == 2, f"Expected 2 images, got {len(cache_extractor._image_cache)}"
    assert cache_extractor._cache_hits == 0, f"Expected 0 hits, got {cache_extractor._cache_hits}"
    assert cache_extractor._cache_misses == 0, f"Expected 0 misses, got {cache_extractor._cache_misses}"
    
    # Simulate some cache usage
    cached_image1 = await cache_extractor.get_cached_image("https://facebook.com/advertiser1/image1.jpg")
    assert cached_image1 is not None, "Expected cache hit for advertiser1 image"
    assert cache_extractor._cache_hits == 1, f"Expected 1 hit, got {cache_extractor._cache_hits}"
    
    print("✅ First advertiser cache populated and used successfully")
    
    # CRITICAL: Clear cache between advertisers (simulating the fix)
    await cache_extractor.clear_cache()
    
    # Verify cache is completely empty and statistics are reset
    assert len(cache_extractor._image_cache) == 0, f"Expected empty cache, got {len(cache_extractor._image_cache)}"
    assert cache_extractor._current_cache_size == 0, f"Expected 0 cache size, got {cache_extractor._current_cache_size}"
    assert cache_extractor._cache_hits == 0, f"Expected 0 hits after clear, got {cache_extractor._cache_hits}"
    assert cache_extractor._cache_misses == 0, f"Expected 0 misses after clear, got {cache_extractor._cache_misses}"
    assert len(cache_extractor._normalized_url_index) == 0, f"Expected empty URL index, got {len(cache_extractor._normalized_url_index)}"
    assert cache_extractor._cache_miss_reasons == {}, f"Expected empty miss reasons, got {cache_extractor._cache_miss_reasons}"
    
    print("✅ Cache cleared successfully - all data and statistics reset")
    
    # Simulate second advertiser processing
    page2 = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page2)
    
    # Populate cache with second advertiser's images
    await page2.trigger_response("https://facebook.com/advertiser2/image1.jpg", content=b"advertiser2_image1")
    await page2.trigger_response("https://facebook.com/advertiser2/image2.jpg", content=b"advertiser2_image2")
    
    # Verify cache only has second advertiser's images
    assert len(cache_extractor._image_cache) == 2, f"Expected 2 images for advertiser2, got {len(cache_extractor._image_cache)}"
    
    # Verify no cross-contamination - first advertiser's images should not be found
    cached_image_old = await cache_extractor.get_cached_image("https://facebook.com/advertiser1/image1.jpg")
    assert cached_image_old is None, "Expected cache miss for advertiser1 image (should be cleared)"
    assert cache_extractor._cache_misses == 1, f"Expected 1 miss, got {cache_extractor._cache_misses}"
    
    # Verify second advertiser's images are available
    cached_image_new = await cache_extractor.get_cached_image("https://facebook.com/advertiser2/image1.jpg")
    assert cached_image_new is not None, "Expected cache hit for advertiser2 image"
    assert cache_extractor._cache_hits == 1, f"Expected 1 hit, got {cache_extractor._cache_hits}"
    
    print("✅ Second advertiser cache populated - no cross-contamination detected")
    print("🎉 Per-advertiser cache clearing test PASSED!")


async def test_statistics_reset():
    """Test that all cache statistics are properly reset when clearing cache"""
    print("🧪 Testing cache statistics reset...")
    
    # Create cache extractor
    mock_logger = MagicMock(spec=logging.Logger)
    config = {'enable_bandwidth_logging': False, 'data_dir': './test_data'}
    
    cache_extractor = BrowserImageCacheExtractor(
        logger=mock_logger,
        config=config,
        max_cache_size_mb=100,
        cache_ttl_minutes=30,
        enable_disk_persistence=False
    )
    
    # Setup initial cache state
    page = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page)
    
    # Populate cache and generate statistics
    await page.trigger_response("https://facebook.com/test/image1.jpg", content=b"test_image1")
    await page.trigger_response("https://facebook.com/test/image2.jpg", content=b"test_image2")
    
    # Generate some hits and misses
    await cache_extractor.get_cached_image("https://facebook.com/test/image1.jpg")  # Hit
    await cache_extractor.get_cached_image("https://facebook.com/test/nonexistent.jpg")  # Miss
    
    # Verify statistics are populated
    assert cache_extractor._cache_hits > 0, f"Expected hits > 0, got {cache_extractor._cache_hits}"
    assert cache_extractor._cache_misses > 0, f"Expected misses > 0, got {cache_extractor._cache_misses}"
    assert cache_extractor._total_saved_bytes > 0, f"Expected saved bytes > 0, got {cache_extractor._total_saved_bytes}"
    assert len(cache_extractor._cache_miss_reasons) > 0, f"Expected miss reasons, got {cache_extractor._cache_miss_reasons}"
    
    print("✅ Statistics populated successfully")
    
    # Clear cache
    await cache_extractor.clear_cache()
    
    # Verify all statistics are reset to zero
    assert cache_extractor._cache_hits == 0, f"Expected 0 hits after clear, got {cache_extractor._cache_hits}"
    assert cache_extractor._cache_misses == 0, f"Expected 0 misses after clear, got {cache_extractor._cache_misses}"
    assert cache_extractor._total_saved_bytes == 0, f"Expected 0 saved bytes after clear, got {cache_extractor._total_saved_bytes}"
    assert cache_extractor._cache_miss_reasons == {}, f"Expected empty miss reasons after clear, got {cache_extractor._cache_miss_reasons}"
    assert cache_extractor._url_collision_count == 0, f"Expected 0 collisions after clear, got {cache_extractor._url_collision_count}"
    assert cache_extractor._normalization_stats == {'successful': 0, 'failed': 0}, f"Expected reset normalization stats, got {cache_extractor._normalization_stats}"
    
    print("✅ All statistics reset successfully")
    print("🎉 Statistics reset test PASSED!")


async def main():
    """Run all tests"""
    print("🚀 Starting per-advertiser cache clearing tests...")
    print("=" * 60)
    
    try:
        await test_cache_cleared_between_advertisers()
        print()
        await test_statistics_reset()
        print()
        print("🎉 ALL TESTS PASSED! Per-advertiser cache clearing is working correctly.")
        print("✅ Cache is properly cleared between advertisers")
        print("✅ Statistics are properly reset")
        print("✅ No cross-advertiser cache contamination")
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
