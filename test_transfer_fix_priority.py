#!/usr/bin/env python3
"""
Test the transfer inheritance fix with priority inheritance for transferor data.
"""
import asyncio
import json
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.transformer.data_transformer import DataTransformer
from config_models.loader import load_config


async def test_single_file():
    """Test transfer inheritance on a single file."""
    
    # Load config
    config_path = "config/transform.yml"
    config = load_config(config_path)
    
    # Set test parameters
    config['test_mode'] = True
    config['verbose'] = True
    config['reprocess_files'] = True
    
    # Initialize transformer
    transformer = DataTransformer(config=config)
    
    # Test file
    test_file = "data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    
    print(f"Testing transfer inheritance fix on: {test_file}")
    
    # Process the single file
    try:
        await transformer.transform_single_file(test_file)
        print("✅ Transformation completed successfully!")
        
        # Read the result to verify inheritance
        with open(test_file, 'r') as f:
            result_data = json.load(f)
        
        print("\n=== INHERITANCE VERIFICATION ===")
        print(f"mdl_num: {result_data.get('mdl_num')} (source: {result_data.get('mdl_classification_source')})")
        print(f"s3_link: {result_data.get('s3_link')}")
        print(f"attorneys_gpt: {len(result_data.get('attorneys_gpt', []))} attorneys")
        print(f"is_transferred: {result_data.get('is_transferred')}")
        print(f"transferor_court_id: {result_data.get('transferor_court_id')}")
        print(f"transferor_docket_num: {result_data.get('transferor_docket_num')}")
        
        # Check inherited law firm data
        print(f"law_firms: {result_data.get('law_firms')}")
        print(f"law_firm: {result_data.get('law_firm')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during transformation: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        await transformer.cleanup()


if __name__ == "__main__":
    asyncio.run(test_single_file())