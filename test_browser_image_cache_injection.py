#!/usr/bin/env python3
"""Test script to verify browser_image_cache_extractor injection fix."""

import asyncio
import logging
from dependency_injector import containers
from src.containers.app_container import AppContainer
from src.containers.fb_ads import FbAdsContainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_browser_image_cache_injection():
    """Test that browser_image_cache_extractor is properly injected."""
    
    # Create container
    container = AppContainer()
    
    # Wire the container (if needed)
    container.wire(modules=[__name__])
    
    # Get the fb_ads container
    fb_ads_container = container.fb_ads()
    
    # Test 1: Check that browser_image_cache_extractor exists in container
    print("\n=== Test 1: Browser Image Cache Extractor in Container ===")
    cache_extractor = fb_ads_container.browser_image_cache_extractor()
    print(f"✅ Browser Image Cache Extractor type: {type(cache_extractor).__name__}")
    print(f"✅ Cache enabled: {cache_extractor.image_cache_enabled}")
    
    # Test 2: Check that orchestrator receives it
    print("\n=== Test 2: Orchestrator Dependencies ===")
    orchestrator = fb_ads_container.facebook_ads_orchestrator()
    print(f"✅ Orchestrator has browser_image_cache_extractor: {orchestrator.browser_image_cache_extractor is not None}")
    
    # Test 3: Check job_global_dependencies
    print("\n=== Test 3: Job Global Dependencies ===")
    job_deps = orchestrator.job_global_dependencies
    print(f"✅ browser_image_cache_extractor in job_global_dependencies: {'browser_image_cache_extractor' in job_deps}")
    
    # Test 4: Create a session manager using the factory
    print("\n=== Test 4: Session Manager Factory ===")
    factory = fb_ads_container.session_manager_factory()
    
    # Simulate job dependencies
    job_dependencies = {
        "logger": logger,
        "config": container.config(),
        "fingerprint_manager": fb_ads_container.fingerprint_manager(),
        "proxy_manager": fb_ads_container.proxy_manager(),
        "law_firms_repository": container.storage.law_firms_repository(),
        "browser_image_cache_extractor": cache_extractor
    }
    
    # Create session manager with dependencies
    session_manager = factory.create(
        config=job_dependencies["config"],
        logger=job_dependencies["logger"],
        firm_id="test_firm_123",
        fingerprint_manager=job_dependencies["fingerprint_manager"],
        proxy_manager=job_dependencies["proxy_manager"],
        law_firms_repository=job_dependencies["law_firms_repository"],
        browser_image_cache_extractor=job_dependencies["browser_image_cache_extractor"]
    )
    
    print(f"✅ Session Manager type: {type(session_manager).__name__}")
    print(f"✅ Session Manager has image_cache_extractor: {hasattr(session_manager, 'image_cache_extractor')}")
    if hasattr(session_manager, 'image_cache_extractor'):
        print(f"✅ image_cache_extractor is not None: {session_manager.image_cache_extractor is not None}")
        print(f"✅ image_cache_enabled: {session_manager.image_cache_enabled}")
    
    print("\n✅ All tests passed! Browser image cache extractor is properly injected.")


if __name__ == "__main__":
    asyncio.run(test_browser_image_cache_injection())