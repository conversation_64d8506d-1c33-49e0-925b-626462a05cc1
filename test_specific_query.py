#!/usr/bin/env python3
"""
Test script to query for specific records you mentioned
"""
import asyncio
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def query_specific_records():
    """Query for the specific records mentioned by user"""
    try:
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        from src.repositories.fb_archive_repository import FBArchiveRepository
        import logging
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # Create test configuration
        class TestConfig:
            aws_region = "us-west-2"
            aws_access_key_id = None
            aws_secret_access_key = None
        
        config = TestConfig()
        
        print("🔍 Querying for specific records mentioned by user...")
        
        async with AsyncDynamoDBStorage(config, logger) as storage:
            repo = FBArchiveRepository(storage, logger)
            
            # Query 1: Look for the specific firm (Dovel & Luner)
            print("\n1. Querying for Dovel & Luner records (PageID: 102403108447809)...")
            
            # Use scan to find records with this page_id
            table = await storage.get_table("FBAdArchive")
            
            # Scan for records with the specific page_id
            response = await table.scan(
                FilterExpression="PageID = :page_id",
                ExpressionAttributeValues={
                    ':page_id': '102403108447809'
                },
                Limit=10  # Limit to first 10 records
            )
            
            records = response.get('Items', [])
            print(f"✅ Found {len(records)} records for PageID 102403108447809")
            
            if records:
                print("📋 Sample records found:")
                for i, record in enumerate(records[:3]):  # Show first 3
                    print(f"   Record {i+1}:")
                    print(f"     AdArchiveID: {record.get('AdArchiveID')}")
                    print(f"     StartDate: {record.get('StartDate')}")
                    print(f"     LastUpdated: {record.get('LastUpdated')}")
                    print(f"     EndDate: {record.get('EndDate')}")
                    print(f"     S3ImageKey: {record.get('S3ImageKey', 'Not set')}")
            
            # Query 2: Look for records with LastUpdated = 20250803
            print(f"\n2. Querying for records with LastUpdated = 20250803...")
            
            response2 = await table.scan(
                FilterExpression="LastUpdated = :last_updated",
                ExpressionAttributeValues={
                    ':last_updated': '20250803'
                },
                Limit=5  # Limit to first 5 records
            )
            
            records2 = response2.get('Items', [])
            print(f"✅ Found {len(records2)} records with LastUpdated = 20250803")
            
            if records2:
                print("📋 Sample records with LastUpdated = 20250803:")
                for i, record in enumerate(records2[:3]):  # Show first 3
                    print(f"   Record {i+1}:")
                    print(f"     AdArchiveID: {record.get('AdArchiveID')}")
                    print(f"     PageID: {record.get('PageID')}")
                    print(f"     LastUpdated: {record.get('LastUpdated')}")
                    print(f"     EndDate: {record.get('EndDate')}")
            
            # Query 3: Look for both conditions
            print(f"\n3. Querying for PageID=102403108447809 AND LastUpdated=20250803...")
            
            response3 = await table.scan(
                FilterExpression="PageID = :page_id AND LastUpdated = :last_updated",
                ExpressionAttributeValues={
                    ':page_id': '102403108447809',
                    ':last_updated': '20250803'
                },
                Limit=10
            )
            
            records3 = response3.get('Items', [])
            print(f"✅ Found {len(records3)} records for PageID=102403108447809 AND LastUpdated=20250803")
            
            if records3:
                print("📋 Records matching both conditions:")
                for i, record in enumerate(records3):
                    print(f"   Record {i+1}:")
                    print(f"     AdArchiveID: {record.get('AdArchiveID')}")
                    print(f"     StartDate: {record.get('StartDate')}")
                    print(f"     LastUpdated: {record.get('LastUpdated')}")
                    print(f"     EndDate: {record.get('EndDate')}")
                    print(f"     S3ImageKey: {record.get('S3ImageKey', 'Not set')}")
            else:
                print("❌ NO RECORDS found matching both PageID=102403108447809 AND LastUpdated=20250803")
                print("   This confirms the user's report of ZERO results!")
        
        return True
        
    except Exception as e:
        print(f"❌ Query failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(query_specific_records())
    if success:
        print("\n✅ Specific query test completed")
    else:
        print("\n❌ Specific query test failed")
    
    sys.exit(0 if success else 1)