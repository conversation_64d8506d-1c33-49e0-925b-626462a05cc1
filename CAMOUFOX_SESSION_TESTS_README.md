# Camoufox Session Management Test Suite

## Overview

This comprehensive test suite validates all critical fixes implemented for Camoufox session management, specifically addressing the **session persistence bug** that caused image downloads to fail with "Session not available" errors.

## Critical Fixes Validated

### 1. **Session Persistence Across Job Phases** 🎯
- **Issue**: Session was being cleaned up prematurely between Phase 1 (GraphQL capture) and Phase 2 (image downloads)
- **Fix**: Removed premature cleanup from job orchestration service
- **Tests**: `test_session_integration_flow.py` - validates session remains valid through all phases

### 2. **Race Condition Prevention** ⚡
- **Issue**: Concurrent operations during cleanup could cause race conditions
- **Fix**: Atomic state management with async locks and pending operation tracking
- **Tests**: `test_race_condition_prevention.py` - validates concurrent operations don't interfere

### 3. **Enhanced Health Validation** 🏥
- **Issue**: Basic session validation was insufficient
- **Fix**: Comprehensive health monitoring with browser, page, token, and network checks
- **Tests**: `test_session_health_validation.py` - validates all health check components

### 4. **Resource Cleanup & Memory Leak Prevention** 🧹
- **Issue**: Improper resource cleanup could cause memory leaks
- **Fix**: Comprehensive resource tracking and cleanup sequencing
- **Tests**: All test files include resource cleanup validation

## Test Files

### 1. `test_camoufox_session_management_comprehensive.py`
**Complete integration test suite covering all fixes**

- ✅ Session health validation with various states
- ✅ Race condition prevention during cleanup
- ✅ Circuit breaker activation and recovery
- ✅ Resource cleanup and memory leak detection
- ✅ Full integration flows
- ✅ Performance monitoring

**Key Test Classes:**
- `TestSessionHealthValidation`
- `TestRaceConditionPrevention`
- `TestErrorRecoveryAndCircuitBreaker`
- `TestResourceCleanupAndMemoryLeaks`
- `TestIntegrationFlows`
- `TestPerformanceAndMetrics`

### 2. `test_session_health_validation.py`
**Focused health monitoring tests**

- ✅ Browser context health validation
- ✅ Page accessibility checks
- ✅ Token validation (fb_dtsg, lsd, jazoest)
- ✅ Network connectivity validation
- ✅ Health result aggregation
- ✅ Timeout handling

### 3. `test_race_condition_prevention.py`
**Concurrent operation safety tests**

- ✅ Concurrent operations during cleanup
- ✅ Atomic state management
- ✅ Pending operations tracking
- ✅ Cleanup sequencing
- ✅ State consistency under load
- ✅ Resource leak prevention

### 4. `test_session_integration_flow.py`
**End-to-end session persistence validation**

- ✅ **Session persistence across phases** (THE MAIN FIX)
- ✅ Image download flow with session
- ✅ Multi-phase coordination
- ✅ Session state transitions
- ✅ Error handling during phases
- ✅ Session recovery scenarios

### 5. `run_camoufox_session_tests.py`
**Comprehensive test runner**

- 🚀 Runs all test suites in sequence
- 📊 Provides detailed reporting
- 🎯 Can run individual test suites
- 📋 Lists available tests
- ⚡ Parallel execution support

## Usage

### Run All Tests
```bash
python run_camoufox_session_tests.py
```

### Run Individual Test Suite
```bash
python run_camoufox_session_tests.py --test health
python run_camoufox_session_tests.py --test race
python run_camoufox_session_tests.py --test integration
```

### List Available Tests
```bash
python run_camoufox_session_tests.py --list
```

### Run Specific Test File Directly
```bash
python test_session_health_validation.py
python test_race_condition_prevention.py
python test_session_integration_flow.py
python test_camoufox_session_management_comprehensive.py
```

## Mock Framework

The test suite includes comprehensive mocks for:

- **MockAsyncCamoufox**: Simulates browser creation and management
- **MockBrowser**: Browser instance with context management
- **MockContext**: Browser context with page creation
- **MockPage**: Page instance with navigation and evaluation
- **MockSessionManager**: Session management with state tracking

## Test Scenarios

### Session Health Validation
- ✅ Healthy browser with multiple contexts
- ✅ Missing browser instance
- ✅ Unresponsive browser
- ✅ Healthy page with Facebook URL
- ✅ Closed page detection
- ✅ Page timeout handling
- ✅ Complete token set (fb_dtsg, lsd, jazoest)
- ✅ Missing critical tokens
- ✅ Aging token detection
- ✅ Network connectivity checks
- ✅ Health result aggregation

### Race Condition Prevention
- ✅ 10+ concurrent operations during cleanup
- ✅ Atomic session creation (5 concurrent attempts)
- ✅ Pending operation tracking and cancellation
- ✅ Proper cleanup sequencing (page → context → browser)
- ✅ 50+ operations under high load
- ✅ Resource leak detection across multiple cycles
- ✅ 8 concurrent create/cleanup cycles

### Integration Flow Validation
- ✅ **Session persistence through job orchestration** (main fix)
- ✅ Image download with session headers
- ✅ Multi-phase coordination (GraphQL → Images → Database)
- ✅ Session state transitions
- ✅ Error recovery during phases
- ✅ Concurrent phase operations
- ✅ Session recovery scenarios

## Expected Results

### Success Criteria
- ✅ **All tests pass** (100% success rate)
- ✅ **Session persists across phases** (main fix validated)
- ✅ **No race conditions detected**
- ✅ **Memory usage remains stable**
- ✅ **Resource cleanup completes successfully**
- ✅ **Health monitoring functions correctly**

### Performance Benchmarks
- 📈 Session creation: < 1 second
- 📈 Health checks: < 100ms each
- 📈 Cleanup operations: < 5 seconds
- 📈 Memory increase: < 50MB for 5 sessions
- 📈 Concurrent operations: No failures under load

## Key Validations

### 🎯 The Main Fix: Session Persistence
The most critical test validates that sessions remain valid between job phases:

```python
# BEFORE (Bug): Session cleanup in job orchestration
async def _process_single_firm_with_worker(self, job):
    try:
        # Phase 1: GraphQL capture
        pass
    finally:
        # BUG: Premature cleanup here!
        await session_manager.cleanup()  # ❌

# AFTER (Fixed): No premature cleanup
async def _process_single_firm_with_worker(self, job):
    try:
        # Phase 1: GraphQL capture
        pass
    finally:
        # FIXED: Cleanup deferred to job runner
        pass  # ✅
```

### 🔍 Critical Test Points
1. **Session validity after job orchestration**: `assert session_manager.is_session_valid()`
2. **Image downloads succeed**: Headers available for HTTP requests
3. **Resource cleanup timing**: Only after ALL phases complete
4. **Concurrent safety**: No race conditions during cleanup
5. **Memory stability**: No resource leaks across multiple cycles

## Debugging Failed Tests

### Common Issues
1. **Import errors**: Ensure all session management modules are available
2. **Async context**: Tests must run in proper async environment
3. **Mock setup**: Verify mock objects are properly configured
4. **Timing issues**: Increase timeouts if tests are flaky

### Debug Commands
```bash
# Run with verbose output
python run_camoufox_session_tests.py --verbose

# Run specific failing test
python test_session_integration_flow.py

# Check specific test method
python -c "import asyncio; from test_session_integration_flow import test_session_persistence_across_phases; asyncio.run(test_session_persistence_across_phases())"
```

## Integration with CI/CD

Add to your CI pipeline:

```yaml
- name: Run Camoufox Session Tests
  run: |
    cd /path/to/project
    python run_camoufox_session_tests.py
  timeout-minutes: 10
```

## Validation Checklist

Before deploying session management fixes:

- [ ] All test suites pass (100% success rate)
- [ ] Session persistence validated across phases
- [ ] Race condition prevention confirmed
- [ ] Memory leaks not detected
- [ ] Health monitoring functional
- [ ] Resource cleanup working
- [ ] Performance benchmarks met
- [ ] Integration tests pass

## Contact

For issues with the test suite or session management fixes:
- Check test output for specific error details
- Review session manager implementation
- Verify mock object configurations
- Ensure proper async/await usage

---

**🎉 Success Indicator**: All tests passing means the critical session persistence bug is fixed and Camoufox session management is working correctly!