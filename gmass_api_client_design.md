# GMass.co API Client Library Design

## Overview

A comprehensive Python client library for the GMass.co email marketing platform API, providing complete endpoint coverage for campaign drafts, campaigns, transactional emails, and Google Sheets integration.

## Architecture Design

### Core Components

#### 1. Base Client Architecture
```python
# Base API client with authentication and common functionality
class GMassAPIClient(AsyncServiceBase):
    - Authentication management (API key)
    - HTTP client configuration 
    - Rate limiting and retry logic
    - Response validation and error handling
    - Logging and monitoring integration

# Configuration model for client settings
class GMassConfig(BaseModel):
    - api_key: str
    - base_url: str = "https://api.gmass.co/api"
    - timeout: int = 30
    - max_retries: int = 3
    - rate_limit_per_minute: int = 60
```

#### 2. Endpoint Service Modules

**Campaign Drafts Service**
```python
class CampaignDraftsService(AsyncServiceBase):
    async def create_draft(
        self, 
        subject: str,
        body: str,
        recipients_list_address: str,
        cc_addresses: List[str] = None,
        bcc_addresses: List[str] = None,
        attachments: List[str] = None
    ) -> CampaignDraftResponse
    
    async def get_draft(self, draft_id: str) -> CampaignDraftResponse
    async def update_draft(self, draft_id: str, **kwargs) -> CampaignDraftResponse
    async def delete_draft(self, draft_id: str) -> bool
```

**Campaigns Service** 
```python
class CampaignsService(AsyncServiceBase):
    async def send_campaign(
        self, 
        gmail_draft_id: str,
        send_at: datetime = None
    ) -> CampaignResponse
    
    async def get_campaign(self, campaign_id: str) -> CampaignResponse
    async def list_campaigns(
        self, 
        limit: int = 50,
        offset: int = 0,
        status: str = None
    ) -> List[CampaignResponse]
    
    async def cancel_campaign(self, campaign_id: str) -> bool
    async def get_campaign_statistics(self, campaign_id: str) -> CampaignStatsResponse
```

**Transactional Emails Service**
```python
class TransactionalService(AsyncServiceBase):
    async def send_email(
        self,
        to: str,
        subject: str,
        body: str,
        cc: List[str] = None,
        bcc: List[str] = None,
        thread_to_campaign: str = None,
        use_custom_smtp: bool = False,
        attachments: List[str] = None
    ) -> TransactionalResponse
    
    async def get_email_status(self, email_id: str) -> TransactionalStatusResponse
```

**Google Sheets Service**
```python
class SheetsService(AsyncServiceBase):
    async def list_sheets(self) -> List[GoogleSheetResponse]
    async def get_sheet(self, sheet_id: str) -> GoogleSheetResponse
    async def list_worksheets(self, sheet_id: str) -> List[WorksheetResponse]
    async def get_worksheet(self, sheet_id: str, worksheet_id: str) -> WorksheetResponse
    async def create_list_from_sheet(
        self,
        sheet_id: str,
        worksheet_id: str,
        email_column: str = "A",
        name_column: str = None
    ) -> ListResponse
```

**Reports Service**
```python
class ReportsService(AsyncServiceBase):
    async def get_opens(self, campaign_id: str) -> List[OpenEvent]
    async def get_clicks(self, campaign_id: str) -> List[ClickEvent]
    async def get_replies(self, campaign_id: str) -> List[ReplyEvent]
    async def get_bounces(self, campaign_id: str) -> List[BounceEvent]
    async def get_unsubscribes(self, campaign_id: str) -> List[UnsubscribeEvent]
    async def get_all_events(self, campaign_id: str) -> CampaignEventsResponse
```

#### 3. Data Models

**Request/Response Models**
```python
# Base models
class GMassBaseModel(BaseModel):
    class Config:
        extra = "forbid"
        validate_assignment = True

# Campaign models
class CampaignDraftRequest(GMassBaseModel):
    subject: str
    body: str
    recipients_list_address: str
    cc_addresses: Optional[List[str]] = None
    bcc_addresses: Optional[List[str]] = None
    attachments: Optional[List[str]] = None

class CampaignDraftResponse(GMassBaseModel):
    gmail_draft_id: str
    subject: str
    created_at: datetime
    status: str

class CampaignResponse(GMassBaseModel):
    campaign_id: str
    gmail_draft_id: str
    subject: str
    status: str
    sent_at: Optional[datetime]
    total_recipients: int
    opens: int
    clicks: int
    replies: int
    bounces: int
    unsubscribes: int

# Transactional models
class TransactionalRequest(GMassBaseModel):
    to: str
    subject: str
    body: str
    cc: Optional[List[str]] = None
    bcc: Optional[List[str]] = None
    thread_to_campaign: Optional[str] = None
    use_custom_smtp: bool = False
    attachments: Optional[List[str]] = None

class TransactionalResponse(GMassBaseModel):
    email_id: str
    status: str
    sent_at: datetime
    message: str

# Sheets models
class GoogleSheetResponse(GMassBaseModel):
    sheet_id: str
    name: str
    url: str
    created_date: datetime

class WorksheetResponse(GMassBaseModel):
    worksheet_id: str
    name: str
    rows: int
    columns: int

# Reports models
class OpenEvent(GMassBaseModel):
    email: str
    opened_at: datetime
    ip_address: str
    user_agent: str

class ClickEvent(GMassBaseModel):
    email: str
    clicked_at: datetime
    url: str
    ip_address: str
```

#### 4. Factory Pattern for Service Creation

```python
class GMassServiceFactory:
    """Factory for creating GMass API service instances with dependency injection."""
    
    def __init__(self, config: GMassConfig, logger: LoggerProtocol):
        self.config = config
        self.logger = logger
        self._http_client = None
    
    async def create_campaigns_service(self) -> CampaignsService:
        client = await self._get_http_client()
        service = CampaignsService(logger=self.logger, config=self.config.dict())
        service.set_dependency("http_client", client)
        await service.initialize()
        return service
    
    async def create_campaign_drafts_service(self) -> CampaignDraftsService:
        # Similar pattern for other services...
        
    async def _get_http_client(self) -> httpx.AsyncClient:
        if not self._http_client:
            self._http_client = httpx.AsyncClient(
                base_url=self.config.base_url,
                timeout=self.config.timeout,
                headers={"X-apikey": self.config.api_key}
            )
        return self._http_client
```

#### 5. Unified Client Interface

```python
class GMassClient(AsyncServiceBase):
    """Main GMass API client providing access to all services."""
    
    def __init__(self, config: GMassConfig, logger: LoggerProtocol):
        super().__init__(logger, config.dict())
        self.config = config
        self._factory = GMassServiceFactory(config, logger)
        
        # Service instances
        self.campaigns: Optional[CampaignsService] = None
        self.campaign_drafts: Optional[CampaignDraftsService] = None
        self.transactional: Optional[TransactionalService] = None
        self.sheets: Optional[SheetsService] = None
        self.reports: Optional[ReportsService] = None
    
    async def _initialize_service(self) -> None:
        """Initialize all service instances."""
        self.campaigns = await self._factory.create_campaigns_service()
        self.campaign_drafts = await self._factory.create_campaign_drafts_service()
        self.transactional = await self._factory.create_transactional_service()
        self.sheets = await self._factory.create_sheets_service()
        self.reports = await self._factory.create_reports_service()
    
    async def _cleanup_service(self) -> None:
        """Cleanup all service instances."""
        services = [
            self.campaigns, self.campaign_drafts, 
            self.transactional, self.sheets, self.reports
        ]
        for service in services:
            if service:
                await service.cleanup()
```

### Advanced Features

#### 1. Error Handling & Retries
```python
class GMassAPIError(Exception):
    """Base exception for GMass API errors."""
    pass

class GMassAuthenticationError(GMassAPIError):
    """Authentication/API key related errors."""
    pass

class GMassRateLimitError(GMassAPIError):
    """Rate limiting errors."""
    pass

class GMassValidationError(GMassAPIError):
    """Request validation errors."""
    pass

# Retry logic with exponential backoff
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((httpx.TimeoutException, GMassRateLimitError))
)
async def _make_request(self, method: str, endpoint: str, **kwargs):
    # Implementation with comprehensive error handling
```

#### 2. Rate Limiting
```python
class RateLimiter:
    """Token bucket rate limiter for API requests."""
    
    def __init__(self, max_requests: int, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.tokens = max_requests
        self.last_refill = time.time()
    
    async def acquire(self) -> None:
        """Acquire a token for making a request."""
        await self._refill_tokens()
        if self.tokens < 1:
            wait_time = self.time_window - (time.time() - self.last_refill)
            await asyncio.sleep(wait_time)
            await self._refill_tokens()
        self.tokens -= 1
```

#### 3. Response Caching
```python
class ResponseCache:
    """Cache for API responses with TTL support."""
    
    def __init__(self, default_ttl: int = 300):
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.default_ttl = default_ttl
    
    async def get(self, key: str) -> Optional[Any]:
        if key in self.cache:
            value, expiry = self.cache[key]
            if time.time() < expiry:
                return value
            del self.cache[key]
        return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> None:
        ttl = ttl or self.default_ttl
        self.cache[key] = (value, time.time() + ttl)
```

### Integration Patterns

#### 1. Async Context Manager Support
```python
class GMassClient(AsyncServiceBase):
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

# Usage:
async with GMassClient(config, logger) as client:
    campaigns = await client.campaigns.list_campaigns()
```

#### 2. Dependency Injection Integration
```python
class GMassContainer(containers.DeclarativeContainer):
    """DI container for GMass services."""
    
    config = providers.Configuration()
    
    # HTTP client
    http_client = providers.Singleton(
        httpx.AsyncClient,
        base_url=config.gmass.base_url,
        timeout=config.gmass.timeout,
        headers={"X-apikey": config.gmass.api_key}
    )
    
    # Rate limiter
    rate_limiter = providers.Singleton(
        RateLimiter,
        max_requests=config.gmass.rate_limit_per_minute
    )
    
    # Services
    campaigns_service = providers.Factory(
        CampaignsService,
        http_client=http_client,
        rate_limiter=rate_limiter,
        config=config.gmass
    )
    
    # Main client
    gmass_client = providers.Factory(
        GMassClient,
        campaigns_service=campaigns_service,
        config=config.gmass
    )
```

### Usage Examples

#### 1. Basic Campaign Management
```python
async def create_and_send_campaign():
    config = GMassConfig(
        api_key="your-api-key",
        rate_limit_per_minute=30
    )
    
    async with GMassClient(config, logger) as client:
        # Create campaign draft
        draft = await client.campaign_drafts.create_draft(
            subject="Welcome to our newsletter",
            body="Hello {FirstName}, welcome to our community!",
            recipients_list_address="<EMAIL>"
        )
        
        # Send campaign
        campaign = await client.campaigns.send_campaign(
            gmail_draft_id=draft.gmail_draft_id
        )
        
        # Monitor campaign performance
        stats = await client.campaigns.get_campaign_statistics(
            campaign_id=campaign.campaign_id
        )
        
        return stats
```

#### 2. Transactional Email with Error Handling
```python
async def send_transactional_email_with_retry():
    async with GMassClient(config, logger) as client:
        try:
            result = await client.transactional.send_email(
                to="<EMAIL>",
                subject="Password Reset",
                body="Click here to reset your password: {reset_link}",
                use_custom_smtp=True
            )
            return result
        except GMassRateLimitError:
            # Handle rate limiting
            await asyncio.sleep(60)
            return await client.transactional.send_email(...)
        except GMassValidationError as e:
            # Handle validation errors
            logger.error(f"Validation error: {e}")
            raise
```

#### 3. Sheets Integration Workflow
```python
async def create_campaign_from_sheet():
    async with GMassClient(config, logger) as client:
        # List available sheets
        sheets = await client.sheets.list_sheets()
        target_sheet = sheets[0]
        
        # Get worksheets
        worksheets = await client.sheets.list_worksheets(target_sheet.sheet_id)
        main_worksheet = worksheets[0]
        
        # Create GMass list from sheet
        email_list = await client.sheets.create_list_from_sheet(
            sheet_id=target_sheet.sheet_id,
            worksheet_id=main_worksheet.worksheet_id,
            email_column="A",
            name_column="B"
        )
        
        # Create and send campaign
        draft = await client.campaign_drafts.create_draft(
            subject="Personalized Newsletter",
            body="Hi {Name}, here's your personalized content...",
            recipients_list_address=email_list.address
        )
        
        campaign = await client.campaigns.send_campaign(
            gmail_draft_id=draft.gmail_draft_id
        )
        
        return campaign
```

### Testing Strategy

#### 1. Unit Tests with Mocking
```python
@pytest.mark.unit
class TestCampaignsService:
    @pytest.fixture
    async def service(self, mock_http_client, logger):
        service = CampaignsService(logger, {})
        service.set_dependency("http_client", mock_http_client)
        await service.initialize()
        return service
    
    async def test_send_campaign_success(self, service, mock_http_client):
        mock_http_client.post.return_value = Mock(
            status_code=200,
            json=lambda: {"campaign_id": "123", "status": "sent"}
        )
        
        result = await service.send_campaign("draft_123")
        assert result.campaign_id == "123"
        assert result.status == "sent"
```

#### 2. Integration Tests
```python
@pytest.mark.integration
class TestGMassClientIntegration:
    @pytest.fixture
    async def client(self):
        config = GMassConfig(
            api_key=os.getenv("GMASS_API_KEY_TEST"),
            base_url="https://api-staging.gmass.co/api"
        )
        async with GMassClient(config, logger) as client:
            yield client
    
    async def test_full_campaign_workflow(self, client):
        # Test complete workflow from draft to campaign
        pass
```

This design provides a comprehensive, production-ready GMass.co API client library that follows modern Python async patterns, includes proper error handling, rate limiting, and integrates seamlessly with the existing LexGenius architecture.