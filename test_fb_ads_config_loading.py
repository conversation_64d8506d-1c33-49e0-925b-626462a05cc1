#!/usr/bin/env python3
"""
Test script to verify Facebook ads configuration loading and profile cleaning
works correctly with both direct config and workflow config loading.

Tests:
1. Direct config loading: --config fb_ads
2. Workflow config loading: --config workflows/fb_ads
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def run_command(cmd, description):
    """Run a command and capture output."""
    print(f"\n{'='*60}")
    print(f"🧪 TEST: {description}")
    print(f"📋 Command: {' '.join(cmd)}")
    print(f"{'='*60}\n")
    
    start_time = time.time()
    try:
        # Run command and capture output
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        elapsed = time.time() - start_time
        
        # Check for key indicators in output
        output = result.stdout + result.stderr
        
        # Look for profile cleaning indicators
        profile_cleaned = "Profile directory successfully deleted" in output
        force_clean_enabled = "force_clean_profile=True" in output
        profile_verification = "Profile directory is clean before browser launch" in output
        ad_blocker_detected = "ad blocker" in output.lower() or "adblocker" in output.lower()
        
        print(f"⏱️ Elapsed time: {elapsed:.2f}s")
        print(f"\n📊 Results:")
        print(f"  - force_clean_profile enabled: {'✅' if force_clean_enabled else '❌'}")
        print(f"  - Profile cleaned: {'✅' if profile_cleaned else '❌'}")
        print(f"  - Profile verified clean: {'✅' if profile_verification else '❌'}")
        print(f"  - Ad blocker detected: {'❌ BAD' if ad_blocker_detected else '✅ GOOD'}")
        
        if ad_blocker_detected:
            print("\n⚠️ AD BLOCKER DETECTION FOUND IN OUTPUT!")
            # Show relevant lines
            for line in output.split('\n'):
                if 'ad' in line.lower() and 'block' in line.lower():
                    print(f"  > {line.strip()}")
        
        # Show key log lines
        print("\n📝 Key log lines:")
        for line in output.split('\n'):
            if any(key in line for key in [
                'force_clean_profile', 
                'Profile directory', 
                'Profile cleaning',
                'Camoufox config section',
                'No addons detected',
                'Found modal'
            ]):
                print(f"  {line.strip()}")
        
        return {
            'success': result.returncode == 0,
            'force_clean_enabled': force_clean_enabled,
            'profile_cleaned': profile_cleaned,
            'profile_verified': profile_verification,
            'ad_blocker_detected': ad_blocker_detected,
            'elapsed': elapsed
        }
        
    except subprocess.TimeoutExpired:
        print(f"❌ Command timed out after 120 seconds")
        return {
            'success': False,
            'force_clean_enabled': False,
            'profile_cleaned': False,
            'profile_verified': False,
            'ad_blocker_detected': False,
            'elapsed': 120
        }
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return {
            'success': False,
            'force_clean_enabled': False,
            'profile_cleaned': False,
            'profile_verified': False,
            'ad_blocker_detected': False,
            'elapsed': 0
        }

def main():
    """Run tests for both config loading methods."""
    print("🔬 Facebook Ads Configuration Loading Test")
    print("Testing profile cleaning and ad blocker prevention\n")
    
    # Check if we're in the right directory
    if not Path("run_pipeline.sh").exists():
        print("❌ Error: run_pipeline.sh not found. Run this from the project root.")
        sys.exit(1)
    
    # Test configurations
    tests = [
        {
            'cmd': ['./run_pipeline.sh', '--config', 'fb_ads'],
            'description': 'Direct config loading (config/fb_ads.yml)'
        },
        {
            'cmd': ['./run_pipeline.sh', '--config', 'workflows/fb_ads'],
            'description': 'Workflow config loading (config/workflows/fb_ads.yml)'
        }
    ]
    
    results = []
    
    # Run tests
    for test in tests:
        result = run_command(test['cmd'], test['description'])
        results.append({
            'test': test['description'],
            'result': result
        })
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}\n")
    
    all_passed = True
    for r in results:
        test_name = r['test']
        result = r['result']
        
        # Test passes if profile cleaning works and no ad blocker detected
        passed = (result['force_clean_enabled'] and 
                 (result['profile_cleaned'] or result['profile_verified']) and 
                 not result['ad_blocker_detected'])
        
        status = '✅ PASS' if passed else '❌ FAIL'
        all_passed = all_passed and passed
        
        print(f"{status} - {test_name}")
        if not passed:
            print(f"     Issues:")
            if not result['force_clean_enabled']:
                print(f"       - force_clean_profile not enabled")
            if not (result['profile_cleaned'] or result['profile_verified']):
                print(f"       - Profile not cleaned")
            if result['ad_blocker_detected']:
                print(f"       - Ad blocker detected!")
    
    print(f"\n{'='*60}")
    if all_passed:
        print("✅ All tests passed! Profile cleaning and ad blocker prevention working correctly.")
    else:
        print("❌ Some tests failed. Check the issues above.")
    
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()