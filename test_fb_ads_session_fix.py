#!/usr/bin/env python3
"""
Test script to verify that the browser session remains valid throughout all job phases.
This tests the fix for the premature session cleanup issue.
"""
import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_session_persistence():
    """Test that session remains valid through all job phases."""
    try:
        # Import necessary modules
        from src.config_models.scraper import ScraperConfig
        from src.config_models.loader import load_config
        from src.container import ServiceContainer
        
        # Load configuration
        config_dict = {
            'date': datetime.now().strftime('%m/%d/%y'),
            'iso_date': datetime.now().strftime('%Y%m%d'),
            'fb_ads': True,
            'use_graphql_capture': True,
            'feature_flags': {
                'graphql_enabled': True
            },
            'single_firm_page_id': '313942829424',  # <PERSON> & <PERSON>
            'headless': True,
            'log_level': 'INFO'
        }
        
        config = ScraperConfig(**config_dict)
        
        # Initialize service container
        container = ServiceContainer()
        container.config.from_dict(config.model_dump())
        
        # Get workflow service
        workflow_service = container.fb_ads_workflow_service()
        
        logger.info("🧪 Starting test: Session persistence through job phases")
        logger.info(f"📅 Testing with date: {config.iso_date}")
        logger.info(f"🏢 Testing firm: Morgan & Morgan (ID: 313942829424)")
        
        # Run single firm workflow
        success = await workflow_service.run_single_firm_workflow('313942829424')
        
        if success:
            logger.info("✅ Test PASSED: Workflow completed successfully")
            logger.info("✅ Session remained valid throughout all phases")
            return True
        else:
            logger.error("❌ Test FAILED: Workflow did not complete successfully")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test FAILED with exception: {e}", exc_info=True)
        return False


async def main():
    """Main test runner."""
    logger.info("=" * 80)
    logger.info("FB Ads Session Persistence Test")
    logger.info("=" * 80)
    logger.info("This test verifies that the browser session remains valid")
    logger.info("throughout all job phases after fixing premature cleanup.")
    logger.info("=" * 80)
    
    # Run the test
    success = await test_session_persistence()
    
    logger.info("=" * 80)
    if success:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("The session cleanup fix is working correctly.")
    else:
        logger.error("❌ TESTS FAILED!")
        logger.error("Please check the logs for details.")
    logger.info("=" * 80)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)