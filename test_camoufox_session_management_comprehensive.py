#!/usr/bin/env python3
"""
Comprehensive Test Suite for Camoufox Session Management Fixes

This test suite validates all the critical session management fixes implemented:
1. Session Health Validation Tests
2. Race Condition Prevention Tests  
3. Error Recovery and Circuit Breaker Tests
4. Resource Cleanup and Memory Leak Tests
5. Integration Flow Tests
6. Performance and Metrics Tests

Test Scenarios:
- Enhanced session validation with various states
- Concurrent operations during cleanup
- Circuit breaker activation and recovery
- Session persistence across job phases
- Resource leak detection and prevention
"""

import asyncio
import pytest
import time
import logging
import unittest.mock as mock
from unittest.mock import AsyncMock, MagicMock, patch, call
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import gc
import tracemalloc
import psutil
import os
from concurrent.futures import ThreadPoolExecutor

# Import the session management components
from src.services.fb_ads.camoufox.robust_camoufox_session_manager import RobustCamoufoxSessionManager
from src.services.fb_ads.camoufox.session_health_monitor import (
    SessionHealthMonitor, HealthStatus, HealthResult,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NetworkHealthCheck
)
from src.services.fb_ads.camoufox.session_circuit_breaker import (
    SessionCircuitBreaker, CircuitState, CircuitBreakerOpenException
)
from src.services.fb_ads.camoufox.session_recovery_manager import (
    SessionRecoveryManager, FailureType, RecoveryResult
)
from src.services.fb_ads.camoufox.session_resource_manager import SessionResourceManager

# Test configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TestMetrics:
    """Track test execution metrics"""
    start_time: float = 0.0
    end_time: float = 0.0
    memory_start: int = 0
    memory_end: int = 0
    cpu_start: float = 0.0
    cpu_end: float = 0.0
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time if self.end_time else 0.0
    
    @property
    def memory_delta(self) -> int:
        return self.memory_end - self.memory_start if self.memory_end else 0


class MockBrowser:
    """Mock browser for testing"""
    def __init__(self, should_fail: bool = False):
        self.should_fail = should_fail
        self.contexts = []
        self.closed = False
        self.browser = self  # AsyncCamoufox compatibility
        
    async def new_context(self, **kwargs):
        if self.should_fail:
            raise Exception("Browser context creation failed")
        context = MockContext()
        self.contexts.append(context)
        return context
    
    async def close(self):
        self.closed = True
        for context in self.contexts:
            await context.close()


class MockContext:
    """Mock browser context for testing"""
    def __init__(self, should_fail: bool = False):
        self.should_fail = should_fail
        self.pages = []
        self.closed = False
        
    async def new_page(self):
        if self.should_fail:
            raise Exception("Page creation failed")
        page = MockPage()
        self.pages.append(page)
        return page
    
    async def close(self):
        self.closed = True
        for page in self.pages:
            await page.close()


class MockPage:
    """Mock page for testing"""
    def __init__(self, should_fail: bool = False):
        self.should_fail = should_fail
        self.closed = False
        self._url = "https://www.facebook.com/ads/library"
        self._title = "Facebook Ad Library"
        
    async def url(self) -> str:
        if self.should_fail:
            raise Exception("Page URL access failed")
        return self._url
    
    async def title(self) -> str:
        if self.should_fail:
            raise Exception("Page title access failed")
        return self._title
    
    def is_closed(self) -> bool:
        return self.closed
    
    async def close(self):
        self.closed = True
    
    async def reload(self, **kwargs):
        if self.should_fail:
            raise Exception("Page reload failed")
        await asyncio.sleep(0.1)  # Simulate reload time
    
    async def evaluate(self, script: str):
        if self.should_fail:
            raise Exception("Page evaluation failed")
        # Mock network test response
        if 'fetch' in script:
            return {'ok': True, 'status': 200, 'url': 'https://www.facebook.com/favicon.ico'}
        return {}
    
    async def set_viewport_size(self, **kwargs):
        pass
    
    async def set_extra_http_headers(self, headers: Dict[str, str]):
        pass


class MockAsyncCamoufox:
    """Mock AsyncCamoufox browser manager"""
    def __init__(self, should_fail: bool = False, **kwargs):
        self.should_fail = should_fail
        self.browser = None
        self.config = kwargs
        
    async def __aenter__(self):
        if self.should_fail:
            raise Exception("Browser startup failed")
        self.browser = MockBrowser(self.should_fail)
        return self.browser
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.browser:
            await self.browser.close()
        return False


class TestSessionHealthValidation:
    """Test suite for session health validation"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'health_monitoring': {
                'check_interval': 5,
                'failure_threshold': 2,
                'continuous_monitoring': False
            },
            'circuit_breaker': {
                'failure_threshold': 3,
                'recovery_timeout': 10
            },
            'recovery': {
                'max_attempts': 3,
                'base_delay': 1.0
            },
            'resource_management': {
                'cleanup_timeout': 30.0,
                'leak_detection': True
            }
        }
        
        self.logger = logging.getLogger("TestSession")
        self.dependencies = {
            'fingerprint_manager': MagicMock(),
            'proxy_manager': MagicMock(),
            'law_firms_repository': MagicMock()
        }
        
        # Configure dependencies
        self.dependencies['fingerprint_manager'].generate_fingerprint.return_value = {
            'user_agent': 'Mozilla/5.0 Test Browser'
        }
        self.dependencies['proxy_manager'].get_proxy_config.return_value = {}
    
    @pytest.mark.asyncio
    async def test_enhanced_session_validation_healthy(self):
        """Test enhanced session validation with healthy session"""
        logger.info("Testing enhanced session validation - healthy state")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(
                self.config, self.logger, **self.dependencies
            )
            
            # Set up session data
            session_manager.session_data = {
                'fb_dtsg': 'test_token_123',
                'lsd': 'test_lsd_456',
                'jazoest': 'test_jazoest_789'
            }
            session_manager._is_session_valid = True
            session_manager.session_start_time = time.time()
            
            # Create mock browser and page
            session_manager.browser = MockBrowser()
            session_manager.page = MockPage()
            
            # Test enhanced validation
            is_valid = session_manager.is_session_valid()
            assert is_valid, "Enhanced validation should return True for healthy session"
            
            # Test health monitor validation
            health_result = await session_manager.health_monitor.perform_health_check()
            assert health_result.status == HealthStatus.HEALTHY, f"Health check should be healthy, got: {health_result.status}"
            
            logger.info("✅ Enhanced session validation - healthy state passed")
    
    @pytest.mark.asyncio
    async def test_enhanced_session_validation_degraded(self):
        """Test enhanced session validation with degraded session"""
        logger.info("Testing enhanced session validation - degraded state")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(
                self.config, self.logger, **self.dependencies
            )
            
            # Set up degraded session (missing tokens)
            session_manager.session_data = {
                'fb_dtsg': 'test_token_123',
                # Missing lsd and jazoest tokens
            }
            session_manager._is_session_valid = True
            session_manager.session_start_time = time.time()
            
            # Create mock browser and page
            session_manager.browser = MockBrowser()
            session_manager.page = MockPage()
            
            # Test health monitor validation
            health_result = await session_manager.health_monitor.perform_health_check()
            assert health_result.status in [HealthStatus.CRITICAL, HealthStatus.FAILED], \
                f"Health check should be critical/failed for degraded session, got: {health_result.status}"
            
            logger.info("✅ Enhanced session validation - degraded state passed")
    
    @pytest.mark.asyncio
    async def test_browser_context_health_validation(self):
        """Test browser context health validation"""
        logger.info("Testing browser context health validation")
        
        # Test healthy browser
        health_check = BrowserHealthCheck()
        session_manager = MagicMock()
        session_manager.browser = MockBrowser()
        
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.HEALTHY, "Browser health check should be healthy"
        
        # Test unhealthy browser (no browser)
        session_manager.browser = None
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.FAILED, "Browser health check should fail with no browser"
        
        logger.info("✅ Browser context health validation passed")
    
    @pytest.mark.asyncio
    async def test_network_connectivity_validation(self):
        """Test network connectivity validation"""
        logger.info("Testing network connectivity validation")
        
        health_check = NetworkHealthCheck()
        session_manager = MagicMock()
        session_manager.page = MockPage()
        
        # Test healthy network
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.HEALTHY, "Network health check should be healthy"
        
        # Test network failure
        session_manager.page = MockPage(should_fail=True)
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.CRITICAL, "Network health check should be critical on failure"
        
        logger.info("✅ Network connectivity validation passed")
    
    @pytest.mark.asyncio
    async def test_page_accessibility_checks(self):
        """Test page accessibility checks"""
        logger.info("Testing page accessibility checks")
        
        health_check = PageHealthCheck()
        session_manager = MagicMock()
        
        # Test healthy page
        session_manager.page = MockPage()
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.HEALTHY, "Page health check should be healthy"
        
        # Test closed page
        session_manager.page = MockPage()
        session_manager.page.closed = True
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.FAILED, "Page health check should fail for closed page"
        
        # Test no page
        session_manager.page = None
        result = await health_check.validate(session_manager)
        assert result.status == HealthStatus.FAILED, "Page health check should fail with no page"
        
        logger.info("✅ Page accessibility checks passed")


class TestRaceConditionPrevention:
    """Test suite for race condition prevention"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'health_monitoring': {'continuous_monitoring': False},
            'circuit_breaker': {'failure_threshold': 5},
            'recovery': {'max_attempts': 2},
            'resource_management': {'cleanup_timeout': 15.0}
        }
        self.logger = logging.getLogger("TestRaceCondition")
    
    @pytest.mark.asyncio
    async def test_concurrent_operations_during_cleanup(self):
        """Test concurrent operations during cleanup don't cause race conditions"""
        logger.info("Testing concurrent operations during cleanup")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Create session
            await session_manager.create_new_session()
            assert session_manager.is_session_valid(), "Session should be valid after creation"
            
            # Start concurrent operations
            async def concurrent_operation(op_id: int):
                """Simulate concurrent session operations"""
                try:
                    for i in range(5):
                        if session_manager.is_session_valid():
                            session_data = await session_manager.get_session_data()
                            await asyncio.sleep(0.1)
                        else:
                            break
                    return f"Operation {op_id} completed"
                except Exception as e:
                    return f"Operation {op_id} failed: {e}"
            
            # Start multiple concurrent operations
            operations = [
                asyncio.create_task(concurrent_operation(i))
                for i in range(5)
            ]
            
            # Start cleanup concurrently
            cleanup_task = asyncio.create_task(session_manager.cleanup())
            
            # Wait for all operations to complete
            results = await asyncio.gather(*operations, return_exceptions=True)
            await cleanup_task
            
            # Verify no exceptions occurred due to race conditions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"Operation {i} had exception (expected during cleanup): {result}")
                else:
                    logger.info(f"Operation {i} result: {result}")
            
            # Verify session is properly cleaned up
            assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
            
            logger.info("✅ Concurrent operations during cleanup test passed")
    
    @pytest.mark.asyncio
    async def test_atomic_state_management(self):
        """Test atomic state management during critical operations"""
        logger.info("Testing atomic state management")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Test atomic session creation
            create_task1 = asyncio.create_task(session_manager.create_new_session())
            create_task2 = asyncio.create_task(session_manager.create_new_session())
            
            results = await asyncio.gather(create_task1, create_task2, return_exceptions=True)
            
            # Both should succeed (create_new_session should be idempotent-safe)
            success_count = sum(1 for r in results if r is True)
            assert success_count >= 1, "At least one session creation should succeed"
            
            # Test atomic cleanup
            cleanup_task1 = asyncio.create_task(session_manager.cleanup())
            cleanup_task2 = asyncio.create_task(session_manager.cleanup())
            
            await asyncio.gather(cleanup_task1, cleanup_task2, return_exceptions=True)
            
            # Session should be properly cleaned up
            assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
            
            logger.info("✅ Atomic state management test passed")
    
    @pytest.mark.asyncio
    async def test_pending_operations_tracking(self):
        """Test tracking of pending operations during cleanup"""
        logger.info("Testing pending operations tracking")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            await session_manager.create_new_session()
            
            # Create long-running operation
            async def long_operation():
                """Simulate long-running operation"""
                await asyncio.sleep(2.0)
                return "Long operation completed"
            
            # Start operation
            operation_task = asyncio.create_task(long_operation())
            
            # Start cleanup while operation is running
            cleanup_start = time.time()
            cleanup_task = asyncio.create_task(session_manager.cleanup())
            
            # Wait for both to complete
            operation_result = await operation_task
            await cleanup_task
            cleanup_duration = time.time() - cleanup_start
            
            logger.info(f"Operation result: {operation_result}")
            logger.info(f"Cleanup duration: {cleanup_duration:.2f}s")
            
            # Cleanup should complete successfully
            assert not session_manager.is_session_valid(), "Session should be cleaned up"
            
            logger.info("✅ Pending operations tracking test passed")
    
    @pytest.mark.asyncio
    async def test_cleanup_sequencing(self):
        """Test proper sequencing of cleanup operations"""
        logger.info("Testing cleanup sequencing")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            await session_manager.create_new_session()
            
            # Track cleanup sequence
            cleanup_sequence = []
            
            # Mock resource manager to track cleanup order
            original_cleanup = session_manager.resource_manager.cleanup_all
            
            async def tracked_cleanup(*args, **kwargs):
                cleanup_sequence.append("resource_manager_cleanup")
                return await original_cleanup(*args, **kwargs)
            
            session_manager.resource_manager.cleanup_all = tracked_cleanup
            
            # Mock core resource cleanup
            original_core_cleanup = session_manager._cleanup_core_resources
            
            async def tracked_core_cleanup():
                cleanup_sequence.append("core_resources_cleanup")
                return await original_core_cleanup()
            
            session_manager._cleanup_core_resources = tracked_core_cleanup
            
            # Perform cleanup
            await session_manager.cleanup()
            
            # Verify cleanup sequence
            expected_sequence = ["resource_manager_cleanup", "core_resources_cleanup"]
            assert cleanup_sequence == expected_sequence, \
                f"Cleanup sequence should be {expected_sequence}, got {cleanup_sequence}"
            
            logger.info("✅ Cleanup sequencing test passed")


class TestErrorRecoveryAndCircuitBreaker:
    """Test suite for error recovery and circuit breaker functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'circuit_breaker': {
                'failure_threshold': 3,
                'recovery_timeout': 5,
                'half_open_max_calls': 2
            },
            'recovery': {
                'max_attempts': 3,
                'base_delay': 0.5,
                'max_delay': 2.0
            },
            'health_monitoring': {'continuous_monitoring': False}
        }
        self.logger = logging.getLogger("TestErrorRecovery")
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_activation_and_reset(self):
        """Test circuit breaker activation and reset behavior"""
        logger.info("Testing circuit breaker activation and reset")
        
        circuit_breaker = SessionCircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1,  # Short timeout for testing
            half_open_max_calls=2
        )
        
        # Test normal operation (closed state)
        assert circuit_breaker.state == CircuitState.CLOSED
        
        async def failing_operation():
            raise Exception("Operation failed")
        
        async def successful_operation():
            return "Success"
        
        # Trigger failures to open circuit
        for i in range(3):
            try:
                await circuit_breaker.call(failing_operation)
            except Exception:
                pass  # Expected failures
        
        # Circuit should now be open
        assert circuit_breaker.state == CircuitState.OPEN
        
        # Operations should be blocked
        with pytest.raises(CircuitBreakerOpenException):
            await circuit_breaker.call(successful_operation)
        
        # Wait for recovery timeout
        await asyncio.sleep(1.2)
        
        # Next call should transition to half-open
        try:
            result = await circuit_breaker.call(successful_operation)
            assert result == "Success"
        except CircuitBreakerOpenException:
            # First call after timeout should transition to half-open
            pass
        
        # After enough successful calls, circuit should close
        for _ in range(2):
            try:
                await circuit_breaker.call(successful_operation)
            except CircuitBreakerOpenException:
                # May still be in half-open state
                pass
        
        logger.info("✅ Circuit breaker activation and reset test passed")
    
    @pytest.mark.asyncio
    async def test_exponential_backoff_behavior(self):
        """Test exponential backoff behavior in recovery attempts"""
        logger.info("Testing exponential backoff behavior")
        
        # Mock recovery manager
        from src.services.fb_ads.camoufox.session_recovery_manager import SessionRecoveryManager
        
        recovery_manager = SessionRecoveryManager(
            session_manager=MagicMock(),
            config={
                'max_attempts': 4,
                'base_delay': 0.1,  # Fast for testing
                'max_delay': 1.0,
                'jitter': False  # Disable jitter for predictable testing
            }
        )
        
        # Track delays
        delays = []
        start_time = time.time()
        
        # Mock a failing recovery strategy
        async def failing_strategy(session_manager, failure_context=None):
            delays.append(time.time() - start_time)
            raise Exception("Recovery failed")
        
        # Add failing strategy
        recovery_manager.strategies = [MagicMock()]
        recovery_manager.strategies[0].execute_with_timeout = failing_strategy
        recovery_manager.strategies[0].is_applicable.return_value = True
        recovery_manager.strategies[0].name = "test_strategy"
        
        # Attempt recovery (should fail with backoff)
        result = await recovery_manager.attempt_recovery(
            FailureType.SESSION_CREATION_FAILED,
            {'test': True}
        )
        
        assert result is False, "Recovery should fail"
        assert len(delays) >= 1, "Should have at least one attempt"
        
        logger.info("✅ Exponential backoff behavior test passed")
    
    @pytest.mark.asyncio
    async def test_session_recreation_after_failures(self):
        """Test session recreation after various failure scenarios"""
        logger.info("Testing session recreation after failures")
        
        failure_scenarios = [
            (MockAsyncCamoufox(should_fail=True), "Browser startup failure"),
            (lambda: MockBrowser(should_fail=True), "Browser context failure"),
        ]
        
        for scenario_setup, description in failure_scenarios:
            logger.info(f"Testing scenario: {description}")
            
            with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', scenario_setup):
                session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
                
                # First attempt should fail
                result = await session_manager.create_new_session()
                
                # Session manager should handle failure gracefully
                if result:
                    logger.info(f"✅ Session creation succeeded despite {description}")
                else:
                    logger.info(f"Session creation failed as expected for {description}")
                
                # Cleanup
                await session_manager.cleanup()
        
        logger.info("✅ Session recreation after failures test passed")
    
    @pytest.mark.asyncio
    async def test_fallback_to_direct_http(self):
        """Test fallback to direct HTTP when browser sessions fail"""
        logger.info("Testing fallback to direct HTTP")
        
        # This would be implemented in the actual image handler
        # For now, we test the principle with session validation
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Create session
            await session_manager.create_new_session()
            
            # Simulate session becoming invalid
            session_manager._is_session_valid = False
            session_manager.page = None
            
            # Test fallback logic (session should be invalid)
            assert not session_manager.is_session_valid(), "Session should be invalid for fallback"
            
            # Test session headers for direct HTTP
            headers = session_manager.get_session_headers()
            assert 'Content-Type' in headers, "Headers should be available for direct HTTP"
            
            await session_manager.cleanup()
        
        logger.info("✅ Fallback to direct HTTP test passed")


class TestResourceCleanupAndMemoryLeaks:
    """Test suite for resource cleanup and memory leak prevention"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'resource_management': {
                'cleanup_timeout': 30.0,
                'leak_detection': True,
                'max_resources_per_type': 10
            },
            'health_monitoring': {'continuous_monitoring': False}
        }
        self.logger = logging.getLogger("TestResourceCleanup")
    
    @pytest.mark.asyncio
    async def test_comprehensive_resource_cleanup(self):
        """Test comprehensive resource cleanup"""
        logger.info("Testing comprehensive resource cleanup")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Create session with resources
            await session_manager.create_new_session()
            
            # Verify resources are created
            assert session_manager.browser is not None, "Browser should be created"
            assert session_manager.page is not None, "Page should be created"
            assert session_manager.context is not None, "Context should be created"
            
            # Perform cleanup
            await session_manager.cleanup()
            
            # Verify resources are cleaned up
            assert session_manager.browser is None, "Browser should be cleaned up"
            assert session_manager.page is None, "Page should be cleaned up"
            assert session_manager.context is None, "Context should be cleaned up"
            assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
            
            logger.info("✅ Comprehensive resource cleanup test passed")
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """Test memory leak detection and prevention"""
        logger.info("Testing memory leak detection")
        
        # Start memory tracking
        tracemalloc.start()
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Create and destroy multiple sessions
        for i in range(5):
            with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
                session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
                
                await session_manager.create_new_session()
                
                # Simulate some work
                await session_manager.get_session_data()
                assert session_manager.is_session_valid()
                
                # Cleanup
                await session_manager.cleanup()
                
                # Force garbage collection
                gc.collect()
        
        # Check memory usage
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Get memory statistics
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        logger.info(f"Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
        logger.info(f"Peak traced memory: {peak / 1024 / 1024:.2f} MB")
        
        # Memory increase should be reasonable (less than 50MB for 5 sessions)
        assert memory_increase < 50 * 1024 * 1024, \
            f"Memory increase too high: {memory_increase / 1024 / 1024:.2f} MB"
        
        logger.info("✅ Memory leak detection test passed")
    
    @pytest.mark.asyncio
    async def test_resource_cleanup_after_failures(self):
        """Test resource cleanup after various failure scenarios"""
        logger.info("Testing resource cleanup after failures")
        
        # Test cleanup after session creation failure
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', 
                   MockAsyncCamoufox(should_fail=True)):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Session creation should fail
            result = await session_manager.create_new_session()
            
            # Cleanup should still work
            await session_manager.cleanup()
            assert not session_manager.is_session_valid()
        
        # Test cleanup after partial resource creation
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Create partial resources
            session_manager.browser = MockBrowser()
            session_manager.page = MockPage()
            # Don't create context (simulate partial failure)
            
            # Cleanup should handle partial resources
            await session_manager.cleanup()
            assert session_manager.browser is None
            assert session_manager.page is None
        
        logger.info("✅ Resource cleanup after failures test passed")
    
    @pytest.mark.asyncio
    async def test_force_cleanup_on_timeout(self):
        """Test force cleanup when normal cleanup times out"""
        logger.info("Testing force cleanup on timeouts")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            await session_manager.create_new_session()
            
            # Mock resource manager to simulate timeout
            async def slow_cleanup(*args, **kwargs):
                await asyncio.sleep(2.0)  # Simulate slow cleanup
                return {"timeout": {"error": "Cleanup timed out"}}
            
            session_manager.resource_manager.cleanup_all = slow_cleanup
            
            # Cleanup should complete (using force cleanup)
            cleanup_start = time.time()
            await session_manager.cleanup()
            cleanup_duration = time.time() - cleanup_start
            
            logger.info(f"Force cleanup completed in {cleanup_duration:.2f}s")
            
            # Session should be cleaned up despite timeout
            assert not session_manager.is_session_valid()
            assert session_manager.browser is None
            assert session_manager.page is None
        
        logger.info("✅ Force cleanup on timeout test passed")


class TestIntegrationFlows:
    """Test suite for integration flows and end-to-end scenarios"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'health_monitoring': {'continuous_monitoring': False},
            'circuit_breaker': {'failure_threshold': 5},
            'recovery': {'max_attempts': 2},
            'session': {'max_duration': 3600}
        }
        self.logger = logging.getLogger("TestIntegration")
    
    @pytest.mark.asyncio
    async def test_full_image_download_flow_with_session_recovery(self):
        """Test full image download flow with session recovery"""
        logger.info("Testing full image download flow with session recovery")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Phase 1: Create session (GraphQL capture simulation)
            await session_manager.create_new_session()
            assert session_manager.is_session_valid(), "Session should be valid after creation"
            
            # Simulate GraphQL data extraction
            session_manager.session_data.update({
                'fb_dtsg': 'test_token',
                'user_id': '123456',
                'page_url': 'https://www.facebook.com/ads/library'
            })
            
            # Phase 2: Image download simulation (this is where the bug was)
            logger.info("Simulating Phase 2: Image downloads")
            
            # Verify session is still valid (this was failing before the fix)
            assert session_manager.is_session_valid(), "Session should remain valid for Phase 2"
            
            # Simulate image downloads using session
            image_urls = [
                "https://scontent.fxyz1-1.fna.fbcdn.net/test1.jpg",
                "https://scontent.fxyz1-1.fna.fbcdn.net/test2.jpg",
                "https://scontent.fxyz1-1.fna.fbcdn.net/test3.jpg"
            ]
            
            successful_downloads = 0
            for i, url in enumerate(image_urls):
                if session_manager.is_session_valid():
                    # Simulate image download using session headers
                    headers = session_manager.get_session_headers()
                    assert 'Content-Type' in headers, "Session headers should be available"
                    
                    # Simulate successful download
                    await asyncio.sleep(0.1)  # Simulate download time
                    successful_downloads += 1
                    logger.info(f"Downloaded image {i+1}: {url}")
                else:
                    logger.error(f"Session invalid during image {i+1} download")
                    break
            
            assert successful_downloads == len(image_urls), \
                f"All {len(image_urls)} images should download successfully, got {successful_downloads}"
            
            # Phase 3: Database operations simulation
            logger.info("Simulating Phase 3: Database operations")
            assert session_manager.is_session_valid(), "Session should remain valid for Phase 3"
            
            # Final cleanup (this should happen AFTER all phases)
            await session_manager.cleanup()
            assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
        
        logger.info("✅ Full image download flow with session recovery test passed")
    
    @pytest.mark.asyncio
    async def test_multi_phase_job_execution_with_session_recovery(self):
        """Test multi-phase job execution with session recovery"""
        logger.info("Testing multi-phase job execution with session recovery")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Job phases simulation
            phases = [
                "Phase 1: GraphQL Capture",
                "Phase 2: Image Downloads", 
                "Phase 3: Database Operations"
            ]
            
            # Start job
            await session_manager.create_new_session()
            job_start_time = time.time()
            
            for i, phase in enumerate(phases):
                logger.info(f"Executing {phase}")
                
                # Verify session is valid for each phase
                assert session_manager.is_session_valid(), f"Session should be valid for {phase}"
                
                # Simulate phase work
                phase_start = time.time()
                
                if i == 0:  # GraphQL phase
                    session_manager.session_data.update({
                        'fb_dtsg': f'token_{int(time.time())}',
                        'capture_time': phase_start
                    })
                    await asyncio.sleep(0.5)  # Simulate GraphQL processing
                    
                elif i == 1:  # Image download phase
                    # This phase was failing before the fix
                    headers = session_manager.get_session_headers()
                    assert headers, "Session headers should be available for image downloads"
                    await asyncio.sleep(0.3)  # Simulate image processing
                    
                elif i == 2:  # Database phase
                    # Simulate database operations
                    await asyncio.sleep(0.2)
                
                phase_duration = time.time() - phase_start
                logger.info(f"{phase} completed in {phase_duration:.2f}s")
                
                # Session should remain valid between phases
                assert session_manager.is_session_valid(), f"Session should remain valid after {phase}"
            
            # Job completion
            job_duration = time.time() - job_start_time
            logger.info(f"Job completed in {job_duration:.2f}s")
            
            # Final cleanup (should happen ONLY after ALL phases)
            await session_manager.cleanup()
            assert not session_manager.is_session_valid(), "Session should be cleaned up after job completion"
        
        logger.info("✅ Multi-phase job execution test passed")
    
    @pytest.mark.asyncio
    async def test_session_persistence_across_job_phases(self):
        """Test session persistence across job phases (the main fix)"""
        logger.info("Testing session persistence across job phases")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Simulate the bug scenario: job orchestration vs job runner cleanup
            await session_manager.create_new_session()
            initial_session_id = session_manager.session_id
            
            # Phase 1: GraphQL capture completes
            logger.info("Phase 1: GraphQL capture")
            session_manager.session_data['graphql_complete'] = True
            assert session_manager.is_session_valid(), "Session should be valid after Phase 1"
            
            # CRITICAL TEST: Simulate job orchestration's finally block (before fix)
            # This was where the premature cleanup happened
            logger.info("Simulating job orchestration finally block (should NOT cleanup session)")
            
            # The fix: job orchestration should NOT call cleanup here
            # session_manager.cleanup()  # This line was the bug!
            
            # Session should still be valid (the fix)
            assert session_manager.is_session_valid(), \
                "CRITICAL: Session should remain valid after job orchestration (the main fix)"
            assert session_manager.session_id == initial_session_id, "Session ID should remain the same"
            
            # Phase 2: Image downloads (this was failing before fix)
            logger.info("Phase 2: Image downloads")
            headers = session_manager.get_session_headers()
            assert headers, "Session headers should be available for Phase 2 (image downloads)"
            session_manager.session_data['images_downloaded'] = 5
            
            # Phase 3: Database operations
            logger.info("Phase 3: Database operations")
            assert session_manager.is_session_valid(), "Session should be valid for Phase 3"
            session_manager.session_data['db_complete'] = True
            
            # FINAL: Job runner cleanup (after ALL phases complete)
            logger.info("Job runner final cleanup (after ALL phases)")
            await session_manager.cleanup()
            assert not session_manager.is_session_valid(), "Session should be cleaned up after ALL phases"
        
        logger.info("✅ Session persistence across job phases test passed")


class TestPerformanceAndMetrics:
    """Test suite for performance monitoring and metrics collection"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.config = {
            'health_monitoring': {
                'check_interval': 1,
                'continuous_monitoring': False
            },
            'circuit_breaker': {'failure_threshold': 5},
            'recovery': {'max_attempts': 2}
        }
        self.logger = logging.getLogger("TestPerformance")
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_and_metrics_collection(self):
        """Test performance monitoring and metrics collection"""
        logger.info("Testing performance monitoring and metrics collection")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            # Create session and collect metrics
            start_time = time.time()
            await session_manager.create_new_session()
            creation_time = time.time() - start_time
            
            # Check session creation metrics
            assert 'session_creation' in session_manager.performance_timings, \
                "Session creation time should be recorded"
            assert session_manager.performance_timings['session_creation'] > 0, \
                "Session creation time should be positive"
            
            # Check session metrics
            assert session_manager.metrics['session_created'] > 0, \
                "Session creation count should be incremented"
            
            # Perform health check and collect metrics
            health_result = await session_manager.health_monitor.perform_health_check()
            
            # Check health monitoring metrics
            health_summary = session_manager.health_monitor.get_health_summary()
            assert health_summary['monitoring']['total_checks'] > 0, \
                "Health check count should be positive"
            assert health_summary['monitoring']['average_check_time'] > 0, \
                "Average health check time should be positive"
            
            # Get comprehensive status
            status = session_manager.get_status()
            
            # Verify status structure
            required_sections = ['session', 'health', 'circuit_breakers', 'recovery', 'resources', 'metrics']
            for section in required_sections:
                assert section in status, f"Status should include {section} section"
            
            # Check session section
            assert 'id' in status['session'], "Session status should include ID"
            assert 'valid' in status['session'], "Session status should include validity"
            assert 'age' in status['session'], "Session status should include age"
            
            # Test performance summary
            perf_summary = session_manager.get_performance_summary()
            assert 'session_health_score' in perf_summary, \
                "Performance summary should include health score"
            assert 'recovery_success_rate' in perf_summary, \
                "Performance summary should include recovery success rate"
            
            logger.info(f"Session creation time: {creation_time:.3f}s")
            logger.info(f"Health score: {perf_summary['session_health_score']:.2f}")
            logger.info(f"Recovery success rate: {perf_summary['recovery_success_rate']:.1f}%")
            
            await session_manager.cleanup()
        
        logger.info("✅ Performance monitoring and metrics collection test passed")
    
    @pytest.mark.asyncio
    async def test_health_score_calculation(self):
        """Test health score calculation and trending"""
        logger.info("Testing health score calculation")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            await session_manager.create_new_session()
            
            # Initial health score should be 1.0 (perfect)
            initial_score = session_manager.health_monitor.health_score
            assert initial_score == 1.0, f"Initial health score should be 1.0, got {initial_score}"
            
            # Perform multiple health checks
            scores = []
            for i in range(5):
                health_result = await session_manager.health_monitor.perform_health_check()
                current_score = session_manager.health_monitor.health_score
                scores.append(current_score)
                logger.info(f"Health check {i+1}: {health_result.status.value}, score: {current_score:.2f}")
                await asyncio.sleep(0.1)
            
            # Health score should be tracked over time
            assert len(scores) == 5, "Should have collected 5 health scores"
            
            # For healthy sessions, score should remain high
            final_score = scores[-1]
            assert final_score >= 0.8, f"Final health score should be high for healthy session, got {final_score}"
            
            await session_manager.cleanup()
        
        logger.info("✅ Health score calculation test passed")
    
    @pytest.mark.asyncio
    async def test_resource_usage_tracking(self):
        """Test resource usage tracking and optimization"""
        logger.info("Testing resource usage tracking")
        
        with patch('src.services.fb_ads.camoufox.robust_camoufox_session_manager.AsyncCamoufox', MockAsyncCamoufox):
            session_manager = RobustCamoufoxSessionManager(self.config, self.logger)
            
            await session_manager.create_new_session()
            
            # Get resource summary
            resource_summary = session_manager.resource_manager.get_resource_summary()
            
            # Should track different resource types
            assert 'trackers' in resource_summary, "Resource summary should include trackers"
            
            # Check if resources are being tracked
            logger.info(f"Resource trackers: {list(resource_summary['trackers'].keys())}")
            
            # Resources should be tracked during session
            status = session_manager.get_status()
            assert 'resources' in status, "Status should include resource information"
            
            await session_manager.cleanup()
        
        logger.info("✅ Resource usage tracking test passed")


async def run_all_tests():
    """Run all test suites"""
    logger.info("🚀 Starting Comprehensive Camoufox Session Management Tests")
    
    test_suites = [
        TestSessionHealthValidation(),
        TestRaceConditionPrevention(), 
        TestErrorRecoveryAndCircuitBreaker(),
        TestResourceCleanupAndMemoryLeaks(),
        TestIntegrationFlows(),
        TestPerformanceAndMetrics()
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    test_results = []
    
    overall_start_time = time.time()
    
    for suite in test_suites:
        suite_name = suite.__class__.__name__
        logger.info(f"\n📋 Running {suite_name}")
        
        # Get all test methods
        test_methods = [method for method in dir(suite) if method.startswith('test_')]
        
        for test_method in test_methods:
            total_tests += 1
            test_start_time = time.time()
            
            try:
                # Setup
                if hasattr(suite, 'setup_method'):
                    suite.setup_method()
                
                # Run test
                method = getattr(suite, test_method)
                await method()
                
                passed_tests += 1
                duration = time.time() - test_start_time
                result = f"✅ {suite_name}.{test_method} - PASSED ({duration:.2f}s)"
                logger.info(result)
                test_results.append(result)
                
            except Exception as e:
                failed_tests += 1
                duration = time.time() - test_start_time
                result = f"❌ {suite_name}.{test_method} - FAILED ({duration:.2f}s): {e}"
                logger.error(result)
                test_results.append(result)
                
                # Log full traceback for debugging
                import traceback
                logger.error(f"Full traceback:\n{traceback.format_exc()}")
    
    overall_duration = time.time() - overall_start_time
    
    # Print summary
    logger.info(f"\n🏁 Test Suite Completed in {overall_duration:.2f}s")
    logger.info(f"📊 Results: {passed_tests} passed, {failed_tests} failed, {total_tests} total")
    logger.info(f"📈 Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        logger.error("\n❌ Failed Tests:")
        for result in test_results:
            if "FAILED" in result:
                logger.error(f"  {result}")
    
    logger.info("\n✅ Passed Tests:")
    for result in test_results:
        if "PASSED" in result:
            logger.info(f"  {result}")
    
    return passed_tests, failed_tests, total_tests


if __name__ == "__main__":
    """Main execution"""
    import sys
    
    logger.info("🔬 Camoufox Session Management Comprehensive Test Suite")
    logger.info("=" * 80)
    
    try:
        # Run all tests
        passed, failed, total = asyncio.run(run_all_tests())
        
        # Exit with appropriate code
        if failed > 0:
            logger.error(f"\n💥 {failed} test(s) failed!")
            sys.exit(1)
        else:
            logger.info(f"\n🎉 All {total} tests passed!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Test suite failed with exception: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)