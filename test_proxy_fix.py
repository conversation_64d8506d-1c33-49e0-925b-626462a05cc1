#!/usr/bin/env python3
"""
Test script to verify proxy configuration fix works correctly.
"""

import logging
import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.infrastructure.browser.proxy_manager import ProxyManager


def test_proxy_configuration():
    """Test that proxy configuration respects the settings."""
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    print("🧪 Testing Proxy Configuration")
    print("=" * 50)
    
    # Test 1: Mobile proxy configuration
    print("\n📱 Test 1: Mobile Proxy Configuration")
    mobile_config = {
        'use_proxy': True,
        'mobile_proxy': True,
        'num_mobile_proxies': 100,
        'num_residential_proxies': 50,
        'oxylabs_num_proxies': 1000,  # This should be ignored for type-specific counts
        'oxy_labs_mobile_username': 'test_mobile_user',
        'oxy_labs_mobile_password': 'test_mobile_pass',
        'oxy_labs_residential_username': 'test_res_user', 
        'oxy_labs_residential_password': 'test_res_pass'
    }
    
    mobile_proxy_manager = ProxyManager(mobile_config, logger)
    print(f"✅ Mobile proxies generated: {len(mobile_proxy_manager.mobile_proxies)}")
    print(f"✅ Residential proxies generated: {len(mobile_proxy_manager.residential_proxies)}")
    print(f"✅ Current proxy type: {mobile_proxy_manager.current_proxy_type}")
    
    assert len(mobile_proxy_manager.mobile_proxies) == 100, f"Expected 100 mobile proxies, got {len(mobile_proxy_manager.mobile_proxies)}"
    assert len(mobile_proxy_manager.residential_proxies) == 50, f"Expected 50 residential proxies, got {len(mobile_proxy_manager.residential_proxies)}"
    assert mobile_proxy_manager.current_proxy_type == 'mobile', f"Expected 'mobile' proxy type, got {mobile_proxy_manager.current_proxy_type}"
    
    # Test 2: Residential proxy configuration  
    print("\n🏠 Test 2: Residential Proxy Configuration")
    residential_config = {
        'use_proxy': True,
        'mobile_proxy': False,
        'num_mobile_proxies': 200,
        'num_residential_proxies': 300,
        'oxylabs_num_proxies': 1000,  # This should be ignored for type-specific counts
        'oxy_labs_mobile_username': 'test_mobile_user',
        'oxy_labs_mobile_password': 'test_mobile_pass',
        'oxy_labs_residential_username': 'test_res_user',
        'oxy_labs_residential_password': 'test_res_pass'
    }
    
    res_proxy_manager = ProxyManager(residential_config, logger)
    print(f"✅ Mobile proxies generated: {len(res_proxy_manager.mobile_proxies)}")
    print(f"✅ Residential proxies generated: {len(res_proxy_manager.residential_proxies)}")
    print(f"✅ Current proxy type: {res_proxy_manager.current_proxy_type}")
    
    assert len(res_proxy_manager.mobile_proxies) == 200, f"Expected 200 mobile proxies, got {len(res_proxy_manager.mobile_proxies)}"
    assert len(res_proxy_manager.residential_proxies) == 300, f"Expected 300 residential proxies, got {len(res_proxy_manager.residential_proxies)}"
    assert res_proxy_manager.current_proxy_type == 'residential', f"Expected 'residential' proxy type, got {res_proxy_manager.current_proxy_type}"
    
    # Test 3: Large proxy counts (like in fb_ads.yml)
    print("\n🚀 Test 3: Large Proxy Counts (fb_ads.yml scenario)")
    large_config = {
        'use_proxy': True,
        'mobile_proxy': True,
        'num_mobile_proxies': 500000,
        'num_residential_proxies': 500000,
        'oxylabs_num_proxies': 500000,
        'oxy_labs_mobile_username': 'test_mobile_user',
        'oxy_labs_mobile_password': 'test_mobile_pass',
        'oxy_labs_residential_username': 'test_res_user',
        'oxy_labs_residential_password': 'test_res_pass'
    }
    
    large_proxy_manager = ProxyManager(large_config, logger)
    print(f"✅ Mobile proxies generated: {len(large_proxy_manager.mobile_proxies)}")
    print(f"✅ Residential proxies generated: {len(large_proxy_manager.residential_proxies)}")
    print(f"✅ Current proxy type: {large_proxy_manager.current_proxy_type}")
    
    # Should generate up to the configured limits
    assert len(large_proxy_manager.mobile_proxies) == 500000, f"Expected 500000 mobile proxies, got {len(large_proxy_manager.mobile_proxies)}"
    assert len(large_proxy_manager.residential_proxies) == 500000, f"Expected 500000 residential proxies, got {len(large_proxy_manager.residential_proxies)}"
    assert large_proxy_manager.current_proxy_type == 'mobile', f"Expected 'mobile' proxy type, got {large_proxy_manager.current_proxy_type}"
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! Proxy configuration is working correctly.")
    print("\n📋 Summary:")
    print("1. ProxyManager correctly reads num_mobile_proxies and num_residential_proxies")
    print("2. mobile_proxy flag correctly sets the initial proxy type")
    print("3. Large proxy counts (500k) are properly handled")
    print("4. Type-specific proxy counts override oxylabs_num_proxies")


if __name__ == "__main__":
    test_proxy_configuration()