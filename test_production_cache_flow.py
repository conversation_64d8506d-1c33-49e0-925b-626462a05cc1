#!/usr/bin/env python3
"""
Test the production cache flow to identify why cache hits aren't occurring.
This simulates the actual workflow where images are intercepted during browser
navigation and then retrieved later by the image handler.
"""

import asyncio
import time
from unittest.mock import MagicMock, AsyncMock, patch
from io import BytesIO
from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)
from src.services.fb_ads.image_handler import ImageHand<PERSON>

async def simulate_browser_interception(cache_extractor: BrowserImageCacheExtractor, test_urls: list[str]):
    """Simulate the browser intercepting images during page navigation."""
    print("\n🌐 PHASE 1: Simulating browser image interception...")
    
    # Create a mock page with response handler
    mock_responses = []
    
    for i, url in enumerate(test_urls):
        # Create mock response
        mock_response = MagicMock()
        mock_response.url = url
        mock_response.status = 200
        mock_response.headers = {'content-type': 'image/jpeg'}
        
        # Simulate image content
        image_content = f"facebook_ad_image_{i}_content_{'x' * 1000}".encode()
        mock_response.body = AsyncMock(return_value=image_content)
        
        mock_responses.append(mock_response)
        print(f"  📷 Created mock response for: {url[:80]}...")
    
    # Simulate the response handler being called
    print("\n  🎣 Triggering response handlers...")
    for response in mock_responses:
        # This simulates what happens inside setup_image_interception
        try:
            # Check if it's a Facebook image
            if any(domain in response.url for domain in ['fbcdn.net', 'facebook.com']):
                content_type = response.headers.get('content-type', '')
                if 'image' in content_type:
                    # Extract image content
                    image_content = await response.body()
                    
                    # Create cached resource
                    cached_image = CachedImageResource(
                        url=response.url,
                        content=image_content,
                        content_type=content_type,
                        content_length=len(image_content),
                        timestamp=time.time(),
                        source="browser_interception"
                    )
                    
                    # Store in cache
                    await cache_extractor._store_cached_image(cached_image)
                    print(f"  ✅ Intercepted and cached: {len(image_content)} bytes from {response.url[:60]}...")
        except Exception as e:
            print(f"  ❌ Failed to intercept: {e}")
    
    # Get cache stats after interception
    stats = cache_extractor.get_cache_stats()
    print(f"\n📊 After interception: {stats['cached_images']} images cached")

async def simulate_image_handler_retrieval(image_handler: ImageHandler, cache_extractor: BrowserImageCacheExtractor, test_urls: list[str]):
    """Simulate the image handler trying to retrieve images."""
    print("\n🔄 PHASE 2: Simulating image handler retrieval...")
    
    results = {
        'cache_hits': 0,
        'cache_misses': 0,
        'download_attempts': 0
    }
    
    # Test with different URL variations that might occur in production
    url_variations = []
    for url in test_urls:
        # Original URL
        url_variations.append((url, "original"))
        
        # URL with different query parameters (common in production)
        if '?' in url:
            base, params = url.split('?', 1)
            # Different size parameter
            variation1 = f"{base}?stp=dst-jpg_s600x600_tt6&_nc_cat=999"
            url_variations.append((variation1, "size_variation"))
            
            # Different session parameters
            variation2 = f"{base}?_nc_gid=DIFFERENT_GID&oh=DIFFERENT_OH&oe=DIFFERENT_OE"
            url_variations.append((variation2, "session_variation"))
    
    print(f"\n  Testing {len(url_variations)} URL variations...")
    
    for test_url, variation_type in url_variations:
        print(f"\n  🔍 Testing {variation_type}: {test_url[:80]}...")
        
        # Mock S3 manager to say image doesn't exist
        image_handler.s3_manager.file_exists = AsyncMock(return_value=(False, None))
        
        # Track if download_image was called
        download_called = False
        original_download = None
        
        if hasattr(image_handler.session_manager, 'download_image'):
            original_download = image_handler.session_manager.download_image
            async def mock_download(url, timeout=30):
                nonlocal download_called
                download_called = True
                results['download_attempts'] += 1
                print(f"    ⬇️  DOWNLOAD ATTEMPTED for {url[:60]}...")
                return b"downloaded_content"
            
            image_handler.session_manager.download_image = mock_download
        
        # Call the image handler
        try:
            # Create mock paths
            local_path = f"/tmp/test_image_{hash(test_url)}.jpg"
            s3_key = f"test/images/{hash(test_url)}.jpg"
            
            # The method we're testing
            result = await image_handler.process_and_upload_ad_image(
                ad_archive_id="test_ad_123",
                ad_creative_id="test_creative_456",
                image_url=test_url
            )
            
            if download_called:
                results['cache_misses'] += 1
                print(f"    ❌ CACHE MISS - Download was triggered")
            else:
                # Check if cache was actually hit
                cached = await cache_extractor.get_cached_image(test_url)
                if cached:
                    results['cache_hits'] += 1
                    print(f"    ✅ CACHE HIT - Retrieved {cached.content_length} bytes")
                else:
                    results['cache_misses'] += 1
                    print(f"    ❌ CACHE MISS - Not found in cache")
            
        except Exception as e:
            print(f"    ⚠️  Error during retrieval: {e}")
            results['cache_misses'] += 1
        
        # Restore original download method
        if original_download:
            image_handler.session_manager.download_image = original_download
    
    return results

async def main():
    """Run the production cache flow test."""
    print("🧪 PRODUCTION CACHE FLOW TEST")
    print("=" * 70)
    
    # Create cache extractor
    config = {"camoufox": {"image_cache": {"enabled": True}}}
    logger = MagicMock()
    cache_extractor = BrowserImageCacheExtractor(
        logger=logger,
        config=config,
        max_cache_size_mb=100,
        cache_ttl_minutes=60,
        enable_disk_persistence=False
    )
    
    # Create image handler with the cache extractor
    mock_s3_manager = MagicMock()
    mock_s3_manager.file_exists = AsyncMock(return_value=(False, None))
    mock_s3_manager.upload_file = AsyncMock(return_value=(True, "uploaded"))
    
    mock_session_manager = MagicMock()
    mock_session_manager.download_image = AsyncMock(return_value=b"downloaded_content")
    
    image_handler = ImageHandler(
        logger=logger,
        config=config,
        s3_manager=mock_s3_manager,
        session_manager=mock_session_manager,
        hash_manager=None,
        bandwidth_logger=None,
        browser_image_cache_extractor=cache_extractor  # Same instance!
    )
    
    # Mock the _save_image_to_jpeg method to avoid file system operations
    image_handler._save_image_to_jpeg = MagicMock(return_value=True)
    
    # Test URLs similar to real Facebook ad images
    test_urls = [
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/447181435_1029863199156819_7025413177941515490_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg&oe=6732D6F0",
        "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/123456789_987654321_1234567890_n.jpg?_nc_cat=102&ccb=1-7&_nc_sid=abc123",
        "https://scontent.xx.fbcdn.net/v/t39.35426-6/999888777_666555444_333222111_n.jpg?_nc_cat=103"
    ]
    
    # Phase 1: Simulate browser interception
    await simulate_browser_interception(cache_extractor, test_urls)
    
    # Small delay to simulate time passing
    await asyncio.sleep(0.1)
    
    # Phase 2: Simulate image handler retrieval
    results = await simulate_image_handler_retrieval(image_handler, cache_extractor, test_urls)
    
    # Final analysis
    print("\n" + "=" * 70)
    print("📊 FINAL ANALYSIS")
    print("=" * 70)
    
    total_attempts = results['cache_hits'] + results['cache_misses']
    hit_rate = (results['cache_hits'] / total_attempts * 100) if total_attempts > 0 else 0
    
    print(f"\nCache Performance:")
    print(f"  Total retrieval attempts: {total_attempts}")
    print(f"  Cache hits: {results['cache_hits']}")
    print(f"  Cache misses: {results['cache_misses']}")
    print(f"  Hit rate: {hit_rate:.1f}%")
    print(f"  Download attempts: {results['download_attempts']}")
    
    # Get final cache stats
    stats = cache_extractor.get_cache_stats()
    print(f"\nCache Statistics:")
    print(f"  Images in cache: {stats['cached_images']}")
    print(f"  Total cache hits: {stats['cache_hits']}")
    print(f"  Total cache misses: {stats['cache_misses']}")
    print(f"  Overall hit rate: {stats['hit_rate_percent']:.1f}%")
    
    # Analyze normalization
    print(f"\nURL Normalization:")
    print(f"  Normalized URL groups: {stats['normalized_url_groups']}")
    print(f"  Average URLs per group: {stats['avg_urls_per_group']}")
    
    # Diagnostic info
    if stats['cache_miss_reasons']:
        print(f"\nCache Miss Reasons:")
        for reason, count in stats['cache_miss_reasons'].items():
            print(f"  {reason}: {count}")
    
    # Success criteria
    print("\n" + "=" * 70)
    if hit_rate >= 50:
        print("✅ SUCCESS: Cache is working well in production flow!")
    else:
        print("❌ ISSUE: Cache hit rate is too low in production flow")
        print("\n💡 Possible causes:")
        print("  1. URL normalization not handling all variations")
        print("  2. Images being intercepted with different URLs than requested")
        print("  3. Cache extractor instance not being shared properly")
        print("  4. Time-based parameters causing normalization issues")
    
    return 0 if hit_rate >= 50 else 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)