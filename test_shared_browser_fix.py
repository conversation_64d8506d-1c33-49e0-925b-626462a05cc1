#!/usr/bin/env python3
"""
Test script to validate the shared browser fix for multiple browser instances.

This script simulates the job orchestration flow to verify that:
1. Only one browser instance is created (shared)
2. Each job gets its own isolated browser context
3. Jobs remain completely isolated from each other
4. Resources are properly cleaned up after job completion

Usage:
    python test_shared_browser_fix.py
"""

import asyncio
import logging
import sys
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_shared_browser_architecture():
    """
    Test the shared browser architecture with multiple concurrent jobs.
    """
    logger.info("🎯 Testing Shared Browser Architecture for Multiple Job Isolation")
    
    try:
        # Import the shared browser manager factory
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Get shared browser manager instance via lazy initialization
        browser_manager = get_shared_browser_manager()
        
        # Configuration for testing
        test_config = {
            'headless': True,
            'humanize': False,  # Disable humanization for testing
            'camoufox': {
                'browser': {
                    'startup_delay': 0.1  # Minimal delay for testing
                }
            }
        }
        
        # Test with multiple concurrent jobs (simulating law firms)
        test_jobs = [
            {'job_id': 'firm_123_acme_law', 'firm_name': 'Acme Law'},
            {'job_id': 'firm_456_smith_attorneys', 'firm_name': 'Smith & Associates'},
            {'job_id': 'firm_789_johnson_legal', 'firm_name': 'Johnson Legal Group'},
        ]
        
        logger.info(f"📊 Testing with {len(test_jobs)} concurrent jobs")
        
        async def test_job_isolation(job_info):
            """Test isolated context for a single job."""
            job_id = job_info['job_id']
            firm_name = job_info['firm_name']
            
            logger.info(f"🚀 Starting job {job_id} ({firm_name})")
            
            try:
                # Get isolated browser context for this job
                context, page = await browser_manager.get_browser_context(
                    job_id=job_id,
                    config=test_config
                )
                
                logger.info(f"✅ Job {job_id}: Got isolated context with {len(context.pages)} pages")
                
                # Simulate some work with the page
                await page.goto('about:blank')
                title = await page.title()
                logger.info(f"📄 Job {job_id}: Page title: '{title}'")
                
                # Simulate job processing time
                await asyncio.sleep(0.5)
                
                # Check context isolation
                context_id = id(context)
                logger.info(f"🔍 Job {job_id}: Context ID: {context_id}")
                
                return {
                    'job_id': job_id,
                    'context_id': context_id,
                    'success': True,
                    'pages_count': len(context.pages)
                }
                
            except Exception as e:
                logger.error(f"❌ Job {job_id} failed: {e}")
                return {
                    'job_id': job_id,
                    'success': False,
                    'error': str(e)
                }
            finally:
                # Clean up job context
                await browser_manager.cleanup_job_context(job_id)
                logger.info(f"🧹 Job {job_id}: Context cleaned up")
        
        # Run all jobs concurrently
        logger.info("🔄 Starting concurrent job execution...")
        results = await asyncio.gather(*[test_job_isolation(job) for job in test_jobs])
        
        # Analyze results
        successful_jobs = [r for r in results if r['success']]
        failed_jobs = [r for r in results if not r['success']]
        
        logger.info(f"📊 Test Results:")
        logger.info(f"   ✅ Successful jobs: {len(successful_jobs)}")
        logger.info(f"   ❌ Failed jobs: {len(failed_jobs)}")
        
        # Verify context isolation
        context_ids = [r['context_id'] for r in successful_jobs if 'context_id' in r]
        unique_contexts = len(set(context_ids))
        
        logger.info(f"🔍 Context Isolation Analysis:")
        logger.info(f"   Total contexts created: {len(context_ids)}")
        logger.info(f"   Unique contexts: {unique_contexts}")
        logger.info(f"   Isolation verified: {'✅' if unique_contexts == len(context_ids) else '❌'}")
        
        # Get browser manager statistics
        stats = browser_manager.get_stats()
        logger.info(f"🖥️ Browser Manager Stats:")
        logger.info(f"   Browser active: {stats['browser_active']}")
        logger.info(f"   Browser healthy: {stats['browser_healthy']}")
        logger.info(f"   Active contexts: {stats['active_contexts']}")
        logger.info(f"   Browser uptime: {stats['browser_uptime']:.1f}s")
        
        # Clean up all resources
        await browser_manager.cleanup_all()
        logger.info("🧹 All resources cleaned up")
        
        # Final verification
        if len(successful_jobs) == len(test_jobs) and unique_contexts == len(context_ids):
            logger.info("🎉 TEST PASSED: Shared browser with isolated contexts working correctly!")
            return True
        else:
            logger.error("❌ TEST FAILED: Issues with job isolation or execution")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Cannot import required modules: {e}")
        logger.error("Make sure Camoufox is installed: pip install camoufox")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False

async def test_session_manager_integration():
    """
    Test the CamoufoxSessionManager integration with SharedBrowserManager.
    """
    logger.info("🎯 Testing CamoufoxSessionManager Integration")
    
    try:
        from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
        
        # Test configuration
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        # Create multiple session managers for different jobs
        session_managers = []
        for i in range(3):
            job_id = f"test_job_{i}"
            session_manager = CamoufoxSessionManager(
                config=test_config,
                logger=logger,
                job_id=job_id
            )
            session_managers.append((job_id, session_manager))
        
        logger.info(f"📊 Created {len(session_managers)} session managers")
        
        # Test concurrent session creation
        async def test_session_creation(job_id, session_manager):
            """Test session creation for a single manager."""
            logger.info(f"🚀 Creating session for {job_id}")
            success = await session_manager.create_new_session()
            
            if success:
                logger.info(f"✅ {job_id}: Session created successfully")
                # Test session validity
                is_valid = session_manager.is_session_valid()
                logger.info(f"🔍 {job_id}: Session valid: {is_valid}")
                return {'job_id': job_id, 'success': True, 'valid': is_valid}
            else:
                logger.error(f"❌ {job_id}: Session creation failed")
                return {'job_id': job_id, 'success': False, 'valid': False}
        
        # Run concurrent session creations
        logger.info("🔄 Testing concurrent session creation...")
        tasks = [test_session_creation(job_id, sm) for job_id, sm in session_managers]
        results = await asyncio.gather(*tasks)
        
        # Analyze results
        successful_sessions = [r for r in results if r['success']]
        valid_sessions = [r for r in results if r['success'] and r['valid']]
        
        logger.info(f"📊 Session Manager Integration Results:")
        logger.info(f"   ✅ Successful sessions: {len(successful_sessions)}")
        logger.info(f"   🔍 Valid sessions: {len(valid_sessions)}")
        
        # Clean up all session managers
        for job_id, session_manager in session_managers:
            await session_manager.cleanup()
            logger.info(f"🧹 {job_id}: Session manager cleaned up")
        
        # Verify success
        if len(successful_sessions) == len(session_managers) and len(valid_sessions) == len(session_managers):
            logger.info("🎉 SESSION MANAGER INTEGRATION PASSED!")
            return True
        else:
            logger.error("❌ SESSION MANAGER INTEGRATION FAILED!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Session manager integration test failed: {e}")
        return False

async def main():
    """Main test runner."""
    logger.info("🎯 Starting Shared Browser Fix Validation Tests")
    
    # Test 1: Shared Browser Architecture
    logger.info("\n" + "="*60)
    logger.info("TEST 1: Shared Browser Architecture")
    logger.info("="*60)
    
    test1_passed = await test_shared_browser_architecture()
    
    # Test 2: Session Manager Integration
    logger.info("\n" + "="*60)
    logger.info("TEST 2: Session Manager Integration")
    logger.info("="*60)
    
    test2_passed = await test_session_manager_integration()
    
    # Final results
    logger.info("\n" + "="*60)
    logger.info("FINAL TEST RESULTS")
    logger.info("="*60)
    
    logger.info(f"Test 1 - Shared Browser Architecture: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    logger.info(f"Test 2 - Session Manager Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        logger.info("🎉 ALL TESTS PASSED - Shared Browser Fix Working Correctly!")
        logger.info("✅ Multiple browser instances issue has been resolved")
        logger.info("✅ Job isolation is maintained through separate contexts")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Fix needs additional work")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)