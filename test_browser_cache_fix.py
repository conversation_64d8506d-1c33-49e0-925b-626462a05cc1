#!/usr/bin/env python3
"""
Test script to verify browser_image_cache_extractor is properly injected.

This script tests the dependency injection chain to ensure the browser_image_cache_extractor
is properly passed from the container through the orchestrator to the session managers.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.containers.fb_ads import FbAdsContainer
from src.containers.storage import StorageContainer
from src.utils.config_loader import load_config


async def test_browser_cache_injection():
    """Test that browser_image_cache_extractor is properly injected."""
    print("🧪 Testing browser_image_cache_extractor dependency injection...")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Load configuration
    config = load_config()
    
    # Create containers
    storage_container = StorageContainer()
    storage_container.config.from_dict(config)
    storage_container.logger.override(lambda: logger)
    
    fb_ads_container = FbAdsContainer()
    fb_ads_container.config.from_dict(config)
    fb_ads_container.logger.override(lambda: logger)
    fb_ads_container.storage_container.override(storage_container)
    
    # Create a mock HTTP session
    import aiohttp
    async with aiohttp.ClientSession() as session:
        fb_ads_container.http_session.override(lambda: session)
        
        # Test 1: Check if browser_image_cache_extractor is created
        print("\n✅ Test 1: Creating browser_image_cache_extractor...")
        cache_extractor = fb_ads_container.browser_image_cache_extractor()
        if cache_extractor:
            print(f"   ✓ browser_image_cache_extractor created: {type(cache_extractor).__name__}")
        else:
            print("   ✗ browser_image_cache_extractor is None!")
            return False
        
        # Test 2: Check if orchestrator receives it
        print("\n✅ Test 2: Creating orchestrator...")
        orchestrator = fb_ads_container.facebook_ads_orchestrator()
        if hasattr(orchestrator, 'browser_image_cache_extractor'):
            if orchestrator.browser_image_cache_extractor:
                print(f"   ✓ Orchestrator has browser_image_cache_extractor: {type(orchestrator.browser_image_cache_extractor).__name__}")
            else:
                print("   ✗ Orchestrator's browser_image_cache_extractor is None!")
                return False
        else:
            print("   ✗ Orchestrator doesn't have browser_image_cache_extractor attribute!")
            return False
        
        # Test 3: Check if it's in job_global_dependencies
        print("\n✅ Test 3: Checking job_global_dependencies...")
        if 'browser_image_cache_extractor' in orchestrator.job_global_dependencies:
            cache_in_deps = orchestrator.job_global_dependencies['browser_image_cache_extractor']
            if cache_in_deps:
                print(f"   ✓ browser_image_cache_extractor in job_global_dependencies: {type(cache_in_deps).__name__}")
            else:
                print("   ✗ browser_image_cache_extractor in job_global_dependencies is None!")
                return False
        else:
            print("   ✗ browser_image_cache_extractor not in job_global_dependencies!")
            return False
        
        # Test 4: Test session manager factory
        print("\n✅ Test 4: Testing session manager factory...")
        factory = fb_ads_container.session_manager_factory()
        
        # Create a test session manager
        test_session_manager = factory.create(
            config=config,
            logger=logger,
            firm_id="test_firm",
            fingerprint_manager=fb_ads_container.fingerprint_manager(),
            proxy_manager=fb_ads_container.proxy_manager(),
            law_firms_repository=storage_container.law_firms_repository(),
            browser_image_cache_extractor=cache_extractor
        )
        
        if hasattr(test_session_manager, 'image_cache_extractor'):
            if test_session_manager.image_cache_extractor:
                print(f"   ✓ Session manager has image_cache_extractor: {type(test_session_manager.image_cache_extractor).__name__}")
                print(f"   ✓ Cache enabled: {test_session_manager.image_cache_enabled}")
            else:
                print("   ✗ Session manager's image_cache_extractor is None!")
                return False
        else:
            print("   ✗ Session manager doesn't have image_cache_extractor attribute!")
            return False
        
        print("\n✅ All tests passed! Browser image cache extractor is properly injected through the dependency chain.")
        return True


if __name__ == "__main__":
    success = asyncio.run(test_browser_cache_injection())
    sys.exit(0 if success else 1)