#!/usr/bin/env python3
"""
Camoufox Session Management Test Runner

This script runs all the session management tests in sequence and provides
a comprehensive report of the validation results.
"""

import asyncio
import subprocess
import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestSuite:
    """Test suite configuration"""
    
    def __init__(self, name: str, script_path: str, description: str):
        self.name = name
        self.script_path = script_path
        self.description = description
        self.passed = 0
        self.failed = 0
        self.duration = 0.0
        self.output = ""
        self.error_output = ""


class CamoufoxTestRunner:
    """Test runner for all Camoufox session management tests"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.test_suites = self._initialize_test_suites()
        self.overall_start_time = 0.0
        self.overall_duration = 0.0
        
    def _initialize_test_suites(self) -> List[TestSuite]:
        """Initialize all test suites"""
        return [
            TestSuite(
                name="Comprehensive Tests",
                script_path="test_camoufox_session_management_comprehensive.py",
                description="Complete test suite covering all session management fixes"
            ),
            TestSuite(
                name="Health Validation Tests",
                script_path="test_session_health_validation.py", 
                description="Focused tests for enhanced session validation"
            ),
            TestSuite(
                name="Race Condition Prevention Tests",
                script_path="test_race_condition_prevention.py",
                description="Tests for atomic state management and concurrent operations"
            ),
            TestSuite(
                name="Integration Flow Tests",
                script_path="test_session_integration_flow.py",
                description="End-to-end integration tests for session persistence"
            )
        ]
    
    async def run_single_test_suite(self, test_suite: TestSuite) -> bool:
        """Run a single test suite"""
        self.logger.info(f"\n{'='*80}")
        self.logger.info(f"🧪 Running {test_suite.name}")
        self.logger.info(f"📝 {test_suite.description}")
        self.logger.info(f"📄 Script: {test_suite.script_path}")
        self.logger.info(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            # Check if test file exists
            test_file = Path(test_suite.script_path)
            if not test_file.exists():
                self.logger.error(f"❌ Test file not found: {test_suite.script_path}")
                test_suite.failed = 1
                return False
            
            # Run the test script
            process = await asyncio.create_subprocess_exec(
                sys.executable, test_suite.script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=Path.cwd()
            )
            
            stdout, stderr = await process.communicate()
            
            test_suite.duration = time.time() - start_time
            test_suite.output = stdout.decode('utf-8') if stdout else ""
            test_suite.error_output = stderr.decode('utf-8') if stderr else ""
            
            # Parse results from output
            self._parse_test_results(test_suite)
            
            if process.returncode == 0:
                self.logger.info(f"✅ {test_suite.name} completed successfully")
                self.logger.info(f"📊 Results: {test_suite.passed} passed, {test_suite.failed} failed")
                self.logger.info(f"⏱️ Duration: {test_suite.duration:.2f}s")
                return True
            else:
                self.logger.error(f"❌ {test_suite.name} failed with exit code {process.returncode}")
                self.logger.error(f"📊 Results: {test_suite.passed} passed, {test_suite.failed} failed")
                self.logger.error(f"⏱️ Duration: {test_suite.duration:.2f}s")
                
                # Show error output
                if test_suite.error_output:
                    self.logger.error("Error output:")
                    for line in test_suite.error_output.split('\n')[-10:]:  # Last 10 lines
                        if line.strip():
                            self.logger.error(f"  {line}")
                
                return False
                
        except Exception as e:
            test_suite.duration = time.time() - start_time
            test_suite.failed = 1
            self.logger.error(f"❌ Exception running {test_suite.name}: {e}")
            return False
    
    def _parse_test_results(self, test_suite: TestSuite):
        """Parse test results from output"""
        output_lines = test_suite.output.split('\n')
        
        # Look for result patterns
        for line in output_lines:
            if "passed" in line and "failed" in line and "total" in line:
                # Try to extract numbers
                words = line.split()
                try:
                    for i, word in enumerate(words):
                        if word == "passed," and i > 0:
                            test_suite.passed = int(words[i-1])
                        elif word == "failed," and i > 0:
                            test_suite.failed = int(words[i-1])
                    break
                except (ValueError, IndexError):
                    continue
        
        # If we couldn't parse, assume success/failure based on return code
        if test_suite.passed == 0 and test_suite.failed == 0:
            if "failed" in test_suite.output.lower() or "error" in test_suite.error_output.lower():
                test_suite.failed = 1
            else:
                test_suite.passed = 1
    
    async def run_all_tests(self) -> Tuple[int, int]:
        """Run all test suites"""
        self.logger.info("🚀 Starting Camoufox Session Management Test Runner")
        self.logger.info(f"📋 Running {len(self.test_suites)} test suites")
        
        self.overall_start_time = time.time()
        
        total_passed = 0
        total_failed = 0
        successful_suites = 0
        failed_suites = 0
        
        for i, test_suite in enumerate(self.test_suites, 1):
            self.logger.info(f"\n🎯 Test Suite {i}/{len(self.test_suites)}")
            
            success = await self.run_single_test_suite(test_suite)
            
            total_passed += test_suite.passed
            total_failed += test_suite.failed
            
            if success:
                successful_suites += 1
            else:
                failed_suites += 1
        
        self.overall_duration = time.time() - self.overall_start_time
        
        # Generate summary report
        self._generate_summary_report(total_passed, total_failed, successful_suites, failed_suites)
        
        return total_passed, total_failed
    
    def _generate_summary_report(self, total_passed: int, total_failed: int, 
                                successful_suites: int, failed_suites: int):
        """Generate comprehensive summary report"""
        self.logger.info(f"\n{'='*80}")
        self.logger.info("📊 CAMOUFOX SESSION MANAGEMENT TEST SUMMARY")
        self.logger.info(f"{'='*80}")
        
        # Overall metrics
        self.logger.info(f"⏱️ Total Runtime: {self.overall_duration:.2f}s")
        self.logger.info(f"📋 Test Suites: {successful_suites} successful, {failed_suites} failed")
        self.logger.info(f"🧪 Individual Tests: {total_passed} passed, {total_failed} failed")
        
        if total_passed + total_failed > 0:
            success_rate = (total_passed / (total_passed + total_failed)) * 100
            self.logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        
        # Per-suite breakdown
        self.logger.info(f"\n📋 Test Suite Details:")
        for suite in self.test_suites:
            status = "✅ PASS" if suite.failed == 0 else "❌ FAIL"
            self.logger.info(f"  {status} {suite.name}")
            self.logger.info(f"    📊 {suite.passed} passed, {suite.failed} failed ({suite.duration:.2f}s)")
            if suite.failed > 0:
                self.logger.info(f"    📝 {suite.description}")
        
        # Critical fixes validation
        self.logger.info(f"\n🔍 Critical Fixes Validation:")
        self._validate_critical_fixes()
        
        # Recommendations
        if total_failed > 0:
            self.logger.error(f"\n⚠️ RECOMMENDATIONS:")
            self.logger.error(f"  • {total_failed} test(s) failed - review session management implementation")
            self.logger.error(f"  • Check logs above for specific failure details")
            self.logger.error(f"  • Verify all dependencies are properly mocked")
            self.logger.error(f"  • Ensure test environment is properly configured")
        else:
            self.logger.info(f"\n🎉 ALL TESTS PASSED!")
            self.logger.info(f"  • Session persistence across job phases: ✅ VERIFIED")
            self.logger.info(f"  • Race condition prevention: ✅ VERIFIED") 
            self.logger.info(f"  • Health monitoring: ✅ VERIFIED")
            self.logger.info(f"  • Resource cleanup: ✅ VERIFIED")
    
    def _validate_critical_fixes(self):
        """Validate that critical fixes are properly tested"""
        critical_fixes = {
            "Session persistence across phases": False,
            "Race condition prevention": False,
            "Enhanced health validation": False,
            "Resource leak prevention": False,
            "Circuit breaker functionality": False
        }
        
        # Check outputs for evidence of critical fix testing
        for suite in self.test_suites:
            output_lower = suite.output.lower()
            
            if "session persistence" in output_lower or "phase" in output_lower:
                critical_fixes["Session persistence across phases"] = True
            
            if "race condition" in output_lower or "concurrent" in output_lower:
                critical_fixes["Race condition prevention"] = True
            
            if "health" in output_lower and "validation" in output_lower:
                critical_fixes["Enhanced health validation"] = True
            
            if "resource" in output_lower and ("leak" in output_lower or "cleanup" in output_lower):
                critical_fixes["Resource leak prevention"] = True
            
            if "circuit breaker" in output_lower:
                critical_fixes["Circuit breaker functionality"] = True
        
        for fix, tested in critical_fixes.items():
            status = "✅ TESTED" if tested else "⚠️ NOT DETECTED"
            self.logger.info(f"  {status} {fix}")
    
    async def run_individual_test(self, test_name: str) -> bool:
        """Run a specific test suite by name"""
        matching_suites = [s for s in self.test_suites if test_name.lower() in s.name.lower()]
        
        if not matching_suites:
            self.logger.error(f"❌ No test suite found matching '{test_name}'")
            self.logger.info("Available test suites:")
            for suite in self.test_suites:
                self.logger.info(f"  • {suite.name}")
            return False
        
        if len(matching_suites) > 1:
            self.logger.warning(f"⚠️ Multiple matches found for '{test_name}', running first match")
        
        suite = matching_suites[0]
        success = await self.run_single_test_suite(suite)
        
        if success:
            self.logger.info(f"✅ {suite.name} completed successfully")
        else:
            self.logger.error(f"❌ {suite.name} failed")
        
        return success


async def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Camoufox Session Management Test Runner")
    parser.add_argument("--test", help="Run specific test suite (partial name match)")
    parser.add_argument("--list", action="store_true", help="List available test suites")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    runner = CamoufoxTestRunner()
    
    if args.list:
        logger.info("Available test suites:")
        for i, suite in enumerate(runner.test_suites, 1):
            logger.info(f"{i}. {suite.name}")
            logger.info(f"   📝 {suite.description}")
            logger.info(f"   📄 {suite.script_path}")
        return 0
    
    if args.test:
        success = await runner.run_individual_test(args.test)
        return 0 if success else 1
    
    # Run all tests
    total_passed, total_failed = await runner.run_all_tests()
    
    if total_failed > 0:
        logger.error(f"\n💥 {total_failed} test(s) failed!")
        return 1
    else:
        logger.info(f"\n🎉 All {total_passed} tests passed!")
        return 0


if __name__ == "__main__":
    """Script entry point"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Test runner failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)