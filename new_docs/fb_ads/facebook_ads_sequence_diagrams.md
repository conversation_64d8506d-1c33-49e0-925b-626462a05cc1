# Facebook Ads Processing - Sequence Diagrams

## Complete Law Firm Processing Sequence

```mermaid
sequenceDiagram
    participant User
    participant Main as main.py
    participant Factory as MainServiceFactory
    participant F<PERSON><PERSON><PERSON> as FbAdsOrchestrator
    participant FbAdsOrch as FacebookAdsOrchestrator
    participant WorkflowSvc as WorkflowService
    participant JobOrch as JobOrchestrationService
    participant <PERSON><PERSON><PERSON><PERSON> as JobRunnerService
    participant SessionFactory as SessionManagerFactory
    participant Session as SessionManager
    participant API as APIClient
    participant Parser as GraphQLResponseParser
    participant AI as AIOrchestrator
    participant AdDB as AdDBService
    participant ImageHandler
    participant S3 as S3Storage
    participant DynamoDB

    User->>Main: python main.py --params fb_ads.yml
    Main->>Factory: create_fb_ads_orchestrator()
    Factory->>FbOrch: Initialize with DI container
    Main->>FbOrch: execute_action({action: "run"})
    
    FbOrch->>FbOrch: _validate_aws_credentials()
    FbOrch->>FbAdsOrch: create via container
    FbOrch->>FbAdsOrch: run()
    
    FbAdsOrch->>FbAdsOrch: run_single_firm_scrape(firm_id)
    FbAdsOrch->>WorkflowSvc: run_single_firm_workflow(firm_id)
    
    %% Workflow Service
    WorkflowSvc->>DynamoDB: get_firm(firm_id)
    DynamoDB-->>WorkflowSvc: firm_data
    WorkflowSvc->>WorkflowSvc: _should_skip_firm(firm_data)
    
    alt Firm should be skipped
        WorkflowSvc-->>FbAdsOrch: return False
    else Process firm
        WorkflowSvc->>WorkflowSvc: _create_job_config(firm_data)
        WorkflowSvc->>JobOrch: execute_job(job_config, dependencies)
    end
    
    %% Job Orchestration
    JobOrch->>JobOrch: retry_with_backoff()
    JobOrch->>JobRunner: run_job(job_config, dependencies)
    
    %% Job Runner - Session Setup
    JobRunner->>SessionFactory: create(config, firm_id)
    SessionFactory->>Session: new CamoufoxSessionManager()
    Session->>Session: create_browser()
    Session-->>JobRunner: session_manager
    
    %% Navigation and Search
    JobRunner->>Session: navigate_to_ad_library(firm_name)
    Session->>Session: page.goto(ad_library_url)
    Session->>Session: search_for_firm(firm_name)
    Session->>Session: capture_graphql_ndjson()
    Session-->>JobRunner: graphql_responses[]
    
    %% Parse Response
    JobRunner->>Parser: parse_ndjson_response(responses)
    Parser->>Parser: extract_ads_from_graphql()
    Parser-->>JobRunner: ads[]
    
    %% Process Each Ad
    loop For each ad
        JobRunner->>JobRunner: _phase_extract_ad_data(ad)
        Note right of JobRunner: Extract fields, images, videos
        
        JobRunner->>JobRunner: _phase_transform_ad_data(ad)
        JobRunner->>DynamoDB: check_ad_exists(ad_id, start_date)
        DynamoDB-->>JobRunner: exists: bool
        
        JobRunner->>JobRunner: _phase_enhance_ad_data(ad)
        alt Ad needs summary
            JobRunner->>AI: generate_summary(ad_text)
            AI->>AI: deepseek.generate_completion()
            AI-->>JobRunner: summary
        end
        
        JobRunner->>JobRunner: _phase_save_ad_data(ad)
        JobRunner->>AdDB: save_ads_to_db([ad])
        AdDB->>DynamoDB: put_item(ad_data)
        
        alt Has images and not deferred
            JobRunner->>ImageHandler: download_and_process_image(url)
            ImageHandler->>ImageHandler: download_image()
            ImageHandler->>ImageHandler: calculate_phash()
            ImageHandler->>DynamoDB: check_hash_exists(phash)
            
            alt Not duplicate
                ImageHandler->>S3: upload(image_data)
                ImageHandler->>DynamoDB: save_hash(phash, ad_id)
            end
        end
    end
    
    %% Update Stats
    JobRunner->>AdDB: update_law_firm_stats(firm_id, stats)
    AdDB->>DynamoDB: update_item(firm_stats)
    
    %% Cleanup
    JobRunner->>Session: cleanup()
    Session->>Session: browser.close()
    
    JobRunner-->>JobOrch: JobResult
    JobOrch-->>WorkflowSvc: success: bool
    WorkflowSvc-->>FbAdsOrch: result
    FbAdsOrch-->>FbOrch: complete
    FbOrch-->>Main: execution complete
```

## Detailed Session Management Sequence

```mermaid
sequenceDiagram
    participant JobRunner
    participant SessionFactory
    participant Config
    participant FingerprintMgr as FingerprintManager
    participant ProxyMgr as ProxyManager
    participant Camoufox as CamoufoxSessionManager
    participant Browser
    participant Page

    JobRunner->>SessionFactory: create(config, firm_id)
    SessionFactory->>Config: get_session_type()
    
    alt session_type == "camoufox"
        SessionFactory->>FingerprintMgr: new()
        SessionFactory->>ProxyMgr: new()
        SessionFactory->>Camoufox: new(fingerprint_mgr, proxy_mgr)
        
        Camoufox->>FingerprintMgr: generate_fingerprint()
        FingerprintMgr-->>Camoufox: fingerprint_data
        
        Camoufox->>ProxyMgr: get_proxy()
        ProxyMgr-->>Camoufox: proxy_config
        
        Camoufox->>Browser: playwright.chromium.launch()
        Browser-->>Camoufox: browser
        
        Camoufox->>Browser: new_context(fingerprint, proxy)
        Browser-->>Camoufox: context
        
        Camoufox->>Camoufox: setup_request_interception()
        Camoufox-->>SessionFactory: session_manager
    else session_type == "facebook"
        SessionFactory->>Session: new FacebookSessionManager()
        Session-->>SessionFactory: session_manager
    end
    
    SessionFactory-->>JobRunner: session_manager
```

## AI Enhancement Sequence

```mermaid
sequenceDiagram
    participant JobRunner
    participant Ad as Ad Data
    participant AI as AIOrchestrator
    participant DeepSeek as DeepSeekService
    participant GPT4 as OpenAIClient
    participant PromptMgr as PromptManager
    participant ImageQueue as LocalImageQueue

    JobRunner->>Ad: check Summary field
    
    alt Summary is empty
        JobRunner->>AI: generate_summary(ad_text)
        
        AI->>AI: _select_model("summary")
        Note right of AI: Selects DeepSeek as primary
        
        AI->>PromptMgr: get_prompt("ad_summary")
        PromptMgr-->>AI: prompt_template
        
        AI->>DeepSeek: generate_completion(prompt)
        
        alt DeepSeek succeeds
            DeepSeek->>DeepSeek: prepare_messages()
            DeepSeek->>DeepSeek: client.chat.completions.create()
            DeepSeek-->>AI: summary_text
        else DeepSeek fails
            AI->>GPT4: chat_completion(prompt)
            GPT4-->>AI: summary_text
        end
        
        AI-->>JobRunner: summary
        JobRunner->>Ad: add Summary field
    end
    
    alt Has images
        JobRunner->>AI: should_extract_image_text()
        
        alt Extract text from image
            JobRunner->>ImageQueue: add_to_queue(ad_id, image_url)
            Note right of JobRunner: Deferred processing
        end
    end
```

## Database Operations Sequence

```mermaid
sequenceDiagram
    participant JobRunner
    participant AdDB as AdDBService
    participant FBRepo as FBArchiveRepository
    participant LawRepo as LawFirmsRepository
    participant DynamoDB
    participant Ad as Ad Data

    %% Check Existence
    JobRunner->>AdDB: check_ad_exists(ad_id, start_date)
    AdDB->>FBRepo: get_item({AdArchiveID, StartDate})
    FBRepo->>DynamoDB: get_item(key)
    DynamoDB-->>FBRepo: existing_ad or None
    FBRepo-->>AdDB: existing_ad
    AdDB-->>JobRunner: exists: bool
    
    %% Save Ad
    JobRunner->>AdDB: save_ads_to_db([ad])
    
    loop For each ad
        AdDB->>AdDB: _prepare_dynamo_item(ad)
        Note right of AdDB: Convert snake_case to PascalCase
        
        alt Ad exists
            AdDB->>AdDB: _should_update_ad(existing, new)
            
            alt Should update
                AdDB->>FBRepo: update_item(key, updates)
                FBRepo->>DynamoDB: update_item()
            end
        else New ad
            AdDB->>FBRepo: put_item(ad_data)
            FBRepo->>DynamoDB: put_item()
        end
    end
    
    %% Update Stats
    AdDB->>AdDB: _calculate_stats(ads)
    AdDB->>LawRepo: update_firm_stats(firm_id, stats)
    LawRepo->>DynamoDB: update_item({ID, Name}, stats)
    
    AdDB-->>JobRunner: SaveResult
```

## Image Processing Sequence

```mermaid
sequenceDiagram
    participant JobRunner
    participant ImageHandler
    participant Session as SessionManager
    participant HashService as FBImageHashService
    participant S3
    participant DynamoDB
    participant BandwidthLogger
    participant LocalQueue as LocalImageQueue

    alt Defer image processing
        JobRunner->>LocalQueue: add_to_queue(ad_id, image_url)
        LocalQueue->>LocalQueue: INSERT INTO queue
        LocalQueue-->>JobRunner: queued
    else Process immediately
        JobRunner->>ImageHandler: download_and_process_image(url, ad_id)
        
        %% Download
        ImageHandler->>Session: download_image(url)
        Session->>Session: GET request
        Session-->>ImageHandler: image_bytes
        
        ImageHandler->>BandwidthLogger: log_download(size, "images")
        
        %% Hash Check
        ImageHandler->>ImageHandler: calculate_phash(image_bytes)
        ImageHandler->>HashService: check_hash_exists(phash)
        HashService->>DynamoDB: get_item({PHash})
        DynamoDB-->>HashService: existing or None
        
        alt Hash exists (duplicate)
            HashService-->>ImageHandler: True
            ImageHandler-->>JobRunner: {skipped: true, reason: "duplicate"}
        else New image
            %% Resize
            ImageHandler->>ImageHandler: resize_image(image_bytes)
            
            %% Upload to S3
            ImageHandler->>ImageHandler: _generate_s3_key(ad_id)
            ImageHandler->>S3: upload(image_data, key)
            S3-->>ImageHandler: s3_url
            
            ImageHandler->>BandwidthLogger: log_upload(size)
            
            %% Save hash
            ImageHandler->>HashService: save_hash(phash, ad_id, url)
            HashService->>DynamoDB: put_item({PHash, AdArchiveID, ...})
            
            ImageHandler-->>JobRunner: {success: true, s3_key, phash}
        end
    end
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant JobOrch as JobOrchestrationService
    participant JobRunner
    participant ErrorHandler as ErrorHandlingService
    participant FailedFirms as FailedFirmsManager
    participant ProcessingTracker
    
    JobOrch->>JobRunner: run_job(job_config)
    
    alt Job succeeds
        JobRunner-->>JobOrch: JobResult{success: true}
        JobOrch->>ProcessingTracker: mark_firm_processed(firm_id)
    else Job fails
        JobRunner-->>JobOrch: Exception
        
        JobOrch->>JobOrch: _should_retry(error)
        
        alt Should retry
            JobOrch->>JobOrch: wait(backoff_time)
            JobOrch->>JobRunner: run_job(job_config) [retry]
        else Should not retry
            JobOrch->>ErrorHandler: handle_error(error, context)
            
            ErrorHandler->>ErrorHandler: get_error_category(error)
            ErrorHandler->>ErrorHandler: _format_error_message()
            ErrorHandler->>ErrorHandler: _save_error_to_file()
            
            JobOrch->>FailedFirms: add_failed_firm(firm_id, error)
            FailedFirms->>FailedFirms: _save_failed_firms()
            
            JobOrch-->>WorkflowService: {success: false, error}
        end
    end
```

## Concurrent Processing Sequence

```mermaid
sequenceDiagram
    participant Workflow as ConcurrentWorkflowService
    participant Semaphore as asyncio.Semaphore
    participant Task1 as Task 1
    participant Task2 as Task 2
    participant TaskN as Task N
    participant JobOrch as JobOrchestrationService
    participant Progress as Progress Bar

    Workflow->>Workflow: get_active_firms()
    Workflow->>Semaphore: new(max_concurrent_firms)
    
    Workflow->>Progress: create_progress_bar(total_firms)
    
    par Concurrent execution
        Workflow->>Task1: create_task(process_firm_1)
        and Workflow->>Task2: create_task(process_firm_2)
        and Workflow->>TaskN: create_task(process_firm_n)
    end
    
    Note over Task1,TaskN: Each task acquires semaphore
    
    Task1->>Semaphore: acquire()
    Semaphore-->>Task1: acquired
    Task1->>JobOrch: execute_job(firm_1_config)
    JobOrch-->>Task1: result
    Task1->>Progress: update(1)
    Task1->>Semaphore: release()
    
    Task2->>Semaphore: acquire()
    Note right of Task2: Waits if limit reached
    Semaphore-->>Task2: acquired
    Task2->>JobOrch: execute_job(firm_2_config)
    
    Workflow->>Workflow: gather(*tasks)
    Workflow-->>Workflow: all_results
    
    Workflow->>Progress: close()
    Workflow->>Workflow: generate_summary(results)
```

## GraphQL Capture Sequence (Camoufox)

```mermaid
sequenceDiagram
    participant Session as CamoufoxSessionManager
    participant Page
    participant Route as Route Handler
    participant Buffer as Response Buffer
    participant Parser as GraphQLResponseParser

    Session->>Page: navigate_to_ad_library()
    Page->>Page: goto(url)
    
    Session->>Page: page.route("**/api/graphql", handle_route)
    Note right of Session: Intercept GraphQL requests
    
    Session->>Page: search_for_firm(firm_name)
    Page->>Page: type(search_box, firm_name)
    Page->>Page: wait_for_selector(".results")
    
    loop GraphQL requests
        Page->>Route: GraphQL request
        Route->>Route: route.fetch()
        Route->>Buffer: response.body()
        
        alt Is NDJSON response
            Route->>Session: graphql_responses.append(body)
        end
        
        Route->>Page: route.fulfill(response)
    end
    
    Session->>Session: wait(capture_time)
    Session-->>JobRunner: graphql_responses[]
    
    JobRunner->>Parser: parse_ndjson_response(responses)
    Parser->>Parser: for line in response.split('\n')
    Parser->>Parser: json.loads(line)
    Parser->>Parser: extract_ads_from_graphql()
    Parser-->>JobRunner: ads[]
```

## Dependency Injection Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as main.py
    participant Factory as MainServiceFactory
    participant Container as MainContainer
    participant FbContainer as FbAdsContainer
    participant StorageContainer
    participant Service as FacebookAdsOrchestrator

    Main->>Factory: new(config, logger)
    Factory->>Container: create_container(config_dict)
    
    Container->>Container: config.from_dict(config_dict)
    Container->>StorageContainer: initialize
    StorageContainer->>StorageContainer: create repositories
    
    Container->>FbContainer: initialize
    FbContainer->>FbContainer: create AI services
    FbContainer->>FbContainer: create session factories
    FbContainer->>FbContainer: create processing services
    
    Factory->>Container: wire(modules)
    
    Main->>Factory: create_fb_ads_orchestrator()
    Factory->>FbContainer: facebook_ads_orchestrator()
    
    FbContainer->>Service: new(logger, config, ...)
    Note right of Service: All dependencies injected via constructor
    
    Service->>Service: _validate_llm_availability()
    Service->>Service: _configure_fb_ads_logging()
    
    FbContainer-->>Factory: orchestrator instance
    Factory-->>Main: orchestrator
```