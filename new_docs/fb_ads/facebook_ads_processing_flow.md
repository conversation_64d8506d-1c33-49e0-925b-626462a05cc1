# Facebook Ads Processing Flow Documentation

## Table of Contents
1. [Overview](#overview)
2. [Complete Processing Flow](#complete-processing-flow)
3. [Detailed Component Flow](#detailed-component-flow)
4. [Class Diagrams](#class-diagrams)
5. [Data Flow Diagrams](#data-flow-diagrams)

## Overview

The Facebook Ads processing system is designed to scrape, process, and store Facebook ad data for law firms. The system uses a modern service-oriented architecture with dependency injection and follows these key principles:

- **Asynchronous Processing**: All I/O operations are async
- **Dependency Injection**: Services are wired through DI containers
- **Job-Based Architecture**: Processing is done through job orchestration
- **AI Enhancement**: Ads are enhanced with AI-generated summaries
- **Deferred Processing**: Image processing can be deferred for performance

## Complete Processing Flow

### High-Level Flow for Processing One Law Firm

```mermaid
flowchart TD
    Start([Start: Process Law Firm]) --> Entry{Entry Point}
    
    Entry -->|Single Firm| SingleFirm[run_single_firm_scrape]
    Entry -->|Full Scrape| FullScrape[run_full_scrape]
    Entry -->|Ignore List| IgnoreList[run_ignore_list_processing]
    
    SingleFirm --> WorkflowService[WorkflowService]
    FullScrape --> WorkflowService
    IgnoreList --> WorkflowService
    
    WorkflowService --> Validate{Validate Firm}
    Validate -->|Invalid| Skip[Skip Firm]
    Validate -->|Valid| CreateJob[Create Processing Job]
    
    CreateJob --> JobOrchestration[JobOrchestrationService]
    JobOrchestration --> SessionManager{Session Manager}
    
    SessionManager -->|Facebook| FBSession[FacebookSessionManager]
    SessionManager -->|Camoufox| CamoufoxSession[CamoufoxSessionManager]
    
    FBSession --> APIClient[API Client]
    CamoufoxSession --> APIClient
    
    APIClient --> FetchAds[Fetch Ads from Facebook]
    FetchAds --> ParseResponse[Parse GraphQL Response]
    
    ParseResponse --> JobRunner[JobRunnerService]
    JobRunner --> ProcessPhases{Processing Phases}
    
    ProcessPhases --> Phase1[Phase 1: Extract]
    Phase1 --> Phase2[Phase 2: Transform]
    Phase2 --> Phase3[Phase 3: Enhance]
    Phase3 --> Phase4[Phase 4: Save]
    
    Phase4 --> Complete([Processing Complete])
    Skip --> Complete
```

### Detailed Processing Phases

```mermaid
flowchart TD
    subgraph Phase1[Phase 1: Extract Ad Data]
        E1[Extract from GraphQL] --> E2[Parse Ad Structure]
        E2 --> E3[Extract Images/Videos]
        E3 --> E4[Map Page Info]
    end
    
    subgraph Phase2[Phase 2: Transform Ad Data]
        T1[Check DB Existence] --> T2{Exists?}
        T2 -->|Yes| T3[Mark for Update]
        T2 -->|No| T4[Mark as New]
        T3 --> T5[Apply Field Mapping]
        T4 --> T5
        T5 --> T6[Snake to PascalCase]
    end
    
    subgraph Phase3[Phase 3: Enhance with AI]
        A1[Check Summary Exists] --> A2{Has Summary?}
        A2 -->|No| A3[Generate with AI]
        A2 -->|Yes| A4[Skip AI Processing]
        A3 --> A5[DeepSeek/GPT-4]
        A5 --> A6[Add to Ad Data]
    end
    
    subgraph Phase4[Phase 4: Save to Storage]
        S1[Prepare DynamoDB Item] --> S2{Existing?}
        S2 -->|Yes| S3[Update Existing]
        S2 -->|No| S4[Create New]
        S3 --> S5[Save to DynamoDB]
        S4 --> S5
        S5 --> S6[Process Images]
        S6 --> S7[Update Law Firm Stats]
    end
```

## Detailed Component Flow

### 1. Entry Points

The system has three main entry points through `FacebookAdsOrchestrator`:

```mermaid
flowchart LR
    User[User/main.py] --> Orchestrator[FacebookAdsOrchestrator]
    
    Orchestrator --> E1[run_full_scrape]
    Orchestrator --> E2[run_single_firm_scrape]
    Orchestrator --> E3[run_ignore_list_processing]
    
    E1 --> WS[WorkflowService]
    E2 --> WS
    E3 --> WS
```

### 2. Workflow Service Flow

```mermaid
flowchart TD
    WS[WorkflowService] --> GetFirms[Get Law Firms]
    
    GetFirms --> Filter{Filter Firms}
    Filter -->|Skip Updated Today| Skip1[Skip]
    Filter -->|Login Required| Skip2[Skip]
    Filter -->|Ignore List| Skip3[Skip]
    Filter -->|Valid| Process[Process Firm]
    
    Process --> CreateJob[Create Job Config]
    CreateJob --> JobOrch[JobOrchestrationService]
    
    JobOrch --> RunJob[execute_job]
    RunJob --> JobRunner[JobRunnerService]
```

### 3. Job Processing Flow

```mermaid
flowchart TD
    JobRunner[JobRunnerService] --> SessionSetup[Setup Session]
    
    SessionSetup --> Navigate[Navigate to Ad Library]
    Navigate --> Search[Search for Firm]
    Search --> Capture[Capture GraphQL]
    
    Capture --> Parse[Parse Response]
    Parse --> ProcessAds{Process Each Ad}
    
    ProcessAds --> Extract[Extract Phase]
    Extract --> Transform[Transform Phase]
    Transform --> Enhance[Enhance Phase]
    Enhance --> Save[Save Phase]
    
    Save --> NextAd{More Ads?}
    NextAd -->|Yes| ProcessAds
    NextAd -->|No| Complete[Complete Job]
```

### 4. Image Processing Flow

```mermaid
flowchart TD
    Ad[Ad with Images] --> Check{Defer Processing?}
    
    Check -->|Yes| Queue[Add to LocalImageQueue]
    Check -->|No| Process[Process Immediately]
    
    Process --> Download[Download Image]
    Download --> Hash[Calculate PHash]
    Hash --> CheckDupe{Duplicate?}
    
    CheckDupe -->|Yes| Skip[Skip Upload]
    CheckDupe -->|No| Upload[Upload to S3]
    
    Upload --> UpdateDB[Update DynamoDB]
    Queue --> Later[Process Later]
```

## Class Diagrams

### Core Service Architecture

```mermaid
classDiagram
    class AsyncServiceBase {
        <<abstract>>
        +logger: LoggerProtocol
        +config: Dict
        +async _execute_action(data: Any) Any
        +async __aenter__()
        +async __aexit__()
    }
    
    class FacebookAdsOrchestrator {
        -workflow_service: WorkflowService
        -job_orchestration_service: JobOrchestrationService
        -interactive_service: InteractiveService
        +async run_full_scrape()
        +async run_single_firm_scrape(firm_id)
        +async run_ignore_list_processing()
    }
    
    class WorkflowService {
        -job_orchestration_service: JobOrchestrationService
        -ad_db_service: AdDBService
        -session_manager: SessionManager
        +async run_full_scrape_workflow()
        +async run_single_firm_workflow(firm_id)
        +async process_law_firm(firm_data)
    }
    
    class JobOrchestrationService {
        -job_runner_service: JobRunnerService
        -failed_firms_manager: FailedFirmsManager
        +async execute_job(job_config)
        +async handle_job_failure(error)
    }
    
    class JobRunnerService {
        -ai_orchestrator: AIOrchestrator
        -graphql_parser: GraphQLResponseParser
        +async run_job(job_config, dependencies)
        -async _phase_extract_ad_data(raw_ad)
        -async _phase_transform_ad_data(ad_data)
        -async _phase_enhance_ad_data(ad_data)
        -async _phase_save_ad_data(ad_data)
    }
    
    AsyncServiceBase <|-- FacebookAdsOrchestrator
    AsyncServiceBase <|-- WorkflowService
    AsyncServiceBase <|-- JobOrchestrationService
    AsyncServiceBase <|-- JobRunnerService
    
    FacebookAdsOrchestrator --> WorkflowService
    WorkflowService --> JobOrchestrationService
    JobOrchestrationService --> JobRunnerService
```

### Session Management Architecture

```mermaid
classDiagram
    class SessionManagerFactory {
        <<factory>>
        +static create(config, firm_id): SessionManager
    }
    
    class SessionManager {
        <<interface>>
        +async create_session()
        +async navigate_to_url(url)
        +async capture_graphql_responses()
    }
    
    class FacebookSessionManager {
        -requests_session: Session
        -cookies: Dict
        +async login()
        +async get_ads_for_page(page_id)
    }
    
    class CamoufoxSessionManager {
        -browser: Browser
        -context: BrowserContext
        -fingerprint_manager: FingerprintManager
        -proxy_manager: ProxyManager
        +async create_browser()
        +async capture_graphql_ndjson()
    }
    
    SessionManager <|.. FacebookSessionManager
    SessionManager <|.. CamoufoxSessionManager
    SessionManagerFactory ..> SessionManager
```

### Data Processing Architecture

```mermaid
classDiagram
    class AdDBService {
        -fb_archive_repository: FBArchiveRepository
        -law_firms_repository: LawFirmsRepository
        +async save_ads_to_db(ads)
        +async update_law_firm_stats(firm_id, stats)
    }
    
    class ImageHandler {
        -s3_manager: S3AsyncStorage
        -hash_manager: FBImageHashService
        -bandwidth_logger: BandwidthLogger
        +async download_and_process_image(url)
        +async check_image_exists(phash)
        +async upload_to_s3(image_data)
    }
    
    class AIOrchestrator {
        -deepseek: DeepSeekService
        -gpt4: OpenAIClient
        +async generate_summary(ad_text)
        +async process_with_fallback(content)
    }
    
    class GraphQLResponseParser {
        +parse_ndjson_response(response_text)
        +extract_ads_from_graphql(data)
        +validate_ad_structure(ad)
    }
```

### Repository Architecture

```mermaid
classDiagram
    class RepositoryBase {
        <<abstract>>
        +storage: AsyncDynamoDBStorage
        +async get_item(key)
        +async put_item(item)
        +async update_item(key, updates)
    }
    
    class FBArchiveRepository {
        +table_name: str = "FBAdArchive"
        +async get_ad(ad_archive_id, start_date)
        +async save_ad(ad_data)
        +async batch_get_ads(keys)
    }
    
    class LawFirmsRepository {
        +table_name: str = "LawFirms"
        +async get_firm(firm_id)
        +async update_firm_stats(firm_id, stats)
        +async get_all_firms()
    }
    
    class FBImageHashRepository {
        +table_name: str = "FBImageHash"
        +async check_hash_exists(phash)
        +async save_hash(phash, ad_id)
    }
    
    RepositoryBase <|-- FBArchiveRepository
    RepositoryBase <|-- LawFirmsRepository
    RepositoryBase <|-- FBImageHashRepository
```

## Data Flow Diagrams

### Ad Data Transformation Flow

```mermaid
flowchart LR
    subgraph Input
        GraphQL[GraphQL Response] --> Parser[Parser]
        Parser --> RawAd[Raw Ad Dict]
    end
    
    subgraph Transform
        RawAd --> Extract[Extract Fields]
        Extract --> Map[Field Mapping]
        Map --> Convert[Snake to Pascal]
        Convert --> Validate[Validate Types]
    end
    
    subgraph Enhance
        Validate --> AI{AI Enhancement}
        AI -->|Text| Summary[Generate Summary]
        AI -->|Image| OCR[Extract Text]
        Summary --> Enhanced[Enhanced Ad]
        OCR --> Enhanced
    end
    
    subgraph Output
        Enhanced --> DynamoDB[DynamoDB Item]
        Enhanced --> S3[S3 Images]
    end
```

### Job Configuration Flow

```mermaid
flowchart TD
    FirmData[Law Firm Data] --> Config[Create Job Config]
    
    Config --> JobConfig{Job Configuration}
    JobConfig --> FirmID[firm_id]
    JobConfig --> PageID[page_id]
    JobConfig --> DateRange[date_range]
    JobConfig --> SessionType[session_type]
    
    JobConfig --> Dependencies{Global Dependencies}
    Dependencies --> APIClient[api_client]
    Dependencies --> ImageHandler[image_handler]
    Dependencies --> AIIntegrator[ai_integrator]
    Dependencies --> Repositories[repositories]
    
    JobConfig --> Execute[Execute Job]
```

### Error Handling Flow

```mermaid
flowchart TD
    Job[Job Execution] --> Try{Try Block}
    
    Try -->|Success| Complete[Mark Complete]
    Try -->|Error| Catch[Catch Exception]
    
    Catch --> Classify{Classify Error}
    Classify -->|Network| Retry1[Retry with Backoff]
    Classify -->|Auth| Refresh[Refresh Session]
    Classify -->|Parse| Log1[Log & Skip Ad]
    Classify -->|Fatal| Fail[Mark Job Failed]
    
    Retry1 --> Try
    Refresh --> Try
    
    Fail --> Record[Record in FailedFirmsManager]
    Record --> Notify[Error Handling Service]
```

## Key Processing Steps for One Law Firm

### Step-by-Step Breakdown

1. **Initialization**
   - User calls `run_single_firm_scrape(firm_id)`
   - WorkflowService retrieves firm data from LawFirmsRepository
   - Validates firm is active and not recently processed

2. **Job Creation**
   - Creates JobConfig with firm details
   - Assigns session type (Facebook or Camoufox)
   - Sets date range for ad retrieval

3. **Session Setup**
   - SessionManagerFactory creates appropriate session manager
   - For Camoufox: Sets up browser with fingerprint and proxy
   - For Facebook: Uses requests session with cookies

4. **Ad Library Navigation**
   - Navigates to Facebook Ad Library
   - Searches for law firm by name or page ID
   - Captures GraphQL responses during search

5. **Response Processing**
   - GraphQLResponseParser extracts ads from NDJSON
   - Each ad goes through 4-phase processing

6. **Phase 1: Extract**
   - Extracts all fields from raw GraphQL data
   - Maps Facebook field names to internal structure
   - Extracts image/video URLs

7. **Phase 2: Transform**
   - Checks if ad exists in DynamoDB
   - Converts field names from snake_case to PascalCase
   - Validates data types and required fields

8. **Phase 3: Enhance**
   - Checks if summary exists
   - If not, generates summary using AI (DeepSeek/GPT-4)
   - Optionally extracts text from images using LLaVA

9. **Phase 4: Save**
   - Saves ad to DynamoDB (create or update)
   - Processes images (immediate or deferred)
   - Updates law firm statistics

10. **Completion**
    - Updates processing tracker
    - Records success/failure in logs
    - Cleans up session resources

## Configuration and Dependencies

### Key Configuration Parameters

```yaml
# Facebook Ads Configuration
fb_ads:
  # Processing modes
  defer_image_processing: true
  use_proxy: true
  session_type: "camoufox"  # or "facebook"
  
  # Date ranges
  default_date_range_days: 14
  single_firm_date_range_days: 30
  
  # API settings
  max_ad_pages: 50
  api_retries: 3
  
  # AI settings
  disable_llava: false
  disable_gpt: false
  disable_deepseek: false
```

### Dependency Injection Structure

```
MainContainer
├── CoreContainer
│   ├── logger
│   └── http_session
├── StorageContainer
│   ├── async_dynamodb_storage
│   ├── s3_async_storage
│   └── repositories
└── FbAdsContainer
    ├── facebook_ads_orchestrator
    ├── workflow_service
    ├── job_orchestration_service
    ├── job_runner_service
    ├── session_manager_factory
    ├── api_client_factory
    ├── image_handler
    ├── ai_orchestrator
    └── ad_db_service
```

## Performance Considerations

1. **Parallel Processing**
   - Multiple firms can be processed concurrently
   - Controlled by `max_concurrent_firms` setting

2. **Deferred Image Processing**
   - Images queued for later processing
   - Reduces blocking during ad scraping

3. **Connection Pooling**
   - Reuses HTTP sessions and browser contexts
   - Reduces overhead of connection setup

4. **Caching**
   - PHash deduplication prevents duplicate uploads
   - Session cookies cached between requests

5. **Batch Operations**
   - Ads saved in batches to DynamoDB
   - Reduces API calls and improves throughput

## Error Recovery

1. **Retry Logic**
   - Exponential backoff for transient errors
   - Configurable retry attempts

2. **Session Recovery**
   - Automatic session refresh on expiry
   - Proxy rotation on failures

3. **Partial Success**
   - Individual ad failures don't fail entire job
   - Failed ads logged for manual review

4. **State Persistence**
   - Processing tracker maintains state
   - Can resume from last successful point

## Monitoring and Logging

1. **Bandwidth Tracking**
   - BandwidthLogger tracks all HTTP traffic
   - Categorizes by type (images, API, HTML)

2. **Performance Metrics**
   - Processing time per firm
   - Success/failure rates
   - AI processing statistics

3. **Error Tracking**
   - FailedFirmsManager tracks all failures
   - Detailed error context preserved

4. **Audit Trail**
   - All database operations logged
   - State changes tracked with timestamps