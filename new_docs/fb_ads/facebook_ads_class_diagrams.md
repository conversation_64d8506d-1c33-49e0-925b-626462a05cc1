# Facebook Ads System - Detailed Class Diagrams

## Complete System Architecture

```mermaid
classDiagram
    direction TB
    
    %% Base Classes
    class AsyncServiceBase {
        <<abstract>>
        #logger: LoggerProtocol
        #config: Dict[str, Any]
        #_initialized: bool
        +__init__(logger, config)
        +async _execute_action(data: Any) Any*
        +async __aenter__() Self
        +async __aexit__(exc_type, exc_val, exc_tb) bool
        +log_info(message: str)
        +log_error(message: str, exc_info: bool)
        +log_debug(message: str)
        +log_warning(message: str)
    }
    
    class RepositoryBase {
        <<abstract>>
        #storage: AsyncDynamoDBStorage
        #table_name: str
        +__init__(storage, table_name)
        +async get_item(key: Dict) Dict
        +async put_item(item: Dict) bool
        +async update_item(key: Dict, updates: Dict) bool
        +async delete_item(key: Dict) bool
        +async query(params: Dict) List[Dict]
        +async scan(params: Dict) List[Dict]
    }
    
    %% Main Orchestrator
    class FacebookAdsOrchestrator {
        -workflow_service: WorkflowService
        -job_orchestration_service: JobOrchestrationService
        -job_runner_service: JobRunnerService
        -interactive_service: InteractiveService
        -error_handling_service: ErrorHandlingService
        -ad_processing_service: AdProcessingService
        -ad_db_service: AdDBService
        -ai_orchestrator: AIOrchestrator
        -session_manager: SessionManager
        -session_manager_factory: SessionManagerFactory
        -api_client: APIClient
        -image_handler: ImageHandler
        -bandwidth_logger: BandwidthLogger
        -local_image_queue: LocalImageQueue
        -processing_tracker: ProcessingTracker
        -failed_firms_manager: FailedFirmsManager
        -skip_term_service: SkipTermService
        -concurrent_workflow_service: ConcurrentWorkflowService
        +__init__(logger, config, ...)
        +async run_full_scrape() bool
        +async run_single_firm_scrape(firm_id: str) bool
        +async run_ignore_list_processing() bool
        +async add_law_firm_interactive()
        +async add_attorney_by_page_id()
        +search_law_firm_in_db(firm_id: str)
        +async lookup_ad_by_id(ad_id: str)
        +show_failed_firms(date: str, add_to_skip: bool)
        +show_failure_summary(start_date: str, end_date: str)
        +get_bandwidth_usage() Dict
        +async cleanup()
        +async run() bool
        -_setup_dates(config: Dict)
        -_setup_progress_tracking()
        -_validate_llm_availability(ai_orchestrator)
        -_configure_fb_ads_logging()
    }
    
    %% Workflow Services
    class WorkflowService {
        -ad_db_service: AdDBService
        -error_handling_service: ErrorHandlingService
        -session_manager: SessionManager
        -job_orchestration_service: JobOrchestrationService
        -data_validation_service: DataValidationService
        -ad_processing_service: AdProcessingService
        -job_global_dependencies: Dict
        +async run_full_scrape_workflow() bool
        +async run_single_firm_workflow(firm_id: str) bool
        +async run_ignore_list_workflow() bool
        +async process_law_firm(firm_data: Dict, date_range: int) bool
        -async _should_skip_firm(firm: Dict) bool
        -_create_job_config(firm: Dict, date_range: int) JobConfig
    }
    
    class ConcurrentWorkflowService {
        -ad_db_service: AdDBService
        -error_handling_service: ErrorHandlingService
        -session_manager: SessionManager
        -job_orchestration_service: JobOrchestrationService
        -data_validation_service: DataValidationService
        -ad_processing_service: AdProcessingService
        -semaphore: asyncio.Semaphore
        +async process_firms_concurrently(firms: List[Dict]) Dict
        +async process_firm_with_timeout(firm: Dict, timeout: int) bool
        -async _process_firm_task(firm: Dict, progress_bar) bool
    }
    
    %% Job Processing
    class JobOrchestrationService {
        -job_runner_service: JobRunnerService
        -failed_firms_manager: FailedFirmsManager
        -retry_attempts: int
        -backoff_factor: float
        +async execute_job(job_config: JobConfig, dependencies: Dict) bool
        +async handle_job_failure(job_config: JobConfig, error: Exception)
        +async retry_with_backoff(func: Callable, max_attempts: int) Any
        -async _run_job_with_retry(job_config: JobConfig, dependencies: Dict) bool
        -_should_retry(error: Exception) bool
    }
    
    class JobRunnerService {
        -ai_orchestrator: AIOrchestrator
        -graphql_parser: GraphQLResponseParser
        -phase_processors: Dict[str, Callable]
        +async run_job(job_config: JobConfig, dependencies: Dict) JobResult
        +async run_job_phases(job_config: JobConfig, dependencies: Dict) JobResult
        -async _setup_session(job_config: JobConfig, dependencies: Dict) SessionManager
        -async _navigate_to_ad_library(session_manager: SessionManager, firm_name: str)
        -async _capture_graphql_responses(session_manager: SessionManager) List[str]
        -async _process_ads(ads: List[Dict], dependencies: Dict) List[Dict]
        -async _phase_extract_ad_data(raw_ad: Dict, dependencies: Dict) Dict
        -async _phase_transform_ad_data(ad_data: Dict, dependencies: Dict) Dict
        -async _phase_enhance_ad_data(ad_data: Dict, dependencies: Dict) Dict
        -async _phase_save_ad_data(ad_data: Dict, dependencies: Dict) bool
        -async _update_law_firm_stats(job_config: JobConfig, stats: Dict, dependencies: Dict)
    }
    
    %% Session Management
    class SessionManagerFactory {
        <<factory>>
        +static create(config: Dict, logger: Logger, firm_id: str, fingerprint_manager, proxy_manager, law_firms_repository) SessionManager
        +static get_session_type(config: Dict) str
    }
    
    class FacebookSessionManager {
        -requests_session: requests.Session
        -cookies: Dict[str, str]
        -user_agent: str
        -proxy_config: Dict
        +async create_session() Session
        +async login(username: str, password: str) bool
        +async navigate_to_url(url: str) Response
        +async get_ads_for_page(page_id: str, date_range: Dict) List[Dict]
        +async capture_graphql_responses() List[str]
        +refresh_session()
        +is_session_valid() bool
    }
    
    class CamoufoxSessionManager {
        -browser: Browser
        -context: BrowserContext
        -page: Page
        -fingerprint_manager: FingerprintManager
        -proxy_manager: ProxyManager
        -graphql_responses: List[str]
        +async create_browser() Browser
        +async create_context(fingerprint: Dict, proxy: Dict) BrowserContext
        +async navigate_to_ad_library(search_term: str)
        +async capture_graphql_ndjson() List[str]
        +async download_image(url: str, save_path: str) bool
        +async cleanup()
        -async _setup_request_interception()
        -async _handle_route(route: Route)
    }
    
    %% API Clients
    class APIClientFactory {
        <<factory>>
        +static create(session_manager: SessionManager, config: Dict, logger: Logger) APIClient
    }
    
    class FacebookAPIClient {
        -session_manager: FacebookSessionManager
        -base_url: str
        -headers: Dict[str, str]
        +async search_pages(query: str) List[Dict]
        +async get_page_ads(page_id: str, limit: int, after: str) Dict
        +async get_ad_details(ad_id: str) Dict
        -_build_graphql_query(page_id: str, limit: int, after: str) str
        -_parse_graphql_response(response: Dict) List[Dict]
    }
    
    class CamoufoxAPIClient {
        -session_manager: CamoufoxSessionManager
        -graphql_parser: GraphQLResponseParser
        +async search_and_capture(search_term: str) List[Dict]
        +async get_ads_from_captures() List[Dict]
        -async _wait_for_graphql_capture(timeout: int) bool
    }
    
    %% Data Processing
    class AdProcessingService {
        -ad_db_service: AdDBService
        -ad_ner_processor: AdNerProcessor
        -ad_categorizer: FBAdCategorizer
        -image_handler: ImageHandler
        -data_validation_service: DataValidationService
        -law_firms_repo: LawFirmsRepository
        -fb_archive_repo: FBArchiveRepository
        +async process_ads(ads: List[Dict], firm_data: Dict) ProcessingResult
        +async enhance_ad_with_ai(ad: Dict, ai_service) Dict
        +async categorize_ads(ads: List[Dict]) List[Dict]
        -async _validate_ad(ad: Dict) bool
        -async _extract_entities(ad: Dict) Dict
        -async _process_images(ad: Dict) Dict
    }
    
    class AdDBService {
        -fb_archive_repository: FBArchiveRepository
        -law_firms_repository: LawFirmsRepository
        +async save_ads_to_db(ads: List[Dict]) SaveResult
        +async update_law_firm_stats(firm_id: str, stats: Dict) bool
        +async get_existing_ads(ad_ids: List[str]) List[Dict]
        +async check_ad_exists(ad_archive_id: str, start_date: str) bool
        -async _prepare_dynamo_item(ad: Dict) Dict
        -async _should_update_ad(existing: Dict, new: Dict) bool
        -_calculate_stats(ads: List[Dict]) Dict
    }
    
    %% AI Services
    class AIOrchestrator {
        -dependencies: Dict[str, Any]
        -deepseek: DeepSeekService
        -gpt4: OpenAIClient
        -llava: LLaVAService
        -mistral: MistralService
        -prompt_manager: PromptManager
        +async generate_summary(text: str, model: str) str
        +async extract_image_text(image_path: str) str
        +async process_with_fallback(content: str, processors: List[str]) str
        +async enhance_ad_content(ad: Dict) Dict
        -async _select_model(task_type: str) str
        -async _handle_model_failure(model: str, error: Exception)
    }
    
    class DeepSeekService {
        -client: DeepSeekClient
        -prompt_manager: PromptManager
        -model_name: str
        -temperature: float
        +async generate_completion(prompt: str, max_tokens: int) str
        +async generate_ad_summary(ad_text: str) str
        +async classify_ad(ad_content: Dict) Dict
        -_prepare_messages(prompt: str) List[Dict]
    }
    
    %% Image Processing
    class ImageHandler {
        -s3_manager: S3AsyncStorage
        -session_manager: SessionManager
        -hash_manager: FBImageHashService
        -bandwidth_logger: BandwidthLogger
        -resize_config: Dict
        +async download_and_process_image(url: str, ad_id: str) ImageResult
        +async download_image(url: str) bytes
        +async resize_image(image_data: bytes, max_size: Tuple[int, int]) bytes
        +async calculate_phash(image_data: bytes) str
        +async upload_to_s3(image_data: bytes, key: str) str
        +async check_image_exists(phash: str) bool
        +async process_deferred_images(queue: LocalImageQueue, batch_size: int)
        -async _download_with_retry(url: str, max_attempts: int) bytes
        -_generate_s3_key(ad_id: str, image_type: str) str
    }
    
    class LocalImageQueue {
        -db_path: str
        -conn: sqlite3.Connection
        +async add_to_queue(ad_id: str, image_url: str, metadata: Dict)
        +async get_pending_items(limit: int) List[Dict]
        +async mark_processed(item_id: int, success: bool)
        +async cleanup_old_items(days: int)
        +get_queue_stats() Dict
        -_create_tables()
        -_serialize_metadata(metadata: Dict) str
        -_deserialize_metadata(data: str) Dict
    }
    
    %% Data Validation
    class DataValidationService {
        -processing_tracker: ProcessingTracker
        -validation_rules: Dict[str, Callable]
        +validate_firm_data(firm: Dict) ValidationResult
        +validate_ad_data(ad: Dict) ValidationResult
        +validate_job_config(config: JobConfig) ValidationResult
        +check_date_range(start: str, end: str) bool
        +sanitize_firm_name(name: str) str
        -_check_required_fields(data: Dict, required: List[str]) List[str]
        -_validate_date_format(date: str) bool
        -_validate_url(url: str) bool
    }
    
    %% Error Handling
    class ErrorHandlingService {
        -processing_tracker: ProcessingTracker
        -failed_firms: Dict[str, List[Dict]]
        +handle_error(error: Exception, context: Dict)
        +show_failed_firms(date: str, add_to_skip: bool)
        +show_failure_summary(start_date: str, end_date: str)
        +get_error_category(error: Exception) str
        +should_retry(error: Exception) bool
        -_format_error_message(error: Exception, context: Dict) str
        -_save_error_to_file(error_data: Dict)
    }
    
    class FailedFirmsManager {
        -data_dir: str
        -failed_firms_file: str
        +add_failed_firm(firm_id: str, error: str, timestamp: str)
        +get_failed_firms(date: str) List[Dict]
        +clear_failed_firms(date: str)
        +export_to_csv(output_path: str)
        -_load_failed_firms() Dict
        -_save_failed_firms(data: Dict)
    }
    
    %% Repositories
    class FBArchiveRepository {
        +table_name: str = "FBAdArchive"
        +async get_ad(ad_archive_id: str, start_date: str) Dict
        +async save_ad(ad_data: Dict) bool
        +async batch_get_ads(keys: List[Dict]) List[Dict]
        +async query_by_page_id(page_id: str, date_range: Dict) List[Dict]
        +async update_ad(key: Dict, updates: Dict) bool
        +async get_ads_by_date_range(start: str, end: str) List[Dict]
    }
    
    class LawFirmsRepository {
        +table_name: str = "LawFirms"
        +async get_firm(firm_id: str) Dict
        +async get_firm_by_name(name: str) Dict
        +async update_firm_stats(firm_id: str, stats: Dict) bool
        +async get_all_firms() List[Dict]
        +async get_active_firms() List[Dict]
        +async add_firm(firm_data: Dict) bool
        +async update_last_processed(firm_id: str, timestamp: str) bool
    }
    
    class FBImageHashRepository {
        +table_name: str = "FBImageHash"
        +async check_hash_exists(phash: str) bool
        +async save_hash(phash: str, ad_id: str, image_url: str) bool
        +async get_hash_info(phash: str) Dict
        +async get_hashes_by_ad(ad_id: str) List[Dict]
        +async cleanup_old_hashes(days: int) int
    }
    
    %% Supporting Services
    class GraphQLResponseParser {
        +parse_ndjson_response(response_text: str) List[Dict]
        +extract_ads_from_graphql(data: Dict) List[Dict]
        +validate_ad_structure(ad: Dict) bool
        +extract_page_info(data: Dict) Dict
        -_parse_ndjson_line(line: str) Dict
        -_traverse_graphql_tree(data: Dict, path: List[str]) Any
        -_normalize_ad_data(raw_ad: Dict) Dict
    }
    
    class BandwidthLogger {
        <<singleton>>
        -_instance: BandwidthLogger
        -total_bytes_downloaded: int
        -total_bytes_uploaded: int
        -request_count: int
        -image_bytes: int
        -html_bytes: int
        -api_bytes: int
        +log_download(bytes: int, category: str)
        +log_upload(bytes: int)
        +get_total_bandwidth() Dict
        +log_summary()
        +reset_counters()
        -_format_bytes(bytes: int) str
    }
    
    class ProcessingTracker {
        -file_path: str
        -tracking_data: Dict
        +mark_firm_processed(firm_id: str, timestamp: str)
        +is_firm_processed_today(firm_id: str) bool
        +get_processing_history(firm_id: str) List[Dict]
        +save_progress(data: Dict)
        +load_progress() Dict
        -_get_today_key() str
        -_cleanup_old_entries(days: int)
    }
    
    class SkipTermService {
        -skip_terms: List[str]
        -config_file: str
        +should_skip_ad(ad_text: str) bool
        +add_skip_term(term: str)
        +remove_skip_term(term: str)
        +get_skip_terms() List[str]
        +reload_config()
        -_load_skip_terms() List[str]
        -_save_skip_terms()
    }
    
    %% Model Classes
    class JobConfig {
        +job_id: str
        +firm_id: str
        +firm_name: str
        +page_id: str
        +date_range: DateRange
        +session_type: str
        +max_pages: int
        +defer_images: bool
        +retry_count: int
        +to_dict() Dict
        +from_dict(data: Dict) JobConfig
        +validate() bool
    }
    
    class JobResult {
        +job_id: str
        +success: bool
        +ads_processed: int
        +ads_saved: int
        +errors: List[str]
        +duration: float
        +bandwidth_used: Dict
        +to_dict() Dict
        +add_error(error: str)
        +merge(other: JobResult) JobResult
    }
    
    %% Relationships
    AsyncServiceBase <|-- FacebookAdsOrchestrator
    AsyncServiceBase <|-- WorkflowService
    AsyncServiceBase <|-- ConcurrentWorkflowService
    AsyncServiceBase <|-- JobOrchestrationService
    AsyncServiceBase <|-- JobRunnerService
    AsyncServiceBase <|-- AdProcessingService
    AsyncServiceBase <|-- AdDBService
    AsyncServiceBase <|-- AIOrchestrator
    AsyncServiceBase <|-- DeepSeekService
    AsyncServiceBase <|-- ImageHandler
    AsyncServiceBase <|-- DataValidationService
    AsyncServiceBase <|-- ErrorHandlingService
    AsyncServiceBase <|-- GraphQLResponseParser
    AsyncServiceBase <|-- BandwidthLogger
    AsyncServiceBase <|-- ProcessingTracker
    AsyncServiceBase <|-- SkipTermService
    
    RepositoryBase <|-- FBArchiveRepository
    RepositoryBase <|-- LawFirmsRepository
    RepositoryBase <|-- FBImageHashRepository
    
    FacebookSessionManager ..|> SessionManager
    CamoufoxSessionManager ..|> SessionManager
    FacebookAPIClient ..|> APIClient
    CamoufoxAPIClient ..|> APIClient
    
    FacebookAdsOrchestrator --> WorkflowService
    FacebookAdsOrchestrator --> JobOrchestrationService
    FacebookAdsOrchestrator --> InteractiveService
    FacebookAdsOrchestrator --> ErrorHandlingService
    
    WorkflowService --> JobOrchestrationService
    WorkflowService --> AdDBService
    WorkflowService --> DataValidationService
    
    JobOrchestrationService --> JobRunnerService
    JobOrchestrationService --> FailedFirmsManager
    
    JobRunnerService --> AIOrchestrator
    JobRunnerService --> GraphQLResponseParser
    JobRunnerService --> AdDBService
    JobRunnerService --> ImageHandler
    
    AdProcessingService --> AdDBService
    AdProcessingService --> ImageHandler
    AdProcessingService --> AIOrchestrator
    
    AdDBService --> FBArchiveRepository
    AdDBService --> LawFirmsRepository
    
    ImageHandler --> FBImageHashRepository
    ImageHandler --> S3AsyncStorage
    ImageHandler --> LocalImageQueue
    
    SessionManagerFactory ..> FacebookSessionManager
    SessionManagerFactory ..> CamoufoxSessionManager
    
    APIClientFactory ..> FacebookAPIClient
    APIClientFactory ..> CamoufoxAPIClient
```

## Dependency Injection Container Structure

```mermaid
classDiagram
    class MainContainer {
        +config: Configuration
        +core: CoreContainer
        +storage: StorageContainer
        +fb_ads: FbAdsContainer
        +pacer: PacerContainer
        +transformer: TransformerContainer
        +reports: ReportsContainer
    }
    
    class CoreContainer {
        +config: Configuration
        +logger: Factory~LoggerWrapper~
        +http_session: Factory~aiohttp.ClientSession~
    }
    
    class StorageContainer {
        +config: Configuration
        +logger: Dependency
        +aws_region: str
        +async_dynamodb_storage: Singleton~AsyncDynamoDBStorage~
        +s3_async_storage: Singleton~S3AsyncStorage~
        +fb_archive_repository: Singleton~FBArchiveRepository~
        +law_firms_repository: Singleton~LawFirmsRepository~
        +fb_image_hash_repository: Singleton~FBImageHashRepository~
    }
    
    class FbAdsContainer {
        +config: Configuration
        +logger: Dependency
        +storage_container: DependenciesContainer
        +http_session: Dependency
        +deepseek_client: Singleton~DeepSeekClient~
        +openai_client: Singleton~OpenAIClient~
        +prompt_manager: Singleton~PromptManager~
        +deepseek_service: Singleton~DeepSeekService~
        +ai_orchestrator: Singleton~AIOrchestrator~
        +session_manager_factory: Object~SessionManagerFactory~
        +session_manager: Factory~SessionManager~
        +api_client: Factory~APIClient~
        +bandwidth_logger: Singleton~BandwidthLogger~
        +image_handler: Singleton~ImageHandler~
        +processing_tracker: Singleton~ProcessingTracker~
        +error_handling_service: Singleton~ErrorHandlingService~
        +data_validation_service: Singleton~DataValidationService~
        +job_runner_service: Singleton~JobRunnerService~
        +job_orchestration_service: Singleton~JobOrchestrationService~
        +ad_processing_service: Singleton~AdProcessingService~
        +ad_db_service: Singleton~AdDBService~
        +workflow_service: Singleton~WorkflowService~
        +concurrent_workflow_service: Singleton~ConcurrentWorkflowService~
        +facebook_ads_orchestrator: Singleton~FacebookAdsOrchestrator~
    }
    
    MainContainer --> CoreContainer
    MainContainer --> StorageContainer
    MainContainer --> FbAdsContainer
    
    FbAdsContainer --> StorageContainer : uses repositories
    FbAdsContainer --> CoreContainer : uses logger, http_session
```

## Processing Flow State Machine

```mermaid
stateDiagram-v2
    [*] --> Initialization
    
    Initialization --> FirmValidation
    FirmValidation --> SkipFirm : Invalid/Skip
    FirmValidation --> CreateJob : Valid
    
    SkipFirm --> [*]
    
    CreateJob --> SessionSetup
    SessionSetup --> SessionFailed : Failed
    SessionSetup --> NavigateToAdLibrary : Success
    
    SessionFailed --> RetrySession : Retryable
    SessionFailed --> MarkFailed : Not Retryable
    RetrySession --> SessionSetup
    
    NavigateToAdLibrary --> SearchFirm
    SearchFirm --> CaptureGraphQL
    CaptureGraphQL --> ParseResponse
    
    ParseResponse --> NoAds : No Ads Found
    ParseResponse --> ProcessAds : Ads Found
    
    NoAds --> UpdateStats
    
    ProcessAds --> ExtractPhase
    ExtractPhase --> TransformPhase
    TransformPhase --> EnhancePhase
    EnhancePhase --> SavePhase
    SavePhase --> NextAd
    
    NextAd --> ProcessAds : More Ads
    NextAd --> UpdateStats : No More Ads
    
    UpdateStats --> Cleanup
    Cleanup --> Success
    
    MarkFailed --> RecordFailure
    RecordFailure --> Cleanup
    
    Success --> [*]
    
    state ProcessAds {
        [*] --> ExtractFields
        ExtractFields --> ValidateData
        ValidateData --> CheckDatabase
        CheckDatabase --> ApplyTransform
        ApplyTransform --> AIEnhancement
        AIEnhancement --> SaveToStorage
        SaveToStorage --> [*]
    }
```

## Error Handling and Recovery Flow

```mermaid
flowchart TD
    Error[Error Occurred] --> Classify{Classify Error}
    
    Classify -->|Network| NetworkError[Network Error]
    Classify -->|Auth| AuthError[Authentication Error]
    Classify -->|Parse| ParseError[Parse Error]
    Classify -->|Storage| StorageError[Storage Error]
    Classify -->|AI| AIError[AI Service Error]
    Classify -->|Unknown| UnknownError[Unknown Error]
    
    NetworkError --> Retry{Retry?}
    AuthError --> RefreshAuth[Refresh Session]
    ParseError --> SkipItem[Skip Current Item]
    StorageError --> Retry
    AIError --> Fallback[Use Fallback Service]
    UnknownError --> LogAndFail[Log and Fail]
    
    Retry -->|Yes| ExponentialBackoff[Exponential Backoff]
    Retry -->|No| RecordFailure[Record Failure]
    
    RefreshAuth --> Success{Success?}
    Success -->|Yes| Continue[Continue Processing]
    Success -->|No| RecordFailure
    
    ExponentialBackoff --> AttemptAgain[Attempt Again]
    AttemptAgain --> CheckAttempts{Max Attempts?}
    CheckAttempts -->|No| ProcessAgain[Process Again]
    CheckAttempts -->|Yes| RecordFailure
    
    Fallback --> FallbackSuccess{Success?}
    FallbackSuccess -->|Yes| Continue
    FallbackSuccess -->|No| SkipEnhancement[Skip Enhancement]
    
    SkipItem --> Continue
    SkipEnhancement --> Continue
    LogAndFail --> RecordFailure
    
    RecordFailure --> NotifyUser[Notify User]
    Continue --> ResumeProcessing[Resume Processing]
```

## Data Model Relationships

```mermaid
erDiagram
    LawFirms ||--o{ FBAdArchive : "has"
    FBAdArchive ||--o{ FBImageHash : "contains"
    
    LawFirms {
        string ID PK
        string Name PK
        string PageID
        string PageAlias
        string Category
        string ImageURI
        datetime LastUpdated
        datetime AdArchiveLastUpdated
        int NumAds
        boolean IsActive
    }
    
    FBAdArchive {
        string AdArchiveID PK
        string StartDate PK
        string PageID FK
        string PageName
        string LawFirm
        datetime LastUpdated
        boolean IsActive
        string EndDate
        string PublisherPlatform
        string LinkUrl
        string VideoHdUrl
        string VideoSdUrl
        string VideoPreviewImageUrl
        string OriginalImageUrl
        string ResizedImageUrl
        string S3ImageKey
        string ImageText
        string Body
        string Caption
        string CtaText
        string Title
        string Summary
        string LLM
        string Category
        string Company
        string Product
        string Injuries
        string LitigationType
        string LitigationName
        string MdlName
        boolean IsForbidden403
        string AdCreativeId
    }
    
    FBImageHash {
        string PHash PK
        string AdArchiveID PK
        string ImageUrl
        string S3ImageKey
        datetime CreatedDate
        datetime LastSeen
    }
    
    ProcessingTracker {
        string FirmID PK
        string Date PK
        datetime ProcessedAt
        string Status
        int AdsProcessed
        int AdsSaved
        string Errors
    }
    
    FailedFirms {
        string FirmID PK
        string Date PK
        datetime FailedAt
        string ErrorType
        string ErrorMessage
        int RetryCount
        boolean AddedToSkipList
    }
```