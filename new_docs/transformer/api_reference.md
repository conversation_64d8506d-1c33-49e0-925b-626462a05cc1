# Data Transformer API Reference

Complete API reference for the Data Transformer service components.

## Core Services

### DataTransformer

Main orchestrator for the transformation pipeline.

```python
class DataTransformer(AsyncServiceBase):
    """Main transformation orchestrator with dependency injection."""
    
    def __init__(
        self,
        config: DataTransformerConfig,
        logger: Logger,
        openai_client: Optional[OpenAI] = None,
        deepseek_service: Optional[DeepSeekService] = None,
        mistral_service: Optional[MistralService] = None,
        s3_manager: Optional[S3AsyncStorage] = None,
        pacer_repository: Optional[PacerRepository] = None
    ):
        """Initialize transformer with dependencies."""
    
    async def run(self) -> Dict[str, Any]:
        """
        Run the transformation pipeline.
        
        Returns:
            Dict containing:
            - processed_count: Number of files processed
            - success_count: Number of successful transformations
            - error_count: Number of errors
            - errors: List of error details
        """
    
    async def _execute_action(
        self, 
        action: str, 
        params: Dict[str, Any]
    ) -> Any:
        """
        Execute a specific transformation action.
        
        Args:
            action: Action name (e.g., "process_file", "validate")
            params: Action parameters
            
        Returns:
            Action-specific results
        """
```

### ComponentFactory

Creates and manages service components.

```python
class ComponentFactory:
    """Factory for creating transformer components."""
    
    def __init__(
        self,
        config: DataTransformerConfig,
        logger: Logger,
        openai_client: Optional[OpenAI] = None,
        deepseek_service: Optional[DeepSeekService] = None,
        mistral_service: Optional[MistralService] = None,
        s3_manager: Optional[S3AsyncStorage] = None,
        pacer_repository: Optional[PacerRepository] = None
    ):
        """Initialize factory with dependencies."""
    
    def create_docket_processor(self) -> DocketProcessor:
        """Create docket processor instance."""
    
    def create_law_firm_processor(self) -> LawFirmProcessor:
        """Create law firm processor instance."""
    
    def create_litigation_classifier(self) -> LitigationClassifier:
        """Create litigation classifier instance."""
    
    def create_mdl_processor(self) -> MDLProcessor:
        """Create MDL processor instance."""
    
    def create_transfer_handler(self) -> TransferHandler:
        """Create transfer handler instance."""
    
    def create_file_handler(self) -> FileHandler:
        """Create file handler instance."""
    
    def create_uploader(self) -> Uploader:
        """Create uploader instance."""
```

## Processing Services

### LawFirmProcessor

Processes law firm data with automatic fallback.

```python
class LawFirmProcessor(AsyncServiceBase):
    """Law firm extraction and normalization with fallback."""
    
    async def process(
        self, 
        docket_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract and normalize law firms from docket data.
        
        Args:
            docket_data: Docket data containing attorney information
            
        Returns:
            Dict with:
            - LawFirm: List of normalized law firm names
            - LawFirms: Deduplicated list of law firms
            - law_firm_source: Source field used ("attorneys_gpt" or "attorney")
            
        Fallback behavior:
        1. Attempts extraction from attorneys_gpt field
        2. Falls back to attorney field if attorneys_gpt empty/null
        3. Applies normalization and deduplication
        4. Uses attorney lookup for enrichment
        """
    
    async def normalize_law_firm_name(
        self, 
        name: str
    ) -> str:
        """
        Normalize a law firm name.
        
        Args:
            name: Raw law firm name
            
        Returns:
            Normalized name with consistent formatting
        """
    
    async def extract_from_attorneys_gpt(
        self,
        attorneys_data: List[Dict[str, Any]]
    ) -> List[str]:
        """Extract law firms from attorneys_gpt field."""
    
    async def extract_from_attorney_field(
        self,
        attorney_data: Union[str, List[Dict], Dict]
    ) -> List[str]:
        """Extract law firms from attorney field (fallback)."""
```

### LitigationClassifier

AI-powered litigation classification.

```python
class LitigationClassifier(AsyncServiceBase):
    """Classify litigation types using AI."""
    
    async def classify(
        self,
        case_text: str,
        case_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Classify litigation type from case text.
        
        Args:
            case_text: Case description or complaint text
            case_metadata: Optional metadata for context
            
        Returns:
            Dict with:
            - litigation_type: Primary litigation type
            - confidence: Confidence score (0-1)
            - sub_types: List of sub-categories
            - reasoning: AI explanation
        """
    
    async def classify_batch(
        self,
        cases: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Classify multiple cases in batch."""
```

### MDLProcessor

Multi-District Litigation processing.

```python
class MDLProcessor(AsyncServiceBase):
    """Process MDL information and associations."""
    
    async def process(
        self,
        case_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process MDL information for a case.
        
        Args:
            case_data: Case data to process
            
        Returns:
            Dict with:
            - MdlNum: MDL number if applicable
            - MdlCat: MDL category
            - mdl_description: MDL description
            - is_mdl_case: Boolean indicator
        """
    
    async def lookup_mdl(
        self,
        mdl_num: str
    ) -> Optional[Dict[str, Any]]:
        """Look up MDL information by number."""
    
    async def associate_with_mdl(
        self,
        case_data: Dict[str, Any],
        mdl_num: str
    ) -> Dict[str, Any]:
        """Associate a case with an MDL."""
```

### DocketProcessor

Core docket data processing.

```python
class DocketProcessor(AsyncServiceBase):
    """Process docket data and metadata."""
    
    async def process(
        self,
        docket_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process raw docket data.
        
        Args:
            docket_data: Raw docket data
            
        Returns:
            Processed docket with enriched metadata
        """
    
    async def validate(
        self,
        docket_data: Dict[str, Any]
    ) -> bool:
        """Validate docket data structure."""
    
    async def clean(
        self,
        docket_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Clean and normalize docket data."""
```

## File Management Services

### FileHandler

High-level file operations.

```python
class FileHandler(AsyncServiceBase):
    """Handle file operations for transformer."""
    
    async def discover_files(
        self,
        pattern: str,
        base_path: Optional[str] = None
    ) -> List[str]:
        """
        Discover files matching pattern.
        
        Args:
            pattern: Glob pattern for files
            base_path: Base directory path
            
        Returns:
            List of file paths
        """
    
    async def read_json(
        self,
        file_path: str
    ) -> Dict[str, Any]:
        """Read and parse JSON file."""
    
    async def write_json(
        self,
        file_path: str,
        data: Dict[str, Any]
    ) -> None:
        """Write data to JSON file."""
    
    async def rename_file(
        self,
        old_path: str,
        new_path: str
    ) -> None:
        """Rename or move a file."""
```

### Uploader

S3 upload operations.

```python
class Uploader(AsyncServiceBase):
    """Handle S3 uploads."""
    
    async def upload_file(
        self,
        local_path: str,
        s3_key: str,
        metadata: Optional[Dict[str, str]] = None
    ) -> str:
        """
        Upload file to S3.
        
        Args:
            local_path: Local file path
            s3_key: S3 object key
            metadata: Optional S3 metadata
            
        Returns:
            S3 URL of uploaded file
        """
    
    async def upload_json(
        self,
        data: Dict[str, Any],
        s3_key: str
    ) -> str:
        """Upload JSON data to S3."""
    
    async def batch_upload(
        self,
        files: List[Tuple[str, str]],
        max_concurrent: int = 10
    ) -> List[str]:
        """Upload multiple files concurrently."""
```

## Job Management Services

### JobOrchestrationService

Manages parallel job execution.

```python
class JobOrchestrationService(AsyncServiceBase):
    """Orchestrate parallel job execution."""
    
    async def process_batch(
        self,
        files: List[str],
        num_workers: Optional[int] = None
    ) -> List[JobResult]:
        """
        Process batch of files in parallel.
        
        Args:
            files: List of file paths to process
            num_workers: Number of workers (default: config)
            
        Returns:
            List of JobResult objects
        """
    
    async def process_with_progress(
        self,
        files: List[str],
        progress_callback: Optional[Callable] = None
    ) -> List[JobResult]:
        """Process with progress tracking."""
```

### JobRunnerService

Executes individual jobs.

```python
class JobRunnerService(AsyncServiceBase):
    """Run individual transformation jobs."""
    
    async def run_job(
        self,
        job: TransformationJob
    ) -> JobResult:
        """
        Execute a single transformation job.
        
        Args:
            job: Job specification
            
        Returns:
            JobResult with status and data
        """
```

## Data Models

### TransformationJob

```python
@dataclass
class TransformationJob:
    """Specification for a transformation job."""
    
    file_path: str
    job_id: str
    priority: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3
```

### JobResult

```python
@dataclass
class JobResult:
    """Result of a transformation job."""
    
    job_id: str
    success: bool
    file_path: str
    output_path: Optional[str] = None
    error: Optional[str] = None
    processing_time: float = 0.0
    enrichments: Dict[str, Any] = field(default_factory=dict)
```

### TransformerError

```python
class TransformerError(Exception):
    """Base exception for transformer errors."""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        recoverable: bool = True,
        context: Optional[Dict[str, Any]] = None
    ):
        """Initialize transformer error."""
```

## Configuration Models

### DataTransformerConfig

```python
class DataTransformerConfig(BaseModel):
    """Configuration for data transformer."""
    
    # Required fields
    iso_date: str
    
    # LLM configuration
    llm_provider: str = "openai"
    llm_timeout: int = 120
    
    # Processing configuration
    num_workers: int = 8
    force_reprocess: bool = False
    reprocess_files: List[str] = []
    
    # Feature flags
    normalize_law_firm_names: bool = True
    enable_ai_processing: bool = True
    enable_caching: bool = True
    
    # MDL configuration
    reprocess_mdl_num: Optional[str] = None
    
    # Timeouts
    pdf_processing_timeout: int = 300
    
    # Debug options
    debug_mode: bool = False
    log_level: str = "INFO"
```

## Utility Functions

### Validation Utilities

```python
def validate_docket_structure(data: Dict[str, Any]) -> bool:
    """Validate docket data structure."""

def validate_file_path(path: str) -> bool:
    """Validate file path format."""

def validate_s3_key(key: str) -> bool:
    """Validate S3 key format."""
```

### Transformation Utilities

```python
async def extract_text_from_pdf(
    pdf_path: str,
    timeout: int = 300
) -> str:
    """Extract text from PDF file."""

def normalize_case_title(title: str) -> str:
    """Normalize case title for consistency."""

def generate_file_name(
    court_id: str,
    year: str,
    docket_num: str,
    versus: str
) -> str:
    """Generate standardized file name."""
```

## Events and Callbacks

### TransformationEvent

```python
@dataclass
class TransformationEvent:
    """Event emitted during transformation."""
    
    type: str  # "job_started", "job_completed", "job_failed"
    timestamp: datetime
    file_name: str
    job_id: str
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
```

### Event Handlers

```python
class EventHandler(Protocol):
    """Protocol for event handlers."""
    
    async def handle_event(
        self, 
        event: TransformationEvent
    ) -> None:
        """Handle transformation event."""
```

## Error Codes

Common error codes used by the transformer:

- `TRNS001`: File not found
- `TRNS002`: Invalid file format
- `TRNS003`: PDF processing error
- `TRNS004`: LLM API error
- `TRNS005`: S3 upload error
- `TRNS006`: DynamoDB error
- `TRNS007`: Validation error
- `TRNS008`: Configuration error
- `TRNS009`: Timeout error
- `TRNS010`: Resource exhaustion

## Best Practices

1. **Always use async context managers** for proper resource cleanup
2. **Handle TransformerError exceptions** appropriately
3. **Configure timeouts** based on your data characteristics
4. **Enable caching** for improved performance
5. **Monitor memory usage** with large batches
6. **Use appropriate worker counts** for your system
7. **Implement retry logic** for transient failures
8. **Log with context** for better debugging
9. **Validate input data** before processing
10. **Test with various data scenarios**

This API reference provides comprehensive documentation for integrating with and using the Data Transformer services.