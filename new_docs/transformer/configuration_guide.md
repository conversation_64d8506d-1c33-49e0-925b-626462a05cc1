# Data Transformer Configuration Guide

This guide covers configuration options for the Data Transformer service architecture.

## Configuration Model

The transformer uses a Pydantic-based configuration model with validation and environment variable support.

### DataTransformerConfig

**Location:** `src/config_models/transformer.py`

**Key Parameters:**

```python
class DataTransformerConfig(BaseModel):
    # Processing date in YYYYMMDD format
    iso_date: str
    
    # LLM provider selection
    llm_provider: str = "openai"  # Options: "openai", "deepseek", "mistral"
    
    # Parallel processing configuration
    num_workers: int = 8  # Number of concurrent workers
    
    # Law firm processing
    normalize_law_firm_names: bool = True  # Enable law firm normalization
    
    # MDL processing
    reprocess_mdl_num: Optional[str] = None  # Force MDL reprocessing
    
    # File processing
    reprocess_files: List[str] = []  # Specific files to reprocess
    force_reprocess: bool = False  # Force reprocessing of all files
    
    # Timeout configuration
    pdf_processing_timeout: int = 300  # PDF processing timeout in seconds
    llm_timeout: int = 120  # LLM API timeout in seconds
    
    # Feature flags
    enable_ai_processing: bool = True  # Enable AI-powered processing
    enable_caching: bool = True  # Enable PDF and AI result caching
    
    # Debug options
    debug_mode: bool = False  # Enable debug logging
    log_level: str = "INFO"  # Logging level
```

## Environment Variables

All configuration parameters can be overridden using environment variables with the `LEXGENIUS_` prefix:

```bash
# Override LLM provider
export LEXGENIUS_LLM_PROVIDER=deepseek

# Set number of workers
export LEXGENIUS_NUM_WORKERS=16

# Enable debug mode
export LEXGENIUS_DEBUG_MODE=true

# Set processing date
export LEXGENIUS_ISO_DATE=20240715
```

## Configuration Files

### YAML Configuration

The transformer supports YAML configuration files:

```yaml
# config/transform.yml
transformer:
  iso_date: "20240715"
  llm_provider: "openai"
  num_workers: 8
  normalize_law_firm_names: true
  enable_ai_processing: true
  pdf_processing_timeout: 300
  
  # Feature flags
  features:
    enable_caching: true
    enable_parallel_processing: true
    
  # LLM configuration
  llm:
    timeout: 120
    max_retries: 3
    fallback_enabled: true
```

### Loading Configuration

```python
from src.config_models.transformer import DataTransformerConfig
from src.config_models.loader import ConfigLoader

# Load from YAML file
config_dict = ConfigLoader.load_yaml("config/transform.yml")
config = DataTransformerConfig(**config_dict["transformer"])

# Or create directly
config = DataTransformerConfig(
    iso_date="20240715",
    llm_provider="openai",
    num_workers=8
)
```

## LLM Provider Configuration

### OpenAI Configuration

```yaml
openai:
  api_key: ${OPENAI_API_KEY}  # From environment variable
  model: "gpt-4"
  temperature: 0.0
  max_tokens: 4000
```

### DeepSeek Configuration

```yaml
deepseek:
  api_key: ${DEEPSEEK_API_KEY}
  base_url: "https://api.deepseek.com/v1"
  model: "deepseek-chat"
  temperature: 0.0
```

### Mistral Configuration

```yaml
mistral:
  api_key: ${MISTRAL_API_KEY}
  model: "mistral-large-latest"
  temperature: 0.0
```

## Performance Tuning

### Parallel Processing

```yaml
# Optimize for high-throughput processing
transformer:
  num_workers: 16  # Increase workers for faster processing
  enable_caching: true  # Cache results to avoid redundant processing
  
  # Memory management
  max_concurrent_pdfs: 5  # Limit concurrent PDF processing
  cache_size_mb: 1000  # Maximum cache size
```

### Resource Limits

```yaml
# Conservative resource usage
transformer:
  num_workers: 4
  pdf_processing_timeout: 600  # Longer timeout for complex PDFs
  max_file_size_mb: 100  # Skip files larger than this
  
  # Error handling
  max_retries: 3
  retry_delay_seconds: 5
```

## Feature Flags

### Specialized Processing Modes

```yaml
transformer:
  # Law firm normalization workflow
  normalize_law_firm_names: true
  law_firm_lookup_enabled: true
  
  # MDL reprocessing
  reprocess_mdl_num: "3016"  # Force reprocess specific MDL
  
  # AFFF calculations
  enable_afff_calculations: true
```

### Debug and Development

```yaml
transformer:
  debug_mode: true
  log_level: "DEBUG"
  
  # Save intermediate results
  save_intermediate_files: true
  intermediate_output_dir: "data/debug"
  
  # Verbose logging
  log_llm_requests: true
  log_file_operations: true
```

## Integration Configuration

### AWS Services

```yaml
aws:
  region: "us-east-1"
  s3:
    bucket: "lexgenius-data"
    prefix: "transformed"
  dynamodb:
    table_prefix: "lexgenius"
```

### Database Configuration

```yaml
database:
  pacer_table: "Pacer"
  law_firms_table: "LawFirms"
  batch_size: 25  # DynamoDB batch write size
```

## Best Practices

1. **Environment-Specific Configuration**
   - Use separate config files for dev/staging/prod
   - Override sensitive values with environment variables
   - Never commit API keys to version control

2. **Performance Optimization**
   - Start with conservative worker counts and increase gradually
   - Monitor memory usage when increasing cache sizes
   - Use caching for repeated processing runs

3. **Error Handling**
   - Set appropriate timeouts based on your data
   - Configure retry policies for transient failures
   - Enable debug mode for troubleshooting

4. **Resource Management**
   - Limit concurrent operations based on available resources
   - Configure appropriate cache sizes for your system
   - Monitor and adjust based on performance metrics

## Example Configurations

### Development Configuration

```yaml
transformer:
  iso_date: "20240715"
  llm_provider: "deepseek"  # Cheaper for development
  num_workers: 4
  debug_mode: true
  log_level: "DEBUG"
  save_intermediate_files: true
```

### Production Configuration

```yaml
transformer:
  iso_date: "20240715"
  llm_provider: "openai"
  num_workers: 16
  enable_caching: true
  max_retries: 5
  log_level: "INFO"
  
  # Performance optimization
  pdf_processing_timeout: 300
  cache_size_mb: 2000
  
  # Feature flags
  normalize_law_firm_names: true
  enable_afff_calculations: true
```

### Batch Processing Configuration

```yaml
transformer:
  # Process specific files
  reprocess_files:
    - "njd_24_12345_smith_v_acme.json"
    - "cand_24_67890_doe_v_corp.json"
  
  # Or force reprocess all
  force_reprocess: true
  
  # Optimize for batch
  num_workers: 20
  enable_caching: false  # Fresh processing
```

This configuration system provides flexibility while maintaining type safety and validation through Pydantic models.