# Data Transformer Integration Guide

This guide explains how to integrate with the Data Transformer service architecture and its components.

## Integration Overview

The Data Transformer integrates with multiple systems and services:

1. **Main Architecture** - Via MainOrchestrator and ProcessingOrchestrator
2. **AWS Services** - S3 for storage, DynamoDB for persistence
3. **AI Services** - Multiple LLM providers for text processing
4. **File System** - Local file operations and management
5. **Other Services** - PACER data, report generation, etc.

## Main Architecture Integration

### Entry Point Integration

The transformer is invoked through the main orchestration flow:

```python
# src/main.py
from src.orchestration.main_orchestrator import MainOrchestrator

orchestrator = MainOrchestrator(config)
await orchestrator.run()

# Which calls ProcessingOrchestrator
# Which invokes DataTransformer
```

### Direct Integration

For direct integration in custom workflows:

```python
from src.services.transformer.data_transformer import DataTransformer
from src.services.ai.deepseek_service import DeepSeekService
from src.services.ai.mistral_service import MistralService
from src.services.storage.s3_async_storage import S3AsyncStorage
from src.repositories.pacer_repository import PacerRepository

# Initialize dependencies
deepseek_service = DeepSeekService(config)
mistral_service = MistralService(config)
s3_storage = S3AsyncStorage(config)
pacer_repository = PacerRepository(dynamodb_client)

# Create transformer with dependency injection
transformer = DataTransformer(
    config=transformer_config,
    logger=logger,
    openai_client=openai_client,
    deepseek_service=deepseek_service,
    mistral_service=mistral_service,
    s3_manager=s3_storage,
    pacer_repository=pacer_repository
)

# Run transformation
results = await transformer.run()
```

## Component Integration

### Using Individual Components

Access specific transformer components through the ComponentFactory:

```python
from src.services.transformer.component_factory import ComponentFactory

# Create factory
factory = ComponentFactory(
    config=config,
    logger=logger,
    openai_client=openai_client,
    deepseek_service=deepseek_service,
    mistral_service=mistral_service,
    s3_manager=s3_storage,
    pacer_repository=pacer_repository
)

# Get specific processors
law_firm_processor = factory.create_law_firm_processor()
litigation_classifier = factory.create_litigation_classifier()
mdl_processor = factory.create_mdl_processor()

# Use processors individually
law_firms = await law_firm_processor.process(docket_data)
litigation_type = await litigation_classifier.classify(case_text)
mdl_info = await mdl_processor.process(case_data)
```

### Custom Processing Workflows

Create custom workflows using transformer components:

```python
from src.services.transformer.data_processing_engine import DataProcessingEngine
from src.services.transformer.specialized_workflows import SpecializedWorkflows

# Custom workflow for specific case types
class CustomCaseProcessor:
    def __init__(self, component_factory):
        self.engine = DataProcessingEngine(
            config=config,
            logger=logger,
            component_factory=component_factory
        )
        self.workflows = SpecializedWorkflows(
            config=config,
            logger=logger,
            component_factory=component_factory
        )
    
    async def process_special_case(self, case_data):
        # Load and validate
        validated_data = await self.engine.load_and_validate(case_data)
        
        # Apply specialized processing
        if self.is_afff_case(validated_data):
            return await self.workflows.process_afff_case(validated_data)
        else:
            return await self.engine.process_standard(validated_data)
```

## Service Dependencies

### Required Services

The transformer requires these services to be initialized:

```python
# AI Services (at least one required)
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
deepseek_service = DeepSeekService(config)
mistral_service = MistralService(config)

# Storage Services
s3_manager = S3AsyncStorage(config)

# Repository Services
pacer_repository = PacerRepository(dynamodb_client)
```

### Optional Services

These services enhance functionality but aren't required:

```python
# Law firm lookup service
law_firm_lookup = LawFirmLookupService(config)

# MDL lookup service
mdl_lookup = MDLLookupService(config)

# Monitoring service
monitoring = MonitoringService(config)
```

## Data Flow Integration

### Input Data Format

The transformer expects JSON files with this structure:

```json
{
    "FilingDate": "20240715",
    "DocketNum": "24-12345",
    "CourtId": "njd",
    "Versus": "Smith v. Acme Corp",
    "attorneys_gpt": [
        {
            "attorney_name": "John Doe",
            "law_firm": "Doe & Associates"
        }
    ],
    "attorney": "John Doe - Doe & Associates",
    "BaseFilename": "njd_24_12345.json"
}
```

### Output Data Format

The transformer enriches data with additional fields:

```json
{
    // Original fields preserved
    "FilingDate": "20240715",
    "DocketNum": "24-12345",
    
    // Added by transformer
    "LawFirm": ["Doe & Associates"],
    "LawFirms": ["Doe & Associates"],
    "LitigationType": "Personal Injury",
    "MdlNum": "3016",
    "MdlCat": "AFFF",
    "ProcessingStatus": "completed",
    "Enrichments": {
        "pdf_text_extracted": true,
        "ai_classification_complete": true,
        "law_firm_normalized": true
    }
}
```

## Event Integration

### Progress Tracking

Subscribe to transformation progress events:

```python
from src.services.transformer.data_transformer import TransformationEvent

class ProgressHandler:
    async def handle_event(self, event: TransformationEvent):
        if event.type == "job_started":
            print(f"Processing {event.file_name}")
        elif event.type == "job_completed":
            print(f"Completed {event.file_name}")
        elif event.type == "job_failed":
            print(f"Failed {event.file_name}: {event.error}")

# Register handler
transformer.register_event_handler(ProgressHandler())
```

### Error Handling

Integrate with error handling:

```python
from src.services.transformer.error_handler import TransformerError

try:
    results = await transformer.run()
except TransformerError as e:
    # Handle transformer-specific errors
    logger.error(f"Transformation failed: {e}")
    if e.recoverable:
        # Retry logic
        await retry_transformation()
    else:
        # Fatal error handling
        await notify_admin(e)
```

## Parallel Processing Integration

### Batch Processing

Process multiple files in parallel:

```python
from src.services.transformer.job_orchestration_service import JobOrchestrationService

# Create job orchestrator
orchestrator = JobOrchestrationService(
    config=config,
    logger=logger,
    component_factory=factory
)

# Process batch of files
files = ["file1.json", "file2.json", "file3.json"]
results = await orchestrator.process_batch(
    files=files,
    num_workers=8
)

# Check results
for result in results:
    if result.success:
        print(f"Processed {result.file_name}")
    else:
        print(f"Failed {result.file_name}: {result.error}")
```

### Custom Worker Configuration

Configure parallel processing:

```python
# Configure for high-throughput
config = DataTransformerConfig(
    num_workers=16,  # More workers
    enable_caching=True,  # Cache for efficiency
    pdf_processing_timeout=300  # Reasonable timeout
)

# Configure for resource-constrained environment
config = DataTransformerConfig(
    num_workers=4,  # Fewer workers
    max_concurrent_pdfs=2,  # Limit PDF processing
    cache_size_mb=500  # Smaller cache
)
```

## Testing Integration

### Unit Testing

Test individual components:

```python
import pytest
from unittest.mock import Mock, AsyncMock

@pytest.mark.asyncio
async def test_law_firm_processor():
    # Mock dependencies
    mock_config = Mock()
    mock_logger = Mock()
    mock_lookup = AsyncMock()
    
    # Create processor
    processor = LawFirmProcessor(
        config=mock_config,
        logger=mock_logger,
        lookup_service=mock_lookup
    )
    
    # Test data
    test_data = {
        "attorneys_gpt": [
            {"law_firm": "Test Firm"}
        ]
    }
    
    # Process
    result = await processor.process(test_data)
    
    # Verify
    assert "Test Firm" in result["LawFirms"]
```

### Integration Testing

Test full transformation flow:

```python
@pytest.mark.integration
async def test_full_transformation():
    # Create test transformer
    transformer = create_test_transformer()
    
    # Create test file
    test_file = create_test_docket_file()
    
    # Run transformation
    results = await transformer.run()
    
    # Verify enrichments
    assert results[0]["LitigationType"] is not None
    assert results[0]["LawFirms"] is not None
    assert results[0]["ProcessingStatus"] == "completed"
```

## Monitoring Integration

### Metrics Collection

Integrate with monitoring systems:

```python
from prometheus_client import Counter, Histogram

# Define metrics
transformation_counter = Counter(
    'transformer_jobs_total',
    'Total transformation jobs',
    ['status']
)

transformation_duration = Histogram(
    'transformer_job_duration_seconds',
    'Transformation job duration'
)

# Instrument transformer
class MonitoredTransformer(DataTransformer):
    async def _execute_action(self, action, params):
        with transformation_duration.time():
            try:
                result = await super()._execute_action(action, params)
                transformation_counter.labels(status='success').inc()
                return result
            except Exception as e:
                transformation_counter.labels(status='failure').inc()
                raise
```

### Logging Integration

Configure structured logging:

```python
import structlog

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

# Use in transformer
logger = structlog.get_logger()
transformer = DataTransformer(config=config, logger=logger)
```

## Best Practices

1. **Dependency Injection**
   - Always inject dependencies rather than creating them internally
   - Use interfaces/protocols for loose coupling
   - Mock dependencies for testing

2. **Error Handling**
   - Catch and handle `TransformerError` exceptions
   - Implement retry logic for transient failures
   - Log errors with full context

3. **Resource Management**
   - Use async context managers for proper cleanup
   - Limit concurrent operations based on resources
   - Monitor memory usage with large batches

4. **Performance**
   - Enable caching for repeated processing
   - Tune worker counts based on workload
   - Monitor processing times and optimize bottlenecks

5. **Testing**
   - Test components in isolation
   - Use integration tests for workflows
   - Mock external dependencies

This integration guide provides the foundation for incorporating the Data Transformer into various workflows and systems.