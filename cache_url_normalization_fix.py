#!/usr/bin/env python3
"""
Fix for URL normalization to handle different Facebook CDN hosts.

The issue is that the current normalization keeps the specific CDN host (netloc)
but Facebook serves the same images from multiple CDN hosts:
- scontent-dfw5-1.xx.fbcdn.net
- scontent-dfw5-2.xx.fbcdn.net  
- scontent.xx.fbcdn.net
- z-m-scontent.xx.fbcdn.net

This fix normalizes ALL Facebook CDN hosts to a common base.
"""

def _normalize_facebook_image_url_fixed(self, url: str) -> str:
    """
    FIXED: Normalize Facebook image URL to handle variations across CDN hosts and parameters.
    
    Facebook image URLs often vary in:
    1. CDN hosts: scontent-dfw5-1.xx.fbcdn.net vs scontent-dfw5-2.xx.fbcdn.net vs z-m-scontent.xx.fbcdn.net
    2. Query parameters: stp=dst-jpg_s60x60_tt6 vs stp=dst-jpg_s600x600_tt6
    3. Tracking params: _nc_gid, oh, oe, ccb that change frequently
    
    This method extracts the stable image identifier and creates a host-agnostic cache key.
    
    Args:
        url: Facebook image URL to normalize
        
    Returns:
        Normalized URL for cache key comparison
    """
    try:
        # Parse URL
        from urllib.parse import urlparse, parse_qs
        import re
        parsed = urlparse(url)
        
        # FIXED: Normalize Facebook CDN hosts to a common base
        # Instead of keeping specific hosts, use a normalized Facebook host
        normalized_host = "scontent.fbcdn.net"
        
        # Check if this is a Facebook CDN URL
        if not any(domain in parsed.netloc for domain in [
            'fbcdn.net', 'facebook.com', 'fbsbx.com'
        ]):
            # Not a Facebook URL, return as-is
            return url
        
        # For Facebook images, extract the stable image identifier from the path
        # Example paths:
        # /v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg
        # /v/t1.6435-9/123456789_987654321_1234567890123456789_n.jpg
        
        # Extract image ID pattern: numbers_numbers_numbers_n.extension
        id_match = re.search(r'(\d+_\d+_\d+_[no]\.)([a-z]+)$', parsed.path)
        if id_match:
            # Use the stable image ID as the primary cache key component
            image_id = id_match.group(1) + id_match.group(2)
            # FIXED: Create a normalized base using common Facebook host and image ID
            normalized_base = f"{parsed.scheme}://{normalized_host}/{image_id}"
        else:
            # Fallback to path normalization with normalized host
            normalized_base = f"{parsed.scheme}://{normalized_host}{parsed.path}"
        
        # Handle query parameters - AGGRESSIVELY filter dynamic ones
        if parsed.query:
            params = parse_qs(parsed.query)
            # CRITICAL: These parameters change frequently and cause cache misses
            dynamic_params = {
                # Size and transformation parameters
                'stp', 'w', 'h', 'q', 'quality',
                # Facebook tracking parameters that change frequently
                '_nc_cat', '_nc_sid', '_nc_ohc', '_nc_oc', '_nc_zt', '_nc_ht', 
                '_nc_gid', 'oh', 'oe', 'ccb', '_nc_aid', '_nc_ad', '_nc_cid',
                # CDN and caching parameters
                'tp', 'nc_pid', 'efg', 'dl', 'nc_eui2', 'hash',
                # Time-based parameters
                'ts', 'time', 'timestamp', 'cache_buster', 'cb',
                # Session-specific parameters
                'session', 'token', 'nonce', 'sig', 'signature'
            }
            
            # Keep only stable, essential identifying parameters
            essential_params = []
            for key, values in params.items():
                if key.lower() not in dynamic_params and not key.startswith('_'):
                    # Additional filter: skip numeric-only parameter names (often dynamic)
                    if not key.replace('_', '').isdigit():
                        essential_params.append(f"{key}={values[0]}")
            
            if essential_params:
                return f"{normalized_base}?{'&'.join(sorted(essential_params))}"
        
        return normalized_base
        
    except Exception as e:
        self.log_debug(f"Failed to normalize URL {url}: {e}")
        # Return simplified fallback normalization
        try:
            # At minimum, strip common dynamic parameters and normalize host
            from urllib.parse import urlparse, parse_qs, urlencode
            parsed = urlparse(url)
            
            # Use normalized Facebook host
            normalized_host = "scontent.fbcdn.net"
            if any(domain in parsed.netloc for domain in ['fbcdn.net', 'facebook.com', 'fbsbx.com']):
                host = normalized_host
            else:
                host = parsed.netloc
            
            if parsed.query:
                params = parse_qs(parsed.query)
                # Remove the most common dynamic parameters
                for param in ['_nc_gid', 'oh', 'oe', 'ccb', '_nc_aid', 'stp']:
                    params.pop(param, None)
                if params:
                    clean_query = urlencode(sorted(params.items()))
                    return f"{parsed.scheme}://{host}{parsed.path}?{clean_query}"
            return f"{parsed.scheme}://{host}{parsed.path}"
        except:
            return url

# Test the fix
def test_fixed_normalization():
    """Test the fixed normalization function"""
    
    class MockExtractor:
        def log_debug(self, msg):
            print(f"DEBUG: {msg}")
    
    extractor = MockExtractor()
    
    test_cases = [
        {
            "name": "Different CDN hosts same image",
            "urls": [
                "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=101",
                "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=104",
                "https://scontent.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=107",
                "https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=110"
            ],
            "should_normalize_to_same": True
        },
        {
            "name": "Thumbnail vs fullsize same image",
            "urls": [
                "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/447181435_1029863199156819_7025413177941515490_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat=101",
                "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/447181435_1029863199156819_7025413177941515490_n.jpg?stp=dst-jpg_s600x600_tt6&_nc_cat=101"
            ],
            "should_normalize_to_same": True
        }
    ]
    
    print("🧪 Testing Fixed URL Normalization")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n📝 Test: {test_case['name']}")
        
        normalized_urls = []
        for url in test_case['urls']:
            normalized = _normalize_facebook_image_url_fixed(extractor, url)
            normalized_urls.append(normalized)
            print(f"   {url[:50]}... -> {normalized}")
        
        # Check if all URLs normalize to the same value
        all_same = len(set(normalized_urls)) == 1
        expected = test_case['should_normalize_to_same']
        
        if all_same == expected:
            print(f"   ✅ PASS: {'All URLs normalized to same value' if all_same else 'URLs normalized to different values'}")
        else:
            print(f"   ❌ FAIL: Expected {'same' if expected else 'different'} normalized values, got {'same' if all_same else 'different'}")
        
        if all_same:
            print(f"   🎯 Normalized to: {normalized_urls[0]}")

if __name__ == "__main__":
    test_fixed_normalization()