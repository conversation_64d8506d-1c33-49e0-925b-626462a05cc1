#!/usr/bin/env python3
"""
Comprehensive test runner for the Facebook cache lookup fix.

This script runs all cache-related tests and validates that the CACHE MISS
issue has been resolved.
"""

import asyncio
import sys
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Any

def run_command(cmd: List[str], cwd: Path = None) -> tuple[int, str, str]:
    """Run a command and return exit code, stdout, stderr"""
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd or Path.cwd(),
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 1, "", "Command timed out after 5 minutes"
    except Exception as e:
        return 1, "", f"Command failed: {e}"

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_test_result(test_name: str, passed: bool, details: str = ""):
    """Print a formatted test result"""
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        for line in details.split('\n'):
            if line.strip():
                print(f"     {line}")

async def main():
    """Run comprehensive cache solution tests"""
    print("🧪 FACEBOOK CACHE LOOKUP FIX - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Activate conda environment
    print("🐍 Activating conda environment: lexgenius")
    activate_cmd = ["conda", "activate", "lexgenius"]
    exit_code, stdout, stderr = run_command(activate_cmd)
    if exit_code != 0:
        print("⚠️  Note: Could not activate conda environment via subprocess")
        print("   Please ensure you're running in the 'lexgenius' environment")
    
    # Test configuration
    test_results = {}
    project_root = Path.cwd()
    
    # 1. Unit Tests - URL Normalization
    print_section("Unit Tests - URL Normalization Logic")
    
    unit_test_cmd = [
        "python", "-m", "pytest", 
        "tests/unit/test_browser_cache_url_normalization.py",
        "-v", "-x", "--tb=short"
    ]
    
    exit_code, stdout, stderr = run_command(unit_test_cmd)
    unit_tests_passed = exit_code == 0
    test_results["unit_tests"] = unit_tests_passed
    
    print_test_result("URL Normalization Unit Tests", unit_tests_passed, 
                     stdout if unit_tests_passed else stderr)
    
    # 2. Integration Tests - Real Facebook URLs
    print_section("Integration Tests - Real Facebook CDN URLs") 
    
    integration_test_cmd = [
        "python", "-m", "pytest",
        "tests/integration/test_facebook_cache_lookup_fix.py", 
        "-v", "-x", "--tb=short"
    ]
    
    exit_code, stdout, stderr = run_command(integration_test_cmd)
    integration_tests_passed = exit_code == 0
    test_results["integration_tests"] = integration_tests_passed
    
    print_test_result("Facebook CDN Integration Tests", integration_tests_passed,
                     stdout if integration_tests_passed else stderr)
    
    # 3. Performance Tests
    print_section("Performance Benchmarks")
    
    performance_test_cmd = [
        "python", "-m", "pytest",
        "tests/performance/test_cache_performance_benchmarks.py",
        "-v", "-s", "--tb=short"
    ]
    
    exit_code, stdout, stderr = run_command(performance_test_cmd)
    performance_tests_passed = exit_code == 0  
    test_results["performance_tests"] = performance_tests_passed
    
    print_test_result("Cache Performance Benchmarks", performance_tests_passed,
                     stdout if performance_tests_passed else stderr)
    
    # 4. Existing Integration Tests
    print_section("Existing Cache Integration Tests")
    
    existing_test_cmd = [
        "python", "-m", "pytest",
        "tests/integration/test_browser_image_cache_extraction.py",
        "-v", "-x", "--tb=short"
    ]
    
    exit_code, stdout, stderr = run_command(existing_test_cmd)
    existing_tests_passed = exit_code == 0
    test_results["existing_tests"] = existing_tests_passed
    
    print_test_result("Existing Cache Integration Tests", existing_tests_passed,
                     stdout if existing_tests_passed else stderr)
    
    # 5. Cache Solution Validation
    print_section("Cache Solution Validation")
    
    validation_passed = await validate_cache_fix()
    test_results["validation"] = validation_passed
    
    print_test_result("Cache Fix Validation", validation_passed)
    
    # 6. Test Summary
    print_section("TEST SUMMARY")
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"Total test suites: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Detailed results
    print("\nDetailed Results:")
    for test_name, passed in test_results.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {test_name.replace('_', ' ').title()}")
    
    # Overall result
    all_passed = all(test_results.values())
    
    if all_passed:
        print_section("🎉 SUCCESS: CACHE MISS FIX VALIDATED")
        print("✅ All tests passed!")
        print("✅ Cache lookup fix is working correctly")
        print("✅ Performance is within acceptable limits")
        print("✅ Facebook CDN URL normalization is effective")
        return 0
    else:
        print_section("❌ FAILURE: ISSUES DETECTED")
        print("Some tests failed. Please review the output above.")
        failed_tests = [name for name, passed in test_results.items() if not passed]
        print(f"Failed test suites: {', '.join(failed_tests)}")
        return 1

async def validate_cache_fix() -> bool:
    """
    Validate that the cache fix actually resolves the original issue.
    
    This creates a realistic scenario and verifies the fix works.
    """
    try:
        from unittest.mock import MagicMock
        from src.services.fb_ads.browser_image_cache_extractor import (
            BrowserImageCacheExtractor,
            CachedImageResource
        )
        
        print("🔍 Running cache fix validation...")
        
        # Create cache extractor  
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Scenario: Thumbnail cached during GraphQL, full-size requested later
        thumbnail_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
            "&oe=6732D6F0"
        )
        
        # Cache the thumbnail
        thumbnail_content = b"validation_thumbnail_content"
        cached_resource = CachedImageResource(
            url=thumbnail_url,
            content=thumbnail_content,
            content_type="image/jpeg",
            content_length=len(thumbnail_content),
            timestamp=time.time(),
            source="validation_test"
        )
        await extractor._store_cached_image(cached_resource)
        
        # Request full-size with different params
        fullsize_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=DifferentHashValue&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=DifferentGIDValue&oh=00_DifferentOHValue&oe=DifferentOEValue"
        )
        
        # This should hit the cache
        cached_image = await extractor.get_cached_image(fullsize_url)
        
        # Validate the fix
        validation_results = []
        
        # 1. Cache hit occurred
        hit_occurred = cached_image is not None
        validation_results.append(("Cache hit occurred", hit_occurred))
        
        # 2. Correct content returned
        correct_content = cached_image and cached_image.content == thumbnail_content
        validation_results.append(("Correct content returned", correct_content))
        
        # 3. Statistics updated correctly
        stats_correct = (extractor._cache_hits == 1 and extractor._cache_misses == 0)
        validation_results.append(("Statistics correct", stats_correct))
        
        # 4. URL normalization worked
        normalized_thumb = extractor._normalize_facebook_image_url(thumbnail_url)
        normalized_full = extractor._normalize_facebook_image_url(fullsize_url)
        normalization_worked = normalized_thumb == normalized_full
        validation_results.append(("URL normalization working", normalization_worked))
        
        # Print validation details
        for test_name, passed in validation_results:
            status = "✅" if passed else "❌"
            print(f"     {status} {test_name}")
        
        # Overall validation result
        all_validations_passed = all(result[1] for result in validation_results)
        
        if all_validations_passed:
            print("     🎉 Cache fix validation PASSED")
        else:
            print("     ❌ Cache fix validation FAILED")
            
        return all_validations_passed
        
    except Exception as e:
        print(f"     ❌ Validation error: {e}")
        return False

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)