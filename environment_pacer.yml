# Minimal environment for PACER services only
# This includes dependencies for:
# - @src/services/pacer/
# - @src/main.py 
# - @src/services/orchestration/
# - @src/protocols/storage.py
# - @src/infrastructure/
# - @src/services/uploader/
# - @src/services/html/
# - @src/services/district_courts/
# - @src/services/ai/

name: lexgenius-pacer
channels:
  - conda-forge
  - defaults
  # Note: pytorch channel removed - not needed for PACER services

dependencies:
  # Python version
  - python=3.11

  # Core dependencies for PACER scraping
  - playwright=1.48.0
  - beautifulsoup4=4.12.3
  - lxml=5.3.0
  - requests=2.32.3
  - aiohttp=3.10.10
  
  # AWS dependencies for storage
  - boto3=1.35.63
  - aioboto3=13.3.0
  - botocore=1.35.63
  
  # Data processing
  - pandas=2.2.3
  - numpy=1.26.4
  - pyyaml=6.0.2
  
  # Configuration and validation
  - pydantic=2.10.2
  - pydantic-settings=2.6.1
  - python-dotenv=1.0.1
  
  # Async support
  - aiofiles=24.1.0
  - backoff=2.2.1
  - tenacity=9.0.0
  
  # AI/LLM dependencies
  - openai=1.56.1
#  - tiktoken=0.8.0
  # Note: transformers and sentence-transformers removed - not used in PACER services
  # They were only used in Facebook Ads components for embeddings
#  - scikit-learn=1.5.2
  
  # NLP dependencies
#  - nltk=3.9.1
#  - fuzzywuzzy=0.18.0
#  - python-levenshtein=0.26.1
  
  # Image processing (for PACER document handling)
#  - pillow=11.0.0
#  - pdf2image=1.17.0
#  - imagehash=4.3.1

  # Console and logging
  - rich=13.9.4
  - colorlog=6.9.0
#  - loguru=0.7.2
  
  # Utilities
  - psutil=6.1.0
  - jinja2=3.1.4
  - tabulate=0.9.0
  - tqdm=4.67.0
  - chardet=5.2.0
  - holidays=0.62
  
  # File handling
  - pypdf2=3.0.1
  - portalocker=2.10.1
  
  # Testing (optional, can be removed for production)
  - pytest=8.3.3
  - pytest-asyncio=0.24.0
  - pytest-cov=6.0.0
  - pytest-mock=3.14.0
  
  # Pip dependencies not available in conda
  - pip
  - pip:
    # AI/LLM clients not in conda
    - mistralai==1.2.6
    - litellm==1.55.11
    
    # Additional AI/ML packages
#    - hdbscan==0.8.40
#    - umap-learn==0.5.7
    
    # Utilities not in conda
    - json-repair==0.31.2
    - undetected-chromedriver==3.5.5
    - webdriver-manager==4.0.2
    - sentencepiece==0.2.0
    
    # Development tools (optional)
    - ruff==0.8.2
    - mypy==1.13.0
    - types-requests==2.32.0.20241016

# Post-installation instructions:
# 1. Install Playwright browsers: playwright install chromium
