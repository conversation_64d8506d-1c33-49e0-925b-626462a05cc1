#!/usr/bin/env python3
"""
End-to-end test of transfer inheritance fix.
This script runs the actual transformation pipeline on the sample JSON file
to verify that transfer fields are properly inherited.
"""
import asyncio
import json
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import the transformation services
from src.services.transformer.data_transformer import DataTransformer
from src.lib.utils.logging_utils import setup_logging

async def test_end_to_end_transfer():
    """Test the complete transformation with transfer inheritance."""
    
    # Setup logging
    logger = setup_logging("test_transfer_fix", level="DEBUG")
    
    # Test configuration
    config = {
        "transform": {
            "enabled": True,
            "force_reprocess": True,
            "skip_upload": True,  # Don't upload to DynamoDB during test
            "process_single_court": ["scd"],
            "reprocess_files": [
                "scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
            ]
        },
        "iso_date": "20250716",
        "date": "07/16/25",
        "aws_region": "us-east-1",
        "dynamodb": {
            "enabled": False  # Disable DynamoDB for testing
        }
    }
    
    # Initialize the transformer
    transformer = DataTransformer(config=config, logger=logger)
    
    # Path to the test JSON file
    json_path = "/Users/<USER>/PycharmProjects/lexgenius/data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    
    print("=" * 80)
    print("End-to-End Transfer Inheritance Test")
    print("=" * 80)
    
    # Load the original JSON data
    with open(json_path, 'r') as f:
        original_data = json.load(f)
    
    print("\nOriginal JSON State:")
    print(f"  court_id: {original_data.get('court_id')}")
    print(f"  docket_num: {original_data.get('docket_num')}")
    print(f"  case_in_other_court: {original_data.get('case_in_other_court')}")
    print(f"  lead_case: {original_data.get('lead_case')}")
    print(f"  mdl_num: {original_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {original_data.get('transferor_court_id', 'NOT PRESENT')}")
    print(f"  transferor_docket_num: {original_data.get('transferor_docket_num', 'NOT PRESENT')}")
    print(f"  is_transferred: {original_data.get('is_transferred', 'NOT PRESENT')}")
    
    # Create a backup of the original file
    backup_path = json_path + ".backup"
    with open(backup_path, 'w') as f:
        json.dump(original_data, f, indent=2)
    print(f"\n✅ Created backup at: {backup_path}")
    
    try:
        # Run the transformation
        print("\n" + "-" * 80)
        print("Running transformation...")
        print("-" * 80)
        
        await transformer.start()
        result = await transformer.transform_data()
        await transformer.cleanup()
        
        print("\n" + "-" * 80)
        print("Transformation completed")
        print("-" * 80)
        
        # Load the transformed JSON data
        with open(json_path, 'r') as f:
            transformed_data = json.load(f)
        
        print("\nTransformed JSON State:")
        print(f"  mdl_num: {transformed_data.get('mdl_num', 'NOT PRESENT')}")
        print(f"  transferor_court_id: {transformed_data.get('transferor_court_id', 'NOT PRESENT')}")
        print(f"  transferor_docket_num: {transformed_data.get('transferor_docket_num', 'NOT PRESENT')}")
        print(f"  is_transferred: {transformed_data.get('is_transferred', 'NOT PRESENT')}")
        print(f"  is_removal: {transformed_data.get('is_removal', 'NOT PRESENT')}")
        print(f"  pending_cto: {transformed_data.get('pending_cto', 'NOT PRESENT')}")
        
        # Check if transfer fields were properly set
        print("\n" + "=" * 80)
        print("VERIFICATION RESULTS:")
        print("=" * 80)
        
        success = True
        
        # Check MDL extraction
        if transformed_data.get('mdl_num'):
            print("✅ MDL number extracted: " + str(transformed_data.get('mdl_num')))
        else:
            print("❌ MDL number NOT extracted")
            success = False
        
        # Check transfer parsing
        if transformed_data.get('transferor_court_id'):
            print("✅ Transferor court ID set: " + transformed_data.get('transferor_court_id'))
        else:
            print("❌ Transferor court ID NOT set")
            success = False
            
        if transformed_data.get('transferor_docket_num'):
            print("✅ Transferor docket number set: " + transformed_data.get('transferor_docket_num'))
        else:
            print("❌ Transferor docket number NOT set")
            success = False
        
        # Check transfer flags
        if transformed_data.get('is_transferred') is True:
            print("✅ is_transferred flag set to True")
        else:
            print("❌ is_transferred flag NOT properly set")
            success = False
        
        if success:
            print("\n🎉 ALL TRANSFER FIELDS PROPERLY SET!")
            print("The fix is working correctly!")
        else:
            print("\n⚠️  Some transfer fields are missing.")
            print("Check the logs for more details.")
        
        # Show what would be queried in DynamoDB
        print("\n" + "-" * 80)
        print("DynamoDB Query (would be executed if enabled):")
        print(f"  Court ID: {transformed_data.get('transferor_court_id', 'N/A')}")
        print(f"  Docket Num: {transformed_data.get('transferor_docket_num', 'N/A')}")
        print("  Would inherit: mdl_num, s3_link, attorneys_gpt (if transferor exists)")
        
    except Exception as e:
        print(f"\n❌ Error during transformation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Restore the original file
        if os.path.exists(backup_path):
            with open(backup_path, 'r') as f:
                original_data = json.load(f)
            with open(json_path, 'w') as f:
                json.dump(original_data, f, indent=2)
            os.remove(backup_path)
            print(f"\n✅ Restored original file from backup")


if __name__ == "__main__":
    asyncio.run(test_end_to_end_transfer())