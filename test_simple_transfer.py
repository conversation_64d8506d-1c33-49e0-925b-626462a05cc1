#!/usr/bin/env python3
"""
Simple test of transfer inheritance fix.
"""
import asyncio
import json
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_transfer():
    """Test transfer inheritance."""
    
    # Import here to avoid path issues
    from services.transformer.transfer_handler import TransferHandler
    from infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
    from services.transformer.mdl_processor import MDLProcessor
    from infrastructure.storage.district_court_storage import DistrictCourtStorage
    
    # Test file
    test_file = "data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    
    # Load current data
    with open(test_file, 'r') as f:
        data = json.load(f)
    
    print("=== BEFORE TRANSFER PROCESSING ===")
    print(f"mdl_num: {data.get('mdl_num')}")
    print(f"attorneys_gpt: {len(data.get('attorneys_gpt', []))} attorneys")
    print(f"s3_link: {data.get('s3_link')}")
    print(f"is_transferred: {data.get('is_transferred')}")
    
    # Initialize services
    try:
        pacer_db = AsyncDynamoDBStorage(table_name='Pacer')
        district_court_db = DistrictCourtStorage()
        mdl_processor = MDLProcessor()
        
        transfer_handler = TransferHandler(
            pacer_db=pacer_db,
            district_court_db=district_court_db,
            mdl_processor=mdl_processor
        )
        
        # Process transfers
        await transfer_handler._execute_action({
            'action': 'process_transfers',
            'data': {
                'docket_data': data
            }
        })
        
        print("\n=== AFTER TRANSFER PROCESSING ===")
        print(f"mdl_num: {data.get('mdl_num')} (source: {data.get('mdl_classification_source')})")
        print(f"attorneys_gpt: {len(data.get('attorneys_gpt', []))} attorneys")
        print(f"s3_link: {data.get('s3_link')}")
        print(f"is_transferred: {data.get('is_transferred')}")
        print(f"transferor_court_id: {data.get('transferor_court_id')}")
        print(f"transferor_docket_num: {data.get('transferor_docket_num')}")
        
        # Check law firm updates
        print(f"law_firms: {data.get('law_firms')}")
        print(f"law_firm: {data.get('law_firm')}")
        
        # Save updated data
        with open(test_file, 'w') as f:
            json.dump(data, f, indent=4)
        
        print(f"\n✅ Updated file saved: {test_file}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_transfer())