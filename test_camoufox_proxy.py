#!/usr/bin/env python3
"""
Test script to diagnose Camoufox proxy issues.
Tests browser creation with and without proxy.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from camoufox.async_api import Async<PERSON>amoufox

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_without_proxy():
    """Test Camoufox without proxy."""
    logger.info("🧪 Testing Camoufox WITHOUT proxy")
    
    browser_args = {
        'headless': True,
        'humanize': True,
        'geoip': True,
        'addons': []
    }
    
    try:
        logger.info(f"Browser args: {browser_args}")
        browser = AsyncCamoufox(**browser_args)
        logger.info("AsyncCamoufox instance created, starting...")
        
        await browser.start()
        logger.info("✅ Browser started successfully WITHOUT proxy")
        
        # Create a page and navigate
        context = await browser.browser.new_context()
        page = await context.new_page()
        await page.goto('https://www.facebook.com/ads/library/', wait_until='domcontentloaded')
        logger.info("✅ Successfully navigated to Facebook ads library")
        
        await context.close()
        await browser.stop()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed WITHOUT proxy: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_with_proxy():
    """Test Camoufox with Oxylabs proxy."""
    logger.info("🧪 Testing Camoufox WITH proxy")
    
    # Use the same proxy format as production
    proxy_config = {
        'server': 'http://pr.oxylabs.io:7777',
        'username': 'customer-lexgeniusmob20250612_7jRlZ-cc-us-sessid-1234567890-sesstime-10',
        'password': '24gbfygq=RBhM2+'  # This is from the config
    }
    
    browser_args = {
        'headless': True,
        'humanize': True,
        'geoip': True,
        'addons': [],
        'proxy': proxy_config
    }
    
    try:
        logger.info(f"Browser args (proxy info redacted): headless={browser_args['headless']}, proxy=configured")
        browser = AsyncCamoufox(**browser_args)
        logger.info("AsyncCamoufox instance created, starting...")
        
        await browser.start()
        logger.info("✅ Browser started successfully WITH proxy")
        
        # Create a page and navigate
        context = await browser.browser.new_context()
        page = await context.new_page()
        await page.goto('https://www.facebook.com/ads/library/', wait_until='domcontentloaded')
        logger.info("✅ Successfully navigated to Facebook ads library WITH proxy")
        
        await context.close()
        await browser.stop()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed WITH proxy: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run both tests."""
    logger.info("🚀 Starting Camoufox proxy diagnostics")
    
    # Test without proxy first
    without_proxy_result = await test_without_proxy()
    
    # Wait a bit between tests
    await asyncio.sleep(2)
    
    # Test with proxy
    with_proxy_result = await test_with_proxy()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 TEST SUMMARY:")
    logger.info(f"Without proxy: {'✅ PASSED' if without_proxy_result else '❌ FAILED'}")
    logger.info(f"With proxy: {'✅ PASSED' if with_proxy_result else '❌ FAILED'}")
    logger.info("="*60)
    
    if not with_proxy_result and without_proxy_result:
        logger.info("💡 The issue appears to be proxy-related!")
        logger.info("Consider:")
        logger.info("1. Check proxy credentials")
        logger.info("2. Try a different proxy endpoint")
        logger.info("3. Check if Camoufox has proxy compatibility issues")

if __name__ == "__main__":
    asyncio.run(main())