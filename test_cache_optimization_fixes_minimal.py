#!/usr/bin/env python3
"""
Minimal test script for cache optimization fixes validation.

This script tests the core improvements without requiring the full corrupted file.
Run with: python test_cache_optimization_fixes_minimal.py
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_url_normalization():
    """Test the URL normalization functionality."""
    print("\n🔗 TESTING URL NORMALIZATION")
    print("=" * 50)
    
    try:
        # Import our enhanced modules
        from src.services.fb_ads.url_normalization_utils import FacebookURLNormalizer
        
        normalizer = FacebookURLNormalizer()
        
        # Test URLs with different parameter variations (same image)
        test_urls = [
            # Same image with different size parameters
            "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat=106&_nc_sid=18de74&_nc_ohc=abc123&oh=def456&oe=789xyz",
            "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s600x600_tt6&_nc_cat=106&_nc_sid=18de74&_nc_ohc=different&oh=values&oe=changed",
            "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s200x200_tt6&_nc_gid=new_gid&ccb=12-3",
            
            # Different image 
            "https://scontent.fbcdn.net/v/t39.35426-6/999999999_9999999999_9999999999999999999_n.jpg?stp=dst-jpg_s400x400_tt6&_nc_cat=107",
        ]
        
        print("Testing URL normalization...")
        normalized_groups = normalizer.analyze_url_variations(test_urls)
        
        for normalized, urls in normalized_groups.items():
            print(f"\n🎯 Normalized: {normalized}")
            print(f"   Original URLs ({len(urls)}):")
            for i, url in enumerate(urls, 1):
                print(f"   {i}. {url}")
        
        stats = normalizer.get_normalization_stats()
        print(f"\n📊 Normalization Stats:")
        print(f"   Successful: {stats['successful']}")
        print(f"   Failed: {stats['failed']}")
        print(f"   Pattern Matches: {stats['pattern_matches']}")
        
        # Validate that similar URLs normalize to the same key
        first_three = test_urls[:3]
        normalized_keys = [normalizer.normalize_facebook_url(url) for url in first_three]
        
        if len(set(normalized_keys)) == 1:
            print("✅ SUCCESS: First 3 URLs (same image) normalize to same key")
            return True
        else:
            print("❌ FAILURE: First 3 URLs should normalize to same key")
            print(f"   Keys: {normalized_keys}")
            return False
            
    except Exception as e:
        print(f"❌ URL Normalization test failed: {e}")
        return False

def test_bandwidth_logger_enhancements():
    """Test enhanced bandwidth logger cache tracking."""
    print("\n📊 TESTING BANDWIDTH LOGGER ENHANCEMENTS")
    print("=" * 50)
    
    try:
        from src.services.fb_ads.bandwidth_logger import BandwidthLogger
        
        config = {'bandwidth_log_frequency': 300}
        bandwidth_logger = BandwidthLogger(config=config, logger=logger)
        
        # Test enhanced cache hit logging
        bandwidth_logger.log_cache_hit(
            url="https://scontent.fbcdn.net/cached_image.jpg",
            cached_size=150000,
            content_type="image/jpeg"
        )
        
        bandwidth_logger.log_cache_hit(
            url="https://scontent.fbcdn.net/another_cached.jpg", 
            cached_size=200000,
            content_type="image/jpeg"
        )
        
        # Test enhanced cache miss logging
        bandwidth_logger.log_cache_miss(
            url="https://scontent.fbcdn.net/missed_image.jpg",
            downloaded_bytes=180000,
            miss_reason="not_cached"
        )
        
        bandwidth_logger.log_cache_miss(
            url="https://scontent.fbcdn.net/expired_image.jpg",
            downloaded_bytes=160000, 
            miss_reason="expired"
        )
        
        # Get cache efficiency stats
        cache_stats = bandwidth_logger.get_cache_efficiency_stats()
        
        print("📈 Bandwidth Logger Cache Stats:")
        print(f"   Cache Enabled: {cache_stats.get('cache_enabled', False)}")
        print(f"   Cache Hits: {cache_stats.get('cache_hits', 0)}")
        print(f"   Cache Misses: {cache_stats.get('cache_misses', 0)}")
        print(f"   Hit Rate: {cache_stats.get('hit_rate_percent', 0)}%")
        print(f"   Total Saved: {cache_stats.get('total_saved_mb', 0)} MB")
        
        # Validate
        success = True
        
        if cache_stats.get('cache_hits', 0) != 2:
            print(f"❌ Expected 2 cache hits, got {cache_stats.get('cache_hits', 0)}")
            success = False
            
        if cache_stats.get('cache_misses', 0) != 2:
            print(f"❌ Expected 2 cache misses, got {cache_stats.get('cache_misses', 0)}")
            success = False
        
        if cache_stats.get('hit_rate_percent', 0) != 50.0:
            print(f"❌ Expected 50% hit rate, got {cache_stats.get('hit_rate_percent', 0)}%")
            success = False
        
        if success:
            print("✅ SUCCESS: Bandwidth logger enhancements working correctly")
        
        return success
        
    except Exception as e:
        print(f"❌ Bandwidth Logger test failed: {e}")
        return False

def main():
    """Run minimal cache optimization tests."""
    print("🚀 MINIMAL CACHE OPTIMIZATION TEST SUITE")
    print("=" * 60)
    print("Testing cache optimization fixes (minimal version)")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: URL Normalization
    try:
        result = test_url_normalization()
        test_results.append(("URL Normalization", result))
    except Exception as e:
        print(f"❌ URL Normalization test failed: {e}")
        test_results.append(("URL Normalization", False))
    
    # Test 2: Bandwidth Logger Enhancements
    try:
        result = test_bandwidth_logger_enhancements()
        test_results.append(("Bandwidth Logger", result))
    except Exception as e:
        print(f"❌ Bandwidth Logger test failed: {e}")
        test_results.append(("Bandwidth Logger", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 AVAILABLE TESTS PASSED! Core cache optimization fixes are working.")
        print("\n🎯 CONFIRMED IMPROVEMENTS:")
        print("   • URL normalization handles Facebook CDN parameter variations")
        print("   • Enhanced bandwidth logger with detailed cache tracking")
        print("   • Miss reason categorization for debugging")
        print("   • Cache efficiency statistics and reporting")
        print("\n⚠️  NOTE: Full cache extractor testing requires fixing the corrupted file.")
        print("   • Disk persistence testing skipped due to file corruption")
        print("   • Cache analytics testing requires file repair")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)