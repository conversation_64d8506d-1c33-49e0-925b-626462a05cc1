#!/usr/bin/env python3
"""
Test script to validate the simplified SharedBrowserManager.

This test validates the simplified SharedBrowserManager that removes the over-engineered
complexity that was actually causing the 'dict' object has no attribute 'is_set' error.

The simplification includes:
1. Removed complex per-operation lock creation
2. Removed event loop tracking and validation
3. Removed circuit breaker complexity
4. Simple persistent asyncio locks
5. Simplified browser creation process

Usage:
    python test_asynccamoufox_corruption_fix.py
"""

import asyncio
import logging
import sys
import threading
import time
from typing import Dict, Any
from unittest.mock import MagicMock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_asynccamoufox_corruption_protection():
    """
    Test that AsyncCamoufox corruption protection works correctly.
    """
    logger.info("🎯 Testing AsyncCamoufox Internal Lock Corruption Protection")
    
    try:
        # Import the fixed SharedBrowserManager
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Configuration for testing
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        logger.info("📋 Phase 1: Testing AsyncCamoufox creation in single event loop")
        
        # Get shared browser manager instance
        browser_manager = get_shared_browser_manager()
        
        # Test creating browser context - this triggers AsyncCamoufox.start()
        job_id = "asynccamoufox_test_job"
        
        try:
            context, page = await browser_manager.get_browser_context(job_id, test_config)
            logger.info("✅ Phase 1 PASSED: AsyncCamoufox creation successful in single event loop")
            
            # Test basic operation
            await page.goto('about:blank')
            title = await page.title()
            logger.info(f"📄 Page title: '{title}'")
            
            # Clean up
            await browser_manager.cleanup_job_context(job_id)
            await browser_manager.cleanup_all()
            
        except Exception as e:
            if ("'dict' object has no attribute" in str(e) or "is_set" in str(e)):
                logger.error(f"❌ Phase 1 FAILED: AsyncCamoufox lock corruption still occurring: {e}")
                return False
            else:
                logger.error(f"❌ Phase 1 FAILED: Other error: {e}")
                return False
        
        logger.info("📋 Phase 2: Testing AsyncCamoufox recreation across event loops")
        
        # Test in same event loop first
        browser_manager = get_shared_browser_manager()
        
        try:
            context, page = await browser_manager.get_browser_context(job_id + "_2", test_config)
            logger.info("✅ Phase 2a PASSED: AsyncCamoufox recreation in same event loop")
            
            await browser_manager.cleanup_job_context(job_id + "_2")
            await browser_manager.cleanup_all()
            
        except Exception as e:
            if ("'dict' object has no attribute" in str(e) or "is_set" in str(e)):
                logger.error(f"❌ Phase 2a FAILED: AsyncCamoufox lock corruption in same loop: {e}")
                return False
            else:
                logger.error(f"❌ Phase 2a FAILED: Other error: {e}")
                return False
        
        logger.info("✅ All phases passed - AsyncCamoufox corruption protection working!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Cannot import required modules: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def run_in_new_event_loop(coro):
    """Helper function to run coroutine in a completely new event loop."""
    def thread_target():
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        try:
            return new_loop.run_until_complete(coro)
        finally:
            new_loop.close()
    
    result_container = {}
    exception_container = {}
    
    def wrapper():
        try:
            result_container['result'] = thread_target()
        except Exception as e:
            exception_container['exception'] = e
    
    thread = threading.Thread(target=wrapper)
    thread.start()
    thread.join()
    
    if 'exception' in exception_container:
        raise exception_container['exception']
    
    return result_container.get('result')

async def test_multi_event_loop_asynccamoufox():
    """
    Test AsyncCamoufox corruption protection across multiple event loops.
    This is where the original corruption typically occurred.
    """
    logger.info("🎯 Testing AsyncCamoufox Across Multiple Event Loops")
    
    # Phase 1: Test in first event loop
    logger.info("📋 Phase 1: Testing in Event Loop A")
    result1 = await test_asynccamoufox_corruption_protection()
    
    if not result1:
        logger.error("❌ Phase 1 failed in Event Loop A")
        return False
    
    logger.info("✅ Phase 1 passed in Event Loop A")
    
    # Phase 2: Test in completely new event loop
    logger.info("📋 Phase 2: Testing in Event Loop B (new event loop)")
    
    try:
        result2 = run_in_new_event_loop(test_asynccamoufox_corruption_protection())
        
        if result2:
            logger.info("✅ Phase 2 passed in Event Loop B")
            logger.info("🎉 MULTI-EVENT-LOOP ASYNCCAMOUFOX TEST PASSED!")
            logger.info("✅ AsyncCamoufox corruption protection working across event loops")
            return True
        else:
            logger.error("❌ Phase 2 failed in Event Loop B")
            return False
            
    except Exception as e:
        if ("'dict' object has no attribute" in str(e) or "is_set" in str(e)):
            logger.error(f"❌ Phase 2 CRITICAL: AsyncCamoufox lock corruption still occurring: {e}")
        else:
            logger.error(f"❌ Phase 2 failed with error: {e}")
        return False

async def test_simplified_browser_management():
    """
    Test that the simplified browser management works correctly.
    """
    logger.info("🎯 Testing Simplified Browser Management")
    
    try:
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Get shared browser manager instance
        browser_manager = get_shared_browser_manager()
        
        # Test that the simplified manager has the expected simple structure
        if hasattr(browser_manager, '_browser_lock') and hasattr(browser_manager, '_context_lock'):
            logger.info("✅ Simple lock management is present")
        else:
            logger.error("❌ Simple lock management missing")
            return False
        
        # Test that complex circuit breaker functionality has been removed
        complex_attributes = [
            '_circuit_breaker_open_time', '_max_consecutive_failures', '_circuit_breaker_timeout',
            '_browser_loop_id', '_consecutive_failures', '_max_browser_creation_attempts'
        ]
        
        found_complex_attrs = []
        for attr in complex_attributes:
            if hasattr(browser_manager, attr):
                found_complex_attrs.append(attr)
        
        if found_complex_attrs:
            logger.error(f"❌ Complex attributes still present: {found_complex_attrs}")
            return False
        else:
            logger.info("✅ Complex circuit breaker and event loop tracking removed")
        
        # Test basic functionality
        stats = browser_manager.get_stats()
        if 'browser_active' in stats and 'browser_healthy' in stats:
            logger.info("✅ Basic statistics functionality working")
            return True
        else:
            logger.error("❌ Basic statistics functionality broken")
            return False
            
    except Exception as e:
        logger.error(f"❌ Simplified browser management test failed: {e}")
        return False

async def main():
    """Main test runner for AsyncCamoufox corruption fix validation."""
    logger.info("🎯 Starting AsyncCamoufox Corruption Fix Validation")
    logger.info("=" * 80)
    
    test_results = []
    
    # Test 1: Single Event Loop AsyncCamoufox Protection
    logger.info("\n" + "=" * 60)
    logger.info("TEST 1: Single Event Loop AsyncCamoufox Protection")
    logger.info("=" * 60)
    
    test1_passed = await test_asynccamoufox_corruption_protection()
    test_results.append(("Single Event Loop Protection", test1_passed))
    
    # Test 2: Multi Event Loop AsyncCamoufox Protection  
    logger.info("\n" + "=" * 60)
    logger.info("TEST 2: Multi Event Loop AsyncCamoufox Protection")
    logger.info("=" * 60)
    
    test2_passed = await test_multi_event_loop_asynccamoufox()
    test_results.append(("Multi Event Loop Protection", test2_passed))
    
    # Test 3: Simplified Browser Management
    logger.info("\n" + "=" * 60)
    logger.info("TEST 3: Simplified Browser Management")
    logger.info("=" * 60)
    
    test3_passed = await test_simplified_browser_management()
    test_results.append(("Simplified Browser Management", test3_passed))
    
    # Final Results
    logger.info("\n" + "=" * 80)
    logger.info("FINAL ASYNCCAMOUFOX CORRUPTION FIX RESULTS")
    logger.info("=" * 80)
    
    passed_count = 0
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if passed:
            passed_count += 1
    
    logger.info(f"\nOverall: {passed_count}/{len(test_results)} tests passed")
    
    if passed_count == len(test_results):
        logger.info("🎉 ALL TESTS PASSED - Simplified SharedBrowserManager Working!")
        logger.info("✅ Removed over-engineered lock management")
        logger.info("✅ Simplified browser creation process")
        logger.info("✅ Removed complex event loop tracking")
        logger.info("✅ Removed circuit breaker complexity")
        logger.info("✅ Simple asyncio locks working correctly")
        logger.info("✅ Production FB ads processing should now work reliably")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Simplified SharedBrowserManager needs additional work")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)