#!/usr/bin/env python3
"""
GMass API Client Usage Examples

Examples demonstrating how to use the GMass API client library.
"""

import asyncio
import logging
from datetime import datetime
from typing import List

# Configure logging for examples
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import GMass client components
from src.services.gmass import (
    GMassClient,
    GMassConfig,
    create_gmass_client,
    GMassAPIError,
    GMassAuthenticationError,
    GMassRateLimitError,
)


async def example_basic_campaign_workflow():
    """Example: Complete campaign workflow from sheet to sending."""
    
    config = GMassConfig(
        api_key="your-api-key-here",
        rate_limit_per_minute=30,  # Adjust based on your plan
        enable_caching=True,
    )
    
    async with GMassClient(config, logger) as client:
        try:
            # 1. List available Google Sheets
            sheets_response = await client.sheets.list_sheets()
            if not sheets_response.sheets:
                logger.error("No Google Sheets available")
                return
            
            target_sheet = sheets_response.sheets[0]
            logger.info(f"Using sheet: {target_sheet.name}")
            
            # 2. Get worksheets from the sheet
            worksheets_response = await client.sheets.list_worksheets(target_sheet.sheet_id)
            if not worksheets_response.worksheets:
                logger.error("No worksheets found in sheet")
                return
            
            main_worksheet = worksheets_response.worksheets[0]
            logger.info(f"Using worksheet: {main_worksheet.name}")
            
            # 3. Create GMass list from sheet
            list_response = await client.sheets.create_list_from_sheet(
                sheet_id=target_sheet.sheet_id,
                worksheet_id=main_worksheet.worksheet_id,
                email_column="A",
                name_column="B",
                header_row=1,
                start_row=2,
                list_name="My Newsletter List",
            )
            
            logger.info(
                f"Created list: {list_response.address} "
                f"with {list_response.total_recipients} recipients"
            )
            
            # 4. Create campaign draft
            draft_response = await client.campaign_drafts.create_draft(
                subject="Welcome to Our Newsletter!",
                body="Hi {Name},\n\nWelcome to our newsletter! We're excited to have you aboard.\n\nBest regards,\nThe Team",
                recipients_list_address=list_response.address,
            )
            
            logger.info(f"Created draft: {draft_response.gmail_draft_id}")
            
            # 5. Send campaign
            campaign_response = await client.campaigns.send_campaign(
                gmail_draft_id=draft_response.gmail_draft_id
            )
            
            logger.info(
                f"Campaign sent: {campaign_response.campaign_id} "
                f"to {campaign_response.total_recipients} recipients"
            )
            
            # 6. Monitor campaign performance (wait a bit for some data)
            await asyncio.sleep(60)  # Wait 1 minute
            
            stats_response = await client.campaigns.get_campaign_statistics(
                campaign_response.campaign_id
            )
            
            logger.info(
                f"Campaign stats: {stats_response.opens} opens, "
                f"{stats_response.clicks} clicks, "
                f"Open rate: {stats_response.open_rate}%"
            )
            
        except GMassAPIError as e:
            logger.error(f"GMass API error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")


async def example_transactional_email():
    """Example: Send transactional emails with error handling."""
    
    # Use the convenience function
    async with create_gmass_client("your-api-key-here", logger) as client:
        try:
            # Send welcome email
            response = await client.transactional.send_email(
                to="<EMAIL>",
                subject="Welcome to Our Platform",
                body="Welcome! Your account has been created successfully.",
                use_custom_smtp=True,
            )
            
            logger.info(f"Welcome email sent: {response.email_id}")
            
            # Send password reset email
            reset_response = await client.transactional.send_email(
                to="<EMAIL>",
                subject="Password Reset Request",
                body="Click here to reset your password: https://example.com/reset?token=abc123",
                custom_headers={"X-Priority": "1"},
            )
            
            logger.info(f"Reset email sent: {reset_response.email_id}")
            
            # Check email status
            status = await client.transactional.get_email_status(response.email_id)
            logger.info(f"Email status: {status.status}")
            
        except GMassRateLimitError as e:
            logger.warning(f"Rate limited: {e}. Waiting {e.retry_after} seconds...")
            if e.retry_after:
                await asyncio.sleep(e.retry_after)
                # Retry logic here
        except GMassAuthenticationError as e:
            logger.error(f"Authentication failed: {e}")
        except GMassAPIError as e:
            logger.error(f"API error: {e}")


async def example_campaign_management():
    """Example: Advanced campaign management operations."""
    
    config = GMassConfig(
        api_key="your-api-key-here",
        base_url="https://api.gmass.co/api",
        timeout=45,
        max_retries=5,
        rate_limit_per_minute=60,
    )
    
    async with GMassClient(config, logger) as client:
        try:
            # List recent campaigns
            recent_campaigns = await client.campaigns.list_campaigns(
                limit=10,
                status="sent",
                start_date=datetime(2024, 1, 1),
            )
            
            logger.info(f"Found {len(recent_campaigns)} recent campaigns")
            
            for campaign in recent_campaigns:
                logger.info(
                    f"Campaign: {campaign.subject[:30]}... "
                    f"Recipients: {campaign.total_recipients}"
                )
                
                # Get detailed statistics
                stats = await client.campaigns.get_campaign_statistics(campaign.campaign_id)
                logger.info(
                    f"  Stats: {stats.opens} opens ({stats.open_rate}%), "
                    f"{stats.clicks} clicks ({stats.click_rate}%)"
                )
                
                # Get campaign events
                opens = await client.reports.get_opens(campaign.campaign_id, limit=5)
                logger.info(f"  Recent opens: {len(opens.opens)}")
                
                clicks = await client.reports.get_clicks(campaign.campaign_id, limit=5)
                logger.info(f"  Recent clicks: {len(clicks.clicks)}")
            
        except Exception as e:
            logger.error(f"Error in campaign management: {e}")


async def example_error_handling_and_retries():
    """Example: Comprehensive error handling and retry strategies."""
    
    config = GMassConfig(
        api_key="your-api-key-here",
        max_retries=5,
        retry_backoff_factor=2.0,
    )
    
    async with GMassClient(config, logger) as client:
        max_attempts = 3
        
        for attempt in range(max_attempts):
            try:
                # Attempt to send transactional email
                response = await client.transactional.send_email(
                    to="<EMAIL>",
                    subject="Test Email",
                    body="This is a test email.",
                )
                
                logger.info(f"Email sent successfully: {response.email_id}")
                break
                
            except GMassRateLimitError as e:
                logger.warning(f"Rate limited on attempt {attempt + 1}")
                if e.retry_after:
                    await asyncio.sleep(e.retry_after)
                else:
                    await asyncio.sleep(60)  # Default 1 minute wait
                
                if attempt == max_attempts - 1:
                    logger.error("Max retry attempts reached")
                    raise
                    
            except GMassAuthenticationError as e:
                logger.error(f"Authentication error: {e}")
                # Don't retry authentication errors
                raise
                
            except GMassAPIError as e:
                logger.warning(f"API error on attempt {attempt + 1}: {e}")
                if attempt == max_attempts - 1:
                    logger.error("Max retry attempts reached")
                    raise
                
                # Exponential backoff
                wait_time = (2 ** attempt) * 1.0
                await asyncio.sleep(wait_time)


async def example_batch_operations():
    """Example: Efficient batch operations with proper rate limiting."""
    
    async with create_gmass_client("your-api-key-here", logger) as client:
        try:
            # Get client statistics
            stats = await client.get_client_stats()
            logger.info(f"Client stats: {stats}")
            
            # Perform health check
            health = await client.health_check()
            logger.info(f"Health check: {health['status']}")
            
            # List all campaigns in batches
            all_campaigns = []
            offset = 0
            batch_size = 50
            
            while True:
                batch = await client.campaigns.list_campaigns(
                    limit=batch_size,
                    offset=offset,
                )
                
                if not batch:
                    break
                
                all_campaigns.extend(batch)
                offset += batch_size
                
                logger.info(f"Loaded {len(all_campaigns)} campaigns so far...")
                
                # Rate limiting is handled automatically by the client
                # But we can add additional delays if needed
                await asyncio.sleep(0.5)
            
            logger.info(f"Total campaigns loaded: {len(all_campaigns)}")
            
            # Process campaigns in parallel (with rate limiting)
            async def process_campaign(campaign):
                try:
                    stats = await client.campaigns.get_campaign_statistics(campaign.campaign_id)
                    return {
                        "campaign_id": campaign.campaign_id,
                        "subject": campaign.subject,
                        "opens": stats.opens,
                        "clicks": stats.clicks,
                    }
                except Exception as e:
                    logger.warning(f"Failed to get stats for {campaign.campaign_id}: {e}")
                    return None
            
            # Process in smaller batches to respect rate limits
            batch_size = 5
            results = []
            
            for i in range(0, len(all_campaigns), batch_size):
                batch = all_campaigns[i:i + batch_size]
                batch_results = await asyncio.gather(
                    *[process_campaign(campaign) for campaign in batch],
                    return_exceptions=True
                )
                
                results.extend([r for r in batch_results if r is not None])
                logger.info(f"Processed batch {i // batch_size + 1}")
                
                # Small delay between batches
                await asyncio.sleep(1.0)
            
            logger.info(f"Successfully processed {len(results)} campaigns")
            
        except Exception as e:
            logger.error(f"Error in batch operations: {e}")


async def main():
    """Run all examples."""
    
    logger.info("Starting GMass API client examples...")
    
    examples = [
        ("Basic Campaign Workflow", example_basic_campaign_workflow),
        ("Transactional Email", example_transactional_email),
        ("Campaign Management", example_campaign_management),
        ("Error Handling", example_error_handling_and_retries),
        ("Batch Operations", example_batch_operations),
    ]
    
    for name, example_func in examples:
        logger.info(f"\n{'=' * 50}")
        logger.info(f"Running example: {name}")
        logger.info(f"{'=' * 50}")
        
        try:
            await example_func()
            logger.info(f"✅ {name} completed successfully")
        except Exception as e:
            logger.error(f"❌ {name} failed: {e}")
        
        # Wait between examples
        await asyncio.sleep(2)
    
    logger.info("\n🎉 All examples completed!")


if __name__ == "__main__":
    # Run examples
    asyncio.run(main())