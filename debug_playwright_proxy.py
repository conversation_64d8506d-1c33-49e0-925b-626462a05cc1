#!/usr/bin/env python3
"""
Debug Playwright proxy connectivity issue
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scripts.analysis.talc.oxylabs_proxy_helper import OxylabsProxyHelper
from playwright.async_api import async_playwright

async def test_playwright_proxy():
    """Test Playwright with different proxy configurations."""
    
    # Initialize helper
    helper = OxylabsProxyHelper(mobile_proxy=True)
    
    # Generate a single proxy
    proxy_list = helper.generate_proxy_list(1)
    
    if not proxy_list:
        print("❌ Failed to generate proxy - check credentials")
        return
        
    original_proxy = proxy_list[0]
    print(f"Testing proxy: {original_proxy}")
    
    # Test with different browser engines
    engines = ['chromium', 'firefox', 'webkit']
    
    for engine in engines:
        print(f"\n🔍 Testing with {engine.upper()} engine:")
        
        playwright = None
        browser = None
        
        try:
            playwright = await async_playwright().start()
            browser_launcher = getattr(playwright, engine)
            
            # Basic launch options
            launch_options = {
                'headless': True,
                'proxy': {'server': original_proxy},
                'args': [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',
                    '--exclude-switches=enable-automation',
                    '--disable-web-security',
                    '--ignore-ssl-errors=true',
                    '--ignore-certificate-errors=true',
                    '--ignore-certificate-errors-spki-list',
                    '--ignore-ssl-errors-list',
                    '--disable-features=TranslateUI',
                ]
            }
            
            print(f"  Launching {engine} browser...")
            browser = await browser_launcher.launch(**launch_options)
            
            context = await browser.new_context(
                ignore_https_errors=True,
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = await context.new_page()
            
            print(f"  Testing connection to httpbin.org...")
            
            # Test with a simple HTTP request first
            try:
                response = await page.goto('https://httpbin.org/ip', timeout=30000, wait_until='domcontentloaded')
                
                if response and response.status == 200:
                    content = await page.content()
                    print(f"  ✅ {engine} SUCCESS - Response: {response.status}")
                    
                    # Try to extract IP from response
                    if 'origin' in content:
                        print(f"  📍 Proxy IP detected in response")
                    else:
                        print(f"  ⚠️  No IP info in response")
                else:
                    print(f"  ❌ {engine} FAILED - Status: {response.status if response else 'None'}")
                    
            except Exception as e:
                error_msg = str(e)
                print(f"  ❌ {engine} ERROR: {error_msg}")
                
                # Check for specific error patterns
                if 'kCFErrorDomainCFNetwork error 310' in error_msg:
                    print(f"  🔍 CFNetwork error 310 detected - proxy connection failure")
                elif 'timeout' in error_msg.lower():
                    print(f"  🔍 Timeout error - proxy might be slow")
                elif 'net::' in error_msg:
                    print(f"  🔍 Network error - connectivity issue")
                    
            finally:
                if context:
                    await context.close()
                
        except Exception as e:
            print(f"  ❌ {engine} LAUNCH ERROR: {e}")
            
        finally:
            if browser:
                await browser.close()
            if playwright:
                await playwright.stop()
    
    print(f"\n🔍 Testing alternative proxy formats:")
    
    # Test with HTTPS proxy format
    https_proxy = original_proxy.replace('http://', 'https://')
    print(f"Testing HTTPS proxy: {https_proxy}")
    
    try:
        playwright = await async_playwright().start()
        browser_launcher = playwright.chromium
        
        launch_options = {
            'headless': True,
            'proxy': {'server': https_proxy},
            'args': ['--no-sandbox', '--disable-setuid-sandbox']
        }
        
        browser = await browser_launcher.launch(**launch_options)
        context = await browser.new_context(ignore_https_errors=True)
        page = await context.new_page()
        
        response = await page.goto('https://httpbin.org/ip', timeout=15000)
        
        if response and response.status == 200:
            print("  ✅ HTTPS proxy format works!")
        else:
            print(f"  ❌ HTTPS proxy failed - Status: {response.status if response else 'None'}")
            
    except Exception as e:
        print(f"  ❌ HTTPS proxy error: {e}")
        
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()

if __name__ == "__main__":
    asyncio.run(test_playwright_proxy())