#!/usr/bin/env python3
"""
Test script for interactive AFFF law firm prompting functionality

This script will test the new interactive features by simulating unknown attorneys
and verifying that:
1. User is prompted for law firm name
2. All items are updated when law firm is provided
3. JSON file is updated without backup
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from afff_case_shell_processor import AFFFCaseShellProcessor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_interactive_functionality():
    """Test the interactive law firm prompting functionality"""
    
    # Create test data with unknown attorneys
    test_html = """
    <html>
    <body>
    <table>
    <tr><td width="94" valign="top" nowrap="">07/16/2025</td><td valign="top"><a href="https://example.com/doc1">5001</a></td><td valign="top">COMPLAINT Test Attorney 1 vs 3M Company, et al. Charleston Division. (Filing fee $ 405)</td></tr>
    <tr><td width="94" valign="top" nowrap="">07/16/2025</td><td valign="top"><a href="https://example.com/doc2">5002</a></td><td valign="top">COMPLAINT Test Attorney 2 vs 3M Company, et al. Charleston Division. (Filing fee $ 405)</td></tr>
    <tr><td width="94" valign="top" nowrap="">07/16/2025</td><td valign="top"><a href="https://example.com/doc3">5003</a></td><td valign="top">COMPLAINT Test Attorney 1 vs 3M Company, et al. Charleston Division. (Filing fee $ 405)</td></tr>
    </table>
    </body>
    </html>
    """
    
    # Create processor instance
    processor = AFFFCaseShellProcessor("20250716", force=False)
    
    # Set up mock responses for user prompts
    # Test Attorney 1 -> "Test Law Firm"
    # Test Attorney 2 -> hit Enter (stays Unknown)
    mock_prompt_responses = {
        "Test Attorney 1": "Test Law Firm",
        "Test Attorney 2": "Unknown"
    }
    
    def mock_prompt_for_law_firm(attorney_name):
        """Mock the _prompt_for_law_firm method"""
        response = mock_prompt_responses.get(attorney_name, "Unknown")
        print(f"🔍 Mock prompt for {attorney_name}: {response}")
        return response
    
    # Replace the prompt method with our mock
    processor._prompt_for_law_firm = mock_prompt_for_law_firm
    
    # Test the HTML parsing and processing
    print("Testing HTML parsing...")
    parsed_data = processor._parse_html_table(test_html)
    print(f"Parsed {len(parsed_data)} entries")
    
    # Test the processing logic
    print("\nTesting attorney processing...")
    processed_data = []
    processor.session_attorney_updates = {}
    
    # Mock the parse_docket_text function
    def mock_parse_docket_text(docket_text):
        """Mock parse_docket_text to extract test attorney names"""
        if "Test Attorney 1" in docket_text:
            return {
                'attorney': 'Test Attorney 1',
                'plaintiff': 'Test Plaintiff 1',
                'defendant': '3M Company',
                'case_number': '2:25-cv-00001'
            }
        elif "Test Attorney 2" in docket_text:
            return {
                'attorney': 'Test Attorney 2',
                'plaintiff': 'Test Plaintiff 2',
                'defendant': '3M Company',
                'case_number': '2:25-cv-00002'
            }
        return {
            'attorney': None,
            'plaintiff': None,
            'defendant': None,
            'case_number': None
        }
    
    # Replace the import
    import scripts.analysis.AFFF.generate_afff_mdl_report as afff_report
    afff_report.parse_docket_text = mock_parse_docket_text
    
    # Process each entry
    for entry in parsed_data:
        parsed = mock_parse_docket_text(entry['docket_text'])
        
        # Look up law firm for attorney
        law_firm = "Unknown"
        if parsed['attorney']:
            attorney_lower = parsed['attorney'].lower()
            
            # Check if we already updated this attorney in this session
            if parsed['attorney'] in processor.session_attorney_updates:
                law_firm = processor.session_attorney_updates[parsed['attorney']]
                print(f"✅ SESSION UPDATE: '{parsed['attorney']}' -> '{law_firm}'")
            elif attorney_lower in processor.attorney_lookup_map:
                # Found in lookup (case-insensitive)
                actual_name = processor.attorney_lookup_map[attorney_lower]
                law_firm = processor.attorney_lookup[actual_name].get('law_firm', 'Unknown')
                print(f"✅ MATCH FOUND: '{parsed['attorney']}' -> '{law_firm}'")
            else:
                # New attorney not in lookup - prompt user
                print(f"❌ NO MATCH: '{parsed['attorney']}' not found in lookup")
                
                # Interactive prompt for law firm
                law_firm = processor._prompt_for_law_firm(parsed['attorney'])
                
                # If user provided a law firm, update session tracking
                if law_firm and law_firm != "Unknown":
                    processor.session_attorney_updates[parsed['attorney']] = law_firm
                    print(f"✅ UPDATED: '{parsed['attorney']}' -> '{law_firm}'")
        
        # Create processed entry
        processed_entry = {
            'date': entry['date'],
            'doc_num': entry['doc_num'],
            'attorney': parsed['attorney'],
            'law_firm': law_firm,
            'versus': f"{parsed['plaintiff']} v. {parsed['defendant']}"
        }
        processed_data.append(processed_entry)
    
    # Apply session updates
    processor._apply_session_updates_to_processed_data(processed_data)
    
    # Display results
    print(f"\n📊 Final Results:")
    print(f"Session attorney updates: {processor.session_attorney_updates}")
    
    for entry in processed_data:
        print(f"  {entry['attorney']} -> {entry['law_firm']}")
    
    # Verify expected behavior
    print(f"\n🔍 Verification:")
    
    # Test Attorney 1 should be "Test Law Firm" for all entries
    attorney_1_entries = [e for e in processed_data if e['attorney'] == 'Test Attorney 1']
    print(f"Test Attorney 1 entries: {len(attorney_1_entries)}")
    for entry in attorney_1_entries:
        expected = "Test Law Firm"
        actual = entry['law_firm']
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {entry['attorney']}: {actual} (expected: {expected})")
    
    # Test Attorney 2 should be "Unknown"
    attorney_2_entries = [e for e in processed_data if e['attorney'] == 'Test Attorney 2']
    print(f"Test Attorney 2 entries: {len(attorney_2_entries)}")
    for entry in attorney_2_entries:
        expected = "Unknown"
        actual = entry['law_firm']
        status = "✅" if actual == expected else "❌"
        print(f"  {status} {entry['attorney']}: {actual} (expected: {expected})")
    
    print(f"\n🎉 Test completed!")

if __name__ == "__main__":
    test_interactive_functionality()