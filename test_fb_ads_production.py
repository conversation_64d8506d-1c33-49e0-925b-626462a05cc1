#!/usr/bin/env python3
"""
Test Facebook ads processing with actual firms from database using Camoufox.
Tests complete data flow from capture to DynamoDB.
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_production_flow():
    """Test processing actual firms to verify complete data flow."""
    logger.info("🧪 Testing Facebook ads processing with ACTUAL firms from database")
    
    try:
        import subprocess
        
        logger.info("🚀 Starting Facebook ads workflow with PRODUCTION CONFIG")
        
        # Run the main pipeline with the production test config
        cmd = [
            sys.executable, 
            "src/main.py", 
            "--params", 
            "config/fb_ads_test_production.yml"
        ]
        
        logger.info(f"Running command: {' '.join(cmd)}")
        
        # Run the process and capture output
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd=os.getcwd()
        )
        
        # Stream output in real-time
        async for line in process.stdout:
            line_str = line.decode().strip()
            if line_str:
                print(line_str)
        
        # Wait for completion
        await process.wait()
        
        if process.returncode == 0:
            logger.info("✅ Facebook ads workflow completed successfully")
            return True
        else:
            logger.error(f"❌ Facebook ads workflow failed with exit code {process.returncode}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Run the production test."""
    success = await test_production_flow()
    
    if success:
        logger.info("\n✅ PRODUCTION TEST PASSED: Complete data flow with Camoufox worked!")
    else:
        logger.error("\n❌ PRODUCTION TEST FAILED: Check logs for details")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)