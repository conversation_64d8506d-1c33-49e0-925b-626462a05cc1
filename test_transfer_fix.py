#!/usr/bin/env python3
"""
Test script to verify transfer handler fix for inheriting fields from transferor dockets.
Tests the specific case: scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json
"""
import asyncio
import json
import logging
import os
import sys
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config_models.base import WorkflowConfig
from src.services.transformer.data_transformer import DataTransformer
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository


async def check_transferor_in_db():
    """Check if the transferor case exists in DynamoDB."""
    storage = AsyncDynamoDBStorage(table_name='Pacer', region_name='us-west-2')
    repo = PacerRepository(storage)
    
    # Query for the transferor case
    transferor_court_id = 'mied'  # Michigan Eastern
    transferor_docket_num = '1:25-cv-11890'
    
    print(f'\n📊 Checking DynamoDB for transferor case: {transferor_court_id}:{transferor_docket_num}')
    
    try:
        results = await repo.query_by_court_and_docket(transferor_court_id, transferor_docket_num)
        
        if results and len(results) > 0:
            print(f'✅ Found {len(results)} records for transferor case')
            item = results[0]
            print(f'  mdl_num: {item.get("mdl_num")}')
            print(f'  s3_link: {item.get("s3_link")}')
            print(f'  attorneys_gpt: {item.get("attorneys_gpt")}')
            print(f'  law_firm: {item.get("law_firm")}')
            print(f'  law_firms: {item.get("law_firms")}')
            return True
        else:
            print('❌ No transferor case found in DynamoDB')
            print('   The transferor case needs to be scraped first for inheritance to work')
            return False
    finally:
        await storage.close()


async def test_single_file_transformation():
    """Test transformation of the specific file."""
    json_path = 'data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json'
    
    if not os.path.exists(json_path):
        print(f"❌ Test file not found: {json_path}")
        return
    
    # Load original data
    print(f"\n📄 Testing file: {json_path}")
    with open(json_path, 'r') as f:
        original_data = json.load(f)
    
    print("\n🔍 Original file state:")
    print(f"  case_in_other_court: {original_data.get('case_in_other_court')}")
    print(f"  lead_case: {original_data.get('lead_case')}")
    print(f"  mdl_num: {original_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  attorneys_gpt: {original_data.get('attorneys_gpt', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {original_data.get('transferor_court_id', 'NOT PRESENT')}")
    
    # Check if transferor exists in DB
    transferor_exists = await check_transferor_in_db()
    
    # Setup configuration
    config = {
        'iso_date': '20250716',
        'directories': {
            'base_dir': os.path.dirname(__file__),
            'data_dir': 'data',
            'log_dir': 'logs'
        },
        'files': {
            'mdl_lookup': 'src/config/mdl/mdl_lookup.json'
        },
        's3_bucket_name': os.environ.get('S3_BUCKET_NAME', 'lexgenius'),
        'upload': False,  # Don't upload during test
        'reprocess_files': True,  # Force reprocessing
        'llm_provider': 'deepseek'
    }
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    print("\n🚀 Running transformation with transfer handler fix...")
    
    try:
        # Create transformer
        workflow_config = WorkflowConfig(**config)
        transformer = DataTransformer(
            config=config,
            logger=logger,
            openai_client=None,
            deepseek_service=None,
            mistral_service=None,
            shutdown_event=None
        )
        
        # Initialize the transformer
        await transformer.async_init()
        
        # Process just this one file
        from src.services.transformer.data_transformer import TransformationJob
        job = TransformationJob(
            json_path=json_path,
            force_reprocess=True,
            transformer=transformer
        )
        
        # Run the transformation
        result = await transformer._process_single_job(job)
        
        if result and job.working_data:
            print("\n✅ Transformation completed!")
            print("\n🔍 After transformation:")
            print(f"  mdl_num: {job.working_data.get('mdl_num', 'NOT PRESENT')}")
            print(f"  attorneys_gpt: {job.working_data.get('attorneys_gpt', 'NOT PRESENT')}")
            print(f"  transferor_court_id: {job.working_data.get('transferor_court_id', 'NOT PRESENT')}")
            print(f"  transferor_docket_num: {job.working_data.get('transferor_docket_num', 'NOT PRESENT')}")
            print(f"  is_transferred: {job.working_data.get('is_transferred')}")
            print(f"  is_removal: {job.working_data.get('is_removal')}")
            print(f"  pending_cto: {job.working_data.get('pending_cto')}")
            
            # Check if inheritance worked
            if transferor_exists:
                if job.working_data.get('mdl_num'):
                    print("\n✅ SUCCESS: mdl_num was inherited from transferor!")
                else:
                    print("\n⚠️  WARNING: mdl_num was NOT inherited (but might be set by other means)")
                    
                if job.working_data.get('attorneys_gpt'):
                    print("✅ SUCCESS: attorneys_gpt was inherited from transferor!")
                else:
                    print("⚠️  WARNING: attorneys_gpt was NOT inherited")
            else:
                print("\n⚠️  Inheritance could not be tested because transferor case is not in DynamoDB")
                
            # Save the transformed data for inspection
            output_path = json_path.replace('.json', '_transformed_test.json')
            with open(output_path, 'w') as f:
                json.dump(job.working_data, f, indent=2)
            print(f"\n💾 Transformed data saved to: {output_path}")
            
        else:
            print("\n❌ Transformation failed!")
            
    except Exception as e:
        print(f"\n❌ Error during transformation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'transformer' in locals():
            await transformer.cleanup()


async def main():
    """Main test function."""
    print("=" * 80)
    print("Transfer Handler Fix Test")
    print("=" * 80)
    
    await test_single_file_transformation()
    
    print("\n" + "=" * 80)
    print("Test completed!")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())