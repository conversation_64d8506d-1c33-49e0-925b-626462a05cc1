#!/usr/bin/env python3
"""
Test script to verify SharedBrowserManager is truly global across session managers.

This test validates that multiple session managers share the same browser instance.

Usage:
    python test_true_global_singleton.py
"""

import asyncio
import logging
import sys
from typing import Dict, Any
from unittest.mock import MagicMock, patch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_true_global_singleton():
    """
    Test that SharedBrowserManager is truly global across multiple session managers.
    """
    logger.info("🎯 Testing TRUE GLOBAL SharedBrowserManager Singleton")
    
    try:
        # Import required modules
        from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
        from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
        
        # Test configuration
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        # Create multiple session managers for different firms
        logger.info("📋 Creating multiple session managers for different firms...")
        
        session_managers = []
        shared_browser_ids = set()
        
        for i in range(3):
            firm_id = f"test_firm_{i}"
            logger.info(f"\n🏢 Creating session manager for {firm_id}")
            
            # Create session manager using factory
            session_manager = SessionManagerFactory.create(
                config=test_config,
                logger=logger,
                firm_id=firm_id,
                fingerprint_manager=None,
                proxy_manager=None,
                law_firms_repository=None
            )
            
            session_managers.append(session_manager)
            
            # Check SharedBrowserManager instance
            if hasattr(session_manager, 'shared_browser_manager'):
                browser_manager_id = id(session_manager.shared_browser_manager)
                shared_browser_ids.add(browser_manager_id)
                logger.info(f"  Session Manager ID: {id(session_manager)}")
                logger.info(f"  SharedBrowserManager ID: {browser_manager_id}")
            else:
                logger.error(f"❌ Session manager missing shared_browser_manager attribute")
                return False
        
        # Verify all session managers share the same SharedBrowserManager
        logger.info(f"\n📊 Unique SharedBrowserManager IDs: {len(shared_browser_ids)}")
        logger.info(f"📊 SharedBrowserManager IDs: {shared_browser_ids}")
        
        if len(shared_browser_ids) == 1:
            logger.info("✅ SUCCESS: All session managers share the SAME SharedBrowserManager!")
            logger.info(f"🌍 Global SharedBrowserManager ID: {list(shared_browser_ids)[0]}")
        else:
            logger.error("❌ FAILURE: Multiple SharedBrowserManager instances detected!")
            logger.error(f"Expected 1 instance, found {len(shared_browser_ids)}")
            return False
        
        # Test browser context isolation
        logger.info("\n📋 Testing browser context isolation...")
        
        # Mock the actual browser to avoid needing Camoufox installed
        with patch('src.services.fb_ads.camoufox.shared_browser_manager.AsyncCamoufox') as MockCamoufox:
            # Setup mock
            mock_browser_instance = MagicMock()
            mock_browser = MagicMock()
            mock_contexts = []
            
            MockCamoufox.return_value = mock_browser_instance
            mock_browser_instance.start = MagicMock(return_value=asyncio.create_task(asyncio.sleep(0)))
            mock_browser_instance.browser = mock_browser
            
            # Track context creation
            def create_mock_context():
                context = MagicMock()
                page = MagicMock()
                context.pages = [page]
                
                async def mock_new_page():
                    return page
                    
                context.new_page = MagicMock(return_value=mock_new_page())
                mock_contexts.append(context)
                return context
            
            # Return a new coroutine each time
            async def mock_new_context():
                return create_mock_context()
            
            mock_browser.new_context = MagicMock(side_effect=lambda: mock_new_context())
            
            # Get browser contexts for each firm (simulating session start)
            for i, session_manager in enumerate(session_managers):
                firm_id = f"test_firm_{i}"
                logger.info(f"\n🚀 Getting browser context for {firm_id}")
                
                try:
                    # Directly get browser context from shared manager
                    context, page = await session_manager.shared_browser_manager.get_browser_context(
                        job_id=firm_id,
                        config={'headless': True}
                    )
                    logger.info(f"✅ Browser context created for {firm_id}")
                except Exception as e:
                    logger.error(f"❌ Failed to get browser context for {firm_id}: {e}")
                    return False
            
            # Verify only one browser was created but multiple contexts
            logger.info(f"\n📊 Browser creation calls: {MockCamoufox.call_count}")
            logger.info(f"📊 Context creation calls: {len(mock_contexts)}")
            
            if MockCamoufox.call_count <= 1 and len(mock_contexts) == len(session_managers):
                logger.info("✅ SUCCESS: One browser, multiple isolated contexts!")
            else:
                logger.error(f"❌ FAILURE: Expected 1 browser and {len(session_managers)} contexts")
                logger.error(f"Got {MockCamoufox.call_count} browsers and {len(mock_contexts)} contexts")
                return False
        
        logger.info("\n🎉 ALL TESTS PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def main():
    """Main test runner."""
    logger.info("🎯 Starting True Global Singleton Test")
    logger.info("=" * 80)
    
    success = await test_true_global_singleton()
    
    logger.info("\n" + "=" * 80)
    logger.info("FINAL RESULTS")
    logger.info("=" * 80)
    
    if success:
        logger.info("🎉 TRUE GLOBAL SINGLETON WORKING!")
        logger.info("")
        logger.info("✅ Multiple session managers share ONE SharedBrowserManager")
        logger.info("✅ Each firm gets its own isolated browser context")
        logger.info("✅ Only ONE browser instance is created")
        logger.info("")
        logger.info("🚀 The fix is working correctly!")
        return 0
    else:
        logger.error("❌ TRUE GLOBAL SINGLETON FAILED")
        logger.error("Multiple browsers are still being created")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)