#!/usr/bin/env python3
"""
Test the actual transformation pipeline by running it on a single file.
"""
import json
import os
import subprocess
import sys
from pathlib import Path

def run_transformation_test():
    """Run the actual transformation and see what happens."""
    
    # Test file path
    json_path = "/Users/<USER>/PycharmProjects/lexgenius/data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    
    # Load original data
    with open(json_path, 'r') as f:
        original_data = json.load(f)
    
    print("=" * 80)
    print("Test Actual Transformation")
    print("=" * 80)
    
    print("\nOriginal data:")
    print(f"  mdl_num: {original_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  is_transferred: {original_data.get('is_transferred', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {original_data.get('transferor_court_id', 'NOT PRESENT')}")
    
    # Create a backup
    backup_path = json_path + ".backup"
    with open(backup_path, 'w') as f:
        json.dump(original_data, f, indent=2)
    
    try:
        # Run the transformation using the actual command
        print("\n" + "-" * 80)
        print("Running transformation command...")
        print("-" * 80)
        
        # Create a minimal config for testing
        config_content = f"""
iso_date: "20250716"
date: "07/16/25"
transform:
  enabled: true
  force_reprocess: true
  skip_upload: true
  reprocess_files:
    - "scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
  process_single_court: 
    - "scd"
"""
        
        config_path = "/tmp/test_transform_config.yml"
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        # Run the transformation
        cmd = [
            sys.executable, 
            "src/main.py", 
            "--params", 
            config_path
        ]
        
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd="/Users/<USER>/PycharmProjects/lexgenius")
        
        print("\nSTDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        print(f"\nReturn code: {result.returncode}")
        
        # Check the results
        with open(json_path, 'r') as f:
            transformed_data = json.load(f)
        
        print("\n" + "-" * 80)
        print("Transformation Results:")
        print("-" * 80)
        
        print(f"  mdl_num: {transformed_data.get('mdl_num', 'NOT PRESENT')}")
        print(f"  is_transferred: {transformed_data.get('is_transferred', 'NOT PRESENT')}")
        print(f"  transferor_court_id: {transformed_data.get('transferor_court_id', 'NOT PRESENT')}")
        print(f"  transferor_docket_num: {transformed_data.get('transferor_docket_num', 'NOT PRESENT')}")
        
        # Check if any transfer fields were added
        transfer_fields = ['is_transferred', 'is_removal', 'pending_cto', 'transferor_court_id', 'transferor_docket_num']
        changes_made = False
        for field in transfer_fields:
            if field in transformed_data and field not in original_data:
                print(f"  ✅ NEW FIELD: {field} = {transformed_data[field]}")
                changes_made = True
            elif field in transformed_data and transformed_data[field] != original_data.get(field):
                print(f"  ✅ CHANGED: {field} = {transformed_data[field]} (was {original_data.get(field)})")
                changes_made = True
        
        if not changes_made:
            print("  ❌ No transfer fields were added or changed")
        
        # Clean up
        os.remove(config_path)
        
    except Exception as e:
        print(f"Error running transformation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Restore original file
        if os.path.exists(backup_path):
            with open(backup_path, 'r') as f:
                original_data = json.load(f)
            with open(json_path, 'w') as f:
                json.dump(original_data, f, indent=2)
            os.remove(backup_path)
            print("\n✅ Restored original file")

if __name__ == "__main__":
    run_transformation_test()