#!/usr/bin/env python3
"""Debug script to check if image cache setup is being called."""

import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
from src.services.fb_ads.browser_image_cache_extractor import BrowserImageCacheExtractor
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_cache_setup():
    """Test if the image cache setup is being called."""
    
    # Create a mock cache extractor
    cache_extractor = BrowserImageCacheExtractor(
        logger=logger,
        config={'fb_ads': {}}
    )
    
    # Create session manager with cache extractor
    config = {
        'camoufox': {
            'browser': {'headless': True},
            'force_clean_profile': True
        }
    }
    
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        browser_image_cache_extractor=cache_extractor
    )
    
    logger.info("Creating browser and page...")
    
    # This should trigger the image cache setup
    result = await session_manager._create_browser_and_page()
    
    if result:
        logger.info("✅ Browser and page created successfully")
        logger.info(f"Image cache extractor present: {session_manager.image_cache_extractor is not None}")
    else:
        logger.error("❌ Failed to create browser and page")
    
    # Clean up
    await session_manager.cleanup()

if __name__ == "__main__":
    asyncio.run(test_cache_setup())