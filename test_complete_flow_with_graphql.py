#!/usr/bin/env python3
"""
Complete test script that demonstrates the full flow with GraphQL capture.
This tests:
1. Language configuration
2. Dropdown setup and selection
3. Search and scrolling
4. GraphQL capture before clicking
5. ad_library_main response filtering and logging
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,  # Use INFO to see the complete flow
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    """Test configuration for complete flow testing."""

    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': True,  # Test headless mode like production
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 10000,
                    'capture_wait': 8  # Extended wait for GraphQL capture
                }
            }
        }

async def test_complete_flow_with_graphql():
    """Test complete flow from start to GraphQL capture."""
    logger.info("🧪 Testing complete flow with GraphQL capture")

    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )

    try:
        logger.info("🚀 Step 1: Creating session")
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ Failed to create session")
            return False

        logger.info("🚀 Step 2: Setting up Ad Library")
        setup_success = await session_manager._setup_ad_library_search()
        if not setup_success:
            logger.error("❌ Failed to setup Ad Library")
            return False

        logger.info("🚀 Step 3: Searching and selecting Wisner Baum")
        search_success = await session_manager._search_advertiser_no_capture("Wisner Baum")
        if not search_success:
            logger.error("❌ Failed to search and select advertiser")
            return False

        logger.info("🚀 Step 4: Now capturing GraphQL after navigation")
        logger.info("📡 Setting up GraphQL interception...")
        
        # Setup GraphQL interception for ad library data
        await session_manager._setup_graphql_interception()
        
        # Wait for page to load
        await asyncio.sleep(3)
        
        # Scroll to trigger ad loading
        logger.info("📜 Scrolling to trigger ad library data loading...")
        for i in range(3):
            await session_manager.page.evaluate("window.scrollBy(0, window.innerHeight)")
            await asyncio.sleep(1.5)
            logger.info(f"📜 Scrolled {i+1} times")
        
        # Process captured responses
        logger.info("🔍 Processing captured GraphQL responses...")
        ad_library_responses = await session_manager._process_captured_graphql_responses()
        
        if ad_library_responses:
            logger.info("✅ SUCCESS: Complete flow with GraphQL capture completed!")

            # Get final captured responses for summary
            captured_responses = session_manager.get_captured_responses()
            logger.info(f"📊 Final Summary: Captured {len(captured_responses)} total GraphQL responses")

            # Get detailed response sequence analysis
            sequence_summary = session_manager.get_response_sequence_summary()
            logger.info("="*80)
            logger.info("📋 RESPONSE SEQUENCE ANALYSIS:")
            logger.info(f"📊 Total responses: {sequence_summary['total_responses']}")
            logger.info(f"📊 Facebook responses: {len(sequence_summary['facebook_responses'])}")
            logger.info(f"📊 GraphQL responses (captured): {sequence_summary['graphql_responses']}")
            logger.info("="*80)

            # Show details of each Facebook response
            for resp in sequence_summary['sequence_analysis']:
                if resp['is_facebook']:
                    logger.info(f"📱 Response #{resp['sequence']}: {resp['status']} - {resp['content_type']}")
                    logger.info(f"   URL: {resp['url']}")
                    logger.info(f"   Size: {resp['size']} chars, Passed filter: {resp['passes_filter']}")
                    if 'ad_library_main' in resp.get('url', ''):
                        logger.info("   🎯 This URL suggests ad_library_main content!")
            logger.info("="*80)

            # Verify we're on the right page by checking for pageID in URL
            await asyncio.sleep(3)  # Wait for page to load
            current_url = session_manager.page.url
            logger.info(f"📍 Final URL: {current_url}")

            # Extract pageID from URL parameters
            import urllib.parse
            parsed_url = urllib.parse.urlparse(current_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            if 'view_all_page_id' in query_params:
                page_id = query_params['view_all_page_id'][0]
                logger.info(f"✅ Verified: Successfully navigated to advertiser page with pageID: {page_id}")
            else:
                logger.warning("⚠️ URL doesn't contain 'view_all_page_id' parameter - check if navigation was successful")

            return True
        else:
            logger.error("❌ FAILED: Complete flow failed")
            return False

    except Exception as e:
        logger.error(f"❌ Complete flow test failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    """Run the complete flow test."""
    logger.info("🚀 Starting Complete Flow Test with GraphQL Capture")
    logger.info("=" * 80)
    logger.info("This test will:")
    logger.info("1. ✅ Create browser session with English language")
    logger.info("2. ✅ Setup Ad Library (country + category selection)")
    logger.info("3. ✅ Search for 'Wisner Baum'")
    logger.info("4. ✅ Click on Wisner Baum to navigate")
    logger.info("5. 📡 Start GraphQL capture AFTER navigation")
    logger.info("6. 📜 Scroll page to trigger ad loading")
    logger.info("7. 📊 Capture and process ad_library_main responses")
    logger.info("8. 🧹 Close browser")
    logger.info("=" * 80)

    success = await test_complete_flow_with_graphql()

    if success:
        logger.info("🎉 Complete flow test PASSED")
        logger.info("📡 Check the logs above for captured ad_library_main GraphQL responses")
    else:
        logger.error("❌ Complete flow test FAILED")

    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)
