#!/usr/bin/env python3
"""
Test script to validate the cache prefix matching fix for Facebook ad image scraping.

This script demonstrates that the enhanced cache lookup can now find cached images
even when URL parameters vary between the original cached URL and lookup URL.
"""

import asyncio
import sys
import time
from unittest.mock import MagicMock

sys.path.append('/Users/<USER>/PycharmProjects/lexgenius-refactor-fb-ads')

from src.services.fb_ads.browser_image_cache_extractor import BrowserImageCacheExtractor, CachedImageResource


async def test_cache_prefix_matching():
    """Test the cache prefix matching functionality."""
    
    print("🧪 Testing Facebook Ad Image Cache Prefix Matching Fix")
    print("=" * 60)
    
    # Create mock logger and config
    mock_logger = MagicMock()
    mock_config = {"verbose": True}
    
    # Create cache extractor instance
    cache_extractor = BrowserImageCacheExtractor(
        logger=mock_logger,
        config=mock_config,
        max_cache_size_mb=10,  # Small cache for testing
        cache_ttl_minutes=60,  # 1 hour TTL
        enable_disk_persistence=False  # Disable disk for testing
    )
    
    # Test URLs - simulate Facebook CDN URLs with different parameters
    original_url = "https://scontent.fbos1-1.fna.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s600x600_tt6&_nc_cat=104&ccb=1-7&_nc_sid=5f2048&_nc_ohc=abc123&_nc_oc=AQn456&_nc_ht=scontent.fbos1-1.fna&_nc_gid=A789&oh=def&oe=456789"
    
    lookup_url = "https://scontent.fbos1-1.fna.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat=108&ccb=1-7&_nc_sid=5f2048&_nc_ohc=xyz999&_nc_oc=AQn111&_nc_ht=scontent.fbos1-1.fna&_nc_gid=B999&oh=ghi&oe=123456"
    
    # Create fake image content
    fake_image_content = b"fake_image_data_12345" * 100  # ~2.5KB
    
    # Step 1: Store an image in the cache using original URL
    print("📥 Step 1: Storing image in cache with original URL")
    cached_resource = CachedImageResource(
        url=original_url,
        content=fake_image_content,
        content_type="image/jpeg",
        content_length=len(fake_image_content),
        timestamp=time.time(),
        ad_archive_id="123456789",
        ad_creative_id="987654321",
        source="test_browser_cache"
    )
    
    # Store in cache
    await cache_extractor._store_cached_image(cached_resource)
    print(f"   ✅ Stored {len(fake_image_content)} bytes")
    print(f"   📊 Cache size: {len(cache_extractor._image_cache)} images")
    
    # Step 2: Try to retrieve with exact URL (should hit)
    print("\n🎯 Step 2: Testing exact URL match")
    exact_match = await cache_extractor.get_cached_image(original_url)
    if exact_match:
        print(f"   ✅ EXACT MATCH: Found {exact_match.content_length} bytes")
    else:
        print("   ❌ EXACT MATCH: Not found")
    
    # Step 3: Try to retrieve with different parameters (should use prefix matching)
    print("\n🔍 Step 3: Testing prefix matching with different URL parameters")
    print(f"   Original: {original_url[:80]}...")
    print(f"   Lookup:   {lookup_url[:80]}...")
    
    prefix_match = await cache_extractor.get_cached_image(lookup_url)
    if prefix_match:
        print(f"   ✅ PREFIX MATCH: Found {prefix_match.content_length} bytes")
        print("   🎉 SUCCESS: Cache prefix matching is working!")
    else:
        print("   ❌ PREFIX MATCH: Not found - fix may need adjustment")
    
    # Step 4: Show cache statistics
    print("\n📊 Step 4: Cache Analytics")
    stats = cache_extractor.get_cache_stats()
    print(f"   Cache Hits: {stats['cache_hits']}")
    print(f"   Cache Misses: {stats['cache_misses']}")
    print(f"   Hit Rate: {stats['hit_rate_percent']}%")
    print(f"   Prefix Recoveries: {stats.get('prefix_recoveries', 0)}")
    
    if 'cache_miss_reasons' in stats:
        print("   Miss Reasons:")
        for reason, count in stats['cache_miss_reasons'].items():
            if reason == 'prefix_match_recovery':
                print(f"     🔍 {reason}: {count} (RECOVERED!)")
            else:
                print(f"     {reason}: {count}")
    
    # Step 5: Test with completely different URL (should miss)
    print("\n❌ Step 5: Testing with unrelated URL (should miss)")
    unrelated_url = "https://example.com/different/image.jpg"
    unrelated_match = await cache_extractor.get_cached_image(unrelated_url)
    if unrelated_match:
        print("   ⚠️  UNEXPECTED: Found match for unrelated URL")
    else:
        print("   ✅ EXPECTED: No match for unrelated URL")
    
    # Final results
    print(f"\n🎯 FINAL RESULTS:")
    print(f"   Exact Match: {'✅ SUCCESS' if exact_match else '❌ FAILED'}")
    print(f"   Prefix Match: {'✅ SUCCESS' if prefix_match else '❌ FAILED'}")
    print(f"   Unrelated Miss: {'✅ SUCCESS' if not unrelated_match else '❌ FAILED'}")
    
    # Success if both exact and prefix matching work
    success = exact_match is not None and prefix_match is not None and unrelated_match is None
    
    print(f"\n🏆 OVERALL CACHE FIX: {'✅ SUCCESS' if success else '❌ NEEDS WORK'}")
    
    if success:
        print("\n💡 The cache prefix matching fix is working correctly!")
        print("   - Exact URL matches work as before")
        print("   - Prefix matching recovers cache misses from URL parameter variations")
        print("   - Unrelated URLs still miss as expected")
    else:
        print("\n⚠️  The cache fix needs adjustment - check the implementation")
    
    return success


async def main():
    """Main test function."""
    try:
        success = await test_cache_prefix_matching()
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)