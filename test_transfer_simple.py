#!/usr/bin/env python3
"""
Simple test to verify transfer handler is being called.
"""
import json
import os
import subprocess
import sys

def main():
    """Run transformation and check if transfer handler is invoked."""
    json_path = 'data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json'
    
    if not os.path.exists(json_path):
        print(f"❌ Test file not found: {json_path}")
        return
    
    # Load original data
    print(f"\n📄 Testing file: {json_path}")
    with open(json_path, 'r') as f:
        original_data = json.load(f)
    
    print("\n🔍 Original file state:")
    print(f"  case_in_other_court: {original_data.get('case_in_other_court')}")
    print(f"  lead_case: {original_data.get('lead_case')}")
    print(f"  mdl_num: {original_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  attorneys_gpt: {original_data.get('attorneys_gpt', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {original_data.get('transferor_court_id', 'NOT PRESENT')}")
    
    # Create a minimal test config
    config_path = 'test_transform_config.yml'
    config_content = f"""# Test config for transform
iso_date: '20250716'
date: '07/16/25'
scraper: false
post_process: true
upload: false
report_generator: false
fb_ads: false
headless: true
run_parallel: false
reprocess_files: true
force_reprocess: true
llm_provider: 'deepseek'
process_specific_files:
  - '{os.path.basename(json_path)}'
"""
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print("\n🚀 Running transformation with config...")
    print(f"   Config: {config_path}")
    print(f"   Processing specific file: {os.path.basename(json_path)}")
    
    # Run the transformation
    cmd = ['python', 'src/main.py', '--params', config_path]
    print(f"\n📌 Running: {' '.join(cmd)}")
    
    try:
        # Run and capture output
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # Check for transfer handler logs
        output = result.stdout + result.stderr
        
        # Look for transfer handler messages
        if "Running transfer processing to inherit fields from transferor" in output:
            print("\n✅ SUCCESS: Transfer handler was called!")
        else:
            print("\n❌ FAILURE: Transfer handler was NOT called")
            
        if "Transfer processing completed" in output:
            print("✅ Transfer processing completed successfully")
            
        if "case_in_other_court" in output and "Michigan Eastern" in output:
            print("✅ Transfer handler parsed case_in_other_court field")
            
        if "Querying DB for transferor" in output:
            print("✅ Transfer handler queried for transferor case")
            
        # Save output for analysis
        with open('test_transfer_output.log', 'w') as f:
            f.write("=== STDOUT ===\n")
            f.write(result.stdout)
            f.write("\n\n=== STDERR ===\n")
            f.write(result.stderr)
        print(f"\n📝 Full output saved to: test_transfer_output.log")
        
        # Check if transformed file was created
        transformed_path = json_path.replace('.json', '_transformed.json')
        if os.path.exists(transformed_path):
            print(f"\n📄 Checking transformed file: {transformed_path}")
            with open(transformed_path, 'r') as f:
                transformed_data = json.load(f)
                
            print("\n🔍 After transformation:")
            print(f"  mdl_num: {transformed_data.get('mdl_num', 'NOT PRESENT')}")
            print(f"  attorneys_gpt: {transformed_data.get('attorneys_gpt', 'NOT PRESENT')}")
            print(f"  transferor_court_id: {transformed_data.get('transferor_court_id', 'NOT PRESENT')}")
            print(f"  transferor_docket_num: {transformed_data.get('transferor_docket_num', 'NOT PRESENT')}")
        else:
            print(f"\n⚠️  No transformed file found at: {transformed_path}")
            
    except subprocess.TimeoutExpired:
        print("\n❌ Transformation timed out after 5 minutes")
    except Exception as e:
        print(f"\n❌ Error running transformation: {e}")
    finally:
        # Cleanup
        if os.path.exists(config_path):
            os.remove(config_path)
            print(f"\n🧹 Cleaned up test config: {config_path}")


if __name__ == "__main__":
    print("=" * 80)
    print("Transfer Handler Integration Test")
    print("=" * 80)
    
    main()
    
    print("\n" + "=" * 80)
    print("Test completed!")
    print("=" * 80)