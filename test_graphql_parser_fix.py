#!/usr/bin/env python3
"""Test the GraphQL parser fix to ensure it extracts all collated ads."""

import json
import logging
import asyncio
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the GraphQL parser
from src.services.fb_ads.graphql_response_parser import GraphQLResponseParser

async def test_parser():
    """Test the GraphQL parser with the Morgan & Morgan response."""
    
    # Load the test response
    response_file = Path('/Users/<USER>/PycharmProjects/lexgenius/errors/morgan_morgan_response.json')
    logger.info(f"Loading test response from: {response_file}")
    
    with open(response_file, 'r') as f:
        response_data = json.load(f)
    
    # Convert to NDJSON format (single line)
    ndjson_response = json.dumps(response_data)
    
    # Initialize parser
    parser = GraphQLResponseParser(logger=logger, config={})
    
    # Parse the response
    logger.info("Parsing GraphQL response...")
    result = await parser.parse_ndjson_response(ndjson_response)
    
    # Print results
    logger.info("=" * 60)
    logger.info("PARSER RESULTS:")
    logger.info(f"Total ads found: {result['total_ads']}")
    logger.info(f"Page info: {result['page_info']}")
    logger.info(f"Is complete: {result['is_complete']}")
    logger.info(f"Errors: {len(result['errors'])}")
    
    # Verify we found all 30 ads
    expected_ads = 30
    if result['total_ads'] == expected_ads:
        logger.info(f"✅ SUCCESS: Found all {expected_ads} ads!")
    else:
        logger.error(f"❌ FAILED: Expected {expected_ads} ads but found {result['total_ads']}")
    
    # Show sample of ads found
    if result['ads']:
        logger.info("\nSample of ads found:")
        for i, ad in enumerate(result['ads'][:5]):
            ad_id = ad.get('adArchiveID', 'Unknown')
            page_name = ad.get('pageName', 'Unknown')
            logger.info(f"  {i+1}. Ad ID: {ad_id}, Page: {page_name}")
        
        if result['total_ads'] > 5:
            logger.info(f"  ... and {result['total_ads'] - 5} more ads")
    
    # Count ads per edge for verification
    logger.info("\nDetailed edge analysis:")
    edges = response_data['data']['ad_library_main']['search_results_connection']['edges']
    total_from_edges = 0
    for i, edge in enumerate(edges):
        collated_count = len(edge['node']['collated_results'])
        total_from_edges += collated_count
        logger.debug(f"  Edge {i}: {collated_count} collated ads")
    
    logger.info(f"Total ads from manual edge count: {total_from_edges}")
    
    return result

if __name__ == "__main__":
    asyncio.run(test_parser())