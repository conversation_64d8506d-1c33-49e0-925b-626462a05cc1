#!/usr/bin/env python3
"""Integration test to verify Facebook ads are properly saved with last_updated field."""

import logging
import asyncio
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_dynamodb_for_recent_updates():
    """Check DynamoDB for ads with today's last_updated date."""
    from src.repositories.fb_archive_repository import FBArchiveRepository
    from src.infrastructure.aws.dynamodb.async_dynamodb_storage import AsyncDynamoDBStorage
    
    # Initialize storage and repository
    storage = AsyncDynamoDBStorage(
        table_name="FBAdArchive",
        use_local=True,
        local_port=8000
    )
    
    repo = FBArchiveRepository(storage=storage, logger=logger)
    
    # Today's date in YYYYMMDD format
    today = datetime.now().strftime("%Y%m%d")
    logger.info(f"Checking for ads with last_updated = {today}")
    
    try:
        # Query for ads updated today
        # Note: This would normally require a GSI on last_updated field
        # For now, let's check a specific ad we know was processed
        test_ad_ids = [
            "624952230635857",  # First ad from morgan_morgan_response.json
            "1137997745013785", # Second ad
            "1261286425672639", # Third ad
        ]
        
        found_with_today = 0
        found_with_other_date = 0
        not_found = 0
        
        for ad_id in test_ad_ids:
            # Try multiple start dates since we don't know which was used
            possible_dates = ["20250718", "20250717", "20250716", "20250715"]
            
            for start_date in possible_dates:
                key = {"AdArchiveID": ad_id, "StartDate": start_date}
                try:
                    item = await repo.get_item(key)
                    if item:
                        last_updated = item.get("last_updated", "Not set")
                        logger.info(f"Ad {ad_id} (start_date={start_date}): last_updated = {last_updated}")
                        
                        if last_updated == today:
                            found_with_today += 1
                        else:
                            found_with_other_date += 1
                        break
                except Exception:
                    continue
            else:
                logger.warning(f"Ad {ad_id} not found in database")
                not_found += 1
        
        logger.info("=" * 60)
        logger.info(f"SUMMARY:")
        logger.info(f"  - Ads with today's date ({today}): {found_with_today}")
        logger.info(f"  - Ads with other dates: {found_with_other_date}")
        logger.info(f"  - Ads not found: {not_found}")
        
        # Also do a scan to count all ads with today's date
        # Note: This is expensive but useful for debugging
        logger.info("\nScanning entire table for today's updates...")
        scan_params = {
            'FilterExpression': 'last_updated = :today',
            'ExpressionAttributeValues': {':today': today}
        }
        
        items = []
        async for item in storage.scan(**scan_params):
            items.append(item)
        
        logger.info(f"Total ads with last_updated = {today}: {len(items)}")
        
        if items:
            logger.info("Sample of ads updated today:")
            for item in items[:5]:
                logger.info(f"  - {item.get('AdArchiveID')} (PageID: {item.get('PageID')})")
        
    finally:
        await storage.close()

if __name__ == "__main__":
    asyncio.run(check_dynamodb_for_recent_updates())