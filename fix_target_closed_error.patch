--- a/src/services/fb_ads/camoufox/camoufox_session_manager.py
+++ b/src/services/fb_ads/camoufox/camoufox_session_manager.py
@@ -3968,6 +3968,29 @@ class CamoufoxSessionManager(SessionManagerBase):
                         # Start the browser
                         await self.browser.start()
                         self.logger.info("✅ Browser started successfully")
+                        
+                        # CRITICAL FIX: Wait for browser to be fully ready
+                        browser_ready = False
+                        max_ready_attempts = 10
+                        for ready_attempt in range(max_ready_attempts):
+                            try:
+                                # Check if browser.browser property exists and is accessible
+                                if hasattr(self.browser, 'browser') and self.browser.browser is not None:
+                                    # Try to access a property to ensure it's really ready
+                                    _ = await self.browser.browser.version()
+                                    browser_ready = True
+                                    self.logger.info(f"✅ Browser internal state verified ready (attempt {ready_attempt + 1})")
+                                    break
+                            except Exception as e:
+                                if ready_attempt < max_ready_attempts - 1:
+                                    self.logger.debug(f"Browser not ready yet (attempt {ready_attempt + 1}): {e}")
+                                    await asyncio.sleep(0.5)  # Wait 500ms before retry
+                                else:
+                                    raise Exception(f"<PERSON><PERSON><PERSON> failed to become ready after {max_ready_attempts} attempts")
+                        
+                        if not browser_ready:
+                            raise Exception("Browser instance not properly initialized - internal browser object not accessible")
                         
                         # Verify browser is actually running
                         if not self.browser:
@@ -4051,10 +4074,52 @@ class CamoufoxSessionManager(SessionManagerBase):
             # Add viewport configuration
             context_args['viewport'] = self.viewport
             
-            self.context = await self.browser.browser.new_context(**context_args)
+            # CRITICAL FIX: Add retry logic for context creation
+            context_created = False
+            max_context_retries = 3
+            for context_attempt in range(max_context_retries):
+                try:
+                    self.context = await self.browser.browser.new_context(**context_args)
+                    context_created = True
+                    self.logger.info(f"✅ Browser context created successfully (attempt {context_attempt + 1})")
+                    break
+                except Exception as e:
+                    if "Target closed" in str(e) or "Target page, context or browser has been closed" in str(e):
+                        self.logger.warning(f"⚠️ TargetClosedError during context creation (attempt {context_attempt + 1}): {e}")
+                        if context_attempt < max_context_retries - 1:
+                            await asyncio.sleep(1)  # Wait before retry
+                            # Re-verify browser is still valid
+                            try:
+                                _ = await self.browser.browser.version()
+                            except:
+                                self.logger.error("❌ Browser became invalid, need full restart")
+                                return False
+                        else:
+                            raise
+                    else:
+                        raise
+            
+            if not context_created:
+                self.logger.error("Failed to create browser context after retries")
+                return False
             
             # Clear all browser storage to ensure clean state
             try:
@@ -4061,7 +4126,35 @@ class CamoufoxSessionManager(SessionManagerBase):
                 self.logger.warning(f"Failed to clear cookies: {e}")
                 
-            self.page = await self.context.new_page()
+            # CRITICAL FIX: Add retry logic for page creation
+            page_created = False
+            max_page_retries = 3
+            for page_attempt in range(max_page_retries):
+                try:
+                    self.page = await self.context.new_page()
+                    page_created = True
+                    self.logger.info(f"✅ Browser page created successfully (attempt {page_attempt + 1})")
+                    break
+                except Exception as e:
+                    if "Target closed" in str(e) or "Target page, context or browser has been closed" in str(e):
+                        self.logger.warning(f"⚠️ TargetClosedError during page creation (attempt {page_attempt + 1}): {e}")
+                        if page_attempt < max_page_retries - 1:
+                            await asyncio.sleep(1)  # Wait before retry
+                            # Re-verify context is still valid
+                            try:
+                                # Try to perform a simple operation on the context
+                                _ = await self.context.cookies()
+                            except:
+                                self.logger.error("❌ Browser context became invalid, need full restart")
+                                return False
+                        else:
+                            raise
+                    else:
+                        raise
+            
+            if not page_created:
+                self.logger.error("Failed to create browser page after retries")
+                return False
             
             # Pre-navigation addon check
             self.logger.info("🔍 Running pre-navigation addon check...")