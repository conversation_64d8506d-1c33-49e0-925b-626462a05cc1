#!/usr/bin/env python3
"""
Test to verify that the BrowserImageCacheExtractor singleton is properly shared
across multiple concurrent workers in the FB ads processing pipeline.
"""

import asyncio
import time
from unittest.mock import MagicMock
from src.containers.fb_ads import FbAdsContainer
from src.services.fb_ads.browser_image_cache_extractor import CachedImageResource

async def worker_task(worker_id: int, container: FbAdsContainer, test_urls: list[str]) -> dict:
    """Simulate a worker processing images and checking cache."""
    results = {
        'worker_id': worker_id,
        'cache_instance_id': None,
        'cache_hits': 0,
        'cache_misses': 0,
        'cached_urls': []
    }
    
    # Get the cache extractor from container
    cache_extractor = container.browser_image_cache_extractor()
    results['cache_instance_id'] = id(cache_extractor)
    
    # Worker 1 stores images, other workers should find them
    if worker_id == 1:
        print(f"Worker {worker_id}: Storing test images in cache...")
        for i, url in enumerate(test_urls[:3]):  # Store first 3 URLs
            content = f"test_image_content_{i}".encode()
            cached_resource = CachedImageResource(
                url=url,
                content=content,
                content_type="image/jpeg",
                content_length=len(content),
                timestamp=time.time(),
                source=f"worker_{worker_id}"
            )
            await cache_extractor._store_cached_image(cached_resource)
            results['cached_urls'].append(url)
            print(f"  Stored: {url[:60]}...")
        
        # Small delay to ensure cache is populated
        await asyncio.sleep(0.1)
    else:
        # Other workers should find the cached images
        print(f"Worker {worker_id}: Looking for cached images...")
        # Give worker 1 time to populate cache
        await asyncio.sleep(0.2)
        
        for url in test_urls[:3]:
            cached_image = await cache_extractor.get_cached_image(url)
            if cached_image:
                results['cache_hits'] += 1
                print(f"  ✅ CACHE HIT: {url[:60]}...")
            else:
                results['cache_misses'] += 1
                print(f"  ❌ CACHE MISS: {url[:60]}...")
    
    return results

async def main():
    print("🧪 CACHE SINGLETON VERIFICATION TEST")
    print("=" * 50)
    
    # Initialize container
    container = FbAdsContainer()
    container.init_resources()
    
    # Test URLs similar to Facebook CDN patterns
    test_urls = [
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/test_111_222_333_n.jpg?_nc_cat=101",
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/test_444_555_666_n.jpg?_nc_cat=102", 
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/test_777_888_999_n.jpg?_nc_cat=103",
    ]
    
    print(f"Testing with {len(test_urls)} URLs across multiple workers...")
    
    # Run multiple workers concurrently
    num_workers = 4
    tasks = []
    for i in range(1, num_workers + 1):
        tasks.append(worker_task(i, container, test_urls))
    
    # Execute all workers concurrently
    results = await asyncio.gather(*tasks)
    
    # Analyze results
    print("\n" + "=" * 50)
    print("📊 RESULTS ANALYSIS")
    print("=" * 50)
    
    # Check if all workers use the same cache instance
    instance_ids = [r['cache_instance_id'] for r in results]
    unique_instances = set(instance_ids)
    
    print(f"\n🔍 Singleton Pattern Check:")
    print(f"   Unique cache instances: {len(unique_instances)}")
    print(f"   Instance IDs: {unique_instances}")
    
    if len(unique_instances) == 1:
        print(f"   ✅ PASS: All workers share the same cache instance!")
    else:
        print(f"   ❌ FAIL: Multiple cache instances detected!")
    
    # Check cache sharing
    print(f"\n🔍 Cache Sharing Check:")
    worker1_results = results[0]
    print(f"   Worker 1 cached {len(worker1_results['cached_urls'])} URLs")
    
    total_hits = sum(r['cache_hits'] for r in results[1:])  # Skip worker 1
    total_misses = sum(r['cache_misses'] for r in results[1:])
    total_checks = total_hits + total_misses
    
    print(f"   Other workers found {total_hits}/{total_checks} cached images")
    
    if total_hits == total_checks and total_checks > 0:
        print(f"   ✅ PASS: All cached images were found by other workers!")
    else:
        print(f"   ❌ FAIL: Some cached images were not found!")
    
    # Detailed worker results
    print(f"\n📋 Detailed Worker Results:")
    for r in results:
        print(f"   Worker {r['worker_id']}:")
        print(f"     Cache Instance: {r['cache_instance_id']}")
        print(f"     Cache Hits: {r['cache_hits']}")
        print(f"     Cache Misses: {r['cache_misses']}")
        if r['cached_urls']:
            print(f"     Cached URLs: {len(r['cached_urls'])}")
    
    # Get cache statistics
    cache_extractor = container.browser_image_cache_extractor()
    stats = cache_extractor.get_cache_stats()
    
    print(f"\n📈 Cache Statistics:")
    print(f"   Total cached images: {stats['cached_images']}")
    print(f"   Cache hits: {stats['cache_hits']}")
    print(f"   Cache misses: {stats['cache_misses']}")
    print(f"   Hit rate: {stats['hit_rate_percent']}%")
    
    # Overall assessment
    singleton_pass = len(unique_instances) == 1
    sharing_pass = total_hits == total_checks and total_checks > 0
    
    print(f"\n" + "=" * 50)
    if singleton_pass and sharing_pass:
        print("🎉 SUCCESS: Cache singleton and sharing working correctly!")
        return 0
    else:
        print("❌ FAILURE: Issues detected with cache singleton or sharing")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)