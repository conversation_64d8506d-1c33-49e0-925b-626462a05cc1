"""
Unit tests for Browser Image Cache URL normalization and lookup logic.

These tests specifically target the cache miss issue where URLs with different
query parameters for the same image were not being matched correctly.
"""

import pytest
import asyncio
import time
from unittest.mock import MagicMock, AsyncMock

from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)


class TestURLNormalization:
    """Test URL normalization logic for Facebook CDN images"""
    
    @pytest.fixture
    def cache_extractor(self):
        """Create cache extractor instance"""
        config = {
            "camoufox": {
                "image_cache": {
                    "enabled": True,
                    "max_cache_size_mb": 10,
                    "cache_ttl_minutes": 60
                }
            }
        }
        logger = MagicMock()
        return BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False  # Disable for unit tests
        )
    
    def test_normalize_facebook_url_with_dynamic_params(self, cache_extractor):
        """Test normalization removes dynamic query parameters"""
        # Test URL with multiple dynamic parameters
        test_url = (
            "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
            "460370463_1037577531718719_2663569665313904600_n.jpg"
            "?stp=dst-jpg_tt6&_nc_cat=104&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=Wt2F9xQvCFAQ7kNvgHnVJJg&_nc_zt=14&_nc_ht=scontent-dfw5-2.xx"
            "&_nc_gid=AgVRJ-RFJPxXfYXiVHBu6Xf&oh=00_AYDU7Xf9e0pX_1BUQKxSJ9fqQDSczKPv9B17FJU-RaLBSg"
            "&oe=67336685"
        )
        
        normalized = cache_extractor._normalize_facebook_image_url(test_url)
        
        # Should extract image ID and remove all dynamic params
        assert "460370463_1037577531718719_2663569665313904600_n.jpg" in normalized
        assert "_nc_cat" not in normalized
        assert "_nc_sid" not in normalized
        assert "_nc_ohc" not in normalized
        assert "_nc_gid" not in normalized
        assert "oh=" not in normalized
        assert "oe=" not in normalized
        assert "stp=" not in normalized
        assert "ccb=" not in normalized
    
    def test_normalize_thumbnail_and_fullsize_urls_match(self, cache_extractor):
        """Test that thumbnail and full-size URLs normalize to same key"""
        # Thumbnail URL
        thumbnail_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
            "&oe=6732D6F0"
        )
        
        # Full-size URL (same image, different size params)
        fullsize_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=differentValue&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=differentGID&oh=differentOH&oe=differentOE"
        )
        
        normalized_thumb = cache_extractor._normalize_facebook_image_url(thumbnail_url)
        normalized_full = cache_extractor._normalize_facebook_image_url(fullsize_url)
        
        # Both should normalize to the same base image ID
        assert normalized_thumb == normalized_full
        assert "447181435_1029863199156819_7025413177941515490_n.jpg" in normalized_thumb
    
    def test_normalize_different_cdn_hosts_same_image(self, cache_extractor):
        """Test normalization handles different CDN hosts for same image"""
        urls = [
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=101",
            "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=104",
            "https://scontent.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=107",
        ]
        
        normalized_urls = [cache_extractor._normalize_facebook_image_url(url) for url in urls]
        
        # All should have the same image ID pattern
        for normalized in normalized_urls:
            assert "123_456_789_n.jpg" in normalized
    
    def test_normalize_preserves_essential_params(self, cache_extractor):
        """Test that essential parameters are preserved during normalization"""
        # URL with mix of dynamic and potentially essential params
        test_url = (
            "https://scontent.fbcdn.net/v/t39.35426-6/image.jpg"
            "?version=2&_nc_cat=101&important_param=value&oh=hash&oe=timestamp"
        )
        
        normalized = cache_extractor._normalize_facebook_image_url(test_url)
        
        # Dynamic params should be removed
        assert "_nc_cat" not in normalized
        assert "oh=" not in normalized
        assert "oe=" not in normalized
        
        # Non-dynamic params might be preserved (based on implementation)
        # This depends on the specific logic for essential params
    
    def test_normalize_handles_malformed_urls(self, cache_extractor):
        """Test normalization handles edge cases gracefully"""
        malformed_urls = [
            "not_a_url",
            "https://example.com/image.jpg",
            "https://scontent.fbcdn.net/",  # No image path
            "https://scontent.fbcdn.net/v/t39.35426-6/",  # No image filename
            "",
            None
        ]
        
        for url in malformed_urls[:-1]:  # Skip None for now
            # Should not raise exception
            try:
                normalized = cache_extractor._normalize_facebook_image_url(url)
                assert normalized is not None
            except Exception as e:
                pytest.fail(f"Normalization failed for {url}: {e}")


class TestCacheLookupWithNormalization:
    """Test cache lookup logic with URL normalization"""
    
    @pytest.fixture
    async def populated_cache(self):
        """Create cache extractor with pre-populated cache"""
        config = {
            "camoufox": {
                "image_cache": {
                    "enabled": True,
                    "max_cache_size_mb": 10,
                    "cache_ttl_minutes": 60
                }
            }
        }
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Pre-populate cache with test images
        test_images = [
            {
                "url": (
                    "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                    "447181435_1029863199156819_7025413177941515490_n.jpg"
                    "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
                ),
                "content": b"thumbnail_image_content",
                "size": "60x60"
            },
            {
                "url": (
                    "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                    "460370463_1037577531718719_2663569665313904600_n.jpg"
                    "?_nc_cat=104&oh=longHash&oe=67336685"
                ),
                "content": b"fullsize_image_content",
                "size": "600x600"
            }
        ]
        
        for img in test_images:
            cached_resource = CachedImageResource(
                url=img["url"],
                content=img["content"],
                content_type="image/jpeg",
                content_length=len(img["content"]),
                timestamp=time.time(),
                source="test_cache"
            )
            await extractor._store_cached_image(cached_resource)
        
        return extractor
    
    @pytest.mark.asyncio
    async def test_cache_hit_with_different_query_params(self, populated_cache):
        """Test cache hit when looking up with different query parameters"""
        extractor = populated_cache
        
        # Look up first image with completely different query params
        lookup_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s600x600_tt6&_nc_cat=999&ccb=9-9&_nc_sid=different"
            "&_nc_ohc=newValue&_nc_gid=newGID&oh=newHash&oe=newTimestamp"
        )
        
        # Should find the cached image despite different params
        cached_image = await extractor.get_cached_image(lookup_url)
        
        assert cached_image is not None
        assert cached_image.content == b"thumbnail_image_content"
        assert extractor._cache_hits == 1
        assert extractor._cache_misses == 0
    
    @pytest.mark.asyncio
    async def test_cache_miss_for_different_image(self, populated_cache):
        """Test cache miss when looking up genuinely different image"""
        extractor = populated_cache
        
        # Look up different image ID
        lookup_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "999999999_888888888888888_7777777777777777777_n.jpg"
            "?_nc_cat=101"
        )
        
        cached_image = await extractor.get_cached_image(lookup_url)
        
        assert cached_image is None
        assert extractor._cache_hits == 0
        assert extractor._cache_misses == 1
    
    @pytest.mark.asyncio
    async def test_normalized_index_collision_handling(self, populated_cache):
        """Test handling of multiple URLs mapping to same normalized key"""
        extractor = populated_cache
        
        # Add another version of the same image with different params
        same_image_different_params = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s1200x1200_tt6&_nc_cat=555&completely=different"
        )
        
        large_content = b"large_image_content_1200x1200"
        cached_resource = CachedImageResource(
            url=same_image_different_params,
            content=large_content,
            content_type="image/jpeg",
            content_length=len(large_content),
            timestamp=time.time(),
            source="test_cache"
        )
        await extractor._store_cached_image(cached_resource)
        
        # Verify normalized index has multiple URLs
        normalized_url = extractor._normalize_facebook_image_url(same_image_different_params)
        assert len(extractor._normalized_url_index[normalized_url]) > 1
        
        # Lookup should return best match (larger, newer image)
        lookup_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?random=params"
        )
        
        cached_image = await extractor.get_cached_image(lookup_url)
        assert cached_image is not None
        assert cached_image.content == large_content  # Should get the larger image
    
    @pytest.mark.asyncio
    async def test_cache_ttl_expiration(self, populated_cache):
        """Test that expired cache entries are not returned"""
        extractor = populated_cache
        
        # Manually expire an entry
        first_url = list(extractor._image_cache.keys())[0]
        extractor._image_cache[first_url].timestamp = time.time() - 7200  # 2 hours ago
        
        # Lookup should miss due to expiration
        cached_image = await extractor.get_cached_image(first_url)
        
        assert cached_image is None
        assert extractor._cache_misses == 1
        assert extractor._cache_miss_reasons.get('expired', 0) == 1
    
    @pytest.mark.asyncio
    async def test_cache_miss_reason_tracking(self, populated_cache):
        """Test detailed tracking of cache miss reasons"""
        extractor = populated_cache
        
        # Test various miss scenarios
        test_cases = [
            {
                "url": "https://example.com/not_facebook.jpg",
                "expected_reason": "not_cached"
            },
            {
                "url": (
                    "https://scontent.fbcdn.net/v/t39.35426-6/"
                    "nonexistent_999_888_777_n.jpg?_nc_cat=101"
                ),
                "expected_reason": "not_cached"
            }
        ]
        
        for test_case in test_cases:
            await extractor.get_cached_image(test_case["url"])
        
        # Verify miss reasons were tracked
        assert extractor._cache_miss_reasons['not_cached'] >= len(test_cases)
    
    @pytest.mark.asyncio
    async def test_normalization_statistics_tracking(self, populated_cache):
        """Test tracking of normalization success/failure statistics"""
        extractor = populated_cache
        
        # Perform multiple lookups
        urls = [
            "https://scontent.fbcdn.net/v/t39.35426-6/test_123_456_789_n.jpg",
            "https://invalid-url-format",
            "https://scontent.fbcdn.net/v/t39.35426-6/test_987_654_321_n.jpg?_nc_cat=101",
        ]
        
        for url in urls:
            await extractor.get_cached_image(url)
        
        # Check normalization stats
        total_attempts = (
            extractor._normalization_stats['successful'] + 
            extractor._normalization_stats['failed']
        )
        assert total_attempts >= len(urls)


class TestCachePerformanceWithLargeDataset:
    """Test cache performance with realistic large datasets"""
    
    @pytest.mark.asyncio
    async def test_performance_with_many_cached_images(self):
        """Test lookup performance with large number of cached images"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=100,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Populate with many images
        num_images = 1000
        start_populate = time.time()
        
        for i in range(num_images):
            url = f"https://scontent.fbcdn.net/v/t39.35426-6/img_{i:06d}_123_456_n.jpg?_nc_cat={i%200}"
            cached_resource = CachedImageResource(
                url=url,
                content=f"content_{i}".encode(),
                content_type="image/jpeg",
                content_length=100000,  # 100KB each
                timestamp=time.time(),
                source="test"
            )
            await extractor._store_cached_image(cached_resource)
        
        populate_time = time.time() - start_populate
        assert populate_time < 5.0  # Should populate 1000 images in under 5 seconds
        
        # Test lookup performance
        lookup_iterations = 100
        start_lookup = time.time()
        hits = 0
        
        for i in range(lookup_iterations):
            # Look up with different query params
            lookup_url = f"https://scontent.fbcdn.net/v/t39.35426-6/img_{i:06d}_123_456_n.jpg?_nc_cat=999&oh=different"
            cached_image = await extractor.get_cached_image(lookup_url)
            if cached_image:
                hits += 1
        
        lookup_time = time.time() - start_lookup
        avg_lookup_time = lookup_time / lookup_iterations
        
        # Performance assertions
        assert avg_lookup_time < 0.01  # Each lookup should be under 10ms
        assert hits == lookup_iterations  # All lookups should hit
        
        # Check cache stats
        stats = extractor.get_cache_stats()
        assert stats['cached_images'] == num_images
        assert stats['normalized_url_groups'] <= num_images
    
    @pytest.mark.asyncio 
    async def test_cache_eviction_performance(self):
        """Test cache eviction performance when cache is full"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,  # Small cache to force eviction
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Fill cache beyond capacity
        num_images = 200
        image_size = 100000  # 100KB each, total 20MB (cache is 10MB)
        
        start_time = time.time()
        
        for i in range(num_images):
            url = f"https://scontent.fbcdn.net/v/t39.35426-6/evict_test_{i}_n.jpg"
            cached_resource = CachedImageResource(
                url=url,
                content=b"x" * image_size,
                content_type="image/jpeg", 
                content_length=image_size,
                timestamp=time.time(),
                source="test"
            )
            await extractor._store_cached_image(cached_resource)
        
        eviction_time = time.time() - start_time
        
        # Should handle eviction efficiently
        assert eviction_time < 10.0  # Should complete in under 10 seconds
        
        # Verify cache size is within limits
        stats = extractor.get_cache_stats()
        assert stats['cache_size_mb'] <= 10.0
        assert stats['cached_images'] < num_images  # Some were evicted


class TestCacheAnalyticsAndRecommendations:
    """Test cache analytics and recommendation generation"""
    
    @pytest.mark.asyncio
    async def test_cache_problem_identification(self):
        """Test identification of cache performance problems"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Simulate poor cache performance
        extractor._cache_hits = 2
        extractor._cache_misses = 8
        extractor._cache_miss_reasons = {
            'normalization_mismatch': 5,
            'expired': 2,
            'not_cached': 1
        }
        extractor._normalization_stats = {'successful': 8, 'failed': 2}
        
        stats = extractor.get_cache_stats()
        
        # Should identify problems
        assert len(stats['needs_attention']) > 0
        assert any('low cache hit rate' in problem for problem in stats['needs_attention'])
        assert any('normalization mismatches' in problem for problem in stats['needs_attention'])
    
    @pytest.mark.asyncio
    async def test_cache_recommendations_generation(self):
        """Test generation of improvement recommendations"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Simulate cache with room for improvement
        extractor._cache_hits = 3
        extractor._cache_misses = 7
        extractor._cache_miss_reasons = {'not_cached': 5, 'expired': 2}
        extractor._current_cache_size = 0.5 * 1024 * 1024  # 0.5MB of 10MB
        
        stats = extractor.get_cache_stats()
        
        # Should generate recommendations
        assert len(stats['recommendations']) > 0
        assert any('TTL' in rec for rec in stats['recommendations'])
        assert any('underutilized' in rec for rec in stats['recommendations'])
    
    def test_analytics_dashboard_formatting(self):
        """Test analytics dashboard output formatting"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Set up some stats
        extractor._cache_hits = 80
        extractor._cache_misses = 20
        extractor._total_saved_bytes = 50 * 1024 * 1024  # 50MB
        extractor._image_cache = {f"url_{i}": MagicMock() for i in range(50)}
        
        dashboard = extractor.get_cache_analytics_dashboard()
        
        # Verify dashboard contains key sections
        assert "BROWSER IMAGE CACHE ANALYTICS DASHBOARD" in dashboard
        assert "CACHE PERFORMANCE:" in dashboard
        assert "CORE METRICS:" in dashboard
        assert "CACHE STATUS:" in dashboard
        assert "URL NORMALIZATION:" in dashboard
        
        # Verify it shows good performance
        assert "🟢" in dashboard or "🟡" in dashboard  # Good or excellent performance


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])