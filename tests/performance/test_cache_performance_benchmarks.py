"""
Performance benchmarks for Browser Image Cache system.

These tests validate that the cache solution performs well with large datasets
and doesn't introduce performance regressions.
"""

import pytest
import asyncio
import time
import statistics
import random
from unittest.mock import MagicMock

from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)


class TestCachePerformanceBenchmarks:
    """Performance benchmarks for cache operations"""
    
    @pytest.fixture
    def performance_cache(self):
        """Cache configured for performance testing"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        return BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=1000,  # Large cache for performance testing
            cache_ttl_minutes=1440,
            enable_disk_persistence=False  # Disable to focus on memory performance
        )
    
    @pytest.mark.asyncio
    async def test_cache_storage_performance(self, performance_cache):
        """Test performance of storing many images in cache"""
        extractor = performance_cache
        
        # Test parameters
        num_images = 5000
        image_size = 150000  # 150KB average image size
        
        # Generate test images
        test_images = []
        for i in range(num_images):
            url = (
                f"https://scontent-dfw5-{(i % 5) + 1}.xx.fbcdn.net/v/t39.35426-6/"
                f"perf_test_{i:06d}_{random.randint(100000, 999999)}_{random.randint(1000000000, 9999999999)}_n.jpg"
                f"?_nc_cat={100 + (i % 20)}&oh=hash{i}&oe=timestamp{i}"
            )
            
            content = f"performance_test_image_{i:06d}".encode() + b"x" * (image_size - 50)
            
            test_images.append(CachedImageResource(
                url=url,
                content=content,
                content_type="image/jpeg",
                content_length=len(content),
                timestamp=time.time(),
                source="performance_test"
            ))
        
        # Measure storage performance
        start_time = time.time()
        
        for img in test_images:
            await extractor._store_cached_image(img)
        
        storage_time = time.time() - start_time
        avg_storage_time = storage_time / num_images
        
        # Performance assertions
        assert storage_time < 30.0, f"Storage took too long: {storage_time:.2f}s for {num_images} images"
        assert avg_storage_time < 0.006, f"Average storage time too slow: {avg_storage_time*1000:.2f}ms per image"
        
        # Verify all images were stored
        assert len(extractor._image_cache) == num_images
        
        print(f"📊 STORAGE PERFORMANCE:")
        print(f"   Total time: {storage_time:.2f}s")
        print(f"   Images stored: {num_images:,}")
        print(f"   Rate: {num_images/storage_time:.0f} images/sec")
        print(f"   Avg per image: {avg_storage_time*1000:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_cache_lookup_performance(self, performance_cache):
        """Test performance of cache lookups with URL normalization"""
        extractor = performance_cache
        
        # Pre-populate cache with test images
        num_cached_images = 2000
        cached_urls = []
        
        for i in range(num_cached_images):
            base_id = f"{random.randint(100000, 999999)}_{random.randint(1000000000, 9999999999)}_{random.randint(1000000000000, 9999999999999)}"
            url = (
                f"https://scontent-dfw5-{(i % 5) + 1}.xx.fbcdn.net/v/t39.35426-6/"
                f"{base_id}_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat={100 + (i % 20)}"
                f"&oh=cached{i}&oe=cached{i}"
            )
            
            cached_urls.append((url, base_id))
            
            cached_resource = CachedImageResource(
                url=url,
                content=f"cached_content_{i}".encode(),
                content_type="image/jpeg",
                content_length=100000,
                timestamp=time.time(),
                source="performance_test"
            )
            await extractor._store_cached_image(cached_resource)
        
        # Test lookup performance with URL variations
        num_lookups = 1000
        lookup_times = []
        hits = 0
        
        for i in range(num_lookups):
            # Pick random cached image and create variation
            cached_url, base_id = random.choice(cached_urls)
            
            # Create lookup URL with different parameters
            lookup_url = (
                f"https://scontent-dfw5-{random.randint(1, 5)}.xx.fbcdn.net/v/t39.35426-6/"
                f"{base_id}_n.jpg?stp=dst-jpg_s600x600_tt6&_nc_cat={200 + random.randint(1, 50)}"
                f"&oh=lookup{i}&oe=lookup{i}&_nc_gid=lookup{i}"
            )
            
            # Measure single lookup time
            lookup_start = time.time()
            cached_image = await extractor.get_cached_image(lookup_url)
            lookup_time = time.time() - lookup_start
            
            lookup_times.append(lookup_time)
            if cached_image:
                hits += 1
        
        # Calculate performance metrics
        avg_lookup_time = statistics.mean(lookup_times)
        median_lookup_time = statistics.median(lookup_times)
        p95_lookup_time = sorted(lookup_times)[int(num_lookups * 0.95)]
        hit_rate = hits / num_lookups
        
        # Performance assertions
        assert avg_lookup_time < 0.005, f"Average lookup too slow: {avg_lookup_time*1000:.2f}ms"
        assert p95_lookup_time < 0.010, f"95th percentile too slow: {p95_lookup_time*1000:.2f}ms"
        assert hit_rate > 0.98, f"Hit rate too low: {hit_rate*100:.1f}%"
        
        print(f"📊 LOOKUP PERFORMANCE:")
        print(f"   Cached images: {num_cached_images:,}")
        print(f"   Lookups tested: {num_lookups:,}")
        print(f"   Hit rate: {hit_rate*100:.1f}%")
        print(f"   Avg lookup time: {avg_lookup_time*1000:.2f}ms")
        print(f"   Median lookup time: {median_lookup_time*1000:.2f}ms")
        print(f"   95th percentile: {p95_lookup_time*1000:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_normalization_performance(self, performance_cache):
        """Test performance of URL normalization with various Facebook URL patterns"""
        extractor = performance_cache
        
        # Generate realistic Facebook URL patterns
        test_urls = []
        for i in range(10000):
            patterns = [
                # Standard scontent URLs
                (
                    f"https://scontent-dfw5-{random.randint(1, 9)}.xx.fbcdn.net/v/t39.35426-6/"
                    f"{random.randint(100000, 999999)}_{random.randint(1000000000, 9999999999)}"
                    f"_{random.randint(1000000000000, 9999999999999)}_n.jpg"
                    f"?stp=dst-jpg_s{random.choice([60, 320, 600, 1200])}x{random.choice([60, 320, 600, 1200])}_tt6"
                    f"&_nc_cat={random.randint(100, 120)}&ccb=1-{random.randint(1, 9)}"
                    f"&_nc_sid=cf96c8&_nc_ohc={''.join(random.choices('abcdefABCDEF0123456789', k=20))}"
                    f"&_nc_zt={random.randint(10, 20)}&_nc_ht=scontent-dfw5-{random.randint(1, 9)}.xx"
                    f"&_nc_gid={''.join(random.choices('abcdefABCDEF0123456789', k=20))}"
                    f"&oh=00_{''.join(random.choices('abcdefABCDEF0123456789', k=40))}"
                    f"&oe={''.join(random.choices('abcdef0123456789', k=8))}"
                ),
                # z-m-scontent URLs (mobile)
                (
                    f"https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/"
                    f"{random.randint(100000, 999999)}_{random.randint(1000000000, 9999999999)}"
                    f"_{random.randint(1000000000000, 9999999999999)}_n.jpg"
                    f"?_nc_cat={random.randint(100, 120)}&oh={''.join(random.choices('abcdef0123456789', k=40))}"
                    f"&oe={''.join(random.choices('abcdef0123456789', k=8))}"
                ),
                # Generic scontent URLs  
                (
                    f"https://scontent.xx.fbcdn.net/v/t39.35426-6/"
                    f"{random.randint(100000, 999999)}_{random.randint(1000000000, 9999999999)}"
                    f"_{random.randint(1000000000000, 9999999999999)}_o.jpg"
                    f"?_nc_cat={random.randint(100, 120)}&tp={random.randint(1, 30)}"
                )
            ]
            
            test_urls.append(random.choice(patterns))
        
        # Measure normalization performance
        start_time = time.time()
        normalized_urls = []
        
        for url in test_urls:
            normalized = extractor._normalize_facebook_image_url(url)
            normalized_urls.append(normalized)
        
        normalization_time = time.time() - start_time
        avg_normalization_time = normalization_time / len(test_urls)
        
        # Performance assertions
        assert normalization_time < 5.0, f"Normalization took too long: {normalization_time:.2f}s"
        assert avg_normalization_time < 0.0005, f"Average normalization too slow: {avg_normalization_time*1000:.2f}ms"
        
        # Verify normalization worked
        unique_normalized = len(set(normalized_urls))
        compression_ratio = unique_normalized / len(test_urls)
        
        print(f"📊 NORMALIZATION PERFORMANCE:")
        print(f"   URLs processed: {len(test_urls):,}")
        print(f"   Total time: {normalization_time:.2f}s")
        print(f"   Rate: {len(test_urls)/normalization_time:.0f} URLs/sec")
        print(f"   Avg per URL: {avg_normalization_time*1000:.3f}ms")
        print(f"   Unique normalized: {unique_normalized:,}")
        print(f"   Compression ratio: {compression_ratio:.3f}")
    
    @pytest.mark.asyncio 
    async def test_concurrent_access_performance(self, performance_cache):
        """Test cache performance under concurrent access"""
        extractor = performance_cache
        
        # Pre-populate cache
        num_cached = 1000
        for i in range(num_cached):
            url = f"https://scontent.fbcdn.net/concurrent_test_{i}_123_456_n.jpg"
            cached_resource = CachedImageResource(
                url=url,
                content=f"concurrent_content_{i}".encode(),
                content_type="image/jpeg",
                content_length=50000,
                timestamp=time.time(),
                source="concurrent_test"
            )
            await extractor._store_cached_image(cached_resource)
        
        # Test concurrent lookups
        async def concurrent_lookup_task(task_id: int, num_lookups: int):
            hits = 0
            lookup_times = []
            
            for i in range(num_lookups):
                # Mix of hits and potential misses
                if random.random() < 0.8:  # 80% should hit
                    url = f"https://scontent.fbcdn.net/concurrent_test_{random.randint(0, num_cached-1)}_123_456_n.jpg?variation=task{task_id}_lookup{i}"
                else:  # 20% should miss
                    url = f"https://scontent.fbcdn.net/concurrent_miss_{task_id}_{i}_999_888_n.jpg"
                
                lookup_start = time.time()
                cached_image = await extractor.get_cached_image(url)
                lookup_time = time.time() - lookup_start
                
                lookup_times.append(lookup_time)
                if cached_image:
                    hits += 1
            
            return hits, lookup_times
        
        # Run concurrent tasks
        num_tasks = 10
        lookups_per_task = 100
        
        start_time = time.time()
        tasks = [
            concurrent_lookup_task(task_id, lookups_per_task)
            for task_id in range(num_tasks)
        ]
        results = await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        
        # Analyze results
        total_hits = sum(result[0] for result in results)
        all_lookup_times = []
        for result in results:
            all_lookup_times.extend(result[1])
        
        total_lookups = num_tasks * lookups_per_task
        hit_rate = total_hits / total_lookups
        avg_lookup_time = statistics.mean(all_lookup_times)
        
        # Performance assertions
        assert concurrent_time < 10.0, f"Concurrent test took too long: {concurrent_time:.2f}s"
        assert avg_lookup_time < 0.010, f"Concurrent lookup too slow: {avg_lookup_time*1000:.2f}ms"
        assert hit_rate > 0.75, f"Concurrent hit rate too low: {hit_rate*100:.1f}%"
        
        print(f"📊 CONCURRENT ACCESS PERFORMANCE:")
        print(f"   Concurrent tasks: {num_tasks}")
        print(f"   Lookups per task: {lookups_per_task}")
        print(f"   Total lookups: {total_lookups:,}")
        print(f"   Total time: {concurrent_time:.2f}s")
        print(f"   Throughput: {total_lookups/concurrent_time:.0f} lookups/sec")
        print(f"   Hit rate: {hit_rate*100:.1f}%")
        print(f"   Avg lookup time: {avg_lookup_time*1000:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_memory_efficiency(self, performance_cache):
        """Test memory efficiency of cache storage"""
        extractor = performance_cache
        
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Add known amount of data
        num_images = 1000
        image_size = 100000  # 100KB each = 100MB total content
        
        for i in range(num_images):
            url = f"https://scontent.fbcdn.net/memory_test_{i}_n.jpg"
            content = b"x" * image_size
            
            cached_resource = CachedImageResource(
                url=url,
                content=content,
                content_type="image/jpeg",
                content_length=len(content),
                timestamp=time.time(),
                source="memory_test"
            )
            await extractor._store_cached_image(cached_resource)
        
        # Measure memory after caching
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Calculate expected vs actual memory usage
        expected_content_size = (num_images * image_size) / 1024 / 1024  # MB
        overhead_ratio = memory_increase / expected_content_size
        
        # Memory efficiency assertions
        assert overhead_ratio < 2.0, f"Memory overhead too high: {overhead_ratio:.2f}x"
        assert memory_increase < expected_content_size * 1.5, f"Memory usage excessive: {memory_increase:.1f}MB"
        
        print(f"📊 MEMORY EFFICIENCY:")
        print(f"   Images cached: {num_images:,}")
        print(f"   Content size: {expected_content_size:.1f}MB")
        print(f"   Memory increase: {memory_increase:.1f}MB")
        print(f"   Overhead ratio: {overhead_ratio:.2f}x")
        print(f"   Memory per image: {(memory_increase*1024)/num_images:.1f}KB")


class TestCacheEvictionPerformance:
    """Test performance of cache eviction mechanisms"""
    
    @pytest.mark.asyncio
    async def test_eviction_performance_under_pressure(self):
        """Test cache eviction performance when repeatedly hitting size limits"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        
        # Small cache to force frequent eviction
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=50,  # Small cache
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Add images that will trigger eviction
        num_images = 2000  # Much more than cache can hold
        image_size = 50000  # 50KB each
        
        eviction_times = []
        start_time = time.time()
        
        for i in range(num_images):
            url = f"https://scontent.fbcdn.net/eviction_test_{i}_n.jpg"
            content = b"x" * image_size
            
            cached_resource = CachedImageResource(
                url=url,
                content=content,
                content_type="image/jpeg", 
                content_length=len(content),
                timestamp=time.time(),
                source="eviction_test"
            )
            
            # Measure time for this storage (including potential eviction)
            store_start = time.time()
            await extractor._store_cached_image(cached_resource)
            store_time = time.time() - store_start
            
            eviction_times.append(store_time)
            
            # Log progress periodically
            if i % 500 == 0 and i > 0:
                avg_time = statistics.mean(eviction_times[-100:])  # Recent average
                print(f"   Progress: {i:,} images, recent avg: {avg_time*1000:.2f}ms")
        
        total_time = time.time() - start_time
        avg_eviction_time = statistics.mean(eviction_times)
        p95_eviction_time = sorted(eviction_times)[int(len(eviction_times) * 0.95)]
        
        # Performance assertions
        assert total_time < 60.0, f"Eviction test took too long: {total_time:.1f}s"
        assert avg_eviction_time < 0.010, f"Average eviction too slow: {avg_eviction_time*1000:.2f}ms"
        assert p95_eviction_time < 0.050, f"95th percentile eviction too slow: {p95_eviction_time*1000:.2f}ms"
        
        # Verify cache is within size limits
        stats = extractor.get_cache_stats()
        assert stats['cache_size_mb'] <= 50, f"Cache exceeded size limit: {stats['cache_size_mb']}MB"
        
        print(f"📊 EVICTION PERFORMANCE:")
        print(f"   Images processed: {num_images:,}")
        print(f"   Total time: {total_time:.1f}s")
        print(f"   Rate: {num_images/total_time:.0f} images/sec")
        print(f"   Avg store+evict: {avg_eviction_time*1000:.2f}ms")
        print(f"   95th percentile: {p95_eviction_time*1000:.2f}ms")
        print(f"   Final cache size: {stats['cache_size_mb']:.1f}MB")
        print(f"   Images retained: {stats['cached_images']:,}")


if __name__ == "__main__":
    # Run with verbose output and timing
    pytest.main([__file__, "-v", "-s", "--tb=short"])