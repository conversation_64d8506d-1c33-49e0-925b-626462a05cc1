"""
Integration tests for Facebook CDN cache lookup fix.

These tests validate that the cache miss issue is resolved by testing 
with real Facebook CDN URL patterns and query parameter variations.
"""

import pytest
import asyncio
import time
from unittest.mock import MagicMock, AsyncMock

from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)


class TestRealFacebookURLPatterns:
    """Test with actual Facebook CDN URL patterns observed in production"""
    
    @pytest.fixture
    def cache_extractor(self):
        """Create cache extractor with production-like configuration"""
        config = {
            "camoufox": {
                "image_cache": {
                    "enabled": True,
                    "max_cache_size_mb": 500,
                    "cache_ttl_minutes": 1440  # 24 hours like production
                }
            }
        }
        logger = MagicMock()
        return BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=500,
            cache_ttl_minutes=1440,
            enable_disk_persistence=True
        )
    
    @pytest.mark.asyncio
    async def test_facebook_thumbnail_to_fullsize_lookup(self, cache_extractor):
        """
        Test the main fix: thumbnail cached, fullsize requested.
        
        This was the primary issue causing cache misses in production.
        """
        # Real Facebook thumbnail URL (captured from production)
        thumbnail_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
            "&oe=6732D6F0"
        )
        
        # Cache the thumbnail
        thumbnail_content = b"thumbnail_60x60_content"
        cached_resource = CachedImageResource(
            url=thumbnail_url,
            content=thumbnail_content,
            content_type="image/jpeg",
            content_length=len(thumbnail_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Later request for full-size version (different query params)
        fullsize_lookup_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=DifferentHashValue123&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=CompletelyDifferentGID&oh=00_DifferentOHValue456&oe=DifferentOE789"
        )
        
        # Should find cached thumbnail despite different parameters
        cached_image = await cache_extractor.get_cached_image(fullsize_lookup_url)
        
        assert cached_image is not None, "Cache lookup failed - this was the bug!"
        assert cached_image.content == thumbnail_content
        assert cache_extractor._cache_hits == 1
        assert cache_extractor._cache_misses == 0
        
        # Log the fix for verification
        cache_extractor.log_info("✅ CACHE MISS FIX VERIFIED: Thumbnail found for fullsize request")
    
    @pytest.mark.asyncio
    async def test_multiple_cdn_hosts_same_image(self, cache_extractor):
        """Test same image served from different CDN hosts"""
        # Image cached from one CDN host
        cached_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "460370463_1037577531718719_2663569665313904600_n.jpg"
            "?_nc_cat=104&ccb=1-7&oh=originalHash&oe=originalOE"
        )
        
        cached_content = b"cdn_host_1_content"
        cached_resource = CachedImageResource(
            url=cached_url,
            content=cached_content,
            content_type="image/jpeg",
            content_length=len(cached_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Same image requested from different CDN host
        lookup_urls = [
            # Different numbered CDN host
            (
                "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                "460370463_1037577531718719_2663569665313904600_n.jpg"
                "?_nc_cat=105&ccb=1-7&oh=differentHash&oe=differentOE"
            ),
            # Generic scontent host
            (
                "https://scontent.xx.fbcdn.net/v/t39.35426-6/"
                "460370463_1037577531718719_2663569665313904600_n.jpg" 
                "?_nc_cat=106&oh=anotherHash&oe=anotherOE"
            ),
            # z-m-scontent host
            (
                "https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/"
                "460370463_1037577531718719_2663569665313904600_n.jpg"
                "?_nc_cat=107"
            )
        ]
        
        for lookup_url in lookup_urls:
            cached_image = await cache_extractor.get_cached_image(lookup_url)
            assert cached_image is not None, f"Failed to find cached image for {lookup_url}"
            assert cached_image.content == cached_content
        
        # Should have multiple hits from normalized lookup
        assert cache_extractor._cache_hits == len(lookup_urls)
        assert cache_extractor._cache_misses == 0
    
    @pytest.mark.asyncio
    async def test_time_sensitive_params_ignored(self, cache_extractor):
        """Test that time-sensitive parameters don't prevent cache hits"""
        # Cache image with current timestamp params
        base_url = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "123456789_987654321_1234567890123456789_n.jpg"
        )
        
        original_url = f"{base_url}?oh=hash123&oe=67336685&_nc_gid=GID_OLD&ccb=1-7&ts=1699123456"
        
        cached_content = b"original_timestamped_content" 
        cached_resource = CachedImageResource(
            url=original_url,
            content=cached_content,
            content_type="image/jpeg", 
            content_length=len(cached_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Lookup with updated timestamp parameters (common in production)
        future_lookup_url = f"{base_url}?oh=hash456&oe=67999999&_nc_gid=GID_NEW&ccb=1-8&ts=1699999999"
        
        cached_image = await cache_extractor.get_cached_image(future_lookup_url)
        
        assert cached_image is not None
        assert cached_image.content == cached_content
        
    @pytest.mark.asyncio
    async def test_session_specific_params_ignored(self, cache_extractor):
        """Test that session-specific parameters don't prevent cache hits"""
        base_image_url = (
            "https://scontent.fbcdn.net/v/t39.35426-6/"
            "session_test_555_666_777_n.jpg"
        )
        
        # Cache with session A parameters
        session_a_url = (
            f"{base_image_url}?_nc_sid=sessionA123&_nc_aid=aidA456"
            "&session=sessionTokenA&token=userTokenA&nonce=nonceA789"
        )
        
        cached_content = b"session_independent_content"
        cached_resource = CachedImageResource(
            url=session_a_url,
            content=cached_content,
            content_type="image/jpeg",
            content_length=len(cached_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Lookup with session B parameters  
        session_b_url = (
            f"{base_image_url}?_nc_sid=sessionB999&_nc_aid=aidB888"
            "&session=sessionTokenB&token=userTokenB&nonce=nonceB111"
        )
        
        cached_image = await cache_extractor.get_cached_image(session_b_url)
        
        assert cached_image is not None
        assert cached_image.content == cached_content
    
    @pytest.mark.asyncio
    async def test_quality_and_size_variations(self, cache_extractor):
        """Test lookup across different quality/size parameters"""
        base_image_url = (
            "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
            "quality_test_888_999_111_n.jpg"
        )
        
        # Cache original quality version
        original_url = f"{base_image_url}?stp=dst-jpg_tt6&q=75"
        
        cached_content = b"original_quality_content"
        cached_resource = CachedImageResource(
            url=original_url, 
            content=cached_content,
            content_type="image/jpeg",
            content_length=len(cached_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Lookup with various quality/size variations
        variations = [
            f"{base_image_url}?stp=dst-jpg_s60x60_tt6&q=50",      # Thumbnail, lower quality
            f"{base_image_url}?stp=dst-jpg_s600x600_tt6&q=90",    # Fullsize, higher quality 
            f"{base_image_url}?stp=dst-jpg_s1200x1200_tt6&q=95",  # Large, highest quality
            f"{base_image_url}?w=400&h=400&quality=80",           # Different param names
        ]
        
        for variation_url in variations:
            cached_image = await cache_extractor.get_cached_image(variation_url)
            assert cached_image is not None, f"Failed lookup for quality variation: {variation_url}"
            assert cached_image.content == cached_content


class TestCacheMissReasonTracking:
    """Test detailed tracking of cache miss reasons for analytics"""
    
    @pytest.fixture
    def cache_extractor(self):
        """Cache extractor for miss tracking tests"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        return BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=30,
            enable_disk_persistence=False
        )
    
    @pytest.mark.asyncio
    async def test_miss_reason_not_cached(self, cache_extractor):
        """Test tracking when image was never cached"""
        url = "https://scontent.fbcdn.net/v/t39.35426-6/never_cached_123_456_n.jpg"
        
        cached_image = await cache_extractor.get_cached_image(url)
        
        assert cached_image is None
        assert cache_extractor._cache_miss_reasons['not_cached'] == 1
        
    @pytest.mark.asyncio
    async def test_miss_reason_expired(self, cache_extractor):
        """Test tracking when cached image has expired"""
        url = "https://scontent.fbcdn.net/v/t39.35426-6/expired_test_789_n.jpg"
        
        # Cache image with old timestamp 
        old_timestamp = time.time() - 7200  # 2 hours ago
        cached_resource = CachedImageResource(
            url=url,
            content=b"expired_content",
            content_type="image/jpeg",
            content_length=15,
            timestamp=old_timestamp,
            source="test"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # Manually set timestamp to simulate expiration
        cache_extractor._image_cache[url].timestamp = old_timestamp
        
        cached_image = await cache_extractor.get_cached_image(url)
        
        assert cached_image is None
        assert cache_extractor._cache_miss_reasons['expired'] == 1
    
    @pytest.mark.asyncio
    async def test_miss_reason_normalization_mismatch(self, cache_extractor):
        """Test tracking normalization issues (should be rare with fix)"""
        # This test verifies our normalization is working
        # If normalization fails, we'd get mismatches
        
        cached_url = "https://scontent.fbcdn.net/v/t39.35426-6/norm_test_456_n.jpg?_nc_cat=101"
        lookup_url = "https://scontent.fbcdn.net/v/t39.35426-6/norm_test_456_n.jpg?_nc_cat=999"
        
        cached_resource = CachedImageResource(
            url=cached_url,
            content=b"normalization_test",
            content_type="image/jpeg", 
            content_length=18,
            timestamp=time.time(),
            source="test"
        )
        await cache_extractor._store_cached_image(cached_resource)
        
        # This should hit due to normalization, not miss
        cached_image = await cache_extractor.get_cached_image(lookup_url)
        
        assert cached_image is not None, "Normalization should allow this hit"
        assert cache_extractor._cache_miss_reasons.get('normalization_mismatch', 0) == 0


class TestProductionScenarioSimulation:
    """Simulate real production scenarios that were causing cache misses"""
    
    @pytest.fixture
    async def production_cache_setup(self):
        """Set up cache with production-like image collection"""
        config = {
            "camoufox": {
                "image_cache": {
                    "enabled": True,
                    "max_cache_size_mb": 500,
                    "cache_ttl_minutes": 1440
                }
            }
        }
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=500,
            cache_ttl_minutes=1440,
            enable_disk_persistence=False
        )
        
        # Simulate images cached during initial GraphQL response capture
        production_images = [
            {
                "url": (
                    "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                    "447181435_1029863199156819_7025413177941515490_n.jpg"
                    "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
                    "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
                    "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
                    "&oe=6732D6F0"
                ),
                "content": b"production_thumbnail_1",
                "ad_id": "ad_001"
            },
            {
                "url": (
                    "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                    "460370463_1037577531718719_2663569665313904600_n.jpg"
                    "?stp=dst-jpg_s60x60_tt6&_nc_cat=104&ccb=1-7&_nc_sid=cf96c8"
                    "&_nc_ohc=Wt2F9xQvCFAQ7kNvgHnVJJg&_nc_zt=14&_nc_ht=scontent-dfw5-2.xx"
                    "&_nc_gid=AgVRJ-RFJPxXfYXiVHBu6Xf&oh=00_AYDU7Xf9e0pX_1BUQKxSJ9fqQDSczKPv9B17FJU-RaLBSg"
                    "&oe=67336685"
                ),
                "content": b"production_thumbnail_2", 
                "ad_id": "ad_002"
            },
            {
                "url": (
                    "https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/"
                    "515043210_987654321_1122334455667788_n.jpg"
                    "?_nc_cat=110&oh=mobileHash&oe=mobileOE"
                ),
                "content": b"production_mobile_image",
                "ad_id": "ad_003"
            }
        ]
        
        for img in production_images:
            cached_resource = CachedImageResource(
                url=img["url"],
                content=img["content"],
                content_type="image/jpeg",
                content_length=len(img["content"]),
                timestamp=time.time(),
                ad_archive_id=img["ad_id"],
                source="browser_cache"
            )
            await extractor._store_cached_image(cached_resource)
        
        return extractor, production_images
    
    @pytest.mark.asyncio
    async def test_image_processing_workflow_cache_hits(self, production_cache_setup):
        """Test that image processing workflow gets cache hits after fix"""
        extractor, cached_images = production_cache_setup
        
        # Simulate image processing requests that come later with different params
        processing_requests = [
            {
                "lookup_url": (
                    "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                    "447181435_1029863199156819_7025413177941515490_n.jpg"
                    "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
                    "&_nc_ohc=NewHashValue&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
                    "&_nc_gid=NewGIDValue&oh=00_NewOHValue&oe=NewOEValue"
                ),
                "expected_content": b"production_thumbnail_1"
            },
            {
                "lookup_url": (
                    "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                    "460370463_1037577531718719_2663569665313904600_n.jpg"
                    "?stp=dst-jpg_s1200x1200_tt6&_nc_cat=999&ccb=2-8&_nc_sid=different"
                    "&_nc_ohc=CompleteDiff&_nc_zt=99&_nc_ht=scontent-dfw5-2.xx"
                    "&_nc_gid=TotallyNew&oh=00_BrandNew&oe=AllNew"
                ),
                "expected_content": b"production_thumbnail_2"
            },
            {
                "lookup_url": (
                    "https://scontent.xx.fbcdn.net/v/t39.35426-6/"
                    "515043210_987654321_1122334455667788_n.jpg"
                    "?_nc_cat=888&oh=desktopHash&oe=desktopOE&stp=dst-jpg_tt6"
                ),
                "expected_content": b"production_mobile_image"
            }
        ]
        
        hits = 0
        misses = 0
        
        for request in processing_requests:
            cached_image = await extractor.get_cached_image(request["lookup_url"])
            
            if cached_image:
                hits += 1
                assert cached_image.content == request["expected_content"]
                extractor.log_info(f"✅ CACHE HIT: {request['lookup_url'][:80]}...")
            else:
                misses += 1
                extractor.log_error(f"❌ CACHE MISS: {request['lookup_url'][:80]}...")
        
        # All requests should hit with the fix
        assert hits == len(processing_requests), f"Expected {len(processing_requests)} hits, got {hits}"
        assert misses == 0, f"Expected 0 misses, got {misses}"
        
        # Verify cache statistics
        stats = extractor.get_cache_stats()
        assert stats['hit_rate_percent'] == 100.0
        assert stats['cache_hits'] == len(processing_requests)
        assert stats['cache_misses'] == 0
    
    @pytest.mark.asyncio
    async def test_bandwidth_savings_calculation(self, production_cache_setup):
        """Test calculation of bandwidth savings from cache hits"""
        extractor, cached_images = production_cache_setup
        
        # Simulate bandwidth logging for cache hits
        total_saved_bytes = 0
        
        for cached_img in cached_images:
            # Simulate looking up each cached image (would have been downloaded otherwise)
            lookup_url = cached_img["url"].replace("_nc_cat=101", "_nc_cat=999")
            cached_image = await extractor.get_cached_image(lookup_url)
            
            if cached_image:
                total_saved_bytes += cached_image.content_length
        
        stats = extractor.get_cache_stats()
        
        assert stats['total_saved_bytes'] == total_saved_bytes
        assert stats['total_saved_mb'] > 0
        assert stats['cache_hits'] == len(cached_images)
        
        # Log bandwidth savings
        extractor.log_info(
            f"💰 BANDWIDTH SAVED: {stats['total_saved_mb']} MB "
            f"({stats['cache_hits']} cache hits, {stats['hit_rate_percent']}% hit rate)"
        )


class TestCacheFixValidation:
    """Validate that the specific cache miss issue has been fixed"""
    
    @pytest.mark.asyncio
    async def test_original_bug_scenario_fixed(self):
        """Test the exact scenario that was causing cache misses before the fix"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=100,
            cache_ttl_minutes=1440,
            enable_disk_persistence=False
        )
        
        # BEFORE FIX: This scenario would result in cache miss
        
        # Step 1: Image gets cached during GraphQL response with thumbnail params
        cached_during_graphql = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
            "&oe=6732D6F0"
        )
        
        thumbnail_content = b"this_was_cached_during_graphql_response"
        cached_resource = CachedImageResource(
            url=cached_during_graphql,
            content=thumbnail_content,
            content_type="image/jpeg",
            content_length=len(thumbnail_content),
            timestamp=time.time(),
            source="browser_cache"
        )
        await extractor._store_cached_image(cached_resource)
        
        # Step 2: Later, image processing requests full-size with different params
        requested_during_processing = (
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
            "447181435_1029863199156819_7025413177941515490_n.jpg"
            "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
            "&_nc_ohc=DifferentOHValue&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
            "&_nc_gid=DifferentGIDValue&oh=00_DifferentOHValue&oe=DifferentOEValue"
        )
        
        # AFTER FIX: This should now be a cache hit
        cached_image = await extractor.get_cached_image(requested_during_processing)
        
        # THE FIX VALIDATION
        assert cached_image is not None, (
            "❌ CACHE MISS BUG NOT FIXED! "
            "Thumbnail cached but fullsize lookup failed. "
            "URL normalization is not working correctly."
        )
        
        assert cached_image.content == thumbnail_content, (
            "❌ Wrong content returned from cache"
        )
        
        assert extractor._cache_hits == 1, "Cache hit not recorded"
        assert extractor._cache_misses == 0, "Should be 0 cache misses"
        
        # Success message 
        extractor.log_info(
            "🎉 CACHE MISS BUG FIXED! "
            f"Thumbnail (stp=dst-jpg_s60x60_tt6) successfully found "
            f"when requesting fullsize (stp=dst-jpg_s600x600_tt6)"
        )
        
    @pytest.mark.asyncio
    async def test_cache_miss_reason_before_and_after_fix(self):
        """Test that miss reasons change after applying the fix"""
        config = {"camoufox": {"image_cache": {"enabled": True}}}
        logger = MagicMock()
        
        # Simulate OLD behavior (without normalization fix)
        old_extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Cache an image
        cached_url = "https://scontent.fbcdn.net/image_123_456_n.jpg?_nc_cat=101&oh=hash1"
        cached_resource = CachedImageResource(
            url=cached_url,
            content=b"cached_content",
            content_type="image/jpeg",
            content_length=14,
            timestamp=time.time(),
            source="test"
        )
        
        # Manually add to cache (simulating no normalization index)
        old_extractor._image_cache[cached_url] = cached_resource
        # Don't add to normalized index to simulate old behavior
        
        # Look up with different params (would miss before fix)
        lookup_url = "https://scontent.fbcdn.net/image_123_456_n.jpg?_nc_cat=999&oh=hash2"
        
        # In old version, this would miss because no normalized index
        # Simulate by direct exact lookup only
        cached_image = old_extractor._image_cache.get(lookup_url)
        if not cached_image:
            old_extractor._cache_misses += 1
            old_extractor._cache_miss_reasons['not_cached'] = 1
        
        # NEW behavior (with normalization fix) 
        new_extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=False
        )
        
        # Add same image to new extractor (with normalization)
        await new_extractor._store_cached_image(cached_resource)
        
        # Same lookup should now hit
        cached_image = await new_extractor.get_cached_image(lookup_url)
        
        # Verify fix
        assert old_extractor._cache_misses == 1, "Old behavior should miss"
        assert old_extractor._cache_miss_reasons['not_cached'] == 1
        
        assert new_extractor._cache_hits == 1, "New behavior should hit"
        assert new_extractor._cache_misses == 0, "New behavior should not miss"
        assert cached_image is not None, "Should find cached image with fix"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])