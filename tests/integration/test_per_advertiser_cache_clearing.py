"""
Integration tests for per-advertiser cache clearing workflow.

Tests that the image cache is properly cleared between advertisers to prevent
cross-advertiser cache contamination, ensuring each advertiser starts with
a fresh empty cache.
"""

import asyncio
import pytest
import logging
from unittest.mock import AsyncMock, MagicMock, patch

from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)


class MockPlaywrightPage:
    """Mock Playwright page for testing"""
    
    def __init__(self):
        self.response_handlers = []
        
    def on(self, event: str, handler):
        """Register event handler"""
        if event == "response":
            self.response_handlers.append(handler)
    
    async def trigger_response(self, url: str, status: int = 200, content_type: str = "image/jpeg", content: bytes = None):
        """Simulate a response for testing"""
        response = MockResponse(url, status, content_type, content or b"fake_image_data")
        for handler in self.response_handlers:
            await handler(response)


class MockResponse:
    """Mock Playwright response"""
    
    def __init__(self, url: str, status: int, content_type: str, content: bytes):
        self.url = url
        self.status = status
        self.headers = {"content-type": content_type}
        self._content = content
        
    async def body(self):
        return self._content


@pytest.fixture
def mock_logger():
    """Create a mock logger for testing"""
    return MagicMock(spec=logging.Logger)


@pytest.fixture
def cache_extractor(mock_logger):
    """Create a BrowserImageCacheExtractor for testing"""
    config = {
        'enable_bandwidth_logging': False,
        'data_dir': './test_data'
    }
    
    return BrowserImageCacheExtractor(
        logger=mock_logger,
        config=config,
        max_cache_size_mb=100,
        cache_ttl_minutes=30,
        enable_disk_persistence=False  # Disabled for per-advertiser workflow
    )


@pytest.mark.asyncio
async def test_cache_cleared_between_advertisers(cache_extractor):
    """Test that cache is cleared between different advertisers"""
    
    # Simulate first advertiser processing
    page1 = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page1)
    
    # Populate cache with first advertiser's images
    await page1.trigger_response("https://facebook.com/advertiser1/image1.jpg", content=b"advertiser1_image1")
    await page1.trigger_response("https://facebook.com/advertiser1/image2.jpg", content=b"advertiser1_image2")
    
    # Verify cache has images from first advertiser
    assert len(cache_extractor._image_cache) == 2
    assert cache_extractor._cache_hits == 0
    assert cache_extractor._cache_misses == 0
    
    # Simulate some cache usage
    cached_image1 = await cache_extractor.get_cached_image("https://facebook.com/advertiser1/image1.jpg")
    assert cached_image1 is not None
    assert cache_extractor._cache_hits == 1
    
    # CRITICAL: Clear cache between advertisers (simulating the fix)
    await cache_extractor.clear_cache()
    
    # Verify cache is completely empty and statistics are reset
    assert len(cache_extractor._image_cache) == 0
    assert cache_extractor._current_cache_size == 0
    assert cache_extractor._cache_hits == 0
    assert cache_extractor._cache_misses == 0
    assert len(cache_extractor._normalized_url_index) == 0
    assert cache_extractor._cache_miss_reasons == {}
    
    # Simulate second advertiser processing
    page2 = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page2)
    
    # Populate cache with second advertiser's images
    await page2.trigger_response("https://facebook.com/advertiser2/image1.jpg", content=b"advertiser2_image1")
    await page2.trigger_response("https://facebook.com/advertiser2/image2.jpg", content=b"advertiser2_image2")
    
    # Verify cache only has second advertiser's images
    assert len(cache_extractor._image_cache) == 2
    
    # Verify no cross-contamination - first advertiser's images should not be found
    cached_image_old = await cache_extractor.get_cached_image("https://facebook.com/advertiser1/image1.jpg")
    assert cached_image_old is None  # Should be cache miss
    assert cache_extractor._cache_misses == 1
    
    # Verify second advertiser's images are available
    cached_image_new = await cache_extractor.get_cached_image("https://facebook.com/advertiser2/image1.jpg")
    assert cached_image_new is not None
    assert cache_extractor._cache_hits == 1


@pytest.mark.asyncio
async def test_cache_statistics_reset_properly(cache_extractor):
    """Test that all cache statistics are properly reset when clearing cache"""
    
    # Setup initial cache state
    page = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page)
    
    # Populate cache and generate statistics
    await page.trigger_response("https://facebook.com/test/image1.jpg", content=b"test_image1")
    await page.trigger_response("https://facebook.com/test/image2.jpg", content=b"test_image2")
    
    # Generate some hits and misses
    await cache_extractor.get_cached_image("https://facebook.com/test/image1.jpg")  # Hit
    await cache_extractor.get_cached_image("https://facebook.com/test/nonexistent.jpg")  # Miss
    
    # Verify statistics are populated
    assert cache_extractor._cache_hits > 0
    assert cache_extractor._cache_misses > 0
    assert cache_extractor._total_saved_bytes > 0
    assert len(cache_extractor._cache_miss_reasons) > 0
    
    # Clear cache
    await cache_extractor.clear_cache()
    
    # Verify all statistics are reset to zero
    assert cache_extractor._cache_hits == 0
    assert cache_extractor._cache_misses == 0
    assert cache_extractor._total_saved_bytes == 0
    assert cache_extractor._cache_miss_reasons == {}
    assert cache_extractor._url_collision_count == 0
    assert cache_extractor._normalization_stats == {'successful': 0, 'failed': 0}


@pytest.mark.asyncio
async def test_no_disk_persistence_prevents_cross_session_contamination(cache_extractor):
    """Test that disabled disk persistence prevents cross-session cache contamination"""
    
    # Verify disk persistence is disabled
    assert cache_extractor._enable_disk_persistence == False
    
    # Populate cache
    page = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page)
    await page.trigger_response("https://facebook.com/test/image1.jpg", content=b"test_image1")
    
    # Verify cache has content
    assert len(cache_extractor._image_cache) == 1
    
    # Clear cache (simulating end of advertiser processing)
    await cache_extractor.clear_cache()
    
    # Verify cache is empty
    assert len(cache_extractor._image_cache) == 0
    
    # Create new cache extractor instance (simulating new session)
    config = {
        'enable_bandwidth_logging': False,
        'data_dir': './test_data'
    }
    
    new_cache_extractor = BrowserImageCacheExtractor(
        logger=MagicMock(spec=logging.Logger),
        config=config,
        max_cache_size_mb=100,
        cache_ttl_minutes=30,
        enable_disk_persistence=False
    )
    
    # Verify new instance starts with empty cache (no disk loading)
    assert len(new_cache_extractor._image_cache) == 0
    assert new_cache_extractor._cache_hits == 0
    assert new_cache_extractor._cache_misses == 0


@pytest.mark.asyncio
async def test_cache_clearing_logging(cache_extractor, mock_logger):
    """Test that cache clearing produces appropriate logging"""
    
    # Populate cache with some data
    page = MockPlaywrightPage()
    await cache_extractor.setup_image_interception(page)
    await page.trigger_response("https://facebook.com/test/image1.jpg", content=b"test_image_data")
    
    # Generate some statistics
    await cache_extractor.get_cached_image("https://facebook.com/test/image1.jpg")  # Hit
    
    # Clear cache
    await cache_extractor.clear_cache()
    
    # Verify appropriate logging calls were made
    log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
    
    # Should have logged cache clearing
    cache_clear_logs = [log for log in log_calls if "CACHE CLEARED for new advertiser" in log]
    assert len(cache_clear_logs) > 0
    
    # Should have logged statistics reset
    stats_reset_logs = [log for log in log_calls if "STATISTICS RESET" in log]
    assert len(stats_reset_logs) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
