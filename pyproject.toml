[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "lexgenius"
version = "0.1.0"
description = "Legal intelligence platform monitoring Facebook ads and court dockets"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "LexGenius Team"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Legal Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]
requires-python = ">=3.11"
dependencies = [
    "requests>=2.32.3",
    "pandas>=2.2.3",
    "selenium>=4.28.1",
    "python-dotenv>=1.0.1",
    "beautifulsoup4>=4.13.3",
    "tqdm>=4.67.1",
    "webdriver-manager>=3.8.5",
    "pydantic>=2.10.6",
    "dependency-injector>=4.41.0",
    "asyncio",
    "aiofiles",
    "boto3",
    "openai",
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.16.0",
    "ruff>=0.1.0",
    "pre-commit>=3.0.0",
    "pytest>=8.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=6.1.0",
    "pytest-timeout>=2.3.0",
    "pytest-xdist>=3.6.0",
    "pytest-mock>=3.12.0",
]
test = [
    "pytest>=8.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=6.1.0",
    "pytest-timeout>=2.3.0",
    "pytest-xdist>=3.6.0",
    "pytest-mock>=3.12.0",
    "responses>=0.25.0",
    "moto>=5.0.0",
]

[project.scripts]
lexgenius = "src.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

# Black configuration
[tool.black]
line-length = 88
target-version = ["py311"]
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | archive
)/
'''

# isort configuration  
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = [
    "pytest",
    "pandas",
    "selenium",
    "requests",
    "boto3",
    "openai",
    "dependency_injector",
    "pydantic",
    "tqdm",
    "dotenv",
    "beautifulsoup4",
]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# Ruff configuration (modern, fast linter)
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "ARG001", # unused-function-argument
    "SIM118", # key-in-dict
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "B904",  # raise from
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "archive",
]

[tool.ruff.per-file-ignores]
"tests/**/*" = ["ARG001", "S101"]
"src/scripts/**/*" = ["T201"]  # Allow print statements in scripts

# MyPy configuration
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false  # Gradually enable this
disallow_incomplete_defs = false  # Gradually enable this
check_untyped_defs = true
disallow_untyped_decorators = false  # Gradually enable this
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "archive/",
    "build/",
    "dist/",
    "venv/",
    ".venv/",
]

[[tool.mypy.overrides]]
module = [
    "selenium.*",
    "webdriver_manager.*",
    "boto3.*",
    "botocore.*",
    "pandas.*",
    "tqdm.*",
    "requests.*",
    "beautifulsoup4.*",
    "dependency_injector.*",
]
ignore_missing_imports = true

# Pytest configuration (extends existing pytest.ini)
[tool.pytest.ini_options]
minversion = "8.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=70",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow tests",
    "requires_aws: Tests requiring AWS credentials",
    "requires_pacer: Tests requiring PACER credentials",
    "smoke: Smoke tests",
]
asyncio_mode = "auto"
timeout = 300
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/archive/*",
    "*/deprecated-src/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false

# Bandit security configuration
[tool.bandit]
exclude_dirs = ["tests", "archive", ".venv", "venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_subprocess_popen
tests = ["B201", "B301", "B401", "B501", "B506", "B601", "B602", "B603", "B604", "B605", "B606", "B607", "B608", "B609"]