#!/usr/bin/env python3
"""
Test the AFFF processor with real data to show the interactive prompting works
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from afff_case_shell_processor import AFFFCaseShellProcessor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_real_afff_processing():
    """Test the AFFF processor with real data but mock S3 operations"""
    
    print("🧪 Testing AFFF processor with real Unknown attorneys")
    print("=" * 60)
    
    # Create processor
    processor = AFFFCaseShellProcessor("20250716", force=False)
    
    # Load the real processed data to find Unknown attorneys
    processed_file = Path("scripts/analysis/AFFF/output/afff_processed_20250716.json")
    
    if not processed_file.exists():
        print("❌ Processed file not found. Run the real processor first.")
        return
    
    with open(processed_file, 'r') as f:
        data = json.load(f)
    
    # Find entries with Unknown law firms
    unknown_entries = [entry for entry in data if entry.get('law_firm') == 'Unknown']
    
    print(f"📋 Found {len(unknown_entries)} entries with Unknown law firms:")
    for entry in unknown_entries[:5]:  # Show first 5
        print(f"  - {entry.get('attorney', 'N/A')}: {entry.get('versus', 'N/A')}")
    
    if not unknown_entries:
        print("✅ No Unknown attorneys found - they may have been fixed already!")
        return
    
    # Initialize services
    await processor.initialize_services()
    
    # Test with a small HTML snippet that includes Unknown attorneys
    test_html = f"""
    <table>
    <tr><td width="94" valign="top" nowrap="">07/16/2025</td><td valign="top"><a href="https://example.com/doc1">5556</a></td><td valign="top">COMPLAINT Anita Powers vs 3M Company, et al. Charleston Division. (Filing fee $ 405 receipt number) (Attachments: # 1 Summons) John Raggio) Assigned AFFF_5556. Modified on 7/16/2025 (cwil, ). (Entered: 07/16/2025)</td></tr>
    <tr><td width="94" valign="top" nowrap="">07/16/2025</td><td valign="top"><a href="https://example.com/doc2">5557</a></td><td valign="top">COMPLAINT Judith Mason vs 3M Company, et al. Charleston Division. (Filing fee $ 405 receipt number) (Attachments: # 1 Summons) Coral Odiot) Assigned AFFF_5557. Modified on 7/16/2025 (cwil, ). (Entered: 07/16/2025)</td></tr>
    </table>
    """
    
    print(f"\n🔍 Testing with mock HTML data...")
    print("This will demonstrate the interactive prompting for Unknown attorneys.")
    print("NOTE: This is just a demonstration - we won't actually upload to DynamoDB.")
    
    try:
        # Process the mock HTML data
        success = await processor.process_afff_data(use_local_file=None)
        
        if success:
            print(f"\n✅ Processing completed successfully!")
            print(f"🗂️ Session updates: {processor.session_attorney_updates}")
        else:
            print(f"\n❌ Processing failed")
            
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        logger.exception("Processing error")
    
    finally:
        await processor.cleanup()

if __name__ == "__main__":
    asyncio.run(test_real_afff_processing())