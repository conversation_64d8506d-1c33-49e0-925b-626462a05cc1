#!/usr/bin/env python3
"""
Test script to verify Camoufox session fix for token extraction race condition.

This test ensures that:
1. Token extraction is protected by operation tracking
2. Cleanup waits for pending operations
3. Retry logic works for failed extractions

Run with: python test_camoufox_session_fix.py
"""

import asyncio
import logging
import sys
from unittest.mock import Mock, patch, AsyncMock

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_token_extraction_protection():
    """Test that token extraction is protected by operation tracking."""
    print("\n🧪 TEST 1: Token Extraction Protection")
    print("=" * 50)
    
    # Import the fixed session manager
    from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
    
    # Create mock config and logger
    config = {
        'camoufox': {
            'headless': True,
            'viewport': {'width': 1920, 'height': 1080},
            'browser_timeout': 30000
        }
    }
    
    # Create session manager instance
    session_manager = CamoufoxSessionManager(
        logger=logger,
        bandwidth_logger=Mock(),
        config=config
    )
    
    # Mock the browser page
    mock_page = AsyncMock()
    mock_page.evaluate = AsyncMock(return_value={
        'fb_dtsg': 'test_token_123',
        'lsd': 'test_lsd',
        'jazoest': '12345',
        'page_url': 'https://facebook.com',
        'user_agent': 'Test Browser'
    })
    
    session_manager.page = mock_page
    
    # Track if operation tracking was used
    original_track_operation = session_manager._track_operation
    track_operation_called = False
    operation_ids = []
    
    async def mock_track_operation(op_id):
        nonlocal track_operation_called, operation_ids
        track_operation_called = True
        operation_ids.append(op_id)
        async with original_track_operation(op_id):
            yield
    
    session_manager._track_operation = mock_track_operation
    
    # Test token extraction
    try:
        tokens = await session_manager._extract_tokens()
        
        # Verify results
        assert track_operation_called, "❌ Operation tracking was NOT used!"
        assert "extract_tokens" in operation_ids or "extract_tokens_recovery" in operation_ids, \
            f"❌ Wrong operation ID used: {operation_ids}"
        assert tokens.get('fb_dtsg') == 'test_token_123', "❌ Token extraction failed"
        
        print("✅ Token extraction is properly protected with operation tracking")
        print(f"✅ Operation IDs tracked: {operation_ids}")
        print(f"✅ Tokens extracted: {list(tokens.keys())}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


async def test_retry_logic():
    """Test that token extraction has retry logic."""
    print("\n🧪 TEST 2: Token Extraction Retry Logic")
    print("=" * 50)
    
    from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
    
    # Create session manager
    config = {'camoufox': {'browser_timeout': 30000}}
    session_manager = CamoufoxSessionManager(
        logger=logger,
        bandwidth_logger=Mock(),
        config=config
    )
    
    # Mock page that fails twice then succeeds
    attempt_count = 0
    async def mock_evaluate(script):
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise Exception(f"Simulated failure {attempt_count}")
        return {
            'fb_dtsg': 'success_after_retries',
            'lsd': 'test_lsd'
        }
    
    mock_page = AsyncMock()
    mock_page.evaluate = mock_evaluate
    session_manager.page = mock_page
    
    # Disable operation tracking for this test
    async def mock_track_operation(op_id):
        yield
    session_manager._track_operation = mock_track_operation
    
    # Test extraction with retries
    try:
        tokens = await session_manager._extract_tokens()
        
        assert attempt_count == 3, f"❌ Expected 3 attempts, got {attempt_count}"
        assert tokens.get('fb_dtsg') == 'success_after_retries', "❌ Retry logic didn't work"
        
        print(f"✅ Retry logic works: succeeded after {attempt_count} attempts")
        print(f"✅ Final tokens: {list(tokens.keys())}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


async def test_cleanup_waits_for_operations():
    """Test that cleanup waits for pending operations."""
    print("\n🧪 TEST 3: Cleanup Waits for Pending Operations")
    print("=" * 50)
    
    from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
    
    # Create session manager
    config = {'camoufox': {'browser_timeout': 30000}}
    session_manager = CamoufoxSessionManager(
        logger=logger,
        bandwidth_logger=Mock(),
        config=config
    )
    
    # Simulate a pending operation
    async with session_manager._operation_lock:
        session_manager._pending_operations.add("test_operation")
    
    # Track cleanup behavior
    wait_called = False
    original_wait = session_manager._wait_for_pending_operations
    
    async def mock_wait():
        nonlocal wait_called
        wait_called = True
        # Simulate operation completion
        async with session_manager._operation_lock:
            session_manager._pending_operations.clear()
        return await original_wait()
    
    session_manager._wait_for_pending_operations = mock_wait
    
    # Test cleanup
    try:
        await session_manager.cleanup()
        
        assert wait_called, "❌ Cleanup did NOT wait for pending operations!"
        assert len(session_manager._pending_operations) == 0, "❌ Operations not cleared"
        
        print("✅ Cleanup properly waits for pending operations")
        print("✅ All operations completed before cleanup proceeded")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 CAMOUFOX SESSION FIX VERIFICATION")
    print("=" * 60)
    print("Testing the fix for 'Page.evaluate: Target page, context or browser has been closed'")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Token extraction protection
    try:
        result = await test_token_extraction_protection()
        test_results.append(("Token Extraction Protection", result))
    except Exception as e:
        print(f"❌ Token extraction protection test failed: {e}")
        test_results.append(("Token Extraction Protection", False))
    
    # Test 2: Retry logic
    try:
        result = await test_retry_logic()
        test_results.append(("Retry Logic", result))
    except Exception as e:
        print(f"❌ Retry logic test failed: {e}")
        test_results.append(("Retry Logic", False))
    
    # Test 3: Cleanup coordination
    try:
        result = await test_cleanup_waits_for_operations()
        test_results.append(("Cleanup Coordination", result))
    except Exception as e:
        print(f"❌ Cleanup coordination test failed: {e}")
        test_results.append(("Cleanup Coordination", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The Camoufox session fix is working correctly.")
        print("\n✅ VERIFIED FIXES:")
        print("   • Token extraction is protected by operation tracking")
        print("   • Retry logic handles transient failures")
        print("   • Cleanup waits for pending operations")
        print("\n💡 The race condition has been eliminated!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    try:
        # For Python 3.7+ compatibility
        if sys.version_info >= (3, 7):
            success = asyncio.run(main())
        else:
            loop = asyncio.get_event_loop()
            success = loop.run_until_complete(main())
        
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)