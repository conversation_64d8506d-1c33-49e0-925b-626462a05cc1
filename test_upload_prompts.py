#!/usr/bin/env python3
"""
Test that the AFFF processor prompts for unknown attorneys during upload workflow
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from unittest.mock import AsyncMock, patch

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from afff_case_shell_processor import AFFFCaseShellProcessor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

async def test_upload_workflow_prompts():
    """Test that upload workflow prompts for unknown attorneys"""
    
    print("🧪 Testing AFFF upload workflow with Unknown attorneys")
    print("=" * 60)
    
    # Create processor
    processor = AFFFCaseShellProcessor("20250716", force=False)
    
    # Mock the DynamoDB upload to prevent actual uploads
    async def mock_upload_to_dynamodb(self, processed_data):
        print(f"🔄 Mock DynamoDB upload called with {len(processed_data)} records")
        return True
    
    # Mock the prompting to simulate user responses
    prompt_calls = []
    def mock_prompt_for_law_firm(attorney_name):
        prompt_calls.append(attorney_name)
        print(f"🔍 PROMPT TRIGGERED: {attorney_name}")
        
        # Simulate user responses
        if attorney_name == "John Raggio":
            return "Raggio Law Firm LLC"
        elif attorney_name == "Coral Odiot":
            return "Odiot & Associates"
        elif attorney_name == "Christopher Randolph":
            return "Unknown"  # User hits enter
        else:
            return "Test Law Firm"
    
    # Apply mocks
    processor._prompt_for_law_firm = mock_prompt_for_law_firm
    processor.upload_to_dynamodb = lambda data: mock_upload_to_dynamodb(processor, data)
    
    # Mock file save to avoid actual file changes
    file_saves = []
    def mock_save_file(data, filepath):
        file_saves.append((filepath, len(data)))
        print(f"📝 Mock file save: {filepath} with {len(data)} records")
    
    # Load real processed data to test with
    processed_file = Path("scripts/analysis/AFFF/output/afff_processed_20250716.json")
    
    if not processed_file.exists():
        print("❌ No processed file found - run processor first")
        return
    
    with open(processed_file, 'r') as f:
        processed_data = json.load(f)
    
    # Show Unknown attorneys before processing
    unknown_before = [entry for entry in processed_data if entry.get('law_firm') == 'Unknown']
    print(f"📋 Found {len(unknown_before)} Unknown attorneys before upload:")
    for entry in unknown_before:
        print(f"  - {entry.get('attorney', 'N/A')}")
    
    # Initialize services without browser (we won't scrape)
    await processor.initialize_services()
    
    try:
        print(f"\n🚀 Testing upload workflow...")
        
        # Call the upload method which should trigger prompts
        await processor._handle_unknown_attorneys_during_upload(processed_data)
        
        # Check results
        unknown_after = [entry for entry in processed_data if entry.get('law_firm') == 'Unknown']
        
        print(f"\n📊 Results:")
        print(f"📞 Prompts triggered for: {prompt_calls}")
        print(f"🗂️ Session updates: {processor.session_attorney_updates}")
        print(f"📋 Unknown attorneys after: {len(unknown_after)} (down from {len(unknown_before)})")
        
        # Show updated attorneys
        if processor.session_attorney_updates:
            print(f"\n✅ Successfully updated attorneys:")
            for attorney, law_firm in processor.session_attorney_updates.items():
                if law_firm != "Unknown":
                    count = sum(1 for entry in processed_data if entry.get('attorney') == attorney)
                    print(f"  - {attorney} → {law_firm} ({count} records)")
        
        # Verify expected behavior
        expected_prompts = ["John Raggio", "Coral Odiot", "Christopher Randolph"]
        
        prompts_correct = all(attorney in prompt_calls for attorney in expected_prompts)
        updates_made = len(processor.session_attorney_updates) > 0
        
        print(f"\n🔍 Verification:")
        print(f"✅ Expected prompts triggered: {'✅' if prompts_correct else '❌'}")
        print(f"✅ Session updates created: {'✅' if updates_made else '❌'}")
        
        if prompts_correct and updates_made:
            print(f"\n🎉 SUCCESS: Upload workflow correctly prompts for Unknown attorneys!")
        else:
            print(f"\n❌ FAILED: Issues with upload workflow prompting")
            
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        logger.exception("Test error")
    
    finally:
        await processor.cleanup()

if __name__ == "__main__":
    asyncio.run(test_upload_workflow_prompts())