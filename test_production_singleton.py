#\!/usr/bin/env python3
"""
Test to diagnose why multiple browsers are created in production.
"""

import asyncio
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_production_scenario():
    """Test production scenario where job orchestration creates session managers."""
    logger.info("🎯 Testing production singleton behavior")
    
    # Import the factory
    from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
    
    # Track SharedBrowserManager instances
    browser_manager_ids = []
    
    # Simulate job orchestration creating multiple session managers
    for i in range(3):
        firm_id = f"test_firm_{i}"
        logger.info(f"\n🏢 Creating session manager for {firm_id} (simulating job orchestration)")
        
        # This is what job orchestration does
        session_manager = SessionManagerFactory.create(
            config={'headless': True},
            logger=logger,
            firm_id=firm_id,
            fingerprint_manager=None,
            proxy_manager=None,
            law_firms_repository=None
        )
        
        # Check SharedBrowserManager instance
        if hasattr(session_manager, 'shared_browser_manager'):
            browser_manager_id = id(session_manager.shared_browser_manager)
            browser_manager_ids.append(browser_manager_id)
            logger.info(f"  SharedBrowserManager ID: {browser_manager_id}")
            
            # Also check class-level singleton
            from src.services.fb_ads.camoufox.shared_browser_manager import SharedBrowserManager
            logger.info(f"  Class _global_instance: {id(SharedBrowserManager._global_instance) if SharedBrowserManager._global_instance else 'None'}")
    
    # Check results
    unique_ids = set(browser_manager_ids)
    logger.info(f"\n📊 Unique SharedBrowserManager IDs: {len(unique_ids)}")
    logger.info(f"📊 IDs: {unique_ids}")
    
    if len(unique_ids) == 1:
        logger.info("✅ Singleton is working correctly\!")
    else:
        logger.error("❌ Multiple SharedBrowserManager instances detected\!")
        
        # Debug: Check if imports are causing issues
        logger.info("\n🔍 Debugging import behavior...")
        
        # Direct import
        from src.services.fb_ads.camoufox.shared_browser_manager import SharedBrowserManager, get_shared_browser_manager
        logger.info(f"Direct import - Class _global_instance: {id(SharedBrowserManager._global_instance) if SharedBrowserManager._global_instance else 'None'}")
        
        # Get via factory function
        manager1 = get_shared_browser_manager()
        logger.info(f"Factory function 1 - Instance ID: {id(manager1)}")
        
        manager2 = get_shared_browser_manager()
        logger.info(f"Factory function 2 - Instance ID: {id(manager2)}")
        
        logger.info(f"Same instance? {manager1 is manager2}")

async def main():
    """Main test runner."""
    logger.info("🎯 Starting Production Singleton Test")
    logger.info("=" * 80)
    
    await test_production_scenario()
    
    logger.info("\n" + "=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
