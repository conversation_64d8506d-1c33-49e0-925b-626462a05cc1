#!/usr/bin/env python3
"""
Test script to validate event loop-aware lock management in SharedBrowserManager.

This test simulates the production scenario where FB ads processing creates
multiple event loops, causing the singleton to persist but locks to become invalid.

The test verifies that:
1. SharedBrowserManager handles event loop transitions correctly
2. Locks are automatically recreated when event loop changes
3. No 'dict' object has no attribute 'is_set' errors occur
4. Proper logging is generated for event loop transitions

Usage:
    python test_event_loop_awareness.py
"""

import asyncio
import logging
import sys
import threading
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_single_event_loop_scenario():
    """Test normal operation within a single event loop."""
    logger.info("🎯 TEST 1: Single Event Loop Scenario")
    
    try:
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Get shared browser manager instance
        browser_manager = get_shared_browser_manager()
        
        # Test configuration
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        # Test basic operations
        job_id = "single_loop_test"
        context, page = await browser_manager.get_browser_context(job_id, test_config)
        
        logger.info(f"✅ Successfully created context in single event loop")
        
        # Cleanup
        await browser_manager.cleanup_job_context(job_id)
        await browser_manager.cleanup_all()
        
        logger.info("✅ TEST 1 PASSED: Single event loop scenario works correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 1 FAILED: Single event loop scenario failed: {e}")
        return False

def run_in_new_event_loop(coro):
    """Helper function to run a coroutine in a completely new event loop."""
    def thread_target():
        # Create a completely new event loop in this thread
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        try:
            return new_loop.run_until_complete(coro)
        finally:
            new_loop.close()
    
    # Run in a separate thread to ensure complete event loop isolation
    result_container = {}
    exception_container = {}
    
    def wrapper():
        try:
            result_container['result'] = thread_target()
        except Exception as e:
            exception_container['exception'] = e
    
    thread = threading.Thread(target=wrapper)
    thread.start()
    thread.join()
    
    if 'exception' in exception_container:
        raise exception_container['exception']
    
    return result_container.get('result')

async def test_multi_event_loop_scenario():
    """Test SharedBrowserManager across multiple event loops (simulating production)."""
    logger.info("🎯 TEST 2: Multi Event Loop Scenario (Production Simulation)")
    
    try:
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Phase 1: Create SharedBrowserManager in first event loop
        logger.info("📋 Phase 1: Creating SharedBrowserManager in Event Loop A")
        browser_manager = get_shared_browser_manager()
        
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        # Create a context in first event loop
        job_id_1 = "loop_a_test"
        context1, page1 = await browser_manager.get_browser_context(job_id_1, test_config)
        logger.info("✅ Phase 1: Successfully created context in Event Loop A")
        
        # Cleanup context but keep singleton alive
        await browser_manager.cleanup_job_context(job_id_1)
        logger.info("✅ Phase 1: Context cleaned up, singleton persists")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 2 FAILED: Multi event loop scenario failed: {e}")
        return False

async def test_event_loop_b_operations():
    """Test operations in Event Loop B (simulating new event loop in production)."""
    logger.info("📋 Phase 2: Accessing SharedBrowserManager in Event Loop B")
    
    try:
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Get the SAME singleton instance (should persist across event loops)
        browser_manager = get_shared_browser_manager()
        
        test_config = {
            'headless': True,
            'humanize': False,
        }
        
        # This should trigger event loop change detection and lock recreation
        job_id_2 = "loop_b_test"
        context2, page2 = await browser_manager.get_browser_context(job_id_2, test_config)
        logger.info("✅ Phase 2: Successfully created context in Event Loop B after lock recreation")
        
        # Test that operations work normally
        await page2.goto('about:blank')
        title = await page2.title()
        logger.info(f"✅ Phase 2: Page operations work normally (title: '{title}')")
        
        # Cleanup
        await browser_manager.cleanup_job_context(job_id_2)
        await browser_manager.cleanup_all()
        
        logger.info("✅ Phase 2: Event Loop B operations completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 2 FAILED: Event Loop B operations failed: {e}")
        return False

async def test_rapid_event_loop_transitions():
    """Test rapid event loop transitions to ensure robustness."""
    logger.info("🎯 TEST 3: Rapid Event Loop Transitions")
    
    try:
        results = []
        
        # Test multiple rapid transitions
        for i in range(3):
            logger.info(f"📋 Transition {i+1}: Testing new event loop")
            
            # Each iteration runs in a completely new event loop
            async def transition_test():
                from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
                
                browser_manager = get_shared_browser_manager()
                test_config = {'headless': True, 'humanize': False}
                
                job_id = f"rapid_test_{i}"
                context, page = await browser_manager.get_browser_context(job_id, test_config)
                
                # Quick operation to verify functionality
                await page.goto('about:blank')
                
                await browser_manager.cleanup_job_context(job_id)
                
                return f"transition_{i}_success"
            
            # Run in new event loop
            result = run_in_new_event_loop(transition_test())
            results.append(result)
            logger.info(f"✅ Transition {i+1}: Completed successfully")
        
        logger.info(f"✅ TEST 3 PASSED: All {len(results)} rapid transitions completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 3 FAILED: Rapid event loop transitions failed: {e}")
        return False

async def main():
    """Main test runner for event loop awareness validation."""
    logger.info("🎯 Starting Event Loop Awareness Validation Tests")
    logger.info("="*70)
    
    test_results = []
    
    # Test 1: Single Event Loop (baseline)
    logger.info("\n" + "="*50)
    test1_passed = await test_single_event_loop_scenario()
    test_results.append(("Single Event Loop", test1_passed))
    
    # Test 2: Multi Event Loop - Phase 1 (Event Loop A)
    logger.info("\n" + "="*50)
    test2a_passed = await test_multi_event_loop_scenario()
    test_results.append(("Multi Event Loop - Phase 1", test2a_passed))
    
    # Test 2: Multi Event Loop - Phase 2 (Event Loop B) 
    logger.info("\n" + "="*50)
    test2b_passed = run_in_new_event_loop(test_event_loop_b_operations())
    test_results.append(("Multi Event Loop - Phase 2", test2b_passed))
    
    # Test 3: Rapid Event Loop Transitions
    logger.info("\n" + "="*50)
    test3_passed = await test_rapid_event_loop_transitions()
    test_results.append(("Rapid Event Loop Transitions", test3_passed))
    
    # Final Results
    logger.info("\n" + "="*70)
    logger.info("FINAL TEST RESULTS")
    logger.info("="*70)
    
    passed_count = 0
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if passed:
            passed_count += 1
    
    logger.info(f"\nOverall: {passed_count}/{len(test_results)} tests passed")
    
    if passed_count == len(test_results):
        logger.info("🎉 ALL TESTS PASSED - Event Loop Awareness Working Correctly!")
        logger.info("✅ SharedBrowserManager handles event loop transitions properly")
        logger.info("✅ No 'dict' object has no attribute 'is_set' errors")
        logger.info("✅ Production event loop scenarios are handled correctly")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Event loop awareness needs fixes")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)