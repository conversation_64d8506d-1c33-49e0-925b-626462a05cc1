#!/usr/bin/env python3
"""
Test the transfer handler logic directly
"""
import asyncio
import json
import logging

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

async def test_transfer_logic():
    """Test transfer handler logic with the sample data."""
    
    # Sample data from the JSON file
    test_data = {
        "court_id": "scd",
        "docket_num": "2:25-cv-07390",
        "case_in_other_court": "Michigan Eastern, 1:25-cv-11890",
        "lead_case": "2:18-mn-02873-RMG",
        "flags": ["JURY", "MDL"],
        "new_filename": "scd_25_07390_Armstrong_et_al_v_3M_Company_et_al"
    }
    
    print("Test Data:")
    print(f"  court_id: {test_data['court_id']}")
    print(f"  case_in_other_court: {test_data['case_in_other_court']}")
    print(f"  lead_case: {test_data['lead_case']}")
    print(f"  flags: {test_data['flags']}")
    print(f"  mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {test_data.get('transferor_court_id', 'NOT PRESENT')}")
    
    # The issue is that the code checks transferor_court_id BEFORE parsing case_in_other_court
    # Let's trace through what happens:
    
    print("\n--- Tracing through process_transfer_conditions ---")
    
    # Step 1: Initial state
    transferor_court_id = test_data.get('transferor_court_id')  # None
    court_id = test_data.get('court_id')  # 'scd'
    mdl_num_raw = test_data.get('mdl_num')  # None
    
    print(f"\nStep 1 - Initial values:")
    print(f"  transferor_court_id: {transferor_court_id}")
    print(f"  court_id: {court_id}")
    print(f"  mdl_num_raw: {mdl_num_raw}")
    
    # Step 2: Since we have MDL flags but no mdl_num, let's assume it gets extracted
    # In real code, this would come from lead_case parsing
    mdl_num_str = "2873"  # Extracted from lead_case "2:18-mn-02873-RMG"
    mdl_court_id = "scd"  # Assuming MDL 2873 is in scd
    
    print(f"\nStep 2 - MDL extraction:")
    print(f"  mdl_num_str: {mdl_num_str}")
    print(f"  mdl_court_id: {mdl_court_id}")
    
    # Step 3: Check the rules
    print(f"\nStep 3 - Checking rules:")
    
    # Rule 2: Has MDL, NO transferor info, NOT in MDL court -> Pending CTO
    if mdl_num_str and not transferor_court_id and mdl_court_id and court_id != mdl_court_id:
        print("  ❌ Would match Rule 2 (Pending CTO) - BUT court IS the MDL court!")
        print(f"     mdl_num_str={mdl_num_str}, transferor_court_id={transferor_court_id}")
        print(f"     court_id={court_id}, mdl_court_id={mdl_court_id}")
        print(f"     court_id != mdl_court_id: {court_id != mdl_court_id}")
    
    # Rule 3: Has MDL, NO transferor info, IS in MDL court -> Not transferred/pending
    if mdl_num_str and not transferor_court_id and mdl_court_id and court_id == mdl_court_id:
        print("  ⚠️  MATCHES Rule 3 - Would return early with is_transferred=False!")
        print("     This prevents parsing of case_in_other_court!")
        return
    
    # This code would never be reached due to early return!
    print("\nStep 4 - Parsing case_in_other_court (NEVER REACHED):")
    case_in_other_court = test_data.get('case_in_other_court')
    if case_in_other_court:
        parts = case_in_other_court.split(',', 1)
        if len(parts) == 2:
            transferor_name = parts[0].strip()  # "Michigan Eastern"
            transferor_docket = parts[1].strip()  # "1:25-cv-11890"
            print(f"  Parsed: '{transferor_name}', '{transferor_docket}'")
            
            # This would determine court type and set transfer flags
            print(f"  Would set transferor_court_id='mied' and is_transferred=True")


if __name__ == "__main__":
    print("=" * 80)
    print("Transfer Handler Logic Test")
    print("=" * 80)
    
    asyncio.run(test_transfer_logic())
    
    print("\n" + "=" * 80)
    print("CONCLUSION: The early return in Rule 3 prevents proper transfer processing!")
    print("The case_in_other_court field is never parsed because of the early return.")
    print("=" * 80)