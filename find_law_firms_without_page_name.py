#!/usr/bin/env python3
"""
Script to find all law firms without a page name using the LawFirmsRepository.
"""

import asyncio
import logging
from typing import List, Dict, Any

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.law_firms_repository import LawFirmsRepository
from src.config_models.storage import StorageConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def find_law_firms_without_page_name():
    """Find all law firms that don't have a page name."""
    try:
        # Create config for DynamoDB
        config = StorageConfig(
            aws_region="us-west-2"
        )
        
        # Initialize storage with config and logger
        storage = AsyncDynamoDBStorage(config=config, logger=logger)
        
        # Use async context manager to properly manage storage lifecycle
        async with storage:
            # Initialize repository
            repository = LawFirmsRepository(storage=storage, logger=logger)
            
            logger.info("Scanning all law firms...")
            
            # Get all law firms
            all_firms = await repository.scan_all()
            
            logger.info(f"Total law firms found: {len(all_firms)}")
            
            # Filter firms without page name
            firms_without_page_name = []
            
            for firm in all_firms:
                page_name = firm.get('page_name', '').strip()
                if not page_name:
                    firms_without_page_name.append(firm)
            
            # Log results
            logger.info(f"\n{'='*80}")
            logger.info(f"Law Firms Without Page Name: {len(firms_without_page_name)}")
            logger.info(f"{'='*80}\n")
            
            if firms_without_page_name:
                # Sort by name for better readability
                sorted_firms = sorted(firms_without_page_name, key=lambda x: x.get('name', '').lower())
                
                for i, firm in enumerate(sorted_firms, 1):
                    firm_id = firm.get('id', 'N/A')
                    firm_name = firm.get('name', 'N/A')
                    category = firm.get('category', 'N/A')
                    num_ads = firm.get('num_ads', 0)
                    last_updated = firm.get('last_updated', 'N/A')
                    
                    logger.info(f"{i}. ID: {firm_id}")
                    logger.info(f"   Name: {firm_name}")
                    logger.info(f"   Category: {category}")
                    logger.info(f"   Number of Ads: {num_ads}")
                    logger.info(f"   Last Updated: {last_updated}")
                    logger.info("")
            else:
                logger.info("All law firms have page names!")
            
            # Summary statistics
            total_firms = len(all_firms)
            firms_with_page_name = total_firms - len(firms_without_page_name)
            percentage_without = (len(firms_without_page_name) / total_firms * 100) if total_firms > 0 else 0
            
            logger.info(f"\n{'='*80}")
            logger.info("SUMMARY STATISTICS")
            logger.info(f"{'='*80}")
            logger.info(f"Total law firms: {total_firms}")
            logger.info(f"Firms with page name: {firms_with_page_name}")
            logger.info(f"Firms without page name: {len(firms_without_page_name)}")
            logger.info(f"Percentage without page name: {percentage_without:.1f}%")
            logger.info(f"{'='*80}\n")
            
            # Create copyable list of page IDs
            if firms_without_page_name:
                page_ids = [f'"{firm.get("id", "")}"' for firm in firms_without_page_name if firm.get("id")]
                logger.info(f"\n{'='*80}")
                logger.info("COPYABLE PAGE ID LIST")
                logger.info(f"{'='*80}")
                logger.info(f"[{', '.join(page_ids)}]")
                logger.info(f"{'='*80}\n")
            
            return firms_without_page_name
    
    except Exception as e:
        logger.error(f"Error finding law firms without page name: {e}", exc_info=True)
        return []


def main():
    """Main entry point."""
    # Run the async function
    asyncio.run(find_law_firms_without_page_name())


if __name__ == "__main__":
    main()