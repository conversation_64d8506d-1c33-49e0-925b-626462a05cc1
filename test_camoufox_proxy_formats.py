#!/usr/bin/env python3
"""
Test different proxy formats with Camoufox to determine the correct format.
"""

import asyncio
import logging
import os
import urllib.parse

from camoufox.async_api import AsyncCamoufox

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Get proxy credentials from environment
PROXY_PASSWORD = os.getenv('OXYLABS_MOBILE_PASSWORD', '24gbfygq=RBhM2+')
PROXY_USERNAME_BASE = 'customer-lexgeniusmob20250612_7jRlZ'

async def test_proxy_format(format_name: str, proxy_config: dict):
    """Test a specific proxy format."""
    logger.info(f"\n{'='*60}")
    logger.info(f"🧪 Testing format: {format_name}")
    logger.info(f"Proxy config: {proxy_config}")
    
    browser_args = {
        'headless': True,
        'humanize': True,
        'geoip': True,
        'addons': [],
        'proxy': proxy_config
    }
    
    try:
        browser = AsyncCamoufox(**browser_args)
        await browser.start()
        logger.info("✅ Browser started successfully")
        
        # Try to navigate
        context = await browser.browser.new_context()
        page = await context.new_page()
        
        logger.info("Attempting navigation to Facebook...")
        await page.goto('https://www.facebook.com/ads/library/', wait_until='domcontentloaded', timeout=30000)
        logger.info(f"✅ Navigation successful with format: {format_name}")
        
        await context.close()
        await browser.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed with format {format_name}: {str(e)}")
        if "NS_ERROR_PROXY_CONNECTION_REFUSED" in str(e):
            logger.error("   -> Proxy connection refused during navigation")
        return False

async def main():
    """Test various proxy formats."""
    session_id = "1234567890"
    username = f"{PROXY_USERNAME_BASE}-cc-us-sessid-{session_id}-sesstime-10"
    
    # Format 1: https:// with URL-encoded password
    encoded_password = urllib.parse.quote(PROXY_PASSWORD, safe='')
    format1 = {
        'server': 'https://pr.oxylabs.io:7777',
        'username': username,
        'password': encoded_password
    }
    
    # Format 2: http:// with URL-encoded password  
    format2 = {
        'server': 'http://pr.oxylabs.io:7777',
        'username': username,
        'password': encoded_password
    }
    
    # Format 3: https:// with raw password
    format3 = {
        'server': 'https://pr.oxylabs.io:7777',
        'username': username,
        'password': PROXY_PASSWORD
    }
    
    # Format 4: http:// with raw password
    format4 = {
        'server': 'http://pr.oxylabs.io:7777',
        'username': username,
        'password': PROXY_PASSWORD
    }
    
    # Format 5: Without protocol
    format5 = {
        'server': 'pr.oxylabs.io:7777',
        'username': username,
        'password': encoded_password
    }
    
    # Test all formats
    results = {}
    results['https + URL-encoded'] = await test_proxy_format('https:// with URL-encoded password', format1)
    await asyncio.sleep(2)
    
    results['http + URL-encoded'] = await test_proxy_format('http:// with URL-encoded password', format2)
    await asyncio.sleep(2)
    
    results['https + raw'] = await test_proxy_format('https:// with raw password', format3)
    await asyncio.sleep(2)
    
    results['http + raw'] = await test_proxy_format('http:// with raw password', format4)
    await asyncio.sleep(2)
    
    results['no protocol'] = await test_proxy_format('No protocol', format5)
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 SUMMARY:")
    for format_name, success in results.items():
        logger.info(f"  {format_name}: {'✅ SUCCESS' if success else '❌ FAILED'}")
    logger.info(f"{'='*60}")
    
    # Recommendations
    successful_formats = [fmt for fmt, success in results.items() if success]
    if successful_formats:
        logger.info(f"\n✅ Working formats: {', '.join(successful_formats)}")
    else:
        logger.info("\n❌ No formats worked - check proxy credentials or Camoufox compatibility")

if __name__ == "__main__":
    asyncio.run(main())