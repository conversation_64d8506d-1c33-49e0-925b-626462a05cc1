#!/usr/bin/env python3
"""
Test that the fixed logic prompts for attorneys with Unknown law firms
"""

import json
import os
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from afff_case_shell_processor import AFFFCaseShellProcessor

def test_unknown_attorney_prompting():
    """Test that attorneys with Unknown law firms trigger prompts"""
    
    # Create a processor instance
    processor = AFFFCaseShellProcessor("20250716", force=False)
    
    # Mock the attorney lookup with some Unknown entries
    processor.attorney_lookup = {
        "<PERSON> Raggio": {"law_firm": "Unknown", "notes": "Test attorney"},
        "Coral Odiot": {"law_firm": "Unknown", "notes": "Test attorney"},
        "Scott Summy": {"law_firm": "Baron & Budd PC", "notes": "Known attorney"}
    }
    
    # Create case-insensitive map
    processor.attorney_lookup_map = {
        "john raggio": "<PERSON>",
        "coral odiot": "Coral Odiot", 
        "scott summy": "<PERSON> Summy"
    }
    
    # Mock the prompt method to simulate user responses
    prompt_calls = []
    
    def mock_prompt_for_law_firm(attorney_name):
        prompt_calls.append(attorney_name)
        print(f"🔍 Mock prompt called for: {attorney_name}")
        
        # Simulate different responses
        if attorney_name == "John Raggio":
            return "Raggio Law Firm"
        elif attorney_name == "Coral Odiot":
            return "Unknown"  # User hits enter
        else:
            return "Unknown"
    
    processor._prompt_for_law_firm = mock_prompt_for_law_firm
    
    # Mock the file update method
    update_calls = []
    def mock_update_immediate(attorney_name, law_firm):
        update_calls.append((attorney_name, law_firm))
        print(f"📝 Mock file update: {attorney_name} -> {law_firm}")
    
    processor._update_attorney_lookup_immediate = mock_update_immediate
    
    # Test attorney lookup logic
    test_attorneys = [
        ("John Raggio", "Should prompt - Unknown in lookup"),
        ("Coral Odiot", "Should prompt - Unknown in lookup"), 
        ("Scott Summy", "Should NOT prompt - Known law firm"),
        ("John Raggio", "Should use session update - already processed"),
        ("New Attorney", "Should prompt - not in lookup")
    ]
    
    processor.session_attorney_updates = {}
    
    print("🧪 Testing attorney lookup logic:")
    print("=" * 50)
    
    for attorney_name, description in test_attorneys:
        print(f"\n📋 Testing: {attorney_name} ({description})")
        
        # Simulate the lookup logic
        law_firm = "Unknown"
        attorney_lower = attorney_name.lower()
        
        # Check if we already updated this attorney in this session
        if attorney_name in processor.session_attorney_updates:
            law_firm = processor.session_attorney_updates[attorney_name]
            print(f"✅ SESSION UPDATE: '{attorney_name}' -> '{law_firm}'")
        elif attorney_lower in processor.attorney_lookup_map:
            # Found in lookup (case-insensitive)
            actual_name = processor.attorney_lookup_map[attorney_lower]
            law_firm = processor.attorney_lookup[actual_name].get('law_firm', 'Unknown')
            
            # If law firm is Unknown, prompt user to update it
            if law_firm == "Unknown":
                print(f"🔄 UNKNOWN LAW FIRM: '{attorney_name}' found with Unknown law firm")
                
                # Interactive prompt for law firm
                new_law_firm = processor._prompt_for_law_firm(attorney_name)
                
                # If user provided a law firm, update session tracking
                if new_law_firm and new_law_firm != "Unknown":
                    law_firm = new_law_firm
                    processor.session_attorney_updates[attorney_name] = law_firm
                    # Update the attorney lookup in memory and file
                    processor._update_attorney_lookup_immediate(attorney_name, law_firm)
                    print(f"✅ UPDATED: '{attorney_name}' -> '{law_firm}'")
                else:
                    print(f"⚠️ REMAINS UNKNOWN: '{attorney_name}' will stay as Unknown")
            else:
                print(f"✅ MATCH FOUND: '{attorney_name}' -> '{law_firm}'")
        else:
            # New attorney not in lookup - prompt user
            print(f"❌ NO MATCH: '{attorney_name}' not found in lookup")
            
            # Interactive prompt for law firm
            law_firm = processor._prompt_for_law_firm(attorney_name)
            
            # If user provided a law firm, update session tracking
            if law_firm and law_firm != "Unknown":
                processor.session_attorney_updates[attorney_name] = law_firm
                # Update the attorney lookup in memory and file
                processor._update_attorney_lookup_immediate(attorney_name, law_firm)
                print(f"✅ UPDATED: '{attorney_name}' -> '{law_firm}'")
        
        print(f"   Final law firm: {law_firm}")
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 Test Results:")
    print(f"📞 Prompt calls: {prompt_calls}")
    print(f"📝 File updates: {update_calls}")
    print(f"🗂️ Session updates: {processor.session_attorney_updates}")
    
    # Verify expected behavior
    expected_prompts = ["John Raggio", "Coral Odiot", "New Attorney"]
    expected_updates = [("John Raggio", "Raggio Law Firm"), ("New Attorney", "Unknown")]
    
    print(f"\n✅ Expected prompts: {expected_prompts}")
    print(f"✅ Expected updates: {expected_updates}")
    
    prompts_match = prompt_calls == expected_prompts
    # Filter out Unknown updates since they don't get saved
    actual_updates = [(a, f) for a, f in update_calls if f != "Unknown"]
    updates_match = len(actual_updates) >= 1  # At least John Raggio should be updated
    
    print(f"\n🔍 Prompts correct: {'✅' if prompts_match else '❌'}")
    print(f"🔍 Updates correct: {'✅' if updates_match else '❌'}")
    
    if prompts_match and updates_match:
        print(f"\n🎉 TEST PASSED: Unknown attorneys now trigger prompts!")
    else:
        print(f"\n❌ TEST FAILED: Issues with prompting logic")

if __name__ == "__main__":
    test_unknown_attorney_prompting()