#!/usr/bin/env python3
"""
Fix for dependency injection reset errors in FacebookAdsOrchestrator.
"""

import os
import re


def fix_dependency_injection_reset(file_path):
    """Fix the dependency injection reset calls to handle providers properly."""
    print(f"\nFixing dependency injection reset in: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace the reset calls with proper provider handling
    # Fix proxy_manager reset
    old_proxy_reset = '''try:
                    # Force recreation of proxy manager with new config
                    fb_ads_container.proxy_manager.reset()
                    self.log_info("✅ Proxy manager reset - will be recreated with new config")
                except Exception as e:
                    self.log_debug(f"Proxy manager reset failed (expected): {e}")'''
    
    new_proxy_reset = '''# Force recreation of proxy manager with new config
                # Dependency injection providers don't have reset(), but we can override the provider
                try:
                    # Clear the singleton instance if it exists
                    if hasattr(fb_ads_container.proxy_manager, '_instance'):
                        del fb_ads_container.proxy_manager._instance
                        self.log_info("✅ Proxy manager singleton cleared - will be recreated with new config")
                    else:
                        self.log_debug("Proxy manager is not a singleton provider - will be created fresh")
                except Exception as e:
                    self.log_debug(f"Proxy manager singleton clear attempt: {e}")'''
    
    content = content.replace(old_proxy_reset, new_proxy_reset)
    
    # Fix session_manager reset
    old_session_reset = '''try:
                    fb_ads_container.session_manager.reset()
                    self.log_info("✅ Default session manager reset - will be recreated with new proxy_manager")
                except Exception as e:
                    self.log_debug(f"Default session manager reset failed (expected): {e}")'''
    
    new_session_reset = '''# Reset session manager
                try:
                    # Clear the singleton instance if it exists
                    if hasattr(fb_ads_container.session_manager, '_instance'):
                        del fb_ads_container.session_manager._instance
                        self.log_info("✅ Default session manager singleton cleared - will be recreated with new proxy_manager")
                    else:
                        self.log_debug("Default session manager is not a singleton provider - will be created fresh")
                except Exception as e:
                    self.log_debug(f"Default session manager singleton clear attempt: {e}")'''
    
    content = content.replace(old_session_reset, new_session_reset)
    
    # Fix camoufox_session_manager reset
    old_camoufox_reset = '''try:
                    fb_ads_container.camoufox_session_manager.reset()
                    self.log_info("✅ Camoufox session manager reset - will be recreated with new proxy_manager")
                except Exception as e:
                    self.log_debug(f"Camoufox session manager reset failed (expected): {e}")'''
    
    new_camoufox_reset = '''# Reset camoufox session manager
                try:
                    # Check if camoufox_session_manager exists in the container
                    if hasattr(fb_ads_container, 'camoufox_session_manager'):
                        # Clear the singleton instance if it exists
                        if hasattr(fb_ads_container.camoufox_session_manager, '_instance'):
                            del fb_ads_container.camoufox_session_manager._instance
                            self.log_info("✅ Camoufox session manager singleton cleared - will be recreated with new proxy_manager")
                        else:
                            self.log_debug("Camoufox session manager is not a singleton provider - will be created fresh")
                    else:
                        self.log_debug("Camoufox session manager not found in container - may be using dynamic creation")
                except Exception as e:
                    self.log_debug(f"Camoufox session manager singleton clear attempt: {e}")'''
    
    content = content.replace(old_camoufox_reset, new_camoufox_reset)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed dependency injection reset issues in {file_path}")


def main():
    """Apply the dependency injection fix."""
    print("🔧 Dependency Injection Reset Fix")
    print("=" * 50)
    
    file_path = "src/services/orchestration/fb_ads_orchestrator.py"
    
    if os.path.exists(file_path):
        fix_dependency_injection_reset(file_path)
        print("\n✅ Fix applied successfully!")
        print("\nThe reset errors should now be properly handled.")
        print("The code will attempt to clear singleton instances when they exist,")
        print("or simply log that fresh instances will be created.")
    else:
        print(f"❌ File not found: {file_path}")


if __name__ == "__main__":
    main()