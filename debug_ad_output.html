<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexGenius AdSpy - Ad View</title>
    <link rel="icon" type="image/x-icon" href="https://cdn.lexgenius.ai/bimi/lexgenius_logo.svg">

    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-9CREQVVWNF"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-9CREQVVWNF');
    </script>

    <style>
        /* Base styles */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8fafc; /* Tailwind gray-50 */
            color: #1f2937; /* Tailwind gray-800 */
        }

        .gradient-header {
            background: linear-gradient(135deg, #115197 0%, #39BEFA 100%);
        }

        /* Facebook ad container styles */
        .fb-ad-render-container {
            max-width: 500px; /* Keep original max-width for the ad itself */
            margin: 0 auto; /* Center the ad container */
            background-color: #fff;
            border: 1px solid #dddfe2;
            border-radius: 8px;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        .fb-ad-render-container .ad-info { /* Style the info block separately */
            padding: 20px;
            background-color: #f8f9fa; /* Light background for info */
            border-bottom: 1px solid #dddfe2;
            font-family: 'Inter', sans-serif; /* Use Inter for the info block */
            color: #1f2937;
        }

        .fb-ad-render-container .field {
            display: flex;
            margin-bottom: 10px;
            font-size: 14px; /* Consistent font size */
            line-height: 1.6;
        }

        .fb-ad-render-container .field label {
            flex: 0 0 100px;
            font-weight: 600; /* Bolder label */
            color: #4b5563; /* Tailwind gray-600 */
            padding-right: 10px;
        }

        .fb-ad-render-container .field div {
            flex: 1;
            word-wrap: break-word;
            color: #374151; /* Tailwind gray-700 */
        }

        .fb-ad-render-container .platforms svg { /* Exact copy */
            width: 20px;
            height: 20px;
            margin-right: 5px;
            display: inline-block; /* Ensure they align nicely */
            vertical-align: middle; /* Align icons with text */
        }

        .fb-ad-render-container .landing-page a { /* Exact copy */
             word-break: break-all;
             color: #2563eb; /* Tailwind blue-600 for links */
             text-decoration: underline;
        }
        .fb-ad-render-container .landing-page a:hover {
            text-decoration: none;
        }
        .fb-ad-render-container .landing-page span { /* Style for 'FB Lead Form' */
            color: #374151;
        }

        .fb-ad { /* Exact copy */
            padding: 12px;
        }

        .ad-header { /* Exact copy */
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .logo { /* Exact copy */
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover; /* Ensure logo images fit well */
        }

        .header-text { /* Exact copy */
            display: flex;
            flex-direction: column;
        }

        .law-firm { /* Exact copy */
            font-weight: 600;
            font-size: 14px;
            color: #050505;
        }

        .sponsored { /* Exact copy */
            font-size: 12px;
            color: #65676b;
        }

        .ad-body-container { /* Exact copy */
            position: relative;
        }

        .ad-body { /* Exact copy */
            font-size: 15px;
            line-height: 1.3333;
            color: #050505;
            margin-bottom: 12px;
            white-space: pre-wrap;
            overflow: hidden; /* Crucial for truncation */
        }

        /* Keep original max-height rules for truncation */
        @media (max-width: 768px) {
             .ad-body.truncated {
                 max-height: 5em; /* Adjusted to match JS */
             }
         }
         @media (min-width: 769px) {
             .ad-body.truncated {
                 max-height: 5.2em; /* Adjusted to match JS */
             }
         }

        .ad-body.truncated::after { /* Exact copy */
            content: ''; /* Removed '...' as it's handled by JS toggle */
            position: absolute;
            bottom: 0;
            right: 0;
            height: 1.3333em; /* Match line-height */
            width: 70px; /* Width of the fade */
            background: linear-gradient(to right, transparent, white 70%);
            pointer-events: none; /* Allow clicking through the gradient */
        }

        .see-more { /* Exact copy */
            color: #65676b;
            font-weight: bold;
            cursor: pointer;
            display: none; /* Initially hidden */
            font-size: 15px; /* Match ad body font size */
            line-height: 1.3333;
        }

        .ad-body.truncated + .see-more { /* Exact copy */
            display: inline; /* Show only when truncated */
        }

        .ad-image { /* Exact copy - CRITICAL FOR IMAGE DISPLAY */
            display: block; /* Ensure it's a block element */
            width: 100%;
            height: auto; /* Maintain aspect ratio */
            max-width: 100%; /* Prevent exceeding container */
            object-fit: cover;
            margin-bottom: 12px;
            border-radius: 8px; /* Keep rounded image */
        }

        .ad-footer { /* Exact copy */
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f0f2f5;
            padding: 12px;
            border-radius: 8px; /* Keep rounded footer */
            margin-top: 12px; /* Add margin if image is not present */
        }
        /* Ensure margin only if image IS present and VISIBLE */
        .ad-image:not([style*="display: none"]) + .ad-footer {
             margin-top: 0;
        }


        .ad-details { /* Exact copy */
            flex-grow: 1;
            min-width: 0; /* Prevent overflow */
            padding-right: 10px; /* Space between details and button */
        }

        .ad-caption { /* Exact copy */
            font-size: 12px;
            color: #65676b;
            margin-bottom: 4px;
            word-wrap: break-word;
            white-space: normal;
        }

        .ad-title { /* Exact copy - including expandable styles */
            font-size: 15px;
            font-weight: 500;
            color: #050505;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 3em; /* approx 2 lines */
            line-height: 1.5em;
            cursor: pointer;
            transition: max-height 0.3s ease-out;
            word-wrap: break-word;
            white-space: normal;
        }

        .ad-title.expanded { /* Exact copy */
            -webkit-line-clamp: unset;
            max-height: none; /* Or a large enough value */
        }

        .ad-description { /* Exact copy - including truncate */
            font-size: 14px;
            color: #65676b;
            margin-bottom: 8px;
            overflow: hidden;
            cursor: pointer;
            word-wrap: break-word;
            white-space: normal;
            line-height: 1.4em; /* Added for better height calculation */
        }

        .ad-description.truncate { /* Exact copy */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            max-height: 2.8em; /* Approximately 2 lines */
        }

        .ad-button { /* Exact copy */
            background-color: #e4e6eb;
            border: none;
            padding: 10px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            display: inline-block;
            white-space: nowrap;
            cursor: pointer;
            margin-left: auto; /* Keep using margin for positioning */
            align-self: center;
            text-decoration: none;
            color: black;
            flex-shrink: 0; /* Prevent button shrinking */
        }
        .ad-button a, .ad-button span {
             text-decoration: none;
             color: black;
        }

        /* --- END: Styles COPIED EXACTLY from view_ad_v1.html --- */

        /* Tooltip styles - Kept from original */
        .tooltip {
            position: absolute;
            display: none;
            padding: 5px 8px; /* Slightly more padding */
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 4px; /* Match other rounded corners */
            font-size: 12px; /* Smaller font */
            z-index: 1000;
            white-space: nowrap; /* Prevent wrapping */
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .fb-ad-render-container {
                /* Remove border/radius on mobile if it touches edges */
                 border-radius: 0;
                 border-left: none;
                 border-right: none;
            }
        }

    </style>
</head>
<body class="antialiased">

    
    <header class="gradient-header shadow-md mb-8 p-4 text-center">
        
        <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg" 
             alt="LexGenius Logo" 
             class="h-[17.5px] sm:h-20 mx-auto mb-4"> 
        <h1 class="text-2xl md:text-3xl font-bold text-white">LexGenius AdSpy</h1>
        <p class="text-md sm:text-lg text-white opacity-90">Advertisement Details</p>
    </header>

    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">

        
        <div class="fb-ad-render-container mb-8">
            
            <div class="ad-info">
                <h2 class="text-xl font-semibold text-center mb-4 text-gray-700">Ad Information</h2>
                <div class="field">
                    <label>Title</label>
                    <div>✅ Check If You Qualify for Compensation</div>
                </div>
                <div class="field">
                    <label>Status</label>
                    <div>Active</div>
                </div>
                <div class="field">
                    <label>Page Name</label>
                    <div>Advocate Alliance Group</div>
                </div>
                <div class="field">
                    <label>Started</label>
                    <div>07/09/25</div>
                </div>
                <div class="field">
                    <label>Ends</label>
                    
                    <div>07/17/25</div>
                </div>
                <div class="field">
                    <label>Platforms</label>
                    <div class="platforms">
                        
                        
                        
                        <svg class="tooltip-icon" data-tooltip="Facebook" fill="#1877F2" viewBox="0 0 512 512">
                            <path d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"></path>
                        </svg>
                        
                        
                         
                         <svg class="tooltip-icon" data-tooltip="Instagram" width="20" height="20" viewBox="0 0 32 32">
                            <defs> <radialGradient id="insta_grad" cx="30%" cy="107%" r="150%"> <stop offset="0%" style="stop-color: #fdf497;"></stop> <stop offset="5%" style="stop-color: #fdf497;"></stop> <stop offset="45%" style="stop-color: #fd5949;"></stop> <stop offset="60%" style="stop-color: #d6249f;"></stop> <stop offset="90%" style="stop-color: #285AEB;"></stop> </radialGradient> </defs>
                            <path fill="url(#insta_grad)" d="M16 0C7.16 0 0 7.16 0 16s7.16 16 16 16 16-7.16 16-16S24.84 0 16 0zm0 29C8.822 29 3 23.178 3 16S8.822 3 16 3s13 5.822 13 13-5.822 13-13 13z"></path> <path fill="url(#insta_grad)" d="M16 7.425c-4.74 0-8.575 3.835-8.575 8.575S11.26 24.575 16 24.575 24.575 20.74 24.575 16 20.74 7.425 16 7.425zm0 14.2C12.588 21.625 10 19.037 10 16s2.588-5.625 6-5.625 6 2.588 6 6-2.588 5.625-6 5.625z"></path> <circle fill="url(#insta_grad)" cx="23.5" cy="8.5" r="1.875"></circle>
                         </svg>
                        
                        
                        <svg class="tooltip-icon" data-tooltip="Audience Network" fill="#4A90E2" viewBox="0 0 512 512">
                            <path d="M256 288c79.5 0 144-64.5 144-144S335.5 0 256 0 112 64.5 112 144s64.5 144 144 144zm128 32h-55.1c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16H128C57.3 320 0 377.3 0 448v16c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48v-16c0-70.7-57.3-128-128-128z"></path>
                        </svg>
                        
                        
                        <svg class="tooltip-icon" data-tooltip="Messenger" fill="#00B2FF" viewBox="0 0 512 512">
                            <path d="M256.55 8C116.52 8 8 110.34 8 248.57c0 72.3 29.71 134.78 78.07 177.94 8.35 7.51 6.63 11.86 8.05 58.23A19.92 19.92 0 0 0 122 502.31c52.91-23.3 53.59-25.14 62.56-22.7C337.85 521.8 504 423.7 504 248.57 504 110.34 396.59 8 256.55 8zm149.24 185.13l-73 115.57a37.37 37.37 0 0 1-53.91 9.93l-58.08-43.47a15 15 0 0 0-18 0l-78.37 59.44c-10.46 7.93-24.16-4.6-17.11-15.67l73-115.57a37.36 37.36 0 0 1 53.91-9.93l58.06 43.46a15 15 0 0 0 18 0l78.41-59.38c10.44-7.98 24.14 4.54 17.09 15.62z"></path>
                        </svg>
                        
                        
                    </div>
                </div>
                <div class="field">
                    <label>Landing Page</label>
                    <div class="landing-page">
                        
                        <a href="https://advocatealliancegroup.com/claims/pfas-groundwater" target="_blank" rel="noopener noreferrer">https://advocatealliancegroup.com/claims/pfas-groundwater</a>
                        
                    </div>
                </div>
            </div>

            
            <div class="fb-ad">
                <div class="ad-header">
                    
                    <img src="https://cdn.lexgenius.ai/assets/images/law-firm-logos/1589261434655726.jpg" class="logo" alt="Advocate Alliance Group logo" onerror="this.style.display='none'; this.onerror=null;"> 
                    
                    <div class="header-text">
                        <div class="law-firm">Advocate Alliance Group</div>
                        <div class="sponsored">Sponsored</div>
                    </div>
                </div>
                
                <div class="ad-body-container">
                    
                    <div class="ad-body" id="adBody">⚠️ PFAS Chemicals May Have Caused Your Cancer. Drank contaminated tap water? PFAS exposure is linked to kidney, liver, testicular, and thyroid cancer. Check your eligibility in 1 minute.</div>
                    <span class="see-more" onclick="toggleAdBody()">See more</span>
                </div>
                
                
                <img src="https://cdn.lexgenius.ai/adarchive/1437552473936525/1265114061890427.jpg" class="ad-image" alt="Ad image" onerror="this.style.display='none'; this.onerror=null;"> 
                
                
                
                <div class="ad-footer">
                    <div class="ad-details">
                        
                        <div class="ad-caption">ADVOCATEALLIANCEGROUP.COM</div>
                        
                        
                        <div class="ad-title expandable-title">✅ Check If You Qualify for Compensation</div>
                        
                        
                        <div class="ad-description truncate" onclick="toggleDescription(this)">PFAS, also known as 'forever chemicals,' have been linked to serious health issues. Learn how to file a claim</div>
                        
                    </div>
                    
                    <div class="ad-button">
                        
                        <a href="https://advocatealliancegroup.com/claims/pfas-groundwater" target="_blank" rel="noopener noreferrer" class="ad-button-text">Learn more</a>
                        
                    </div>
                    
                </div>
                
            </div>
        </div>

    </div>

    
    <footer class="bg-gray-800 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-gray-400">
            <div class="mb-4">
                 
                 <img src="https://cdn.lexgenius.ai/assets/images/icons/lexgenius_logo.svg" 
                      alt="LexGenius Logo" 
                      class="h-14 mx-auto"> 
            </div>
            <p class="text-sm mb-2">LexGenius | 20 Eastgate Drive, Unit D, Boynton Beach, FL 33436</p>
            <p class="text-sm mb-4">© 2024 LexGenius. All Rights Reserved.</p>
            
            <!-- <a href="#" class="text-blue-400 hover:text-blue-300 hover:underline text-sm">Manage Preferences</a> -->
        </div>
    </footer>

    
    <div class="tooltip"></div>

    
    <script>
        // Expandable Ad Title (from original)
        document.addEventListener('DOMContentLoaded', function () {
            var expandableTitles = document.querySelectorAll('.expandable-title');
            expandableTitles.forEach(function (title) {
                 // Check if text actually overflows after rendering
                 // Use setTimeout to allow browser to render first
                 setTimeout(() => {
                    if (title.scrollHeight > title.clientHeight) {
                         title.style.cursor = 'pointer'; // Only add cursor if expandable
                         title.addEventListener('click', function () {
                            this.classList.toggle('expanded');
                        });
                     } else {
                        title.style.cursor = 'default'; // Not clickable if not overflowing
                     }
                 }, 0);
            });
        });

        // Platform Icon Tooltips (from original, slightly improved positioning)
        document.addEventListener('DOMContentLoaded', function () {
            const tooltipElement = document.querySelector('.tooltip');
            if (!tooltipElement) return;

            document.querySelectorAll('.tooltip-icon').forEach(function (element) {
                element.addEventListener('mouseenter', function (e) {
                    tooltipElement.textContent = element.getAttribute('data-tooltip') || '';
                    tooltipElement.style.display = 'block';
                    positionTooltip(e, tooltipElement);
                });

                element.addEventListener('mousemove', (e) => positionTooltip(e, tooltipElement));

                element.addEventListener('mouseleave', function () {
                    tooltipElement.style.display = 'none';
                });
            });

            function positionTooltip(e, tooltip) {
                const scrollX = window.scrollX || window.pageXOffset;
                const scrollY = window.scrollY || window.pageYOffset;
                let left = e.clientX + scrollX + 10;
                let top = e.clientY + scrollY + 15;

                // Ensure tooltip stays within viewport bounds
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const tooltipWidth = tooltip.offsetWidth;
                const tooltipHeight = tooltip.offsetHeight;

                 // Adjust horizontally
                if (left + tooltipWidth > scrollX + viewportWidth - 5) { // Add small buffer
                    left = e.clientX + scrollX - tooltipWidth - 10;
                }
                if (left < scrollX + 5) { // Add small buffer
                    left = scrollX + 5;
                }

                // Adjust vertically
                if (top + tooltipHeight > scrollY + viewportHeight - 5) { // Add small buffer
                     top = e.clientY + scrollY - tooltipHeight - 10; // Position above cursor
                 }
                 if (top < scrollY + 5) { // Add small buffer
                     top = scrollY + 5;
                 }


                tooltip.style.left = `${left}px`;
                tooltip.style.top = `${top}px`;
            }
        });

        // Expandable Ad Description (from original)
        function toggleDescription(element) {
            // Only toggle if the content is actually truncated or if it's already expanded
            if ((element.scrollHeight > element.clientHeight && element.classList.contains('truncate')) || !element.classList.contains('truncate')) {
                 element.classList.toggle('truncate');
            }
        }
         // Add cursor pointer only if description is overflowing
         document.addEventListener('DOMContentLoaded', function () {
             document.querySelectorAll('.ad-description.truncate').forEach(el => {
                 // Use setTimeout to check after rendering
                 setTimeout(() => {
                     if (el.scrollHeight > el.clientHeight) {
                        el.style.cursor = 'pointer';
                     } else {
                        el.style.cursor = 'default';
                        // Optional: remove the class if not actually truncated on load
                        // el.classList.remove('truncate');
                     }
                 }, 0);
             });
         });


        // Ad Body Truncation and "See More" (from original, minor adjustments)
         function checkAdBodyTruncation() {
            const adBody = document.getElementById('adBody');
            const seeMore = document.querySelector('.see-more');
            if (!adBody || !seeMore) return;

            // Measure scrollHeight *before* applying truncation styles
             const wasTruncated = adBody.classList.contains('truncated');
             adBody.classList.remove('truncated'); // Temporarily remove to measure full height
             adBody.style.maxHeight = 'none';

            const scrollHeight = adBody.scrollHeight;

             // Restore original state if it was truncated before measuring
             if(wasTruncated) {
                 adBody.classList.add('truncated');
             }
             adBody.style.maxHeight = ''; // Use CSS definition


             // Estimate max height based on em units and font size
             const computedStyle = window.getComputedStyle(adBody);
             const fontSize = parseFloat(computedStyle.fontSize);
             const lineHeight = parseFloat(computedStyle.lineHeight); // Use computed line height
             const maxEm = window.innerWidth <= 768 ? 5 : 5.2;
             const maxHeightPixels = maxEm * fontSize;

             // More reliable check: Does the full scroll height exceed the calculated max height?
             // Add a small tolerance (e.g., 1 pixel or small fraction of line height)
             const tolerance = Math.max(1, lineHeight * 0.1);
            if (scrollHeight > maxHeightPixels + tolerance) {
                 adBody.classList.add('truncated');
                 adBody.style.maxHeight = `${maxEm}em`; // Apply max-height via style for consistency
                 seeMore.style.display = 'inline';
                 seeMore.textContent = 'See more';
            } else {
                 adBody.classList.remove('truncated');
                 adBody.style.maxHeight = 'none'; // Explicitly remove height limit
                 seeMore.style.display = 'none';
            }
        }

        function toggleAdBody() {
            const adBody = document.getElementById('adBody');
            const seeMore = document.querySelector('.see-more');
            if (!adBody || !seeMore) return;

            if (adBody.classList.contains('truncated')) {
                adBody.classList.remove('truncated');
                adBody.style.maxHeight = 'none';
                seeMore.textContent = 'See less';
            } else {
                // Re-apply truncation based on current state/window size
                const maxEm = window.innerWidth <= 768 ? 5 : 5.2;
                adBody.classList.add('truncated');
                adBody.style.maxHeight = `${maxEm}em`;
                seeMore.textContent = 'See more';
            }
        }

        // Initial check and resize listener
        document.addEventListener('DOMContentLoaded', function () {
            // Check if ad body exists before processing
            const adBody = document.getElementById('adBody');
            if (adBody) {
                 // Rely on white-space: pre-wrap for newlines
                 // Run check after a tiny delay to ensure layout is complete
                 setTimeout(checkAdBodyTruncation, 0);
            }
            // Debounce resize listener slightly for performance
            let resizeTimer;
             window.addEventListener('resize', () => {
                 clearTimeout(resizeTimer);
                 resizeTimer = setTimeout(checkAdBodyTruncation, 150);
             });
        });

        // Add onerror handlers to prevent broken image loops
        document.addEventListener('DOMContentLoaded', function() {
             document.querySelectorAll('img').forEach(img => {
                 img.addEventListener('error', function() {
                     // Prevent infinite loops if the placeholder itself fails
                     if (!this.dataset.error) {
                         this.style.display = 'none';
                         this.dataset.error = 'true'; // Mark as errored
                     }
                 });
             });
        });


    </script>

</body>
</html>