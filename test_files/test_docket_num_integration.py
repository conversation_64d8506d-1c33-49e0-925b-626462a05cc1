#!/usr/bin/env python3
"""
Integration tests for docket_num methods from the actual LexGenius codebase.
These tests import and test the real methods to ensure they work correctly.
"""

import unittest
import asyncio
import sys
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, List, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Suppress import warnings for testing
import warnings
warnings.filterwarnings("ignore")

class TestActualDocketNumMethods(unittest.TestCase):
    """Test actual docket_num methods from the codebase."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_dockets = [
            ("1:25-cv-08592", "1:25-cv-08592"),
            ("1:25-cv-08592-RMB-SAK", "1:25-cv-08592"),
            ("3:24-cv-123", "3:24-cv-00123"),
            ("2:23-sf-1", "2:23-sf-00001"),
        ]
    
    def test_html_case_parser_normalization(self):
        """Test HTMLCaseParser._normalize_docket_to_13_chars method."""
        try:
            from src.core.html.html_case_parser import HTMLCaseParser
            
            for input_docket, expected in self.test_dockets:
                result = HTMLCaseParser._normalize_docket_to_13_chars(input_docket)
                self.assertEqual(result, expected, 
                    f"HTMLCaseParser normalization failed: {input_docket} -> {result}, expected {expected}")
                self.assertEqual(len(result), 13, 
                    f"Result should be 13 characters: {result}")
                    
        except ImportError as e:
            self.skipTest(f"Could not import HTMLCaseParser: {e}")
    
    def test_pacer_utils_normalize_docket_number(self):
        """Test pacer_utils.normalize_docket_number method."""
        try:
            from src.utils.pacer_utils import normalize_docket_number
            
            test_cases = [
                ("No. 1:25-cv-08592", "1:25-cv-08592"),
                ("NO. 3:24-sf-12345", "3:24-sf-12345"),
                ("# 2:23-cv-00001", "2:23-cv-00001"),
                ("1:25-cv-08592", "1:25-cv-08592"),  # No prefix
            ]
            
            for input_docket, expected in test_cases:
                result = normalize_docket_number(input_docket)
                self.assertEqual(result, expected,
                    f"pacer_utils normalization failed: {input_docket} -> {result}, expected {expected}")
                    
        except ImportError as e:
            self.skipTest(f"Could not import normalize_docket_number: {e}")
    
    def test_pacer_utils_extract_year_from_docket(self):
        """Test pacer_utils.extract_year_from_docket method."""
        try:
            from src.utils.pacer_utils import extract_year_from_docket
            
            test_cases = [
                ("1:25-cv-08592", 2025),
                ("3:24-sf-12345", 2024),
                ("2:23-cv-00001", 2023),
                ("1:21-cv-08592", 2021),
                ("invalid-docket", None),
            ]
            
            for input_docket, expected in test_cases:
                result = extract_year_from_docket(input_docket)
                self.assertEqual(result, expected,
                    f"Year extraction failed: {input_docket} -> {result}, expected {expected}")
                    
        except ImportError as e:
            self.skipTest(f"Could not import extract_year_from_docket: {e}")
    
    def test_docket_processor_extract_db_docket_number(self):
        """Test DocketProcessor._extract_db_docket_number method."""
        try:
            from src.pacer.docket_processor import DocketProcessor
            
            # Create a mock DocketProcessor instance
            mock_args = {
                'court_id': 'test',
                'iso_date': '2025-01-01',
                'start_date_obj': Mock(),
                'end_date_obj': Mock(),
                'navigator': None,
                'file_manager': Mock(),
                'pacer_db': None,
                'dc_db': None,
                's3_manager': None,
                'gpt_interface': None,
                'config': {},
            }
            
            with patch.object(DocketProcessor, '_load_configurations'):
                with patch.object(DocketProcessor, '_load_relevant_defendants', return_value=[]):
                    processor = DocketProcessor(**mock_args)
                    
                    for input_docket, expected in self.test_dockets:
                        result = processor._extract_db_docket_number(input_docket)
                        self.assertEqual(result, expected,
                            f"DocketProcessor DB extraction failed: {input_docket} -> {result}, expected {expected}")
                        self.assertEqual(len(result), 13,
                            f"Result should be 13 characters: {result}")
                            
        except ImportError as e:
            self.skipTest(f"Could not import DocketProcessor: {e}")
        except Exception as e:
            self.skipTest(f"Could not create DocketProcessor: {e}")
    
    def test_docket_processor_extract_clean_docket_number(self):
        """Test DocketProcessor._extract_clean_docket_number method."""
        try:
            from src.pacer.docket_processor import DocketProcessor
            
            # Create a mock DocketProcessor instance
            mock_args = {
                'court_id': 'test',
                'iso_date': '2025-01-01',
                'start_date_obj': Mock(),
                'end_date_obj': Mock(),
                'navigator': None,
                'file_manager': Mock(),
                'pacer_db': None,
                'dc_db': None,
                's3_manager': None,
                'gpt_interface': None,
                'config': {},
            }
            
            with patch.object(DocketProcessor, '_load_configurations'):
                with patch.object(DocketProcessor, '_load_relevant_defendants', return_value=[]):
                    processor = DocketProcessor(**mock_args)
                    
                    test_cases = [
                        ("1:25-cv-08592", "25_08592"),
                        ("3:24-sf-12345", "24_12345"),
                        ("2:23-cv-00001", "23_00001"),
                    ]
                    
                    for input_docket, expected in test_cases:
                        result = processor._extract_clean_docket_number(input_docket)
                        self.assertEqual(result, expected,
                            f"DocketProcessor clean extraction failed: {input_docket} -> {result}, expected {expected}")
                            
        except ImportError as e:
            self.skipTest(f"Could not import DocketProcessor: {e}")
        except Exception as e:
            self.skipTest(f"Could not create DocketProcessor: {e}")


class TestDocketNumRepositoryMethods(unittest.TestCase):
    """Test repository methods that use docket_num."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_storage = AsyncMock()
        
    def test_pacer_repository_field_mapping(self):
        """Test PacerRepository._map_fields_to_dynamodb method."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            repo = PacerRepository(self.mock_storage)
            
            test_record = {
                'court_id': 'nysd',
                'docket_num': '1:25-cv-08592',
                'filing_date': '20250101',
                'versus': 'Smith v. Jones',
                'is_removal': True,
                'extra_field': 'test',
            }
            
            result = repo._map_fields_to_dynamodb(test_record)
            
            # Check that docket_num was mapped correctly
            self.assertIn('DocketNum', result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            self.assertNotIn('docket_num', result)
            
            # Check other expected mappings
            self.assertIn('CourtId', result)
            self.assertIn('FilingDate', result)
            self.assertIn('Versus', result)
            self.assertIn('IsRemoval', result)
            
            # Check boolean conversion
            self.assertIsInstance(result['IsRemoval'], bool)
            self.assertTrue(result['IsRemoval'])
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    async def async_test_pacer_repository_query_methods(self):
        """Test async query methods of PacerRepository."""
        try:
            from src.repositories.pacer_repository import PacerRepository
            
            # Mock storage responses
            self.mock_storage.query.return_value = [
                {'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592', 'FilingDate': '20250101'}
            ]
            self.mock_storage.get_item.return_value = {
                'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592', 'FilingDate': '20250101'
            }
            
            repo = PacerRepository(self.mock_storage)
            
            # Test check_docket_exists
            exists = await repo.check_docket_exists('nysd', '1:25-cv-08592')
            self.assertTrue(exists)
            
            # Test query_by_court_and_docket
            results = await repo.query_by_court_and_docket('nysd', '1:25-cv-08592')
            self.assertEqual(len(results), 1)
            self.assertEqual(results[0]['DocketNum'], '1:25-cv-08592')
            
            # Test get_by_filing_date_and_docket
            result = await repo.get_by_filing_date_and_docket('20250101', '1:25-cv-08592')
            self.assertIsNotNone(result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerRepository: {e}")
    
    def test_pacer_repository_query_methods(self):
        """Wrapper for async repository tests."""
        try:
            asyncio.run(self.async_test_pacer_repository_query_methods())
        except Exception as e:
            self.skipTest(f"Async test failed: {e}")


class TestDocketNumFileManagerMethods(unittest.TestCase):
    """Test file manager methods that use docket_num."""
    
    def test_file_manager_create_base_filename_static(self):
        """Test PacerFileManager.create_base_filename_static method."""
        try:
            from src.pacer.file_manager import PacerFileManager
            
            test_cases = [
                ("nysd", "1:25-cv-08592", "Smith v. Jones", "nysd_25_08592_Smith_v_Jones"),
                ("cand", "3:24-sf-12345", "ABC Corp v. XYZ Inc", "cand_24_12345_ABC_Corp_v_XYZ_Inc"),
            ]
            
            for court_id, docket_num, versus, expected_pattern in test_cases:
                result = PacerFileManager.create_base_filename_static(court_id, docket_num, versus)
                
                # Check that result is a string and contains expected components
                self.assertIsInstance(result, str)
                self.assertIn(court_id, result)
                self.assertNotIn(' ', result)  # No spaces
                
                # Check year and docket number extraction pattern
                if "25_08592" in expected_pattern:
                    self.assertIn("25_08592", result)
                if "24_12345" in expected_pattern:
                    self.assertIn("24_12345", result)
                    
        except ImportError as e:
            self.skipTest(f"Could not import PacerFileManager: {e}")


class TestDocketNumCaseTransferMethods(unittest.TestCase):
    """Test case transfer handler methods that use docket_num."""
    
    def test_case_transfer_handler_clean_docket_number(self):
        """Test CaseTransferHandler._clean_docket_number method."""
        try:
            from src.pacer.case_transfer_handler import CaseTransferHandler
            
            test_cases = [
                ("1:25-cv-08592-RMB-SAK", "1:25-cv-08592"),
                ("3:24-sf-12345", "3:24-sf-12345"),
                ("No. 2:23-cv-00001", "2:23-cv-00001"),
            ]
            
            for input_docket, expected in test_cases:
                result = CaseTransferHandler._clean_docket_number(input_docket)
                # The exact behavior may vary, but it should return a cleaned string
                self.assertIsInstance(result, str)
                self.assertNotEqual(result, "")
                
        except ImportError as e:
            self.skipTest(f"Could not import CaseTransferHandler: {e}")
        except AttributeError as e:
            self.skipTest(f"Method not found in CaseTransferHandler: {e}")


class TestDocketNumServiceMethods(unittest.TestCase):
    """Test service layer methods that use docket_num."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_repo = AsyncMock()
    
    async def async_test_pacer_query_service_methods(self):
        """Test PacerQueryService methods."""
        try:
            from src.services.pacer.query_service import PacerQueryService
            
            # Mock repository responses
            self.mock_repo.query_by_court_and_docket.return_value = [
                {'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592', 'FilingDate': '20250101'}
            ]
            self.mock_repo.query_transfer_info_async.return_value = {
                'CourtId': 'nysd', 'DocketNum': '1:25-cv-08592', 'S3Link': 'test.pdf'
            }
            
            service = PacerQueryService(self.mock_repo)
            
            # Test query_transfer_info
            result = await service.query_transfer_info('nysd', '1:25-cv-08592')
            self.assertIsNotNone(result)
            self.assertEqual(result['DocketNum'], '1:25-cv-08592')
            
            # Verify repository was called with correct parameters
            self.mock_repo.query_transfer_info_async.assert_called_with('nysd', '1:25-cv-08592')
            
        except ImportError as e:
            self.skipTest(f"Could not import PacerQueryService: {e}")
    
    def test_pacer_query_service_methods(self):
        """Wrapper for async service tests."""
        try:
            asyncio.run(self.async_test_pacer_query_service_methods())
        except Exception as e:
            self.skipTest(f"Async test failed: {e}")


def run_integration_tests():
    """Run all integration tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestActualDocketNumMethods,
        TestDocketNumRepositoryMethods,
        TestDocketNumFileManagerMethods,
        TestDocketNumCaseTransferMethods,
        TestDocketNumServiceMethods,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*80)
    print(f"DOCKET_NUM INTEGRATION TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(getattr(result, 'skipped', []))}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\nFAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"- {test}")
            print(f"  {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\nERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"- {test}")
            print(f"  {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)