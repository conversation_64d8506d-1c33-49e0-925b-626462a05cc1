#!/usr/bin/env python3
"""
Master test runner for all docket_num related tests.
This script runs comprehensive tests for every method that uses docket_num in the LexGenius codebase.

Test Coverage:
1. Normalization Functions - HTM<PERSON><PERSON><PERSON><PERSON><PERSON>, DocketProcessor, Orchestrator
2. Utility Functions - pacer_utils.py methods
3. Database Operations - Repository layer methods
4. Service Layer - Query services and business logic
5. File Operations - Filename generation and file management
6. Transfer Handling - Case transfer processing
7. Validation - Data validation and format checking
8. Edge Cases - Error handling and malformed input
9. Integration Tests - Real method testing with mocks
10. Performance Tests - Large dataset handling
"""

import sys
import os
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_test_file(test_file: str) -> Dict[str, Any]:
    """Run a single test file and return results."""
    print(f"\n{'='*80}")
    print(f"RUNNING: {test_file}")
    print('='*80)
    
    start_time = time.time()
    
    try:
        # Run the test file
        result = subprocess.run(
            [sys.executable, test_file],
            capture_output=True,
            text=True,
            cwd=project_root,
            timeout=300  # 5 minute timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Parse output for test results
        output = result.stdout
        stderr = result.stderr
        
        # Extract test counts from output
        tests_run = 0
        failures = 0
        errors = 0
        skipped = 0
        
        for line in output.split('\n'):
            if 'Tests run:' in line:
                try:
                    tests_run = int(line.split('Tests run:')[1].split()[0])
                except:
                    pass
            if 'Failures:' in line:
                try:
                    failures = int(line.split('Failures:')[1].split()[0])
                except:
                    pass
            if 'Errors:' in line:
                try:
                    errors = int(line.split('Errors:')[1].split()[0])
                except:
                    pass
            if 'Skipped:' in line:
                try:
                    skipped = int(line.split('Skipped:')[1].split()[0])
                except:
                    pass
        
        success = result.returncode == 0 and failures == 0 and errors == 0
        
        return {
            'file': test_file,
            'success': success,
            'tests_run': tests_run,
            'failures': failures,
            'errors': errors,
            'skipped': skipped,
            'duration': duration,
            'output': output,
            'stderr': stderr,
            'returncode': result.returncode
        }
        
    except subprocess.TimeoutExpired:
        return {
            'file': test_file,
            'success': False,
            'tests_run': 0,
            'failures': 0,
            'errors': 1,
            'skipped': 0,
            'duration': 300,
            'output': '',
            'stderr': 'Test timed out after 5 minutes',
            'returncode': -1
        }
    except Exception as e:
        return {
            'file': test_file,
            'success': False,
            'tests_run': 0,
            'failures': 0,
            'errors': 1,
            'skipped': 0,
            'duration': 0,
            'output': '',
            'stderr': str(e),
            'returncode': -1
        }


def print_test_result(result: Dict[str, Any]):
    """Print formatted test result."""
    file_name = Path(result['file']).name
    status = "✅ PASS" if result['success'] else "❌ FAIL"
    
    print(f"{status} | {file_name}")
    print(f"     Tests: {result['tests_run']}, "
          f"Failures: {result['failures']}, "
          f"Errors: {result['errors']}, "
          f"Skipped: {result['skipped']}")
    print(f"     Duration: {result['duration']:.2f}s")
    
    if not result['success'] and result['stderr']:
        print(f"     Error: {result['stderr'][:200]}...")


def generate_detailed_report(results: List[Dict[str, Any]]):
    """Generate detailed test report."""
    print("\n" + "="*100)
    print("COMPREHENSIVE DOCKET_NUM TEST REPORT")
    print("="*100)
    
    total_tests = sum(r['tests_run'] for r in results)
    total_failures = sum(r['failures'] for r in results)
    total_errors = sum(r['errors'] for r in results)
    total_skipped = sum(r['skipped'] for r in results)
    total_duration = sum(r['duration'] for r in results)
    
    successful_files = sum(1 for r in results if r['success'])
    total_files = len(results)
    
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   Test Files Run: {total_files}")
    print(f"   Successful Files: {successful_files}")
    print(f"   Failed Files: {total_files - successful_files}")
    print(f"   Total Tests: {total_tests}")
    print(f"   Total Failures: {total_failures}")
    print(f"   Total Errors: {total_errors}")
    print(f"   Total Skipped: {total_skipped}")
    print(f"   Total Duration: {total_duration:.2f}s")
    
    if total_tests > 0:
        success_rate = ((total_tests - total_failures - total_errors) / total_tests) * 100
        print(f"   Success Rate: {success_rate:.1f}%")
    
    print(f"\n📋 FILE-BY-FILE RESULTS:")
    for result in results:
        print_test_result(result)
    
    # Failed tests details
    failed_results = [r for r in results if not r['success']]
    if failed_results:
        print(f"\n🔍 FAILURE DETAILS:")
        for result in failed_results:
            print(f"\n   {Path(result['file']).name}:")
            if result['stderr']:
                print(f"      Error: {result['stderr']}")
            if result['returncode'] != 0 and result['returncode'] != -1:
                print(f"      Return code: {result['returncode']}")
    
    print(f"\n🎯 COVERAGE ANALYSIS:")
    categories = {
        'comprehensive': 'Core normalization and utility functions',
        'integration': 'Real method testing with actual imports',
        'repository_service': 'Database and service layer operations',
    }
    
    for category, description in categories.items():
        category_results = [r for r in results if category in r['file']]
        if category_results:
            category_success = all(r['success'] for r in category_results)
            status = "✅" if category_success else "❌"
            print(f"   {status} {category.title()}: {description}")


def run_all_docket_tests():
    """Run all docket_num related tests."""
    print("🚀 Starting Comprehensive docket_num Test Suite")
    print(f"Project Root: {project_root}")
    
    # Define test files to run
    test_files = [
        "test_files/test_docket_num_comprehensive.py",
        "test_files/test_docket_num_integration.py", 
        "test_files/test_docket_num_repository_service.py",
        "test_files/simple_docket_test.py",  # Basic functionality test
    ]
    
    # Verify test files exist
    existing_files = []
    for test_file in test_files:
        full_path = project_root / test_file
        if full_path.exists():
            existing_files.append(str(full_path))
        else:
            print(f"⚠️  Test file not found: {test_file}")
    
    if not existing_files:
        print("❌ No test files found!")
        return False
    
    print(f"\n📁 Found {len(existing_files)} test files:")
    for file_path in existing_files:
        print(f"   - {Path(file_path).name}")
    
    # Run all tests
    results = []
    start_total = time.time()
    
    for test_file in existing_files:
        result = run_test_file(test_file)
        results.append(result)
        
        # Print immediate result
        print_test_result(result)
    
    end_total = time.time()
    total_duration = end_total - start_total
    
    # Generate comprehensive report
    generate_detailed_report(results)
    
    print(f"\n⏱️  Total execution time: {total_duration:.2f}s")
    
    # Determine overall success
    overall_success = all(r['success'] for r in results)
    
    if overall_success:
        print("\n🎉 ALL DOCKET_NUM TESTS PASSED!")
        print("✅ The docket number normalization and processing is working correctly.")
        print("✅ All methods that use docket_num have been validated.")
    else:
        print("\n💥 SOME TESTS FAILED!")
        print("❌ Review the failure details above and fix the issues.")
        failed_files = [r['file'] for r in results if not r['success']]
        print(f"❌ Failed files: {[Path(f).name for f in failed_files]}")
    
    return overall_success


def run_quick_validation():
    """Run a quick validation of core docket functionality."""
    print("\n🔍 Quick Validation of Core Docket Functionality:")
    
    # Test core normalization function
    def test_core_normalization():
        import re
        
        def normalize_docket_to_13_chars(docket_num: str) -> str:
            if not docket_num or ':' not in docket_num:
                return docket_num
                
            try:
                prefix = docket_num.split(':')[0]
                suffix_parts = docket_num.split(':')[1].split('-')
                
                if len(suffix_parts) >= 3:
                    year = suffix_parts[0]
                    case_type = suffix_parts[1]
                    docket_part = suffix_parts[2]
                    
                    num_match = re.search(r'(\d{1,5})', docket_part)
                    if num_match:
                        digits = num_match.group(1)
                        five_digits = digits.zfill(5)
                        return f"{prefix}:{year}-{case_type}-{five_digits}"
                
                return docket_num
            except Exception:
                return docket_num
        
        test_cases = [
            ("1:25-cv-08592", "1:25-cv-08592"),
            ("1:25-cv-08592-RMB-SAK", "1:25-cv-08592"),
            ("3:24-cv-123", "3:24-cv-00123"),
            ("2:23-sf-1", "2:23-sf-00001"),
        ]
        
        all_passed = True
        for input_docket, expected in test_cases:
            result = normalize_docket_to_13_chars(input_docket)
            passed = result == expected
            status = "✅" if passed else "❌"
            print(f"   {status} {input_docket} → {result}")
            if not passed:
                all_passed = False
                print(f"      Expected: {expected}")
        
        return all_passed
    
    core_passed = test_core_normalization()
    
    if core_passed:
        print("✅ Core docket normalization is working correctly!")
    else:
        print("❌ Core docket normalization has issues!")
    
    return core_passed


if __name__ == "__main__":
    print("=" * 100)
    print("LEXGENIUS DOCKET_NUM COMPREHENSIVE TEST SUITE")
    print("=" * 100)
    
    # Run quick validation first
    quick_success = run_quick_validation()
    
    if not quick_success:
        print("\n❌ Quick validation failed. Skipping full test suite.")
        sys.exit(1)
    
    # Run full test suite
    success = run_all_docket_tests()
    
    sys.exit(0 if success else 1)