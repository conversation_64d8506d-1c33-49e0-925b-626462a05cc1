#!/usr/bin/env python
"""
Compare NER processing between SpaCy and Flair backends
"""
import time
import subprocess
import sys

def run_classifier(config_file, limit=100):
    """Run the classifier and measure time"""
    start_time = time.time()
    
    cmd = [
        sys.executable, "-m", "src.scripts.hybrid_classifier",
        "--config", config_file,
        "--limit", str(limit),
        "--log-level", "INFO"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    # Extract processing stats from output
    lines = result.stdout.split('\n')
    for line in lines:
        if "Total processing time:" in line:
            print(f"  Reported time: {line.strip()}")
        if "Average speed:" in line:
            print(f"  {line.strip()}")
    
    print(f"  Actual elapsed time: {elapsed:.2f} seconds")
    
    # Check for NER cache activity
    ner_hits = 0
    ner_misses = 0
    for line in lines:
        if "NER cache" in line and "Hits:" in line:
            print(f"  {line.strip()}")
    
    return elapsed, result.returncode

def main():
    limit = 100  # Process 100 ads for comparison
    
    print(f"\n{'='*60}")
    print(f"Comparing NER backends with {limit} ads")
    print(f"{'='*60}\n")
    
    # Test SpaCy
    print("1. Testing SpaCy backend:")
    spacy_time, spacy_code = run_classifier(
        "src/config/fb_ad_categorizer/hybrid_classifier_config.yml", 
        limit
    )
    
    if spacy_code != 0:
        print("  ERROR: SpaCy run failed!")
    
    print("\n" + "-"*60 + "\n")
    
    # Test Flair
    print("2. Testing Flair backend:")
    flair_time, flair_code = run_classifier(
        "src/config/fb_ad_categorizer/hybrid_classifier_flair_config.yml", 
        limit
    )
    
    if flair_code != 0:
        print("  ERROR: Flair run failed!")
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY:")
    print(f"{'='*60}")
    print(f"SpaCy time: {spacy_time:.2f} seconds ({limit/spacy_time:.2f} ads/sec)")
    print(f"Flair time: {flair_time:.2f} seconds ({limit/flair_time:.2f} ads/sec)")
    print(f"Speed ratio: SpaCy is {flair_time/spacy_time:.2f}x faster than Flair")
    
    if flair_time < spacy_time:
        print("\n⚠️  WARNING: Flair is faster than SpaCy! This is unexpected.")
        print("   Flair should be 3-10x SLOWER than SpaCy.")
        print("   Check if Flair is actually processing entities.")

if __name__ == "__main__":
    main()