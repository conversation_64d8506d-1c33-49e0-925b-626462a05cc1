#!/usr/bin/env python3
"""
Parse AFFF Filings Case Shell CSV data to extract structured information.
Interactive script with rich formatting and multiple export options.
"""

import csv
import json
import re
import time
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

from rich.console import Console
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
from rich.progress import track, Progress


def parse_docket_text(text):
    """
    Parse a single docket text entry to extract structured information.
    
    Args:
        text: The docket text string to parse
        
    Returns:
        dict: Parsed information with keys: plaintiff, defendant, attorney, case_number
    """
    # Initialize result
    result = {
        'plaintiff': None,
        'defendant': None,
        'attorney': None,
        'case_number': None
    }
    
    # Extract case number first - look for patterns like "2:25-cv-00052-RMG" or "2:25-cv-00052"
    case_match = re.search(r'Case\s*#?\s*(\d+:\d+-cv-\d+)(?:-[A-Z]+)?', text)
    if case_match:
        result['case_number'] = case_match.group(1)
    else:
        # Sometimes case number appears without "Case #" prefix
        case_match = re.search(r'(\d+:\d+-cv-\d+)(?:-[A-Z]+)?', text)
        if case_match:
            result['case_number'] = case_match.group(1)
    
    # Extract attorney name before removing patterns
    # The attorney name appears in different patterns:
    # 1. "...receipt number ASCDC-12183581). Frazer, Thomas)"
    # 2. "...receipt number ASCDC-12183581). (Attachments: # 1 Summons)Pels, Jon)"
    # 3. "...receipt number ASCDC-12183581). Wilson, Nicholas)"
    
    # Look for pattern: ). LastName, FirstName) or )LastName, FirstName)
    # Updated to handle names with capital letters in the middle (e.g., MaryAnne, McCloud)
    # and names with spaces (e.g., Ava Marie)
    # Also handles compound last names like "Freeman Cappio, Gretchen"
    # Also handles parenthetical names like "(Sampson, Benjamin)"
    attorney_patterns = [
        # Parenthetical patterns: (LastName, FirstName) - these should be checked first as they're more specific
        r'\(([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*Modified docket text.*?$',  # (Sampson, Benjamin) Modified docket text on 5/29/2025 (sshe, ). (Entered: 05/29/2025)
        r'\(([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*\([^)]*\)\s*$',  # (Sampson, Benjamin) (Entered: 05/29/2025)
        r'\(([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*$',  # (Sampson, Benjamin) at end
        r'\(([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*Modified docket text.*?$',  # (Freeman Cappio, Gretchen) Modified docket text...
        r'\(([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*\([^)]*\)\s*$',  # (Freeman Cappio, Gretchen) (Entered: ...)
        r'\(([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*$',  # (Freeman Cappio, Gretchen) at end
        
        # Standard patterns: LastName, FirstName
        r'\)\s*\.?\s*([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',  # ). Frazer, Thomas) or Cavaco, Ava Marie)
        r'\)([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',           # )Pels, Jon)
        r'\)\s*([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*(?:Modified|$)',  # ) Wilson, Nicholas) Modified
        r'\)\s+([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',  # ) Cavaco, Ava Marie)
        
        # Compound last name patterns: FirstName LastName, FirstName (e.g., Freeman Cappio, Gretchen)
        r'\)\s*\.?\s*([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',  # ). Freeman Cappio, Gretchen)
        r'\)([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',           # )Freeman Cappio, Gretchen)
        r'\)\s*([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*(?:Modified|$)',  # ) Freeman Cappio, Gretchen) Modified
        r'\)\s+([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',  # ) Freeman Cappio, Gretchen)
    ]
    
    for pattern in attorney_patterns:
        attorney_match = re.search(pattern, text)
        if attorney_match:
            name_part1 = attorney_match.group(1)  # Could be LastName or FirstName LastName
            name_part2 = attorney_match.group(2)  # Could be FirstName or just FirstName
            
            # Check if name_part1 contains a space (indicating compound last name format)
            if ' ' in name_part1:
                # Format: "FirstName LastName, FirstName" -> "FirstName FirstName LastName"
                result['attorney'] = f"{name_part2} {name_part1}"
            else:
                # Format: "LastName, FirstName" -> "FirstName LastName"
                result['attorney'] = f"{name_part2} {name_part1}"
            break
    
    # Now clean the text for versus extraction
    # Remove everything after the attorney name to avoid confusion
    if result['attorney']:
        # Find where the attorney pattern ends and remove everything after
        for pattern in attorney_patterns:
            match = re.search(pattern, text)
            if match:
                text = text[:match.end()]
                break
    
    # Remove common noise patterns
    text_clean = re.sub(r'\(\s*Filing fee[^)]+\)', '', text)
    text_clean = re.sub(r'\(Attachments:[^)]+\)', '', text_clean)
    text_clean = re.sub(r'Charleston Division\.?\s*', '', text_clean)
    
    # Extract versus information (plaintiff vs defendant)
    # Pattern: COMPLAINT [Plaintiff] vs [Defendant]
    versus_match = re.search(r'COMPLAINT\s+(.+?)\s+vs\s+(.+?)(?:\s+\.|$)', text_clean)
    if versus_match:
        plaintiff = versus_match.group(1).strip()
        defendant_part = versus_match.group(2).strip()
        
        # Clean up plaintiff name - remove "for the Estate of" if present
        plaintiff = re.sub(r'\s+for the Estate of.+', '', plaintiff)
        result['plaintiff'] = plaintiff
        
        # Clean up defendant
        defendant = defendant_part
        # Remove parenthetical content like (f/k/a ...)
        defendant = re.sub(r'\s*\([^)]+\)', '', defendant)
        # Remove trailing dots and spaces
        defendant = defendant.rstrip('. ')
        # Handle "et al" variations
        defendant = re.sub(r',?\s*et\.?\s*al\.?', ' et al', defendant)
        
        result['defendant'] = defendant.strip()
    
    return result


def process_csv(input_file, output_file):
    """
    Process the CSV file and write parsed results to a new CSV.
    
    Args:
        input_file: Path to input CSV file
        output_file: Path to output CSV file
    """
    results = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        # Skip the header line
        next(f)
        
        for line in f:
            # Parse CSV line manually to handle quotes properly
            parts = []
            current = []
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(''.join(current).strip())
                    current = []
                else:
                    current.append(char)
            
            # Add the last part
            if current:
                parts.append(''.join(current).strip())
            
            if len(parts) >= 3:
                date_str = parts[0]
                doc_num = parts[1]
                docket_text = parts[2].strip('"')
                
                # Parse the docket text
                parsed = parse_docket_text(docket_text)
                
                # Add additional fields
                parsed['date'] = date_str
                parsed['doc_num'] = doc_num
                
                # Create versus string
                if parsed['plaintiff'] and parsed['defendant']:
                    parsed['versus'] = f"{parsed['plaintiff']} vs {parsed['defendant']}"
                else:
                    parsed['versus'] = None
                
                results.append(parsed)
    
    # Write results to output CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['date', 'doc_num', 'plaintiff', 'defendant', 'versus', 'attorney', 'case_number']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    print(f"Processed {len(results)} records")
    print(f"Output written to: {output_file}")
    
    # Print some statistics
    attorneys = set()
    cases_with_numbers = 0
    for r in results:
        if r['attorney']:
            attorneys.add(r['attorney'])
        if r['case_number']:
            cases_with_numbers += 1
    
    print(f"\nStatistics:")
    print(f"- Unique attorneys found: {len(attorneys)}")
    print(f"- Cases with case numbers: {cases_with_numbers}")
    print(f"- Sample attorneys: {list(attorneys)[:5]}")


class AFFFFilingsParser:
    """Interactive AFFF Filings Parser with rich formatting."""
    
    def __init__(self):
        self.console = Console()
        self.input_file = Path("/scripts/analysis/AFFF/AFFF-Filings-Case-Shell.csv")
        self.parsed_data = []
        self.attorney_data = defaultdict(lambda: {"law_firm": "", "filings": []})
        
    def load_data(self):
        """Load and parse CSV data into memory."""
        if not self.input_file.exists():
            self.console.print(f"[red]Error: Input file not found: {self.input_file}[/red]")
            return False
            
        self.console.print("[cyan]Loading data...[/cyan]")
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            # Skip header
            next(f)
            
            for line in track(f, description="Parsing records..."):
                # Parse CSV line manually to handle quotes properly
                parts = []
                current = []
                in_quotes = False
                
                for char in line:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == ',' and not in_quotes:
                        parts.append(''.join(current).strip())
                        current = []
                    else:
                        current.append(char)
                
                # Add the last part
                if current:
                    parts.append(''.join(current).strip())
                
                if len(parts) >= 3:
                    date_str = parts[0]
                    doc_num = parts[1]
                    docket_text = parts[2].strip('"')
                    
                    # Parse the docket text
                    parsed = parse_docket_text(docket_text)
                    
                    # Add additional fields
                    parsed['date'] = date_str
                    parsed['doc_num'] = doc_num
                    parsed['docket_text'] = docket_text  # Store original text for analysis
                    
                    # Create versus string
                    if parsed['plaintiff'] and parsed['defendant']:
                        parsed['versus'] = f"{parsed['plaintiff']} vs {parsed['defendant']}"
                    else:
                        parsed['versus'] = None
                    
                    self.parsed_data.append(parsed)
                    
                    # Aggregate by attorney
                    if parsed['attorney']:
                        attorney_name = self._normalize_attorney_name(parsed['attorney'])
                        self.attorney_data[attorney_name]["filings"].append({
                            "date": parsed['date'],
                            "doc_num": parsed['doc_num'],
                            "docket_num": parsed['case_number'] or "N/A",
                            "versus": parsed['versus'] or "N/A"
                        })
        
        self.console.print(f"[green]✓ Loaded {len(self.parsed_data)} records[/green]")
        return True
    
    def _normalize_attorney_name(self, name: str) -> str:
        """Normalize attorney name for consistency."""
        # Ensure proper title case
        parts = name.split()
        return ' '.join(word.capitalize() for word in parts)
    
    def export_csv(self):
        """Export parsed data to CSV."""
        output_file = Prompt.ask(
            "Enter output CSV filename",
            default="parsed_afff_filings.csv"
        )
        
        output_path = Path(output_file)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['date', 'doc_num', 'plaintiff', 'defendant', 'versus', 'attorney', 'case_number']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in self.parsed_data:
                writer.writerow(result)
        
        self.console.print(f"[green]✓ Exported to {output_path}[/green]")
    
    def show_attorney_summary(self):
        """Display attorney filing summary sorted by number of filings."""
        # Create summary table
        table = Table(title="Attorney Filing Summary", show_header=True, header_style="bold magenta")
        table.add_column("Rank", style="cyan", width=6)
        table.add_column("Attorney Name", style="yellow", width=30)
        table.add_column("Number of Filings", style="green", width=20, justify="right")
        
        # Sort attorneys by filing count
        attorney_summary = [
            (name, len(data["filings"])) 
            for name, data in self.attorney_data.items()
        ]
        attorney_summary.sort(key=lambda x: x[1], reverse=True)
        
        # Calculate total filings
        total_filings = sum(count for _, count in attorney_summary)
        
        # Add rows to table
        for rank, (attorney, count) in enumerate(attorney_summary, 1):
            table.add_row(str(rank), attorney, f"{count:,}")
        
        # Add separator row
        table.add_row("", "─" * 28, "─" * 18, style="dim")
        
        # Add total row
        table.add_row(
            "",
            "[bold cyan]TOTAL[/bold cyan]",
            f"[bold green]{total_filings:,}[/bold green]"
        )
        
        self.console.print(table)
        
        # Show summary statistics
        self.console.print(f"\n[cyan]Total unique attorneys: {len(attorney_summary)}[/cyan]")
        self.console.print(f"[cyan]Total filings: {total_filings:,}[/cyan]")
        self.console.print(f"[cyan]Average filings per attorney: {total_filings/len(attorney_summary):.1f}[/cyan]")
    
    def export_attorney_json(self):
        """Export attorney data to JSON with law firm placeholders."""
        output_file = Prompt.ask(
            "Enter output JSON filename",
            default="attorney_filings.json"
        )
        
        # Prepare data for export
        export_data = {}
        
        # Sort attorneys by filing count for better readability
        attorney_summary = [
            (name, data) 
            for name, data in self.attorney_data.items()
        ]
        attorney_summary.sort(key=lambda x: len(x[1]["filings"]), reverse=True)
        
        for attorney_name, data in attorney_summary:
            export_data[attorney_name] = {
                "law_firm": data["law_firm"],  # Empty for now
                "filing_count": len(data["filings"]),
                "filings": data["filings"]
            }
        
        # Write JSON file
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.console.print(f"[green]✓ Exported attorney data to {output_path}[/green]")
    
    def search_attorney_law_firms(self):
        """Search for law firms associated with attorneys using web search."""
        self.console.print("\n[cyan]Law Firm Search Options:[/cyan]")
        self.console.print("1. Search top 10 attorneys")
        self.console.print("2. Search specific attorney")
        self.console.print("3. Search all attorneys (may take a while)")
        self.console.print("4. Back to main menu")
        
        choice = Prompt.ask("Select option", choices=["1", "2", "3", "4"], default="1")
        
        if choice == "4":
            return
            
        attorneys_to_search = []
        
        if choice == "1":
            # Get top 10 attorneys
            attorney_summary = [
                (name, len(data["filings"])) 
                for name, data in self.attorney_data.items()
            ]
            attorney_summary.sort(key=lambda x: x[1], reverse=True)
            attorneys_to_search = [name for name, _ in attorney_summary[:10]]
            
        elif choice == "2":
            # Search specific attorney
            attorney_name = Prompt.ask("Enter attorney name")
            matching = [name for name in self.attorney_data.keys() 
                       if attorney_name.lower() in name.lower()]
            
            if not matching:
                self.console.print("[red]No matching attorney found[/red]")
                return
            elif len(matching) == 1:
                attorneys_to_search = matching
            else:
                self.console.print("\n[yellow]Multiple matches found:[/yellow]")
                for i, name in enumerate(matching, 1):
                    self.console.print(f"{i}. {name}")
                idx = IntPrompt.ask("Select attorney", default=1) - 1
                attorneys_to_search = [matching[idx]]
                
        elif choice == "3":
            # Search all attorneys
            if not Confirm.ask(f"Search for law firms for all {len(self.attorney_data)} attorneys? This may take several minutes"):
                return
            attorneys_to_search = list(self.attorney_data.keys())
        
        # Perform searches
        self._perform_law_firm_searches(attorneys_to_search)
    
    def _perform_law_firm_searches(self, attorneys: List[str]):
        """Perform web searches to find law firms for given attorneys."""
        results = {}
        
        with Progress() as progress:
            task = progress.add_task("[cyan]Searching for law firms...", total=len(attorneys))
            
            for attorney in attorneys:
                # Search for law firm
                law_firm = self._search_single_attorney(attorney)
                
                if law_firm:
                    results[attorney] = law_firm
                    self.attorney_data[attorney]["law_firm"] = law_firm
                
                progress.update(task, advance=1)
                
                # Rate limiting - be respectful to search APIs
                if len(attorneys) > 1:
                    time.sleep(1)  # 1 second delay between searches
        
        # Display results
        self._display_law_firm_results(results)
        
        # Ask if user wants to save
        if results and Confirm.ask("\nSave law firm data?"):
            self._save_law_firm_data()
    
    def _search_single_attorney(self, attorney_name: str) -> Optional[str]:
        """Search for a single attorney's law firm."""
        try:
            # Construct search query
            # Add "AFFF" or "3M" to make the search more specific to mass tort
            search_query = f'"{attorney_name}" attorney "law firm" AFFF OR 3M mass tort'
            
            # Use WebSearch tool if available
            from scripts import WebSearch
            
            results = WebSearch(
                query=search_query,
                allowed_domains=["law.com", "reuters.com", "bloomberg.com", "linkedin.com"]
            )
            
            # Parse results to extract law firm name
            # This is a simplified version - you'd want more sophisticated parsing
            if results:
                # Look for patterns like "partner at [Law Firm]" or "[Law Firm] attorney"
                import re
                
                # Common patterns for law firm mentions
                patterns = [
                    r'(?:partner|attorney|lawyer|counsel)\s+(?:at|with)\s+([A-Z][A-Za-z\s&,]+(?:LLP|LLC|PC|PA|PLLC))',
                    r'([A-Z][A-Za-z\s&,]+(?:LLP|LLC|PC|PA|PLLC))\s+(?:partner|attorney|lawyer)',
                    r'(?:works?\s+at|employed\s+by)\s+([A-Z][A-Za-z\s&,]+(?:LLP|LLC|PC|PA|PLLC))',
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, str(results), re.IGNORECASE)
                    if match:
                        return match.group(1).strip()
            
            return None
            
        except Exception as e:
            self.console.print(f"[yellow]Search failed for {attorney_name}: {str(e)}[/yellow]")
            return None
    
    def _display_law_firm_results(self, results: Dict[str, str]):
        """Display the law firm search results."""
        if not results:
            self.console.print("\n[yellow]No law firms found[/yellow]")
            return
            
        table = Table(title="Law Firm Search Results", show_header=True, header_style="bold magenta")
        table.add_column("Attorney", style="yellow", width=30)
        table.add_column("Law Firm", style="green", width=40)
        
        for attorney, firm in results.items():
            table.add_row(attorney, firm)
            
        self.console.print("\n")
        self.console.print(table)
        self.console.print(f"\n[cyan]Found law firms for {len(results)} attorneys[/cyan]")
    
    def _save_law_firm_data(self):
        """Save the updated attorney data with law firms."""
        output_file = Prompt.ask(
            "Enter filename to save law firm data",
            default="attorney_law_firms.json"
        )
        
        # Prepare data for export
        export_data = {}
        
        for attorney_name, data in self.attorney_data.items():
            if data["law_firm"]:  # Only include attorneys with law firms
                export_data[attorney_name] = {
                    "law_firm": data["law_firm"],
                    "filing_count": len(data["filings"]),
                    "filings": data["filings"]
                }
        
        # Write JSON file
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.console.print(f"[green]✓ Saved law firm data to {output_path}[/green]")
    
    def show_statistics(self):
        """Display parsing statistics."""
        failed_extractions = [r for r in self.parsed_data if not r['attorney']]
        stats_panel = Panel.fit(
            f"""[bold cyan]Parsing Statistics[/bold cyan]
            
Total Records: [yellow]{len(self.parsed_data)}[/yellow]
Records with Attorneys: [green]{sum(1 for r in self.parsed_data if r['attorney'])}[/green]
Records with Case Numbers: [green]{sum(1 for r in self.parsed_data if r['case_number'])}[/green]
Unique Attorneys: [yellow]{len(self.attorney_data)}[/yellow]
Failed Attorney Extractions: [red]{len(failed_extractions)}[/red]
            """,
            title="Statistics",
            border_style="blue"
        )
        self.console.print(stats_panel)
    
    def show_failed_attorney_extractions(self):
        """Display entries where attorney extraction failed."""
        failed_extractions = [r for r in self.parsed_data if not r['attorney']]
        
        if not failed_extractions:
            self.console.print("[green]All entries have attorney information extracted![/green]")
            return
        
        self.console.print(f"\n[red]Found {len(failed_extractions)} entries without attorney names[/red]")
        
        # Show first 20 entries for analysis
        limit = min(20, len(failed_extractions))
        
        table = Table(title=f"Failed Attorney Extractions (showing first {limit} of {len(failed_extractions)})", 
                     show_header=True, header_style="bold red")
        table.add_column("Date", style="cyan", width=12)
        table.add_column("Doc #", style="yellow", width=8)
        table.add_column("Docket Text (truncated)", style="white", width=80)
        
        for i, record in enumerate(failed_extractions[:limit]):
            # Truncate long docket text for display
            docket_text = record.get('docket_text', 'N/A')
            if len(docket_text) > 80:
                docket_text = docket_text[:77] + "..."
            
            table.add_row(
                record['date'],
                record['doc_num'], 
                docket_text
            )
        
        self.console.print(table)
        
        # Option to export full list
        if Confirm.ask(f"\nExport all {len(failed_extractions)} failed extractions to CSV?"):
            self._export_failed_extractions(failed_extractions)
    
    def _export_failed_extractions(self, failed_extractions: List[dict]):
        """Export failed attorney extractions to CSV for analysis."""
        output_file = Prompt.ask(
            "Enter output CSV filename for failed extractions",
            default="failed_attorney_extractions.csv"
        )
        
        output_path = Path(output_file)
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['date', 'doc_num', 'docket_text', 'case_number', 'plaintiff', 'defendant']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            writer.writeheader()
            for record in failed_extractions:
                writer.writerow({
                    'date': record['date'],
                    'doc_num': record['doc_num'],
                    'docket_text': record['docket_text'],
                    'case_number': record.get('case_number', ''),
                    'plaintiff': record.get('plaintiff', ''),
                    'defendant': record.get('defendant', '')
                })
        
        self.console.print(f"[green]✓ Exported {len(failed_extractions)} failed extractions to {output_path}[/green]")
    
    def run(self):
        """Run the interactive parser."""
        # Display welcome message
        self.console.print(Panel.fit(
            "[bold cyan]AFFF Filings Parser[/bold cyan]\n"
            "Parse and analyze AFFF case filings data",
            border_style="blue"
        ))
        
        # Load data
        if not self.load_data():
            return
        
        # Main menu loop
        while True:
            self.console.print("\n[bold cyan]Main Menu:[/bold cyan]")
            self.console.print("1. Show parsing statistics")
            self.console.print("2. Show attorney summary (sorted by filings)")
            self.console.print("3. Show failed attorney extractions")
            self.console.print("4. Search for attorney law firms")
            self.console.print("5. Export parsed data to CSV")
            self.console.print("6. Export attorney data to JSON")
            self.console.print("7. Exit")
            
            choice = Prompt.ask(
                "\nSelect an option",
                choices=["1", "2", "3", "4", "5", "6", "7"],
                default="1"
            )
            
            if choice == "1":
                self.show_statistics()
            elif choice == "2":
                self.show_attorney_summary()
            elif choice == "3":
                self.show_failed_attorney_extractions()
            elif choice == "4":
                self.search_attorney_law_firms()
            elif choice == "5":
                self.export_csv()
            elif choice == "6":
                self.export_attorney_json()
            elif choice == "7":
                if Confirm.ask("Are you sure you want to exit?"):
                    self.console.print("[yellow]Goodbye![/yellow]")
                    break



def test_attorney_parsing():
    """Test the attorney parsing with various formats."""
    test_cases = [
        {
            'text': 'COMPLAINT CITY OF WESTMINSTER, COLORADO vs THE 3M COMPANY Charleston Division. ( Filing fee $ 405 receipt number ASCDC-12255412). (Attachments: # 1 Summons)Freeman Cappio, Gretchen) Assigned 2:25-cv-00772-RMG. Modified on 2/11/2025 (cwil, ). (Entered: 02/10/2025)',
            'expected': 'Gretchen Freeman Cappio'
        },
        {
            'text': 'COMPLAINT Something vs Something. Filing fee receipt number ASCDC-12183581). Frazer, Thomas)',
            'expected': 'Thomas Frazer'
        },
        {
            'text': 'COMPLAINT Something vs Something. Filing fee receipt number ASCDC-12183581). (Attachments: # 1 Summons)Pels, Jon)',
            'expected': 'Jon Pels'
        },
        {
            'text': 'Letter from COMPLAINT Gerald Heinzel vs The 3M Company Charleston Division. ( Filing fee $ 405 receipt number ASCDC-12481165). (Attachments: # 1 Summons Summons on Complaint - Gerald Heinzel)(Sampson, Benjamin) (Entered: 05/29/2025)',
            'expected': 'Benjamin Sampson'
        },
        {
            'text': 'Letter from COMPLAINT David Holland vs The 3M Company Charleston Division. ( Filing fee $ 405 receipt number ASCDC-12481176).. (Attachments: # 1 Summons Summons on Complaint David Holland v. 3M)(Sampson, Benjamin) (Entered: 05/29/2025)',
            'expected': 'Benjamin Sampson'
        },
        {
            'text': 'Letter from COMPLAINT Mark Lepore vs The 3M Company Charleston Division. ( Filing fee $ 405 receipt number ASCDC-12481182).. (Attachments: # 1 Summons Summons on Complaint Lepore v. 3M)(Sampson, Benjamin) (Entered: 05/29/2025)',
            'expected': 'Benjamin Sampson'
        },
        {
            'text': 'Letter from COMPLAINT Peter Trapani vs The 3M Company Charleston Division. ( Filing fee $ 405 receipt number ASCDC-12481157).. (Attachments: # 1 Summons Summons on Complaint Trapani v. 3M)(Sampson, Benjamin) (Entered: 05/29/2025)',
            'expected': 'Benjamin Sampson'
        },
        {
            'text': 'Complaint Roy Gault v. 3M Company. ( Filing fee $ 405 receipt number ASCDC-12480922). (Attachments: # 1 Summons Summons on Complaint of Roy Gault)(Sampson, Benjamin) Modified docket text on 5/29/2025 (sshe, ). (Entered: 05/29/2025)',
            'expected': 'Benjamin Sampson'
        }
    ]
    
    console = Console()
    console.print("[bold cyan]Testing Attorney Name Parsing[/bold cyan]")
    
    for i, test_case in enumerate(test_cases, 1):
        result = parse_docket_text(test_case['text'])
        extracted = result.get('attorney', 'None')
        expected = test_case['expected']
        
        status = "[green]✓[/green]" if extracted == expected else "[red]✗[/red]"
        console.print(f"{status} Test {i}: Expected '{expected}', Got '{extracted}'")
        
        if extracted != expected:
            console.print(f"   Text: {test_case['text'][:100]}...")


def main():
    """Main function to run the interactive parser."""
    import sys
    
    # Check if running in test mode
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_attorney_parsing()
        return
    
    parser = AFFFFilingsParser()
    parser.run()


if __name__ == "__main__":
    main()