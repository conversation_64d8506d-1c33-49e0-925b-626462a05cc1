#!/usr/bin/env python3
"""
Demo script showing the 7-day local file checking functionality.
This demonstrates how the system now checks for existing files over the last 7 days.
"""

from datetime import date, timedelta
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def demonstrate_7_day_logic():
    """Demonstrate the 7-day lookback logic."""
    print("📅 7-Day Local File Check Logic Demonstration")
    print("=" * 60)
    
    # Example scenario
    end_date = date(2025, 1, 11)  # Friday - the date we're processing
    start_date = end_date - timedelta(days=6)  # 7 days total including end_date
    
    print(f"🎯 Processing Date (end_date): {end_date.strftime('%Y-%m-%d (%A)')}")
    print(f"📁 Looking back to: {start_date.strftime('%Y-%m-%d (%A)')}")
    print(f"📊 Total days checked: 7 days")
    print()
    
    print("📋 Days that will be checked:")
    current_date = start_date
    day_count = 1
    while current_date <= end_date:
        print(f"   Day {day_count}: {current_date.strftime('%Y-%m-%d (%A)')}")
        current_date += timedelta(days=1)
        day_count += 1
    
    print()
    print("🔍 File Pattern Matching:")
    print("   Court ID: nysd")
    print("   Docket Number: 1:25-cv-08592")
    print("   Extracted Pattern: 25_08592")
    print("   Search Pattern: nysd_25_08592*")
    print()
    
    example_files = [
        ("nysd_25_08592_Smith_v_Jones.json", True, "✅ MATCH - Exact pattern"),
        ("nysd_25_08592_Different_Case.json", True, "✅ MATCH - Same docket, different versus"),
        ("NYSD_25_08592_Case.json", True, "✅ MATCH - Case insensitive"),
        ("cand_25_08592_Case.json", False, "❌ NO MATCH - Different court"),
        ("nysd_24_08592_Case.json", False, "❌ NO MATCH - Different year"),
        ("nysd_25_12345_Case.json", False, "❌ NO MATCH - Different case number"),
    ]
    
    print("📄 Example File Matching:")
    for filename, matches, description in example_files:
        print(f"   {filename:35} | {description}")
    
    print()
    print("⚡ Processing Logic:")
    print("   1. Extract docket number from HTML: '1:25-cv-08592-RMB-SAK'")
    print("   2. Normalize to 13 characters: '1:25-cv-08592'")
    print("   3. Extract pattern for files: '25_08592'")
    print("   4. Search last 7 days for files starting with 'nysd_25_08592'")
    print("   5. If found with PDF/ZIP or valid skip reason → SKIP processing")
    print("   6. If not found → PROCEED with download")


def demonstrate_date_scenarios():
    """Demonstrate different date scenarios."""
    print("\n📅 Different Date Scenarios")
    print("=" * 60)
    
    scenarios = [
        ("New Year's Eve", date(2025, 1, 1)),
        ("End of February", date(2025, 2, 28)),
        ("Month Boundary", date(2025, 3, 1)),
        ("Weekend Processing", date(2025, 1, 12)),  # Saturday
    ]
    
    for scenario_name, end_date in scenarios:
        start_date = end_date - timedelta(days=6)
        print(f"📋 {scenario_name}:")
        print(f"   End Date: {end_date.strftime('%Y-%m-%d (%A)')}")
        print(f"   Start Date: {start_date.strftime('%Y-%m-%d (%A)')}")
        print(f"   Days: {(end_date - start_date).days + 1}")
        print()


def demonstrate_implementation_benefits():
    """Show the benefits of this implementation."""
    print("🎯 Implementation Benefits")
    print("=" * 60)
    
    benefits = [
        "Avoids reprocessing recently downloaded cases",
        "Efficient 7-day lookback window",
        "Case-insensitive filename matching",
        "Handles different docket formats consistently",
        "Supports both exact and pattern-based matching",
        "Integrates with existing file structure",
        "Works for both explicitly requested and report-scraped cases",
        "Provides clear logging for debugging",
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"   {i}. ✅ {benefit}")
    
    print()
    print("🔧 Technical Implementation:")
    print("   • PacerFileManager.check_if_downloaded_last_7_days_by_pattern()")
    print("   • DocketProcessor.verify_case() integration")
    print("   • Pattern matching: {court_id}_{year}_{case_number}")
    print("   • Comprehensive test suite validation")


if __name__ == "__main__":
    print("🚀 7-Day Local File Check Implementation Demo")
    print("=" * 80)
    
    demonstrate_7_day_logic()
    demonstrate_date_scenarios()
    demonstrate_implementation_benefits()
    
    print("\n" + "=" * 80)
    print("✅ Implementation Complete!")
    print("📁 The system now checks the last 7 days for existing files")
    print("🎯 Files matching {court_id}_NN_NNNNN pattern will be detected")
    print("⏭️  Matching cases will be skipped to avoid duplicate processing")
    print("=" * 80)