"""Test async FB query directly"""
import asyncio
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius')

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.services.fb_archive.query_service import FBArchiveQueryService
from boto3.dynamodb.conditions import Key

class MockConfig:
    aws_region = 'us-west-2'
    dynamodb_endpoint = None

async def test_query():
    config = MockConfig()
    
    # Test direct query
    async with AsyncDynamoDBStorage(config) as storage:
        print("Testing direct storage query...")
        items = await storage.query(
            table_name='FBAdArchive',
            key_condition=Key('StartDate').eq('20250211'),
            index_name='StartDate-index'
        )
        print(f"Direct query returned {len(items)} items")
        
        # Test repository query
        print("\nTesting repository query...")
        repo = FBArchiveRepository(storage)
        items2 = await repo.query_by_start_date('20250211')
        print(f"Repository query returned {len(items2)} items")
        
        # Test service query
        print("\nTesting service query...")
        service = FBArchiveQueryService(repo)
        items3 = await service.get_ads_by_date_range('20250211', '20250212')
        print(f"Service query returned {len(items3)} items")

# Run the test
asyncio.run(test_query())