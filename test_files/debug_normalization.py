#!/usr/bin/env python3
"""
Debug script to test law firm normalization
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.utils.law_firm_normalizer import normalize_law_firm_name

# Test the exact strings
test_strings = [
    "The Vort Legal Group",
    "Lieff Cabraser Heimann & Bernstein ; The Vort Legal Group ; Equal Justice Society",
    "The Vont Legal Group"
]

print("Testing law firm normalization:")
print("=" * 50)

for test_str in test_strings:
    normalized = normalize_law_firm_name(test_str)
    print(f"Input:  '{test_str}'")
    print(f"Output: '{normalized}'")
    print(f"Changed: {test_str != normalized}")
    print("-" * 30)

# Test individual components if semicolon-separated
semicolon_test = "Lieff Cabraser Heiman<PERSON> & Bernstein ; The Vort Legal Group ; Equal Justice Society"
print(f"\nTesting semicolon-separated normalization:")
print(f"Full string: '{semicolon_test}'")

firms = [firm.strip() for firm in semicolon_test.split(';')]
print(f"Split firms: {firms}")

for i, firm in enumerate(firms):
    normalized_firm = normalize_law_firm_name(firm)
    print(f"Firm {i+1}: '{firm}' -> '{normalized_firm}' (changed: {firm != normalized_firm})")

# Test the reports module logic
print(f"\nTesting reports module logic:")
def normalize_firm_field_reports(name_str):
    """Handle both single firms and semicolon-separated firms (reports module logic)"""
    if ';' in name_str:
        firms = [firm.strip() for firm in name_str.split(';')]
        normalized_firms = []
        for firm in firms:
            if firm:
                normalized_firm = normalize_law_firm_name(firm)
                normalized_firms.append(normalized_firm)
        return ' ; '.join(normalized_firms)
    else:
        return normalize_law_firm_name(name_str)

result = normalize_firm_field_reports(semicolon_test)
print(f"Reports logic result: '{result}'")
print(f"Contains 'The Yost Legal Group': {'The Yost Legal Group' in result}")