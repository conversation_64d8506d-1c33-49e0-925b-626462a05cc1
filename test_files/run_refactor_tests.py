#!/usr/bin/env python3
"""Run tests for refactored components."""
import subprocess
import sys
from pathlib import Path

# Test files to run
TEST_FILES = [
    "tests/unit/infrastructure/storage/test_s3_async.py",
    "tests/unit/services/ai/test_ai_orchestrator.py",
    "tests/unit/infrastructure/external/test_deepseek_client.py",
]


def run_tests():
    """Run all refactored component tests."""
    print("Running tests for refactored components...")
    print("=" * 60)
    
    failed_tests = []
    
    for test_file in TEST_FILES:
        print(f"\nRunning {test_file}...")
        print("-" * 40)
        
        # Run pytest on the test file
        result = subprocess.run(
            [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"],
            capture_output=True,
            text=True
        )
        
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
            
        if result.returncode != 0:
            failed_tests.append(test_file)
            print(f"❌ {test_file} FAILED")
        else:
            print(f"✅ {test_file} PASSED")
            
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if failed_tests:
        print(f"❌ {len(failed_tests)} test file(s) failed:")
        for test in failed_tests:
            print(f"   - {test}")
        return 1
    else:
        print(f"✅ All {len(TEST_FILES)} test files passed!")
        return 0


if __name__ == "__main__":
    sys.exit(run_tests())