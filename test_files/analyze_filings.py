#!/usr/bin/env python3
"""
Analyze attorney filings data by law firm and date.
"""

import json
from collections import defaultdict
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.table import Table
from rich.panel import Panel

def analyze_filings():
    """Analyze filings by law firm and date."""
    console = Console()
    
    # Load comprehensive data
    with open('test_files/attorney_comprehensive_data.json', 'r') as f:
        data = json.load(f)
    
    # Initialize aggregation structures
    filings_by_firm = defaultdict(int)
    attorneys_by_firm = defaultdict(list)
    filings_by_date = defaultdict(int)
    
    # Process each attorney
    for attorney_name, attorney_data in data['attorneys'].items():
        law_firm = attorney_data['law_firm']
        filing_count = attorney_data['filing_count']
        
        # Aggregate by law firm
        filings_by_firm[law_firm] += filing_count
        attorneys_by_firm[law_firm].append((attorney_name, filing_count))
        
        # Aggregate by date
        for filing in attorney_data['filings']:
            date = filing['date']
            filings_by_date[date] += 1
    
    # Sort law firms by filing count
    sorted_firms = sorted(filings_by_firm.items(), key=lambda x: x[1], reverse=True)
    
    # Display law firm summary
    console.print("\n")
    console.print(Panel.fit("[bold cyan]Filings by Law Firm[/bold cyan]", border_style="blue"))
    
    # Create law firm table
    firm_table = Table(show_header=True, header_style="bold magenta")
    firm_table.add_column("Rank", style="cyan", width=6)
    firm_table.add_column("Law Firm", style="yellow", width=50)
    firm_table.add_column("Attorneys", style="green", width=10, justify="center")
    firm_table.add_column("Total Filings", style="green", width=15, justify="right")
    
    total_filings = 0
    for rank, (firm, count) in enumerate(sorted_firms[:20], 1):  # Top 20 firms
        attorney_count = len(attorneys_by_firm[firm])
        firm_table.add_row(
            str(rank),
            firm,
            str(attorney_count),
            f"{count:,}"
        )
        total_filings += count
    
    # Add separator and total
    firm_table.add_row("", "─" * 48, "─" * 8, "─" * 13, style="dim")
    firm_table.add_row(
        "",
        f"[bold]TOP 20 TOTAL[/bold]",
        f"[bold]{sum(len(attorneys_by_firm[firm]) for firm, _ in sorted_firms[:20])}[/bold]",
        f"[bold]{total_filings:,}[/bold]"
    )
    firm_table.add_row(
        "",
        f"[bold cyan]ALL FIRMS TOTAL ({len(sorted_firms)})[/bold cyan]",
        f"[bold cyan]{len(data['attorneys'])}[/bold cyan]",
        f"[bold cyan]{sum(count for _, count in sorted_firms):,}[/bold cyan]"
    )
    
    console.print(firm_table)
    
    # Show top attorneys for top 3 firms
    console.print("\n")
    console.print(Panel.fit("[bold cyan]Top Attorneys by Firm (Top 3 Firms)[/bold cyan]", border_style="blue"))
    
    for i, (firm, _) in enumerate(sorted_firms[:3]):
        if firm != "Unknown":
            console.print(f"\n[bold yellow]{firm}[/bold yellow]")
            sorted_attorneys = sorted(attorneys_by_firm[firm], key=lambda x: x[1], reverse=True)
            for j, (attorney, count) in enumerate(sorted_attorneys[:5]):  # Top 5 attorneys per firm
                console.print(f"  {j+1}. {attorney}: {count} filings")
    
    # Sort dates chronologically
    sorted_dates = sorted(filings_by_date.items(), 
                         key=lambda x: datetime.strptime(x[0], "%m/%d/%Y"))
    
    # Display date summary
    console.print("\n")
    console.print(Panel.fit("[bold cyan]Filings by Date[/bold cyan]", border_style="blue"))
    
    # Create date table
    date_table = Table(show_header=True, header_style="bold magenta")
    date_table.add_column("Date", style="yellow", width=15)
    date_table.add_column("Filings", style="green", width=10, justify="right")
    date_table.add_column("Cumulative", style="cyan", width=12, justify="right")
    
    cumulative = 0
    dates_to_show = 20  # Show first and last 10 dates
    
    # Show first 10 dates
    for date, count in sorted_dates[:10]:
        cumulative += count
        date_table.add_row(date, str(count), f"{cumulative:,}")
    
    if len(sorted_dates) > dates_to_show:
        date_table.add_row("...", "...", "...", style="dim")
        
        # Calculate cumulative for skipped dates
        cumulative = sum(count for _, count in sorted_dates[:len(sorted_dates)-10])
        
        # Show last 10 dates
        for date, count in sorted_dates[-10:]:
            cumulative += count
            date_table.add_row(date, str(count), f"{cumulative:,}")
    else:
        # Show all remaining dates
        for date, count in sorted_dates[10:]:
            cumulative += count
            date_table.add_row(date, str(count), f"{cumulative:,}")
    
    # Add total row
    date_table.add_row("", "─" * 8, "─" * 10, style="dim")
    date_table.add_row(
        "[bold]TOTAL[/bold]",
        f"[bold]{sum(count for _, count in sorted_dates):,}[/bold]",
        f"[bold]{sum(count for _, count in sorted_dates):,}[/bold]"
    )
    
    console.print(date_table)
    
    # Monthly summary
    console.print("\n")
    console.print(Panel.fit("[bold cyan]Filings by Month[/bold cyan]", border_style="blue"))
    
    # Aggregate by month
    filings_by_month = defaultdict(int)
    for date_str, count in filings_by_date.items():
        date_obj = datetime.strptime(date_str, "%m/%d/%Y")
        month_key = date_obj.strftime("%Y-%m (%B)")
        filings_by_month[month_key] += count
    
    # Sort months chronologically
    sorted_months = sorted(filings_by_month.items(), 
                          key=lambda x: datetime.strptime(x[0][:7], "%Y-%m"))
    
    # Create monthly table
    month_table = Table(show_header=True, header_style="bold magenta")
    month_table.add_column("Month", style="yellow", width=20)
    month_table.add_column("Filings", style="green", width=10, justify="right")
    month_table.add_column("Percentage", style="cyan", width=12, justify="right")
    
    total = sum(count for _, count in sorted_months)
    
    for month, count in sorted_months:
        percentage = (count / total) * 100
        month_table.add_row(month, f"{count:,}", f"{percentage:.1f}%")
    
    # Add total row
    month_table.add_row("", "─" * 8, "─" * 10, style="dim")
    month_table.add_row(
        "[bold]TOTAL[/bold]",
        f"[bold]{total:,}[/bold]",
        "[bold]100.0%[/bold]"
    )
    
    console.print(month_table)
    
    # Save aggregated data to JSON
    output_data = {
        "filings_by_law_firm": [
            {
                "law_firm": firm,
                "total_filings": count,
                "attorney_count": len(attorneys_by_firm[firm]),
                "attorneys": [
                    {"name": name, "filings": filing_count}
                    for name, filing_count in sorted(attorneys_by_firm[firm], 
                                                    key=lambda x: x[1], 
                                                    reverse=True)
                ]
            }
            for firm, count in sorted_firms
        ],
        "filings_by_date": [
            {"date": date, "count": count}
            for date, count in sorted_dates
        ],
        "filings_by_month": [
            {"month": month, "count": count}
            for month, count in sorted_months
        ],
        "summary": {
            "total_filings": sum(count for _, count in sorted_firms),
            "total_attorneys": len(data['attorneys']),
            "total_law_firms": len(sorted_firms),
            "date_range": f"{sorted_dates[0][0]} to {sorted_dates[-1][0]}"
        }
    }
    
    output_file = Path("test_files/filing_analysis.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    console.print(f"\n[green]✓ Analysis saved to {output_file}[/green]")

if __name__ == "__main__":
    analyze_filings()