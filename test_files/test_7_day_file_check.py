#!/usr/bin/env python3
"""
Test for the 7-day local file checking functionality.
Verifies that the enhanced file checking looks back 7 days from end_date
and matches filenames starting with {court_id}_{docket_pattern}.
"""

import unittest
import asyncio
from datetime import date, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class Test7DayFileCheck(unittest.TestCase):
    """Test the 7-day file checking functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_end_date = date(2025, 1, 11)  # Friday
        self.expected_start_date = date(2025, 1, 5)  # Previous Saturday (7 days back)
        
        self.test_court_id = "nysd"
        self.test_docket_pattern = "25_08592"
        
    def test_date_range_calculation(self):
        """Test that 7-day range is calculated correctly."""
        # 7 days back from end_date should include:
        # Jan 11 (end), Jan 10, Jan 9, Jan 8, Jan 7, Jan 6, Jan 5 (start)
        expected_dates = [
            date(2025, 1, 5),   # start (7 days back)
            date(2025, 1, 6),
            date(2025, 1, 7),
            date(2025, 1, 8),
            date(2025, 1, 9),
            date(2025, 1, 10),
            date(2025, 1, 11),  # end
        ]
        
        start_date = self.test_end_date - timedelta(days=6)
        self.assertEqual(start_date, self.expected_start_date)
        
        # Verify all 7 dates are covered
        current_date = start_date
        actual_dates = []
        while current_date <= self.test_end_date:
            actual_dates.append(current_date)
            current_date += timedelta(days=1)
        
        self.assertEqual(len(actual_dates), 7, "Should cover exactly 7 days")
        self.assertEqual(actual_dates, expected_dates, "Should cover correct date range")

    def test_file_manager_7_day_method(self):
        """Test the new check_if_downloaded_last_7_days_by_pattern method."""
        try:
            from src.pacer.file_manager import PacerFileManager
            
            # Create mock file manager
            mock_base_dir = "/mock/data"
            file_manager = PacerFileManager(mock_base_dir)
            
            # Mock the underlying check_if_downloaded_across_dates_by_pattern method
            with patch.object(file_manager, 'check_if_downloaded_across_dates_by_pattern', new_callable=AsyncMock) as mock_check:
                mock_check.return_value = True
                
                async def run_test():
                    result = await file_manager.check_if_downloaded_last_7_days_by_pattern(
                        self.test_end_date, self.test_court_id, self.test_docket_pattern
                    )
                    
                    # Verify the method was called with correct date range
                    mock_check.assert_called_once_with(
                        self.expected_start_date,  # start_date: 7 days back
                        self.test_end_date,        # end_date: provided date
                        self.test_court_id,        # court_id
                        self.test_docket_pattern   # docket_pattern
                    )
                    
                    self.assertTrue(result, "Should return True when files are found")
                
                asyncio.run(run_test())
                
        except ImportError as e:
            self.skipTest(f"Could not import PacerFileManager: {e}")

    def test_filename_pattern_matching(self):
        """Test that filenames are matched correctly with the pattern."""
        test_cases = [
            # (filename, court_id, docket_pattern, should_match, description)
            ("nysd_25_08592_Smith_v_Jones.json", "nysd", "25_08592", True, "Exact pattern match"),
            ("nysd_25_08592_Some_Other_Case.json", "nysd", "25_08592", True, "Same pattern, different versus"),
            ("NYSD_25_08592_Case.json", "nysd", "25_08592", True, "Case insensitive match"),
            ("cand_25_08592_Case.json", "nysd", "25_08592", False, "Different court"),
            ("nysd_24_08592_Case.json", "nysd", "25_08592", False, "Different year"),
            ("nysd_25_12345_Case.json", "nysd", "25_08592", False, "Different docket number"),
            ("random_file.json", "nysd", "25_08592", False, "No pattern match"),
        ]
        
        for filename, court_id, pattern, should_match, description in test_cases:
            start_pattern = f"{court_id.lower()}_{pattern.lower()}"
            actual_match = filename.lower().startswith(start_pattern)
            
            self.assertEqual(actual_match, should_match, 
                f"Pattern matching failed for {description}: {filename} vs {start_pattern}")

    def test_docket_processor_integration(self):
        """Test integration with DocketProcessor.verify_case method."""
        try:
            from src.pacer.docket_processor import DocketProcessor
            
            # Mock all required dependencies
            mock_args = {
                'court_id': 'nysd',
                'iso_date': '20250111',
                'start_date_obj': date(2025, 1, 10),
                'end_date_obj': self.test_end_date,
                'navigator': None,
                'file_manager': Mock(),
                'pacer_db': None,
                'dc_db': None,
                's3_manager': None,
                'gpt_interface': None,
                'config': {},
            }
            
            with patch.object(DocketProcessor, '_load_configurations'):
                with patch.object(DocketProcessor, '_load_relevant_defendants', return_value=[]):
                    processor = DocketProcessor(**mock_args)
                    
                    # Mock the file manager's 7-day check
                    processor.file_manager.check_if_downloaded_last_7_days_by_pattern = AsyncMock(return_value=True)
                    
                    async def run_test():
                        case_details = {
                            'court_id': 'nysd',
                            'docket_num': '1:25-cv-08592',
                            '_is_explicitly_requested': True
                        }
                        
                        # This should return False (skip) because file was found
                        result = await processor.verify_case(case_details)
                        
                        # Verify the 7-day check was called
                        processor.file_manager.check_if_downloaded_last_7_days_by_pattern.assert_called_once_with(
                            self.test_end_date, 'nysd', '25_08592'
                        )
                        
                        self.assertFalse(result, "Should return False when file found in last 7 days")
                        self.assertIn("Last 7 Days", case_details.get('_processing_notes', ""))
                    
                    asyncio.run(run_test())
                    
        except ImportError as e:
            self.skipTest(f"Could not import DocketProcessor: {e}")
        except Exception as e:
            self.skipTest(f"Could not create DocketProcessor: {e}")

    def test_edge_cases(self):
        """Test edge cases for 7-day file checking."""
        # Test with different end dates
        edge_cases = [
            (date(2025, 1, 1), date(2024, 12, 26)),  # New Year's edge
            (date(2025, 2, 28), date(2025, 2, 22)),  # End of February
            (date(2025, 3, 1), date(2025, 2, 23)),   # Month boundary
        ]
        
        for end_date, expected_start in edge_cases:
            actual_start = end_date - timedelta(days=6)
            self.assertEqual(actual_start, expected_start,
                f"7-day calculation failed for end_date {end_date}")
            
            # Verify it's exactly 7 days
            date_count = (end_date - actual_start).days + 1
            self.assertEqual(date_count, 7, f"Should be exactly 7 days for {end_date}")


def run_7_day_file_check_tests():
    """Run all 7-day file check tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test class
    tests = unittest.TestLoader().loadTestsFromTestCase(Test7DayFileCheck)
    test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*80)
    print(f"7-DAY FILE CHECK TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(getattr(result, 'skipped', []))}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\nFAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print(f"\nERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("🔍 Testing 7-Day Local File Check Functionality")
    print("="*80)
    
    success = run_7_day_file_check_tests()
    
    if success:
        print("\n✅ All 7-day file check tests passed!")
        print("📁 File checking will now look back 7 days from end_date")
        print("🎯 Filenames matching {court_id}_NN_NNNNN pattern will be detected")
        print("⏭️  Cases with matching files will be skipped to avoid reprocessing")
    else:
        print("\n❌ Some tests failed - review implementation")
    
    sys.exit(0 if success else 1)