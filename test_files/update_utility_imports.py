#!/usr/bin/env python3
"""Update imports for moved utilities."""
import re
from pathlib import Path
from typing import List, Dict

# Define import mappings
IMPORT_MAPPINGS = [
    # Utils
    (r'from src\.lib\.utils import', 'from src.utils.file_utils import'),
    (r'from lib\.utils import', 'from src.utils.file_utils import'),
    
    # JSON safety
    (r'from src\.lib\.json_safety import', 'from src.utils.json_safety import'),
    (r'from lib\.json_safety import', 'from src.utils.json_safety import'),
    
    # PDF extractor
    (r'from src\.lib\.pdf_extractor import', 'from src.utils.pdf_utils import'),
    (r'from lib\.pdf_extractor import', 'from src.utils.pdf_utils import'),
    
    # Scraper logging
    (r'from src\.lib\.scraper_logging import', 'from src.utils.logging_utils import'),
    (r'from lib\.scraper_logging import', 'from src.utils.logging_utils import'),
    
    # Cleanup utils
    (r'from src\.lib\.cleanup_utils import', 'from src.utils.cleanup_utils import'),
    (r'from lib\.cleanup_utils import', 'from src.utils.cleanup_utils import'),
    (r'from src\.lib\.resource_cleanup_utils import', 'from src.utils.cleanup_utils import'),
    (r'from lib\.resource_cleanup_utils import', 'from src.utils.cleanup_utils import'),
]


def update_file(file_path: Path) -> bool:
    """Update imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False
    
    original_content = content
    
    # Update imports
    for old_import, new_import in IMPORT_MAPPINGS:
        content = re.sub(old_import, new_import, content)
    
    # Only write if content changed
    if content != original_content:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"Error writing {file_path}: {e}")
            return False
    
    return False


def find_python_files(root_dir: Path, exclude_dirs: List[str]) -> List[Path]:
    """Find all Python files in the project."""
    python_files = []
    
    for file_path in root_dir.rglob('*.py'):
        # Skip excluded directories
        if any(excluded in file_path.parts for excluded in exclude_dirs):
            continue
        python_files.append(file_path)
    
    return python_files


def main():
    """Main function to update all imports."""
    root_dir = Path(__file__).parent
    exclude_dirs = ['.venv', 'venv', '__pycache__', '.git', 'build', 'dist', '.pytest_cache', 'deprecated']
    
    print("Finding Python files...")
    python_files = find_python_files(root_dir, exclude_dirs)
    print(f"Found {len(python_files)} Python files")
    
    updated_files = []
    
    print("\nUpdating imports...")
    for file_path in python_files:
        if update_file(file_path):
            updated_files.append(file_path)
            print(f"✓ Updated: {file_path.relative_to(root_dir)}")
    
    print(f"\nSummary:")
    print(f"Total files scanned: {len(python_files)}")
    print(f"Files updated: {len(updated_files)}")
    
    if updated_files:
        print("\nUpdated files:")
        for file_path in updated_files:
            print(f"  - {file_path.relative_to(root_dir)}")


if __name__ == "__main__":
    main()