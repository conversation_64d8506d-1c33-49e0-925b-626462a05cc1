#!/usr/bin/env python3
"""Diagnose LLaVA timeout issues."""
import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure.external.llava_client import LlavaClient
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_llava_basic():
    """Test basic LLaVA functionality."""
    print("\n1. Testing basic LLaVA connection...")
    
    async with LlavaClient(timeout=30) as client:
        # Check if model is available
        available = await client.check_model_available()
        print(f"   Model available: {available}")
        
        if not available:
            print("   ERROR: Model not available. Please run: ollama pull llama3.2-vision:11b-instruct-q4_K_M")
            return False
    
    return True

async def test_image_analysis(image_path: str = None):
    """Test image analysis with timeout monitoring."""
    if not image_path:
        # Create a simple test image
        from PIL import Image, ImageDraw, ImageFont
        import tempfile
        
        img = Image.new('RGB', (200, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 40), "Test Image", fill='black')
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            img.save(tmp.name)
            image_path = tmp.name
    
    print(f"\n2. Testing image analysis with: {image_path}")
    
    # Test with different timeout values
    timeouts = [30, 60, 120]
    
    for timeout in timeouts:
        print(f"\n   Testing with {timeout}s timeout...")
        try:
            async with LlavaClient(timeout=timeout) as client:
                result = await client.extract_text_from_image(image_path)
                print(f"   SUCCESS: Got result ({len(result)} chars)")
                return True
        except asyncio.TimeoutError:
            print(f"   TIMEOUT: Request exceeded {timeout}s")
        except Exception as e:
            print(f"   ERROR: {type(e).__name__}: {e}")
    
    return False

async def check_system_resources():
    """Check system resources and Ollama status."""
    print("\n3. Checking system resources...")
    
    # Check Ollama status
    import subprocess
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        print("   Ollama models:")
        for line in result.stdout.strip().split('\n')[1:]:  # Skip header
            if line:
                print(f"     {line}")
    except Exception as e:
        print(f"   ERROR checking Ollama: {e}")
    
    # Check memory usage
    try:
        import psutil
        mem = psutil.virtual_memory()
        print(f"\n   Memory: {mem.percent}% used ({mem.used / 1024**3:.1f}GB / {mem.total / 1024**3:.1f}GB)")
        
        # Check if Ollama process is running
        for proc in psutil.process_iter(['pid', 'name', 'memory_percent']):
            if 'ollama' in proc.info['name'].lower():
                print(f"   Ollama process: PID {proc.info['pid']}, Memory: {proc.info['memory_percent']:.1f}%")
    except ImportError:
        print("   (Install psutil for system resource monitoring)")

async def main():
    """Run diagnostic tests."""
    print("LLaVA Timeout Diagnostics")
    print("=" * 50)
    
    # Run tests
    if await test_llava_basic():
        await test_image_analysis()
    
    await check_system_resources()
    
    print("\n4. Recommendations:")
    print("   - If timeouts occur with small test images, check Ollama server resources")
    print("   - Monitor GPU/CPU usage during requests")
    print("   - Consider using a smaller model for faster processing")
    print("   - Check Ollama logs: journalctl -u ollama -f")
    print("   - Increase timeout in config/fb_ads.yml if needed")

if __name__ == "__main__":
    asyncio.run(main())