--- a/src/services/fb_ads/camoufox/camoufox_session_manager.py
+++ b/src/services/fb_ads/camoufox/camoufox_session_manager.py
@@ -387,6 +387,8 @@ class CamoufoxSessionManager:
         """
         if not self.page:
             return {}
+        
+        operation_id = f"extract_tokens_{uuid.uuid4().hex[:8]}"
             
         try:
+            async with self._track_operation(operation_id):
                 # Execute JavaScript to extract tokens
                 tokens = await self.page.evaluate("""
                     () => {
@@ -848,6 +850,8 @@ class CamoufoxSessionManager:
                 try:
+                    operation_id = f"close_page_{uuid.uuid4().hex[:8]}"
+                    async with self._track_operation(operation_id):
                         await self.page.close()
                 except Exception as e:
                     self.logger.debug(f"Error closing page: {str(e)}")
@@ -1103,6 +1107,8 @@ class CamoufoxSessionManager:
                 await asyncio.sleep(0.5)
                 
                 # Scroll down once to trigger loading
+                operation_id = f"scroll_{uuid.uuid4().hex[:8]}"
+                async with self._track_operation(operation_id):
                     await self.page.evaluate("window.scrollBy(0, window.innerHeight)")
                 
                 # Take screenshot for debugging
@@ -1723,6 +1729,8 @@ class CamoufoxSessionManager:
             # Set referer for the navigation
             if self.page:
+                operation_id = f"set_headers_{uuid.uuid4().hex[:8]}"
+                async with self._track_operation(operation_id):
                     await self.page.set_extra_http_headers({'Referer': referrer_url})
             
             # Navigate to the Facebook ads library
@@ -1728,6 +1736,8 @@ class CamoufoxSessionManager:
                 try:
+                    operation_id = f"navigate_{uuid.uuid4().hex[:8]}"
+                    async with self._track_operation(operation_id):
                         await self.page.goto('https://www.facebook.com/ads/library/', 
                                            wait_until='domcontentloaded', 
                                            timeout=60000)
@@ -1736,6 +1746,8 @@ class CamoufoxSessionManager:
                         # If page is already at ads library, refresh
                         if 'facebook.com/ads/library' in self.page.url:
+                            operation_id = f"refresh_{uuid.uuid4().hex[:8]}"
+                            async with self._track_operation(operation_id):
                                 await self.page.goto('https://www.facebook.com/ads/library/', 
                                                    wait_until='domcontentloaded', 
                                                    timeout=60000)
@@ -1831,6 +1843,8 @@ class CamoufoxSessionManager:
             await asyncio.sleep(1)
             
             # Wait for search input to be enabled (Facebook's way of indicating page is ready)
+            operation_id = f"wait_selector_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 await self.page.wait_for_selector('input[placeholder*="Search"][disabled]', state='hidden')
             
             # Additional wait to ensure full initialization
@@ -1887,6 +1901,8 @@ class CamoufoxSessionManager:
             # Wait for the specific dropdown to be visible
             selector = 'div#js_p'
+            operation_id = f"wait_dropdown_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 await self.page.wait_for_selector(selector, state='visible', timeout=5000)
             
             # Click on the country/region dropdown to open it
@@ -1931,6 +1947,8 @@ class CamoufoxSessionManager:
             # Find and click United States option
             option_selector = '[role="option"][aria-selected="false"]:has-text("United States")'
+            operation_id = f"wait_option_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 element = await self.page.wait_for_selector(option_selector, state='visible', timeout=2000)
             
             if element:
@@ -1957,6 +1975,8 @@ class CamoufoxSessionManager:
         """
         try:
             # Wait for category dropdown
+            operation_id = f"wait_category_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 await self.page.wait_for_selector('div#js_p', state='visible', timeout=2000)
             
             # Search for the search input with more flexible selector
@@ -1963,6 +1983,8 @@ class CamoufoxSessionManager:
             search_selector = 'input[placeholder*="Search"]'
             
             # Wait for search input
+            operation_id = f"wait_search_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 search_input = await self.page.wait_for_selector(search_selector, state='visible', timeout=2000)
             
             if search_input:
@@ -2008,6 +2030,8 @@ class CamoufoxSessionManager:
         """
         try:
             # Wait for and click on the All ads category dropdown
+            operation_id = f"wait_cat_dropdown_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 category_dropdown = await self.page.wait_for_selector('div#js_p', state='visible')
             
             if category_dropdown:
@@ -2030,9 +2054,13 @@ class CamoufoxSessionManager:
             self.logger.info(f"Current URL: {self.page.url}")
             
             # Debug: Log page title
+            operation_id = f"get_title_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 self.logger.info(f"🔍 DEBUG: Page title: {await self.page.title()}")
             
             # Debug: Take screenshot
+            operation_id = f"screenshot_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 await self.page.screenshot(path="debug_country_dropdown.png")
             self.logger.info("📸 DEBUG: Screenshot saved as debug_country_dropdown.png")
             
@@ -2049,6 +2077,8 @@ class CamoufoxSessionManager:
                 # Try alternative approach - look for any dropdown-like element
                 self.logger.info("🔍 DEBUG: Looking for alternative dropdown elements...")
+                operation_id = f"query_dropdowns_{uuid.uuid4().hex[:8]}"
+                async with self._track_operation(operation_id):
                     potential_dropdowns = await self.page.query_selector_all('[role="button"], [role="combobox"], select, div[id^="js_"]')
                 
                 self.logger.info(f"🔍 DEBUG: Found {len(potential_dropdowns)} potential dropdown elements")
@@ -2064,6 +2094,8 @@ class CamoufoxSessionManager:
             
             # Wait for results grid to appear after all filters are set
             # This confirms the page is fully loaded with filters applied
+            operation_id = f"wait_grid_{uuid.uuid4().hex[:8]}"
+            async with self._track_operation(operation_id):
                 await self.page.wait_for_selector('div[role="grid"]', state='visible', timeout=10000)
             
             self.logger.info("✅ Facebook ads library page fully initialized")
@@ -2112,6 +2144,19 @@ class CamoufoxSessionManager:
     
     async def create_new_session(self) -> bool:
         """
         Create a new browser session.
+        
+        This method now includes comprehensive operation tracking to prevent
+        premature cleanup during critical operations like token extraction.
+        
+        Key improvements:
+        1. All browser operations wrapped with _track_operation
+        2. Token extraction has retry logic
+        3. Session creation is atomic - fully succeeds or fails
+        4. Proper cleanup on any failure
         
         Returns:
             bool: True if session creation was successful
         """
+        operation_id = f"create_session_{uuid.uuid4().hex[:8]}"
+        
+        async with self._track_operation(operation_id):
             try:
                 self.logger.info("Creating new Camoufox session")
                 
@@ -2125,6 +2170,18 @@ class CamoufoxSessionManager:
                 if not await self._navigate_to_ads_library_home():
                     return False
                 
+                # Extract session tokens with retry logic
+                max_token_retries = 3
+                for attempt in range(max_token_retries):
+                    self.session_data = await self._extract_tokens()
+                    
+                    if self.session_data.get('fb_dtsg'):
+                        break
+                    
+                    self.logger.warning(f"Token extraction attempt {attempt + 1}/{max_token_retries} incomplete")
+                    if attempt < max_token_retries - 1:
+                        await asyncio.sleep(2)
+                
                 # Extract session tokens
-                self.session_data = await self._extract_tokens()
                 
                 # Validate session
@@ -2172,6 +2229,8 @@ class CamoufoxSessionManager:
                 # Try with shorter timeout first, then longer
                 nav_timeout = 30000 if attempt == 0 else 60000
                 
+                operation_id = f"nav_home_{uuid.uuid4().hex[:8]}"
+                async with self._track_operation(operation_id):
                     # Navigate to Facebook ads library for session setup
                     await self.page.goto(
                         'https://www.facebook.com/ads/library/',
@@ -2180,6 +2239,8 @@ class CamoufoxSessionManager:
                     )
                 
                 # Wait for page to load and extract tokens
+                operation_id = f"wait_idle_{uuid.uuid4().hex[:8]}"
+                async with self._track_operation(operation_id):
                     await self.page.wait_for_load_state('networkidle', timeout=10000)
                 await asyncio.sleep(2)  # Additional wait for JS execution