#!/usr/bin/env python3
"""
Test complete transfer fix by simulating the transformation process.
"""
import json
import re

def test_complete_fix():
    """Simulate the complete transformation with all fixes."""
    
    # Original JSON data
    test_data = {
        "court_id": "scd",
        "docket_num": "2:25-cv-07390",
        "case_in_other_court": "Michigan Eastern, 1:25-cv-11890",
        "lead_case": "2:18-mn-02873-RMG",
        "flags": ["JURY", "MDL"],
        "new_filename": "scd_25_07390_Armstrong_et_al_v_3M_Company_et_al"
    }
    
    print("Initial State:")
    print(f"  court_id: {test_data.get('court_id')}")
    print(f"  case_in_other_court: {test_data.get('case_in_other_court')}")
    print(f"  lead_case: {test_data.get('lead_case')}")
    print(f"  mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  is_transferred: {test_data.get('is_transferred', 'NOT PRESENT')}")
    
    # Step 1: Extract MDL from lead_case (simulating extract_mdl_from_flags)
    lead_case = test_data.get('lead_case', '')
    if lead_case:
        lead_case_match = re.search(r'\d+:\d+-mn-(\d+)', lead_case, re.IGNORECASE)
        if lead_case_match:
            mdl_num = lead_case_match.group(1).lstrip('0')
            test_data['mdl_num'] = mdl_num
            print(f"\n✅ Step 1: Extracted mdl_num={mdl_num} from lead_case")
    
    # Step 2: Process transfers (with our fix - case_in_other_court parsed FIRST)
    case_in_other_court = test_data.get('case_in_other_court')
    if case_in_other_court:
        parts = case_in_other_court.split(',', 1)
        if len(parts) == 2:
            transferor_name = parts[0].strip()  # "Michigan Eastern"
            transferor_docket = parts[1].strip()  # "1:25-cv-11890"
            
            # Determine transferor court ID (simplified)
            if "Michigan Eastern" in transferor_name:
                transferor_court_id = "mied"
            else:
                transferor_court_id = "unknown"
            
            test_data['transferor_court_id'] = transferor_court_id
            test_data['transferor_docket_num'] = transferor_docket
            
            # Calculate transfer flags (FEDERAL-TO-FEDERAL)
            current_court_id = test_data['court_id']  # scd
            has_mdl = bool(test_data.get('mdl_num'))
            
            # Federal to federal transfer with MDL = is_transferred = True
            if transferor_court_id != current_court_id and has_mdl:
                test_data['is_transferred'] = True
                test_data['is_removal'] = False
                test_data['pending_cto'] = False
                
                print(f"\n✅ Step 2: Transfer processing complete:")
                print(f"  transferor_court_id: {transferor_court_id}")
                print(f"  transferor_docket_num: {transferor_docket}")
                print(f"  is_transferred: True (federal-to-federal with MDL)")
    
    # Step 3: Query transferor in DB (would inherit attorneys_gpt, etc.)
    print(f"\n📊 Step 3: Would query DynamoDB for transferor {test_data.get('transferor_court_id')}:{test_data.get('transferor_docket_num')}")
    print("  If found, would inherit: attorneys_gpt, s3_link (if needed), etc.")
    
    # Final state
    print("\n✅ Final State After All Fixes:")
    print(f"  mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  transferor_court_id: {test_data.get('transferor_court_id', 'NOT PRESENT')}")
    print(f"  transferor_docket_num: {test_data.get('transferor_docket_num', 'NOT PRESENT')}")
    print(f"  is_transferred: {test_data.get('is_transferred', 'NOT PRESENT')}")
    print(f"  is_removal: {test_data.get('is_removal', 'NOT PRESENT')}")
    print(f"  pending_cto: {test_data.get('pending_cto', 'NOT PRESENT')}")


if __name__ == "__main__":
    print("=" * 80)
    print("Complete Transfer Fix Test")
    print("=" * 80)
    
    test_complete_fix()
    
    print("\n" + "=" * 80)