name: lexgenius
channels:
  - defaults
  - conda-forge
dependencies:
  - python=3.11
  - pip
  - mypy
  - pandas
  - playwright
  - requests
  - tqdm
  - boto3
  - backoff
  - rich
  - undetected-chromedriver
  - aiohttp
  - python-dotenv
  - tiktoken
  - beautifulsoup4
  - pytesseract
  - pdf2image
  - tenacity
  - openai
  - portalocker
  - retrying
  - jinja2
  - webdriver-manager
  - fasteners
  - colorlog
  - chardet
  - matplotlib
  - pypdf2
  - tabulate
  - nltk
  - levenshtein
  - scikit-learn
  - imagehash
  - pillow
  - networkx
  - holidays
  - retry
  - seaborn
  - cairosvg
  - pyyaml
  - lxml
  - aioboto3
  - mistralai
  - aiobotocore
  - sentencepiece
  - transformers
  - pytorch
  - sentence-transformers
  - faiss-cpu
  - hdbscan
  - psutil
  - spacy
  - fuzzywuzzy
  - pytest
  - pytest-cov
  - pytest-timeout
  - pytest-asyncio
  - pydantic
  - pydantic-settings
  - pyperclip
  - streamlit
  - umap-learn
  - freezegun
  - path
  - json-repair
  - litellm
  - loguru
  - types-requests
  - scipy==1.15.3
  - numpy=1.26.4
  - libprotobuf
  - protobuf
  - dependency-injector
  - pip:
    - docext
prefix: /Users/<USER>/miniconda3/envs/lexgenius
