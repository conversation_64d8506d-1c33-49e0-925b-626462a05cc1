#!/usr/bin/env python3
"""
Test to verify that the browser deduplication fix is complete.
This test simulates error recovery scenarios to ensure they use the shared browser.
"""

import asyncio
import logging
import sys
from unittest.mock import AsyncMock, Mock, patch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_error_recovery_uses_shared_browser():
    """Test that error recovery scenarios use the shared browser."""
    logger.info("🎯 Testing error recovery with shared browser")
    
    # Import the session manager
    from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
    from src.services.fb_ads.camoufox.shared_browser_manager import SharedBrowserManager
    
    # Track browser creation calls
    browser_creation_calls = []
    
    # Create a mock shared browser manager that tracks calls
    mock_shared_browser_manager = Mock(spec=SharedBrowserManager)
    
    # Mock the get_browser_context method
    async def mock_get_browser_context(*args, **kwargs):
        job_id = args[0] if args else kwargs.get('job_id', 'unknown')
        browser_creation_calls.append(f"get_browser_context for job {job_id}")
        logger.info(f"🖥️ SharedBrowserManager.get_browser_context called for job {job_id}")
        
        # Return mock context and page
        mock_context = AsyncMock()
        mock_page = AsyncMock()
        mock_page.url = "https://www.facebook.com/ads/library/"
        mock_page.goto = AsyncMock()
        return mock_context, mock_page
    
    mock_shared_browser_manager.get_browser_context = mock_get_browser_context
    
    # Create session manager with mocked shared browser manager
    session_manager = CamoufoxSessionManager(
        config={'headless': True},
        logger=logger,
        job_id="test_firm_recovery",
        fingerprint_manager=None,
        proxy_manager=None,
        law_firms_repository=None
    )
    
    # Inject our mock shared browser manager
    session_manager.shared_browser_manager = mock_shared_browser_manager
    
    logger.info("\n🔬 Testing navigation error recovery...")
    
    # Mock _create_shared_browser_context to track calls
    original_create_shared = session_manager._create_shared_browser_context
    create_shared_calls = []
    
    async def mock_create_shared():
        create_shared_calls.append("_create_shared_browser_context called")
        logger.info("✅ _create_shared_browser_context called (uses SharedBrowserManager)")
        # Call the original to set up context and page
        return await original_create_shared()
    
    session_manager._create_shared_browser_context = mock_create_shared
    
    # Test 1: Simulate Target closed error during navigation
    logger.info("\n📋 Test 1: Target closed error recovery")
    
    # First create the browser context
    await session_manager._create_shared_browser_context()
    
    # Mock _check_setup_state to return not on ad library page
    async def mock_check_setup_state():
        return {'on_ad_library_page': False}
    
    session_manager._check_setup_state = mock_check_setup_state
    
    # Mock cleanup
    cleanup_calls = []
    async def mock_cleanup():
        cleanup_calls.append("cleanup called")
        logger.info("🧹 Cleanup called")
    
    session_manager.cleanup = mock_cleanup
    
    # Mock page.goto to raise Target closed error once
    error_count = 0
    original_goto = session_manager.page.goto
    
    async def mock_goto_with_error(*args, **kwargs):
        nonlocal error_count
        error_count += 1
        if error_count == 1:
            logger.info("💥 Simulating Target closed error")
            raise Exception("Target page, context or browser has been closed")
        else:
            logger.info("✅ Navigation successful after recovery")
            return
    
    session_manager.page.goto = mock_goto_with_error
    
    # Call the method that includes error recovery
    try:
        # This method contains the error recovery logic at line 1343
        await session_manager._setup_ad_library_search()
        logger.info("✅ Navigation with error recovery completed")
    except Exception as e:
        logger.error(f"❌ Navigation failed: {e}")
    
    # Check results
    logger.info(f"\n📊 Browser creation calls: {len(browser_creation_calls)}")
    for call in browser_creation_calls:
        logger.info(f"  - {call}")
    
    logger.info(f"\n📊 _create_shared_browser_context calls: {len(create_shared_calls)}")
    for call in create_shared_calls:
        logger.info(f"  - {call}")
    
    # Verify only shared browser was used
    if len(browser_creation_calls) > 0:
        logger.info("\n✅ SUCCESS: All browser creation goes through SharedBrowserManager!")
        logger.info("✅ Error recovery correctly uses shared browser infrastructure!")
    else:
        logger.error("\n❌ FAILURE: SharedBrowserManager was not used!")
    
    # Test 2: Verify _create_browser_and_page redirects correctly
    logger.info("\n📋 Test 2: Verify _create_browser_and_page redirects to shared browser")
    
    # Reset counters
    create_shared_calls.clear()
    
    # Call the deprecated method directly
    result = await session_manager._create_browser_and_page()
    
    if len(create_shared_calls) > 0 and result:
        logger.info("✅ _create_browser_and_page correctly redirects to _create_shared_browser_context!")
    else:
        logger.error("❌ _create_browser_and_page did not redirect properly!")
    
    return len(browser_creation_calls) > 0

async def test_multiple_firms_share_browser():
    """Test that multiple firms share the same browser instance."""
    logger.info("\n🎯 Testing multiple firms share the same browser")
    
    from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
    from src.services.fb_ads.camoufox.shared_browser_manager import SharedBrowserManager
    
    # Get the actual SharedBrowserManager class instance
    logger.info(f"SharedBrowserManager class _global_instance: {id(SharedBrowserManager._global_instance) if SharedBrowserManager._global_instance else 'None'}")
    
    # Create multiple session managers for different firms
    session_managers = []
    shared_browser_ids = []
    
    for i in range(3):
        firm_id = f"test_firm_{i}"
        logger.info(f"\n🏢 Creating session manager for {firm_id}")
        
        session_manager = SessionManagerFactory.create(
            config={'headless': True},
            logger=logger,
            firm_id=firm_id,
            fingerprint_manager=None,
            proxy_manager=None,
            law_firms_repository=None
        )
        
        session_managers.append(session_manager)
        
        # Check SharedBrowserManager instance
        if hasattr(session_manager, 'shared_browser_manager'):
            browser_manager_id = id(session_manager.shared_browser_manager)
            shared_browser_ids.append(browser_manager_id)
            logger.info(f"  SharedBrowserManager ID: {browser_manager_id}")
    
    # Verify all use the same SharedBrowserManager
    unique_ids = set(shared_browser_ids)
    logger.info(f"\n📊 Unique SharedBrowserManager IDs: {len(unique_ids)}")
    
    if len(unique_ids) == 1:
        logger.info("✅ All firms share the SAME SharedBrowserManager instance!")
        return True
    else:
        logger.error(f"❌ Multiple SharedBrowserManager instances detected: {unique_ids}")
        return False

async def main():
    """Main test runner."""
    logger.info("🎯 Starting Browser Deduplication Fix Verification")
    logger.info("=" * 80)
    
    # Test 1: Error recovery
    test1_passed = await test_error_recovery_uses_shared_browser()
    
    # Test 2: Multiple firms
    test2_passed = await test_multiple_firms_share_browser()
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 TEST SUMMARY:")
    logger.info(f"  Error Recovery Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    logger.info(f"  Multiple Firms Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        logger.info("\n🎉 ALL TESTS PASSED! Browser deduplication fix is complete!")
        logger.info("✅ Only ONE browser instance will be used across all firms and error scenarios!")
    else:
        logger.error("\n❌ Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    asyncio.run(main())