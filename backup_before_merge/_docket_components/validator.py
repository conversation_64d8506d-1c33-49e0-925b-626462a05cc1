# /src/services/transformer/_docket_components/validator.py
import ast
import logging
from typing import Dict, Optional, Tuple, Any, List

# Removed dependency_injector imports - using container-based injection
# Direct async imports - no compatibility layer needed
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class DocketValidator(AsyncServiceBase):
    """Validates docket data dictionary."""

    # Define fields that MUST exist and be non-empty/non-null for a record to be considered complete.
    # Adjust this list based on your absolute minimum requirements.
    CORE_REQUIRED_FIELDS = [
        'court_id',
        'docket_num',
        'date_filed',
        'plaintiff',
        'defendant',
        'new_filename',
        'attorneys_gpt',
        'plaintiffs_gpt',
        's3_link',
        'title',  # Usually essential
        'versus',  # Usually essential (or title fallback)
        # Add other fields that are CRITICAL for your downstream use
    ]

    # --- ADDED: Date validation requirement ---
    # Field that must exist and be a valid YYYYMMDD string
    REQUIRED_DATE_FIELDS = ['added_on']
    COMPLETION_MARKER_FIELD = 'added_on'

    def __init__(self,
                 config: Optional[Dict] ,
                 logger: Optional[logging.Logger] ):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]  # Match FileHandler

    async def _execute_action(self, data: Any) -> Any:
        """Execute DocketValidator actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'validate_docket':
                docket_data = action_data.get('docket_data', {})
                return self.validate_docket(docket_data)
            elif action == 'is_data_complete':
                docket_data = action_data.get('docket_data', {})
                return self.is_data_complete(docket_data)
            elif action == 'validate_core_fields':
                docket_data = action_data.get('docket_data', {})
                return self._validate_core_fields(docket_data)
            elif action == 'validate_date_fields':
                docket_data = action_data.get('docket_data', {})
                return self._validate_date_fields(docket_data)
            elif action == 'validate_completion_marker':
                docket_data = action_data.get('docket_data', {})
                return self._validate_completion_marker(docket_data)
            elif action == 'validate_s3_links':
                docket_data = action_data.get('docket_data', {})
                return self._validate_s3_links(docket_data)
            elif action == 'validate_data_format':
                docket_data = action_data.get('docket_data', {})
                return self._validate_data_format(docket_data)
            elif action == 'convert_strings_to_literals':
                input_data = action_data.get('input_data', {})
                return self._convert_strings_to_literals(input_data)
            elif action == 'get_validation_summary':
                docket_data = action_data.get('docket_data', {})
                return self.get_validation_summary(docket_data)
            elif action == 'get_required_fields':
                return self.get_required_fields()
            elif action == 'validate_field_value':
                field_name = action_data.get('field_name', '')
                field_value = action_data.get('field_value')
                return self._validate_field_value(field_name, field_value)
            elif action == 'check_processing_error':
                docket_data = action_data.get('docket_data', {})
                return self._check_processing_error(docket_data)
        raise TransformerServiceError("Invalid action data provided to DocketValidator")

    def validate_docket(self, data: Dict) -> bool:
        """
        DEPRECATED or simplified usage.
        This method previously just logged incompleteness but returned True.
        The primary logic is now in is_data_complete.
        If called, it should perhaps reflect the outcome of is_data_complete.
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data format: Expected dict for validation.")
            return False
        # log_filename = data.get('new_filename', data.get('docket_num', 'NA'))

        is_complete, _ = self.is_data_complete(data)
        # Maybe log if needed, but return the actual completeness status
        # self.log_debug(f"validate_docket called for {log_filename}. Completeness: {is_complete}")
        return is_complete  # Return the result of the check

    @staticmethod
    def _is_valid_date_format(date_str: Any) -> bool:  # Now 'Any' is defined
        """Checks if a string is a valid YYYYMMDD date."""
        if not isinstance(date_str, str):
            return False
        if len(date_str) != 8:
            return False
        if not date_str.isdigit():
            return False
        # Basic check, doesn't validate month/day ranges, but ensures format
        return True

    def is_data_complete(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Checks if the core required fields AND the completion marker field are present and valid.
        This determines if a file has been fully processed successfully in a previous run.

        Args:
            data: The docket data dictionary.

        Returns:
            A tuple: (bool indicating completeness, list of missing/invalid fields).
        """
        if not isinstance(data, dict):
            self.log_warning("Validation check received non-dict data.")
            return False, ['Invalid data type']

        missing_or_invalid = []

        # 1. Check CORE_REQUIRED_FIELDS
        for field in self.CORE_REQUIRED_FIELDS:
            value = data.get(field)
            if value in self.NULL_CONDITIONS or value is None:  # Check presence and validity
                missing_or_invalid.append(field)
            # Add more specific checks if needed (e.g., date_filed format, s3_link structure)
            elif field == 's3_link' and (not isinstance(value, str) or not value.lower().endswith('.pdf')):
                missing_or_invalid.append(f"{field} (invalid format)")

        # 2. Check COMPLETION_MARKER_FIELD ('added_on')
        completion_marker_value = data.get(self.COMPLETION_MARKER_FIELD)
        if not self._is_valid_completion_date(completion_marker_value):
            missing_or_invalid.append(f"{self.COMPLETION_MARKER_FIELD} (missing or invalid format)")

        # 3. Check for Explicit Error Marker
        if 'processing_error' in data:
            # Technically, an errored file isn't "complete" in the desired sense
            # but we handle this separately in the calling logic (_load_and_validate).
            # We don't add it to missing_or_invalid here, as its presence signifies
            # *why* it might be missing required fields.
            pass

        is_complete = not missing_or_invalid
        # Logging is handled in the calling methods based on the return value

        return is_complete, missing_or_invalid

    @staticmethod
    def _is_s3_html_valid(s3_html: Optional[str]) -> bool:
        """Check if s3_html looks like an HTML link."""
        return isinstance(s3_html, str) and s3_html.lower().endswith('.html')  # Check lower case

    def _convert_strings_to_literals(self, input_data):
        """Recursive literal evaluation (use with caution)."""
        # Duplicates TransferHandler method - consider moving to utils
        if isinstance(input_data, str):
            if (input_data.startswith('[') and input_data.endswith(']')) or \
                    (input_data.startswith('{') and input_data.endswith('}')):
                try:
                    return ast.literal_eval(input_data)
                except (ValueError, SyntaxError, TypeError):
                    # self.log_debug(f"ast.literal_eval failed: '{input_data[:100]}...'") # Reduce verbosity
                    return input_data
            else:
                return input_data
        elif isinstance(input_data, list):
            return [self._convert_strings_to_literals(item) for item in input_data]
        elif isinstance(input_data, dict):
            return {key: self._convert_strings_to_literals(value) for key, value in input_data.items()}
        else:
            return input_data

    @staticmethod
    def _is_valid_completion_date(date_str: Any) -> bool:
        """Checks if the 'added_on' value is a valid YYYYMMDD string."""
        if not isinstance(date_str, str):
            return False
        if len(date_str) != 8:
            return False
        if not date_str.isdigit():
            return False
        # Validate month/day ranges
        try:
            from datetime import datetime
            datetime.strptime(date_str, '%Y%m%d')
        except ValueError:
            return False  # Invalid date components
        return True

    def _validate_core_fields(self, data: Dict) -> Tuple[bool, List[str]]:
        """Validate core required fields."""
        missing_fields = []
        for field in self.CORE_REQUIRED_FIELDS:
            value = data.get(field)
            if value in self.NULL_CONDITIONS or value is None:
                missing_fields.append(field)
        return len(missing_fields) == 0, missing_fields

    def _validate_date_fields(self, data: Dict) -> Tuple[bool, List[str]]:
        """Validate date fields format."""
        invalid_dates = []
        for field in self.REQUIRED_DATE_FIELDS:
            value = data.get(field)
            if not self._is_valid_date_format(value):
                invalid_dates.append(field)
        return len(invalid_dates) == 0, invalid_dates

    def _validate_completion_marker(self, data: Dict) -> bool:
        """Validate completion marker field."""
        completion_value = data.get(self.COMPLETION_MARKER_FIELD)
        return self._is_valid_completion_date(completion_value)

    def _validate_s3_links(self, data: Dict) -> Tuple[bool, List[str]]:
        """Validate S3 link fields."""
        invalid_links = []

        # Check s3_link (PDF)
        s3_link = data.get('s3_link')
        if s3_link and not (isinstance(s3_link, str) and s3_link.lower().endswith('.pdf')):
            invalid_links.append('s3_link (invalid PDF format)')

        # Check s3_html (HTML)
        s3_html = data.get('s3_html')
        if s3_html and not self._is_s3_html_valid(s3_html):
            invalid_links.append('s3_html (invalid HTML format)')

        return len(invalid_links) == 0, invalid_links

    def _validate_data_format(self, data: Any) -> bool:
        """Validate data format is dictionary."""
        return isinstance(data, dict)

    def get_validation_summary(self, data: Dict) -> Dict[str, Any]:
        """Get comprehensive validation summary."""
        summary = {
            'is_complete': False,
            'core_fields_valid': False,
            'date_fields_valid': False,
            'completion_marker_valid': False,
            's3_links_valid': False,
            'data_format_valid': False,
            'has_processing_error': False,
            'missing_fields': [],
            'invalid_dates': [],
            'invalid_links': []
        }

        # Overall completeness
        is_complete, missing_or_invalid = self.is_data_complete(data)
        summary['is_complete'] = is_complete
        summary['missing_fields'] = missing_or_invalid

        # Individual validations
        summary['data_format_valid'] = self._validate_data_format(data)

        if summary['data_format_valid']:
            summary['core_fields_valid'], _ = self._validate_core_fields(data)
            summary['date_fields_valid'], summary['invalid_dates'] = self._validate_date_fields(data)
            summary['completion_marker_valid'] = self._validate_completion_marker(data)
            summary['s3_links_valid'], summary['invalid_links'] = self._validate_s3_links(data)
            summary['has_processing_error'] = self._check_processing_error(data)

        return summary

    def get_required_fields(self) -> Dict[str, List[str]]:
        """Get lists of required fields."""
        return {
            'core_required_fields': self.CORE_REQUIRED_FIELDS,
            'required_date_fields': self.REQUIRED_DATE_FIELDS,
            'completion_marker_field': [self.COMPLETION_MARKER_FIELD],
            'null_conditions': self.NULL_CONDITIONS
        }

    def _validate_field_value(self, field_name: str, field_value: Any) -> Dict[str, Any]:
        """Validate a specific field value."""
        result = {
            'field_name': field_name,
            'is_valid': True,
            'validation_type': 'general',
            'message': 'Field is valid'
        }

        # Check if null/empty
        if field_value in self.NULL_CONDITIONS:
            result['is_valid'] = False
            result['message'] = 'Field is null/empty'
            return result

        # Specific field validations
        if field_name in self.REQUIRED_DATE_FIELDS or field_name == self.COMPLETION_MARKER_FIELD:
            result['validation_type'] = 'date'
            if not self._is_valid_date_format(field_value):
                result['is_valid'] = False
                result['message'] = 'Invalid date format (expected YYYYMMDD)'

        elif field_name == 's3_link':
            result['validation_type'] = 's3_pdf'
            if not (isinstance(field_value, str) and field_value.lower().endswith('.pdf')):
                result['is_valid'] = False
                result['message'] = 'Invalid S3 PDF link format'

        elif field_name == 's3_html':
            result['validation_type'] = 's3_html'
            if not self._is_s3_html_valid(field_value):
                result['is_valid'] = False
                result['message'] = 'Invalid S3 HTML link format'

        return result

    def _check_processing_error(self, data: Dict) -> bool:
        """Check if data has processing error marker."""
        return 'processing_error' in data
