# /src/services/transformer/_docket_components/html_processor.py
"""

HTML processing for docket data.

This module handles HTML parsing and data extraction extracted from 
docket_processor.py as part of Phase 3.2 refactoring.
"""
import logging
import re
from typing import Dict, List, Optional, Any


from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class HTMLProcessor(AsyncServiceBase):
    """Handles HTML parsing and data extraction for docket data."""

    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    def __init__(self,
                 html_data_updater: Any =None,
                 config: Optional[Dict] =None,
                 logger: Optional[logging.Logger] =None,
                 html_processing_service: Optional[Any] =None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.html_data_updater = html_data_updater
        self.html_processing_service = html_processing_service

    async def _execute_action(self, data: Any) -> Any:
        """Execute DocketHTMLProcessor actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'process_s3_html':
                docket_data = action_data.get('docket_data', {})
                json_path = action_data.get('json_path', '')
                return await self.process_s3_html(docket_data, json_path)
            elif action == 'extract_s3_html_link':
                return self._extract_s3_html_link(action_data)
            elif action == 'parse_html_content':
                html_link = action_data.get('html_link', '')
                filename = action_data.get('filename', 'unknown')
                return await self._parse_html_content(html_link, filename)
            elif action == 'extract_party_information':
                docket_data = action_data.get('docket_data', {})
                parsed_content = action_data.get('parsed_content', {})
                filename = action_data.get('filename', 'unknown')
                return self._extract_party_information(docket_data, parsed_content, filename)
            elif action == 'extract_case_information':
                docket_data = action_data.get('docket_data', {})
                parsed_content = action_data.get('parsed_content', {})
                filename = action_data.get('filename', 'unknown')
                return self._extract_case_information(docket_data, parsed_content, filename)
            elif action == 'validate_html_extraction':
                data_dict = action_data.get('data_dict', {})
                original_data = action_data.get('original_data', {})
                return self.validate_html_extraction(data_dict, original_data)
            elif action == 'get_html_processing_summary':
                return self.get_html_processing_summary(action_data)
            elif action == 'download_html_content':
                html_link = action_data.get('html_link', '')
                filename = action_data.get('filename', 'unknown')
                return await self._download_html_content(html_link, filename)
            elif action == 'cleanup_plural_fields':
                self._cleanup_plural_fields(action_data)
                return action_data
            elif action == 'direct_parse_html':
                html_content = action_data.get('html_content', '')
                filename = action_data.get('filename', 'unknown')
                return await self._direct_parse_html(html_content, filename)
        raise TransformerServiceError("Invalid action data provided to DocketHTMLProcessor")

    async def process_s3_html(self, data: Dict, json_path: str) -> bool:
        """
        Process S3 HTML content and extract case information.
        
        Args:
            data: Docket data dictionary (modified in place)
            json_path: Path to JSON file for context
            
        Returns:
            True if HTML was processed successfully, False otherwise
        """
        if not isinstance(data, dict):
            self.log_error("Invalid data dictionary provided to process_s3_html")
            return False

        filename = data.get('new_filename', data.get('docket_num', 'unknown'))

        # ENHANCED LOGGING: Log the initial state of s3_html field
        self.log_info(f"[{filename}] Starting HTML processing. s3_html field present: {'s3_html' in data}")
        if 's3_html' in data:
            self.log_info(f"[{filename}] s3_html value: {data['s3_html']}")

        try:
            # ENHANCED: Use HTML processing service if available for comprehensive processing
            if self.html_processing_service:
                self.log_debug(f"Using enhanced HTML processing service for {filename}")

                # Get S3 HTML link
                s3_html_link = self._extract_s3_html_link(data)
                if not s3_html_link:
                    self.log_debug(f"No S3 HTML link found for {filename}")
                    return False

                # Download HTML content for processing
                self.log_info(f"[{filename}] Downloading HTML content from: {s3_html_link}")
                html_content = await self._download_html_content(s3_html_link, filename)
                if not html_content:
                    self.log_error(f"[{filename}] Failed to download HTML content - aborting HTML processing")
                    return False

                self.log_info(f"[{filename}] Successfully downloaded HTML content: {len(html_content)} chars")

                # Store original data for comparison
                original_data = data.copy()

                # Use enhanced HTML processing service
                updated_data = await self.html_processing_service.process_html_content(
                    data, html_content, json_path
                )

                # CRITICAL FIX: Check if HTML processing failed before merging
                if updated_data.get('_html_processing_failed') or updated_data.get('_html_processing_error'):
                    self.log_warning(
                        f"HTML processing failed for {filename}, not merging fields. "
                        f"Error: {updated_data.get('_html_processing_error', 'Unknown error')}"
                    )
                    # Only update error fields, not content fields
                    data['_html_processing_error'] = updated_data.get('_html_processing_error')
                    data['_html_processing_failed'] = True
                    if 's3_html_error' in updated_data:
                        data['s3_html_error'] = updated_data['s3_html_error']
                    if 'html_update_error' in updated_data:
                        data['html_update_error'] = updated_data['html_update_error']
                    return False

                # Update the original data dictionary in place (only if no errors)
                data.update(updated_data)

                # Validate results
                validation_report = self.html_processing_service.validate_html_processing_results(
                    data, original_data
                )

                if validation_report['changes']:
                    self.log_info(f"Enhanced HTML processing for {filename}: {', '.join(validation_report['changes'])}")
                    return True
                else:
                    self.log_debug(f"No updates from enhanced HTML processing for {filename}")
                    return False

            # FALLBACK: Use original HTML processing logic
            else:
                self.log_info(f"[{filename}] Using fallback HTML processing (no enhanced service available)")

                # Get S3 HTML link
                s3_html_link = self._extract_s3_html_link(data)
                if not s3_html_link:
                    self.log_warning(f"[{filename}] No S3 HTML link found - cannot process HTML")
                    return False

                self.log_info(f"[{filename}] Found S3 HTML link: {s3_html_link}")

                # Parse HTML content
                self.log_info(f"[{filename}] Parsing HTML content from link")
                parsed_content = await self._parse_html_content(html_link, filename)
                if not parsed_content:
                    self.log_error(f"[{filename}] Failed to parse HTML content")
                    return False

                self.log_info(f"[{filename}] Successfully parsed HTML content: {type(parsed_content)}")

                # Extract and update party information
                self.log_info(f"[{filename}] Extracting party information from parsed HTML")
                parties_updated = self._extract_party_information(data, parsed_content, filename)
                self.log_info(f"[{filename}] Party information extraction result: {parties_updated}")

                # Extract and update case information
                self.log_info(f"[{filename}] Extracting case information from parsed HTML")
                case_info_updated = self._extract_case_information(data, parsed_content, filename)
                self.log_info(f"[{filename}] Case information extraction result: {case_info_updated}")

                # Clean up plural fields for consistency
                self._cleanup_plural_fields(data)

                # Log processing results
                if parties_updated or case_info_updated:
                    self.log_info(f"[{filename}] HTML processing SUCCESS - Updated docket data from HTML")
                    return True
                else:
                    self.log_warning(f"[{filename}] HTML processing completed but NO DATA was extracted")
                    return False

        except Exception as e:
            self.log_error(f"Error processing S3 HTML for {filename}: {e}")
            return False

    def _extract_s3_html_link(self, data: Dict) -> Optional[str]:
        """
        Extract S3 HTML link from data.
        
        Args:
            data: Docket data dictionary
            
        Returns:
            S3 HTML link or None
        """
        # Check for HTML link in various possible fields
        # CRITICAL: Added 's3_html' as the first field to check since that's what PACER services set
        html_link_fields = ['s3_html', 's3_html_link', 's3_link_html', 'html_link', 's3_link']

        # Log what fields we have
        available_fields = [f for f in html_link_fields if f in data]
        self.log_info(f"[HTML Link Extraction] Looking for HTML link. Available fields: {available_fields}")

        # Log all available fields and their values for debugging
        for field in available_fields:
            value = data.get(field)
            self.log_info(f"[HTML Link Extraction] Field '{field}' contains: {value}")

        for field in html_link_fields:
            link = data.get(field)
            if link and isinstance(link, str):
                # Log what we're checking
                self.log_debug(f"[HTML Link Extraction] Checking field '{field}' with value: {link}")

                # Accept both relative paths and full URLs
                if '.html' in link.lower() or (field == 's3_html' and '/html/' in link):
                    self.log_info(f"[HTML Link Extraction] Found valid HTML link in field '{field}'")

                    # Convert relative path to full URL if needed
                    if not link.startswith('http'):
                        original_link = link
                        link = link.lstrip('/')
                        link = f"https://cdn.lexgenius.ai/{link}"
                        self.log_info(
                            f"[HTML Link Extraction] Converted relative path '{original_link}' to full URL: {link}")

                    self.log_info(f"[HTML Link Extraction] RETURNING HTML link from field '{field}': {link}")
                    return link
                else:
                    self.log_debug(f"[HTML Link Extraction] Field '{field}' does not contain valid HTML link pattern")

        # Check if s3_link is HTML (not PDF)
        s3_link = data.get('s3_link')
        if s3_link and isinstance(s3_link, str):
            if s3_link.lower().endswith('.html'):
                return s3_link
            # Convert PDF link to HTML link
            elif s3_link.lower().endswith('.pdf'):
                html_link = s3_link.replace('.pdf', '.html')
                # CRITICAL FIX: HTML files should use /html/ path, not /dockets/
                if '/dockets/' in html_link:
                    html_link = html_link.replace('/dockets/', '/html/')
                return html_link

        # Log when no HTML link was found
        self.log_warning(f"[HTML Link Extraction] No HTML link found in any of the checked fields: {html_link_fields}")
        return None

    async def _parse_html_content(self, html_link: str, filename: str) -> Optional[Dict]:
        """
        Parse HTML content from the provided link.
        
        Args:
            html_link: URL to HTML content
            filename: Filename for logging context
            
        Returns:
            Parsed content dictionary or None
        """
        self.log_info(f"[HTML Parse] Starting HTML parsing for {filename}")

        try:
            # First, try to download and parse HTML directly
            self.log_info(f"[HTML Parse] Attempting direct HTML download and parse for {filename}")
            html_content = await self._download_html_content(html_link, filename)

            if html_content:
                # Try direct parsing first
                direct_parsed = await self._direct_parse_html(html_content, filename)
                if direct_parsed:
                    self.log_info(f"[HTML Parse] Direct parsing successful for {filename}")
                    return direct_parsed

            # Fallback to html_data_updater if available
            if not self.html_data_updater:
                self.log_warning(
                    f"[HTML Parse] HTML data updater not available for {filename}, and direct parsing failed")
                return None

            self.log_info(f"[HTML Parse] Using HTML data updater for {filename}")

            # Use HTML data updater to parse content
            if hasattr(self.html_data_updater, 'parse_html_async'):
                self.log_debug(f"[HTML Parse] Using parse_html_async method")
                parsed_content = await self.html_data_updater.parse_html_async(html_link)
            elif hasattr(self.html_data_updater, 'parse_html'):
                self.log_debug(f"[HTML Parse] Using parse_html method with asyncio.to_thread")
                import asyncio
                parsed_content = await asyncio.to_thread(self.html_data_updater.parse_html, html_link)
            else:
                self.log_error(f"[HTML Parse] HTML data updater missing parse methods for {filename}")
                return None

            if parsed_content and isinstance(parsed_content, dict):
                self.log_info(
                    f"[HTML Parse] Successfully parsed HTML content for {filename} - Keys: {list(parsed_content.keys())}")
                return parsed_content
            else:
                self.log_warning(
                    f"[HTML Parse] HTML parsing returned empty/invalid content for {filename}: {type(parsed_content)}")
                return None

        except Exception as e:
            self.log_error(f"[HTML Parse] Error parsing HTML content for {filename}: {e}")
            return None

    def _extract_party_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        """
        Extract plaintiff and defendant information from parsed HTML.
        
        Args:
            data: Docket data dictionary (modified in place)
            parsed_content: Parsed HTML content
            filename: Filename for logging context
            
        Returns:
            True if party information was updated, False otherwise
        """
        updated_parties = False

        # Extract plaintiff names
        plaintiff_names = self._extract_plaintiff_names(parsed_content)
        if plaintiff_names:
            # CRITICAL FIX: More permissive update logic for plaintiff data
            # Update if field is missing, null, empty array, or contains only null-condition values
            current_plaintiff = data.get('plaintiff', [])
            should_overwrite = (
                    current_plaintiff is None or
                    current_plaintiff == [] or
                    not isinstance(current_plaintiff, list) or
                    all(p in self.NULL_CONDITIONS for p in current_plaintiff)
            )

            if should_overwrite:
                data['plaintiff'] = plaintiff_names
                updated_parties = True
                self.log_info(f"Updated 'plaintiff' from HTML: {plaintiff_names} for {filename}")
            else:
                self.log_debug(f"Skipping plaintiff update - existing data: {current_plaintiff} for {filename}")

        # Extract defendant names
        defendant_names = self._extract_defendant_names(parsed_content)
        if defendant_names:
            # CRITICAL FIX: More permissive update logic for defendant data
            current_defendant = data.get('defendant', [])
            current_defendants = data.get('defendants', [])

            should_overwrite_defendant = (
                    current_defendant is None or
                    current_defendant == [] or
                    not isinstance(current_defendant, list) or
                    all(d in self.NULL_CONDITIONS for d in current_defendant)
            )
            should_overwrite_defendants = (
                    current_defendants is None or
                    current_defendants == [] or
                    not isinstance(current_defendants, list) or
                    all(d in self.NULL_CONDITIONS for d in current_defendants)
            )

            if should_overwrite_defendant:
                data['defendant'] = defendant_names
                updated_parties = True
                self.log_info(f"Updated 'defendant' from HTML: {defendant_names} for {filename}")
            else:
                self.log_debug(f"Skipping defendant update - existing data: {current_defendant} for {filename}")

            if should_overwrite_defendants:
                data['defendants'] = defendant_names
                updated_parties = True
                self.log_info(f"Updated 'defendants' from HTML: {defendant_names} for {filename}")
            else:
                self.log_debug(f"Skipping defendants update - existing data: {current_defendants} for {filename}")

        return updated_parties

    def _extract_plaintiff_names(self, parsed_content: Dict) -> List[str]:
        """
        Extract plaintiff names from parsed HTML content.
        
        Args:
            parsed_content: Parsed HTML content dictionary
            
        Returns:
            List of cleaned plaintiff names
        """
        plaintiff_names = []

        # Try different possible field names for plaintiffs
        plaintiff_fields = ['plaintiffs', 'plaintiff', 'plaintiff_names']

        for field in plaintiff_fields:
            raw_plaintiffs = parsed_content.get(field, [])

            if isinstance(raw_plaintiffs, list):
                seen_plaintiffs = set()
                for p in raw_plaintiffs:
                    if isinstance(p, dict):
                        # If plaintiffs are dict objects, extract name
                        name = p.get('name') or p.get('plaintiff_name') or str(p)
                    elif isinstance(p, str):
                        name = p
                    else:
                        continue

                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_plaintiffs:
                        plaintiff_names.append(cleaned_name)
                        seen_plaintiffs.add(cleaned_name)

            elif isinstance(raw_plaintiffs, str) and raw_plaintiffs.strip():
                cleaned_name = self._clean_party_name(raw_plaintiffs)
                if cleaned_name:
                    plaintiff_names.append(cleaned_name)

        return plaintiff_names

    def _extract_defendant_names(self, parsed_content: Dict) -> List[str]:
        """
        Extract defendant names from parsed HTML content.
        
        Args:
            parsed_content: Parsed HTML content dictionary
            
        Returns:
            List of cleaned defendant names
        """
        defendant_names = []

        # Try different possible field names for defendants
        defendant_fields = ['defendants', 'defendant', 'defendant_names']

        for field in defendant_fields:
            raw_defendants = parsed_content.get(field, [])

            if isinstance(raw_defendants, list):
                seen_defendants = set()
                for d in raw_defendants:
                    if isinstance(d, dict):
                        # If defendants are dict objects, extract name
                        name = d.get('name') or d.get('defendant_name') or str(d)
                    elif isinstance(d, str):
                        name = d
                    else:
                        continue

                    cleaned_name = self._clean_party_name(name)
                    if cleaned_name and cleaned_name not in seen_defendants:
                        defendant_names.append(cleaned_name)
                        seen_defendants.add(cleaned_name)

            elif isinstance(raw_defendants, str) and raw_defendants.strip():
                cleaned_name = self._clean_party_name(raw_defendants)
                if cleaned_name:
                    defendant_names.append(cleaned_name)

        return defendant_names

    def _clean_party_name(self, name: str) -> Optional[str]:
        """
        Clean and validate a party name.
        
        Args:
            name: Raw party name
            
        Returns:
            Cleaned party name or None if invalid
        """
        if not isinstance(name, str):
            return None

        # Basic cleaning
        cleaned = name.strip()

        # Remove common prefixes/suffixes
        cleaned = re.sub(r'^(plaintiff|defendant):\s*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'\s*\(.*?\)\s*$', '', cleaned)  # Remove trailing parentheses

        # Check for null conditions
        if not cleaned or cleaned in self.NULL_CONDITIONS:
            return None

        # Check for minimum length
        if len(cleaned) < 2:
            return None

        # Check for obviously invalid names
        invalid_patterns = [
            r'^[0-9\-\s]+$',  # Only numbers, dashes, spaces
            r'^[^a-zA-Z]*$',  # No letters at all
            r'^\W+$'  # Only special characters
        ]

        for pattern in invalid_patterns:
            if re.match(pattern, cleaned):
                return None

        return cleaned

    def _extract_case_information(self, data: Dict, parsed_content: Dict, filename: str) -> bool:
        """
        Extract case information from parsed HTML content.
        
        Args:
            data: Docket data dictionary (modified in place)
            parsed_content: Parsed HTML content
            filename: Filename for logging context
            
        Returns:
            True if case information was updated, False otherwise
        """
        updated_case_info = False

        # Fields to extract from parsed content
        case_info_fields = {
            'nature_of_suit': ['nature_of_suit', 'nature', 'cause'],
            'jurisdiction': ['jurisdiction', 'court_jurisdiction'],
            'case_type': ['case_type', 'type'],
            'judge': ['judge', 'assigned_judge', 'presiding_judge'],
            'case_flags': ['case_flags', 'flags', 'status_flags']
        }

        for target_field, source_fields in case_info_fields.items():
            for source_field in source_fields:
                if source_field in parsed_content:
                    value = parsed_content[source_field]

                    # Only update if current field is missing/null
                    if target_field not in data or data[target_field] in self.NULL_CONDITIONS:
                        if value not in self.NULL_CONDITIONS:
                            data[target_field] = value
                            updated_case_info = True
                            self.log_debug(f"Updated '{target_field}' from HTML for {filename}")
                    break

        # Merge any other relevant case_info fields
        if 'case_info' in parsed_content and isinstance(parsed_content['case_info'], dict):
            case_info = parsed_content['case_info']

            for key, value in case_info.items():
                # Avoid overwriting plaintiffs/defendants handled above
                if key not in ['plaintiffs', 'defendants', 'plaintiff', 'defendant']:
                    if key not in data or data[key] in self.NULL_CONDITIONS:
                        if value not in self.NULL_CONDITIONS:
                            data[key] = value
                            updated_case_info = True

        return updated_case_info

    def validate_html_extraction(self, data: Dict, original_data: Dict) -> Dict[str, Any]:
        """
        Validate HTML extraction results.
        
        Args:
            data: Updated data dictionary
            original_data: Original data before HTML processing
            
        Returns:
            Validation report
        """
        report = {
            'status': 'success',
            'updates': [],
            'warnings': [],
            'errors': []
        }

        # Check what was updated
        fields_to_check = ['plaintiff', 'defendant', 'defendants', 'nature_of_suit', 'jurisdiction', 'judge']

        for field in fields_to_check:
            original_value = original_data.get(field)
            updated_value = data.get(field)

            if original_value != updated_value:
                report['updates'].append(f"Updated {field}")

                # Validate the update
                if field in ['plaintiff', 'defendant', 'defendants']:
                    if isinstance(updated_value, list) and len(updated_value) == 0:
                        report['warnings'].append(f"HTML extraction resulted in empty {field} list")
                    elif isinstance(updated_value, list) and len(updated_value) > 50:
                        report['warnings'].append(f"Unusually large number of {field}s extracted: {len(updated_value)}")

        # Overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'
        elif not report['updates']:
            report['status'] = 'no_changes'

        return report

    def get_html_processing_summary(self, data: Dict) -> str:
        """
        Get summary of HTML processing results.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        summary_parts = []

        # Count extracted parties
        plaintiff_count = len(data.get('plaintiff', []))
        defendant_count = len(data.get('defendant', []))

        if plaintiff_count > 0:
            summary_parts.append(f"{plaintiff_count} plaintiff(s)")
        if defendant_count > 0:
            summary_parts.append(f"{defendant_count} defendant(s)")

        # Check for case information
        case_fields = ['nature_of_suit', 'jurisdiction', 'judge']
        extracted_case_fields = [f for f in case_fields if data.get(f) not in self.NULL_CONDITIONS]

        if extracted_case_fields:
            summary_parts.append(f"{len(extracted_case_fields)} case info fields")

        if summary_parts:
            return f"HTML extraction: {', '.join(summary_parts)}"
        else:
            return "HTML processed but no content extracted"

    async def _download_html_content(self, html_link: str, filename: str) -> Optional[str]:
        """
        Download HTML content from the provided link.
        
        Args:
            html_link: URL to HTML content
            filename: Filename for logging context
            
        Returns:
            HTML content as string or None
        """
        self.log_info(f"[HTML Download] Starting download for {filename} from: {html_link}")

        try:
            import aiohttp

            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                self.log_debug(f"[HTML Download] Created aiohttp session for {filename}")

                async with session.get(html_link) as response:
                    self.log_info(f"[HTML Download] Response status for {filename}: {response.status}")

                    if response.status == 200:
                        content = await response.text()
                        content_length = len(content)
                        self.log_info(f"[HTML Download] SUCCESS - Downloaded {content_length} chars for {filename}")

                        # Log first 500 chars of content for debugging
                        preview = content[:500] if content else ""
                        self.log_debug(f"[HTML Download] Content preview for {filename}: {preview}...")

                        return content
                    else:
                        self.log_error(
                            f"[HTML Download] FAILED - HTTP {response.status} for {filename} from {html_link}")
                        return None

        except Exception as e:
            # Handle all exceptions generically since aiohttp might not be available
            self.log_error(f"[HTML Download] ERROR - Failed to download HTML for {filename}: {e}")
            return None

    def _cleanup_plural_fields(self, data: Dict) -> None:
        """
        Clean up plural field names to maintain consistency with original DataUpdaterService behavior.
        
        Args:
            data: Data dictionary to clean up (modified in place)
        """
        # Clean up plural fields to avoid confusion - matches DataUpdaterService behavior
        fields_to_remove = ['plaintiffs', 'defendants', 'plaintiffs_gpt']
        for field in fields_to_remove:
            if field in data:
                del data[field]
                self.log_debug(f"Removed plural field '{field}' for consistency")

    async def _direct_parse_html(self, html_content: str, filename: str) -> Optional[Dict]:
        """
        Directly parse HTML content without relying on external services.
        
        Args:
            html_content: Raw HTML content string
            filename: Filename for logging context
            
        Returns:
            Parsed content dictionary or None
        """
        self.log_info(f"[Direct Parse] Attempting direct HTML parsing for {filename}")

        try:
            from bs4 import BeautifulSoup
            import re

            soup = BeautifulSoup(html_content, 'html.parser')
            parsed_data = {}

            # Look for plaintiff/defendant information in common patterns
            # Pattern 1: Table rows with "Plaintiff" and "Defendant" labels
            for row in soup.find_all('tr'):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    label = cells[0].get_text(strip=True).lower()
                    value = cells[1].get_text(strip=True)

                    if 'plaintiff' in label and value:
                        if 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        parsed_data['plaintiffs'].append(value)
                        self.log_debug(f"[Direct Parse] Found plaintiff: {value}")

                    elif 'defendant' in label and value:
                        if 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []
                        parsed_data['defendants'].append(value)
                        self.log_debug(f"[Direct Parse] Found defendant: {value}")

            # Pattern 2: Look for sections with party information
            # Search for divs or sections that might contain party lists
            party_sections = soup.find_all(['div', 'section'], string=re.compile(r'(Plaintiff|Defendant)', re.I))
            for section in party_sections:
                # Look for following siblings or nested lists
                next_element = section.find_next_sibling()
                if next_element and next_element.name in ['ul', 'ol']:
                    items = next_element.find_all('li')
                    if 'plaintiff' in section.get_text(strip=True).lower():
                        if 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        for item in items:
                            parsed_data['plaintiffs'].append(item.get_text(strip=True))
                    elif 'defendant' in section.get_text(strip=True).lower():
                        if 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []
                        for item in items:
                            parsed_data['defendants'].append(item.get_text(strip=True))

            # Pattern 3: Look for specific class names or IDs that might contain party info
            for class_name in ['plaintiff', 'plaintiffs', 'defendant', 'defendants']:
                elements = soup.find_all(class_=re.compile(class_name, re.I))
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if text and not text.lower().startswith(class_name):  # Avoid just the label
                        if 'plaintiff' in class_name and 'plaintiffs' not in parsed_data:
                            parsed_data['plaintiffs'] = []
                        if 'defendant' in class_name and 'defendants' not in parsed_data:
                            parsed_data['defendants'] = []

                        if 'plaintiff' in class_name:
                            parsed_data['plaintiffs'].append(text)
                        else:
                            parsed_data['defendants'].append(text)

            # Pattern 4: Look for the specific table structure in actual PACER HTML
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for i, row in enumerate(rows):
                    row_text = row.get_text(strip=True).lower()
                    # Check for plaintiff/defendant headers with bold and underline tags
                    if 'plaintiff' in row_text and row.find('b') and row.find('u'):
                        # Next rows contain plaintiff info
                        j = i + 1
                        while j < len(rows) and 'defendant' not in rows[j].get_text(strip=True).lower():
                            bold_tags = rows[j].find_all('b')
                            for tag in bold_tags:
                                name = tag.get_text(strip=True)
                                if name and name not in ['V.', 'v.', '', 'plaintiff', 'PLAINTIFF']:
                                    # Skip if this looks like attorney info (contains multiple spaces between words)
                                    if '    ' not in name:  # Multiple spaces indicate attorney formatting
                                        if 'plaintiffs' not in parsed_data:
                                            parsed_data['plaintiffs'] = []
                                        if name not in parsed_data['plaintiffs']:  # Avoid duplicates
                                            parsed_data['plaintiffs'].append(name)
                                            self.log_debug(f"[Direct Parse] Found plaintiff via table pattern: {name}")
                            j += 1
                    elif 'defendant' in row_text and row.find('b') and row.find('u'):
                        # Following rows contain defendant info
                        j = i + 1
                        while j < len(rows):
                            bold_tags = rows[j].find_all('b')
                            for tag in bold_tags:
                                name = tag.get_text(strip=True)
                                if name and name not in ['', 'V.', 'v.', 'defendant', 'DEFENDANT']:
                                    # Skip if this looks like attorney info (contains multiple spaces between words)
                                    if '    ' not in name:  # Multiple spaces indicate attorney formatting
                                        if 'defendants' not in parsed_data:
                                            parsed_data['defendants'] = []
                                        if name not in parsed_data['defendants']:  # Avoid duplicates
                                            parsed_data['defendants'].append(name)
                                            self.log_debug(f"[Direct Parse] Found defendant via table pattern: {name}")
                            j += 1

            # Log what we found
            self.log_info(f"[Direct Parse] Parsed data summary for {filename}: "
                          f"plaintiffs={len(parsed_data.get('plaintiffs', []))}, "
                          f"defendants={len(parsed_data.get('defendants', []))}")

            if parsed_data:
                return parsed_data
            else:
                self.log_warning(f"[Direct Parse] No party data found in HTML for {filename}")
                return None

        except ImportError:
            self.log_error(f"[Direct Parse] BeautifulSoup not available for direct HTML parsing")
            return None
        except Exception as e:
            self.log_error(f"[Direct Parse] Error during direct HTML parsing for {filename}: {e}")
            return None

    def set_html_processing_service(self, html_processing_service: Any) -> None:
        """
        Set the HTML processing service for enhanced functionality.
        
        Args:
            html_processing_service: HTML processing service instance
        """
        self.html_processing_service = html_processing_service
        self.log_debug("HTML processing service has been set")
