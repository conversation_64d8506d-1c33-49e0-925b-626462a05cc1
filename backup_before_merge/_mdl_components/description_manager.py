# /src/services/transformer/mdl_description_manager.py
"""

MDL description and summary management with LLM integration.

This module handles MDL description processing, summary generation, and LLM 
interactions extracted from mdl_processor.py as part of Phase 3.3 refactoring.
"""
import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Tuple

import pandas as pd
from tqdm import tqdm

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError


class MDLDescriptionManager(AsyncServiceBase):
    """Manages MDL descriptions and summaries with LLM integration."""

    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    def __init__(self,
                 file_handler=None,
                 gpt_client=None,
                 config: Optional[Dict] =None,
                 download_dir: Optional[str] =None,
                 logger: Optional[logging.Logger] =None):
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        self.file_handler = file_handler
        self.gpt = gpt_client
        self.download_dir = download_dir

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLDescriptionManager actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'update_mdl_descriptions_from_dockets':
                mdl_litigations = action_data.get('mdl_litigations')
                json_paths = action_data.get('json_paths')
                return await self.update_mdl_descriptions_from_dockets(mdl_litigations, json_paths)
            elif action == 'match_titles_to_mdl_litigation':
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.match_titles_to_mdl_litigation(mdl_litigations)
            elif action == 'summarize_existing_descriptions':
                mdl_litigations = action_data.get('mdl_litigations')
                return await self.summarize_existing_descriptions(mdl_litigations)
            elif action == 'validate_description_data':
                mdl_litigations = action_data.get('mdl_litigations')
                return self.validate_description_data(mdl_litigations)
            elif action == 'get_description_summary':
                mdl_litigations = action_data.get('mdl_litigations')
                return self.get_description_summary(mdl_litigations)
            elif action == 'extract_unique_mdl_numbers':
                df = action_data.get('dataframe')
                return await self._extract_unique_mdl_numbers(df)
            elif action == 'should_update_mdl_summary':
                mdl_litigations = action_data.get('mdl_litigations')
                mdl_num = action_data.get('mdl_num', '')
                return await self._should_update_mdl_summary(mdl_litigations, mdl_num)
            elif action == 'get_docket_data_for_mdl':
                df = action_data.get('dataframe')
                mdl_num = action_data.get('mdl_num', '')
                return await self._get_docket_data_for_mdl(df, mdl_num)
            elif action == 'generate_summary_with_llm':
                text = action_data.get('text', '')
                return await self._generate_summary_with_llm(text)
            elif action == 'extract_text_for_summary':
                mdl_num = action_data.get('mdl_num', '')
                return await self._extract_text_for_summary(mdl_num)
            elif action == 'process_docket_files_for_descriptions':
                json_paths = action_data.get('json_paths', [])
                return await self._process_docket_files_for_descriptions(json_paths)
            elif action == 'needs_summary_update':
                mdl_entry = action_data.get('mdl_entry', {})
                return self._needs_summary_update(mdl_entry)
            elif action == 'get_json_file_paths':
                return await self._get_json_file_paths()
            elif action == 'extract_mdl_number_from_data':
                docket_data = action_data.get('docket_data', {})
                return await self._extract_mdl_number_from_data(docket_data)
        raise TransformerServiceError("Invalid action data provided to MDLDescriptionManager")

    async def update_mdl_descriptions_from_dockets(self, mdl_litigations: pd.DataFrame,
                                                   json_paths: Optional[List[str]] = None) -> Tuple[pd.DataFrame, int]:
        """
        Update MDL descriptions by processing docket files and generating summaries.
        
        Args:
            mdl_litigations: DataFrame containing MDL litigation data
            json_paths: Optional list of specific JSON paths to process
            
        Returns:
            Tuple of (updated DataFrame, number of updates made)
        """
        self.log_info("Starting MDL description update from dockets...")

        # Read docket data
        if json_paths:
            df = await self._read_specific_jsons(json_paths)
        else:
            df = await self._read_all_jsons()

        if df.empty:
            self.log_warning("No docket data read, cannot update MDL descriptions.")
            return mdl_litigations, 0

        # Get unique MDL numbers from the dockets
        unique_mdl_nums = await self._extract_unique_mdl_numbers(df)

        if not unique_mdl_nums:
            self.log_warning("No valid MDL numbers found in docket data.")
            return mdl_litigations, 0

        self.log_info(f"Found {len(unique_mdl_nums)} unique MDL numbers in dockets to check for updates.")

        updated_count = 0

        # Process each unique MDL number
        for mdl_num in tqdm(unique_mdl_nums, desc='Updating MDL Descriptions'):
            try:
                if await self._should_update_mdl_summary(mdl_litigations, mdl_num):
                    # Find docket data for this MDL
                    docket_data = await self._get_docket_data_for_mdl(df, mdl_num)

                    if docket_data:
                        # Find the target index in the MDL lookup table
                        target_idx = await self._find_mdl_index(mdl_litigations, mdl_num)

                        if target_idx is not None:
                            # Attempt to update the summary
                            if await self._update_mdl_summary_from_docket(
                                    mdl_litigations, target_idx, mdl_num, docket_data
                            ):
                                updated_count += 1

            except Exception as e:
                self.log_error(f"Error processing MDL {mdl_num}: {e}")
                continue

        self.log_info(f"Updated descriptions for {updated_count} MDLs from docket data.")
        return mdl_litigations, updated_count

    async def match_titles_to_mdl_litigation(self, mdl_litigations: pd.DataFrame,
                                             json_paths: List[str]) -> Tuple[pd.DataFrame, int]:
        """
        Match titles from specific JSON files to MDL numbers and update summaries.
        
        Args:
            mdl_litigations: DataFrame containing MDL litigation data
            json_paths: List of JSON file paths to process
            
        Returns:
            Tuple of (updated DataFrame, number of updates made)
        """
        if not json_paths:
            self.log_info("No JSON paths provided for MDL title matching.")
            return mdl_litigations, 0

        # Read data from specified JSON files
        df = await self._read_specific_jsons(json_paths)
        if df.empty:
            self.log_info("No data read from the provided JSON paths for MDL matching.")
            return mdl_litigations, 0

        self.log_info(f"Attempting to match titles for {len(json_paths)} files against MDL data.")
        updated_count = 0

        # Process each JSON file
        for json_path in tqdm(json_paths, desc="Matching Titles to MDL"):
            try:
                base_filename = os.path.basename(json_path)

                # Find corresponding data in the DataFrame
                docket_data = await self._find_docket_data_by_filename(df, base_filename, json_path)

                if not docket_data:
                    continue

                # Check if this docket has a valid MDL number
                mdl_num = await self._extract_mdl_number_from_data(docket_data)

                if not mdl_num:
                    self.log_debug(f"Skipping {base_filename}: No valid MDL number.")
                    continue

                # Find matching entry in MDL lookup table
                target_idx = await self._find_mdl_index(mdl_litigations, mdl_num)

                if target_idx is None:
                    self.log_debug(f"MDL {mdl_num} from {base_filename} not found in lookup table.")
                    continue

                # Check if summary needs update
                if await self._should_update_mdl_summary_at_index(mdl_litigations, target_idx):
                    self.log_info(f"MDL {mdl_num} needs summary update. Processing data from {base_filename}.")

                    # Update the summary
                    if await self._update_mdl_summary_from_docket(
                            mdl_litigations, target_idx, mdl_num, docket_data
                    ):
                        updated_count += 1

            except Exception as e:
                self.log_error(f"Error processing {json_path}: {e}")
                continue

        self.log_info(f"Attempted updates for {updated_count} MDL summaries from title matching.")
        return mdl_litigations, updated_count

    async def summarize_existing_descriptions(self, mdl_litigations: pd.DataFrame) -> Tuple[pd.DataFrame, int]:
        """
        Summarize existing MDL descriptions using LLM where short_summary is missing.
        
        Args:
            mdl_litigations: DataFrame containing MDL litigation data
            
        Returns:
            Tuple of (updated DataFrame, number of updates made)
        """
        self.log_info("Starting summarization of existing MDL descriptions...")

        # Filter for entries needing summarization
        needs_summary_df = await self._filter_entries_needing_summary(mdl_litigations)

        if needs_summary_df.empty:
            self.log_info("No MDL entries found requiring description summarization.")
            return mdl_litigations, 0

        self.log_info(f"Found {len(needs_summary_df)} MDL entries requiring summarization.")
        updated_count = 0

        for index, row in tqdm(needs_summary_df.iterrows(), total=len(needs_summary_df), desc="Summarizing MDLs"):
            try:
                mdl_num = row['mdl_num']
                description = row['description']

                self.log_debug(f"Summarizing MDL No {mdl_num}")

                # Generate summary using LLM
                summary = await self._generate_summary_with_llm(description)

                if summary and summary not in self.NULL_CONDITIONS:
                    # Update all rows with the same mdl_num
                    await self._update_summary_in_dataframe(mdl_litigations, mdl_num, summary)
                    updated_count += 1
                else:
                    self.log_warning(f"LLM failed to generate a valid summary for MDL No {mdl_num}")

            except Exception as e:
                self.log_error(f"Error summarizing description for MDL {row.get('mdl_num', 'unknown')}: {e}")

        self.log_info(f"Generated summaries for {updated_count} MDLs.")
        return mdl_litigations, updated_count

    async def _read_specific_jsons(self, json_paths: List[str]) -> pd.DataFrame:
        """Read data from specific JSON files."""
        records = []
        if not json_paths:
            return pd.DataFrame()

        self.log_info(f"Reading data from {len(json_paths)} specified JSON files.")
        for file_path in tqdm(json_paths, desc="Reading JSONs"):
            try:
                data = self.file_handler.load_json(file_path)
                if data:
                    data['_source_json_path'] = file_path
                    records.append(data)
                else:
                    self.log_warning(f"Could not load data from {os.path.basename(file_path)}")
            except Exception as e:
                self.log_error(f"Error reading {file_path}: {e}")

        return pd.DataFrame(records) if records else pd.DataFrame()

    async def _read_all_jsons(self) -> pd.DataFrame:
        """Read all docket JSON files from the determined directory."""
        target_docket_dir = await self._get_dockets_directory()

        if not target_docket_dir or not os.path.isdir(target_docket_dir):
            self.log_error(f"Target dockets directory does not exist: {target_docket_dir}")
            return pd.DataFrame()

        try:
            json_files = [
                os.path.join(target_docket_dir, f)
                for f in os.listdir(target_docket_dir)
                if f.endswith('.json')
            ]
        except OSError as e:
            self.log_error(f"Error listing files in {target_docket_dir}: {e}")
            return pd.DataFrame()

        records = []
        self.log_info(f"Reading all {len(json_files)} JSON files from {target_docket_dir}.")

        for file_path in tqdm(json_files, desc="Reading all JSONs"):
            try:
                data = self.file_handler.load_json(file_path)
                if data:
                    records.append(data)
            except Exception as e:
                self.log_error(f"Error reading {file_path}: {e}")

        return pd.DataFrame(records)

    async def _get_dockets_directory(self) -> Optional[str]:
        """Determine the dockets directory path."""
        if self.download_dir:
            return os.path.join(self.download_dir, 'dockets')
        elif hasattr(self.file_handler, 'download_dir') and self.file_handler.download_dir:
            return os.path.join(self.file_handler.download_dir, 'dockets')
        else:
            self.log_error("Cannot determine target dockets directory.")
            return None

    async def _extract_unique_mdl_numbers(self, df: pd.DataFrame) -> List[str]:
        """Extract unique valid MDL numbers from DataFrame."""
        if df.empty or 'mdl_num' not in df.columns:
            return []

        # Clean and filter MDL numbers
        unique_mdl_nums = (
            df['mdl_num']
            .dropna()
            .astype(str)
            .str.replace(r'\.0$', '', regex=True)
            .unique()
            .tolist()
        )

        # Filter out invalid values
        valid_mdl_nums = [
            num for num in unique_mdl_nums
            if num not in ["NA", '9000', '', 'nan'] and num.strip()
        ]

        return valid_mdl_nums

    async def _should_update_mdl_summary(self, mdl_litigations: pd.DataFrame, mdl_num: str) -> bool:
        """Check if an MDL summary should be updated."""
        try:
            if 'mdl_num' not in mdl_litigations.columns:
                self.log_error("MDL lookup table missing 'mdl_num' column.")
                return False

            match_index = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index

            if match_index.empty:
                self.log_debug(f"MDL {mdl_num} not found in the active MDL lookup table.")
                return False

            # Check if short_summary needs updating
            target_idx = match_index[0]
            return await self._should_update_mdl_summary_at_index(mdl_litigations, target_idx)

        except Exception as e:
            self.log_error(f"Error checking if MDL {mdl_num} needs update: {e}")
            return False

    async def _should_update_mdl_summary_at_index(self, mdl_litigations: pd.DataFrame, target_idx: int) -> bool:
        """Check if the summary at a specific index should be updated."""
        try:
            short_summary_value = mdl_litigations.at[target_idx, 'short_summary']

            summary_missing = (
                    pd.isna(short_summary_value) or
                    (isinstance(short_summary_value, str) and not short_summary_value.strip()) or
                    short_summary_value in self.NULL_CONDITIONS
            )

            return summary_missing

        except Exception as e:
            self.log_error(f"Error checking summary at index {target_idx}: {e}")
            return False

    async def _get_docket_data_for_mdl(self, df: pd.DataFrame, mdl_num: str) -> Optional[Dict]:
        """Get docket data for a specific MDL number."""
        try:
            # Create temporary column for matching
            df['mdl_num_str'] = df['mdl_num'].astype(str).str.replace(r'\.0$', '', regex=True)
            match_row_df = df[df['mdl_num_str'] == mdl_num]

            if match_row_df.empty:
                self.log_warning(f"No docket data found for MDL {mdl_num}")
                return None

            # Return the first matching row as dictionary
            return match_row_df.iloc[0].to_dict()

        except Exception as e:
            self.log_error(f"Error getting docket data for MDL {mdl_num}: {e}")
            return None
        finally:
            # Clean up temporary column
            if 'mdl_num_str' in df.columns:
                df.drop(columns=['mdl_num_str'], inplace=True)

    async def _find_mdl_index(self, mdl_litigations: pd.DataFrame, mdl_num: str) -> Optional[int]:
        """Find the index of an MDL in the litigation DataFrame."""
        try:
            if 'mdl_num' not in mdl_litigations.columns:
                return None

            match_index = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index
            return match_index[0] if not match_index.empty else None

        except Exception as e:
            self.log_error(f"Error finding index for MDL {mdl_num}: {e}")
            return None

    async def _update_mdl_summary_from_docket(self, mdl_litigations: pd.DataFrame,
                                              target_idx: int, mdl_num: str,
                                              docket_data: Dict) -> bool:
        """
        Update MDL summary using docket data and LLM processing.
        
        Args:
            mdl_litigations: DataFrame to update
            target_idx: Index to update
            mdl_num: MDL number being processed
            docket_data: Docket data dictionary
            
        Returns:
            True if update was successful
        """
        try:
            # Get text content from associated files
            full_text = await self._extract_text_from_docket(docket_data, mdl_num)

            if not full_text:
                self.log_warning(f"No text content could be obtained for MDL {mdl_num}")
                return False

            # Generate summary using LLM
            self.log_info(f"Generating summary for MDL {mdl_num} from text ({len(full_text)} chars).")
            summary = await self._generate_summary_with_llm(full_text)

            if summary and summary not in self.NULL_CONDITIONS:
                # Update the DataFrame
                mdl_litigations.at[target_idx, 'short_summary'] = summary
                self.log_info(f"Successfully updated MDL {mdl_num} with summary: '{summary[:100]}...'")
                return True
            else:
                self.log_warning(f"LLM did not generate a valid summary for MDL {mdl_num}")
                return False

        except Exception as e:
            self.log_error(f"Error updating MDL summary for {mdl_num}: {e}")
            return False

    async def _extract_text_from_docket(self, docket_data: Dict, mdl_num: str) -> str:
        """Extract text content from docket files."""
        try:
            docket_dir = await self._get_dockets_directory()
            if not docket_dir:
                return ""

            # Get filename
            base_filename = docket_data.get('new_filename') or docket_data.get('original_filename')
            if not base_filename:
                self.log_error(f"Could not determine filename for MDL {mdl_num}")
                return ""

            md_path = os.path.join(docket_dir, f'{base_filename}.md')
            pdf_path = os.path.join(docket_dir, f'{base_filename}.pdf')

            # Try reading from .md file first
            if os.path.exists(md_path):
                try:
                    with open(md_path, 'r', encoding='utf-8') as f:
                        full_text = f.read()
                    if full_text and len(full_text.strip()) > 10:
                        self.log_debug(f"Loaded text from {os.path.basename(md_path)} for MDL {mdl_num}")
                        return full_text
                except Exception as e:
                    self.log_warning(f"Error reading .md file {os.path.basename(md_path)}: {e}")

            # TODO: Add PDF text extraction when pdf_processor is available
            # This would require integration with the text extraction components
            if os.path.exists(pdf_path):
                self.log_warning(f"PDF text extraction not yet implemented for {pdf_path}")

            return ""

        except Exception as e:
            self.log_error(f"Error extracting text for MDL {mdl_num}: {e}")
            return ""

    async def _generate_summary_with_llm(self, text: str) -> Optional[str]:
        """Generate summary using LLM client."""
        try:
            if not hasattr(self.gpt, 'extract_summary'):
                self.log_error("LLM client does not have 'extract_summary' method.")
                return None

            # Check if extract_summary is async
            if asyncio.iscoroutinefunction(self.gpt.extract_summary):
                summary = await self.gpt.extract_summary(text, 'short')
            else:
                summary = self.gpt.extract_summary(text, 'short')

            return summary if summary and summary not in self.NULL_CONDITIONS else None

        except Exception as e:
            self.log_error(f"Error during LLM summary generation: {e}")
            return None

    async def _find_docket_data_by_filename(self, df: pd.DataFrame, base_filename: str,
                                            json_path: str) -> Optional[Dict]:
        """Find docket data by filename in DataFrame."""
        try:
            file_identifier = os.path.splitext(base_filename)[0]

            # Try to find matching row based on filename
            match_row_df = df[
                (df.get('new_filename') == file_identifier) |
                (df.get('original_filename') == file_identifier)
                ]

            if not match_row_df.empty:
                return match_row_df.iloc[0].to_dict()

            # Fallback: load individual file
            self.log_debug(f"No data found in pre-read DataFrame for {base_filename}, loading directly")
            data = self.file_handler.load_json(json_path)
            return data if data else None

        except Exception as e:
            self.log_error(f"Error finding docket data for {base_filename}: {e}")
            return None

    async def _extract_mdl_number_from_data(self, docket_data: Dict) -> Optional[str]:
        """Extract and validate MDL number from docket data."""
        try:
            mdl_num_raw = docket_data.get('mdl_num')

            if mdl_num_raw in self.NULL_CONDITIONS or pd.isna(mdl_num_raw):
                return None

            # Clean up potential float conversion artifacts
            mdl_num = str(mdl_num_raw).lstrip('0').replace('.0', '')

            return mdl_num if mdl_num else None

        except Exception as e:
            self.log_error(f"Error extracting MDL number: {e}")
            return None

    async def _filter_entries_needing_summary(self, mdl_litigations: pd.DataFrame) -> pd.DataFrame:
        """Filter DataFrame for entries needing summarization."""
        if mdl_litigations.empty:
            return pd.DataFrame()

        # Check required columns
        required_cols = ['description', 'short_summary', 'mdl_num']
        missing_cols = [col for col in required_cols if col not in mdl_litigations.columns]

        if missing_cols:
            self.log_error(f"MDL DataFrame missing required columns: {missing_cols}")
            return pd.DataFrame()

        # Drop duplicates and filter for entries needing summarization
        mdl_litigations_unique = mdl_litigations.drop_duplicates(subset=['mdl_num'], keep='first')

        needs_summary_mask = (
                mdl_litigations_unique['description'].notna() &
                (mdl_litigations_unique['description'].astype(str).str.strip().str.len() > 10) &
                (
                        mdl_litigations_unique['short_summary'].isna() |
                        (mdl_litigations_unique['short_summary'].astype(str).str.strip().str.len() <= 3) |
                        (mdl_litigations_unique['short_summary'].isin(self.NULL_CONDITIONS))
                )
        )

        return mdl_litigations_unique[needs_summary_mask]

    async def _update_summary_in_dataframe(self, mdl_litigations: pd.DataFrame,
                                           mdl_num: str, summary: str):
        """Update summary for all rows with the same MDL number."""
        try:
            # Find all rows with the same mdl_num and update them
            original_indices = mdl_litigations[mdl_litigations['mdl_num'] == mdl_num].index

            if not original_indices.empty:
                mdl_litigations.loc[original_indices, 'short_summary'] = summary
                self.log_debug(
                    f"Generated summary for MDL {mdl_num}: '{summary[:100]}...' "
                    f"(updated {len(original_indices)} rows)"
                )
            else:
                self.log_warning(
                    f"MDL {mdl_num} was found for summarization but original index not found?"
                )

        except Exception as e:
            self.log_error(f"Error updating summary for MDL {mdl_num}: {e}")

    def validate_description_data(self, mdl_litigations: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate MDL description data and return validation report.
        
        Args:
            mdl_litigations: DataFrame to validate
            
        Returns:
            Validation report with status and details
        """
        report = {
            'status': 'success',
            'warnings': [],
            'errors': [],
            'total_entries': len(mdl_litigations),
            'entries_with_descriptions': 0,
            'entries_with_summaries': 0,
            'entries_needing_summaries': 0
        }

        if mdl_litigations.empty:
            report['status'] = 'error'
            report['errors'].append("MDL litigation DataFrame is empty")
            return report

        # Check required columns
        required_cols = ['mdl_num', 'description', 'short_summary']
        missing_cols = [col for col in required_cols if col not in mdl_litigations.columns]

        if missing_cols:
            report['errors'].append(f"Missing required columns: {missing_cols}")
            report['status'] = 'error'
            return report

        # Count entries with valid data
        report['entries_with_descriptions'] = (
                mdl_litigations['description'].notna() &
                (mdl_litigations['description'].astype(str).str.strip().str.len() > 10)
        ).sum()

        report['entries_with_summaries'] = (
                mdl_litigations['short_summary'].notna() &
                (mdl_litigations['short_summary'].astype(str).str.strip().str.len() > 3) &
                (~mdl_litigations['short_summary'].isin(self.NULL_CONDITIONS))
        ).sum()

        # Calculate entries needing summaries
        has_description = (
                mdl_litigations['description'].notna() &
                (mdl_litigations['description'].astype(str).str.strip().str.len() > 10)
        )
        missing_summary = (
                mdl_litigations['short_summary'].isna() |
                (mdl_litigations['short_summary'].astype(str).str.strip().str.len() <= 3) |
                (mdl_litigations['short_summary'].isin(self.NULL_CONDITIONS))
        )

        report['entries_needing_summaries'] = (has_description & missing_summary).sum()

        # Add warnings
        if report['entries_needing_summaries'] > 0:
            report['warnings'].append(
                f"{report['entries_needing_summaries']} entries have descriptions but missing summaries")

        if report['entries_with_descriptions'] == 0:
            report['warnings'].append("No entries have valid descriptions")

        # Set overall status
        if report['errors']:
            report['status'] = 'error'
        elif report['warnings']:
            report['status'] = 'warning'

        return report

    def get_description_summary(self, mdl_litigations: pd.DataFrame) -> str:
        """
        Get summary of MDL description processing status.
        
        Args:
            mdl_litigations: DataFrame to summarize
            
        Returns:
            Human-readable summary
        """
        if mdl_litigations.empty:
            return "No MDL data available"

        total_entries = len(mdl_litigations)

        # Count entries with descriptions and summaries
        with_descriptions = (
                mdl_litigations['description'].notna() &
                (mdl_litigations['description'].astype(str).str.strip().str.len() > 10)
        ).sum() if 'description' in mdl_litigations.columns else 0

        with_summaries = (
                mdl_litigations['short_summary'].notna() &
                (mdl_litigations['short_summary'].astype(str).str.strip().str.len() > 3) &
                (~mdl_litigations['short_summary'].isin(self.NULL_CONDITIONS))
        ).sum() if 'short_summary' in mdl_litigations.columns else 0

        return (f"MDL descriptions: {total_entries} total, "
                f"{with_descriptions} with descriptions, "
                f"{with_summaries} with summaries")

    # === Missing utility methods for test compatibility ===

    async def _get_json_file_paths(self) -> List[str]:
        """Get list of JSON file paths from dockets directory."""
        # Determine the dockets directory
        docket_dir = None
        if self.download_dir:
            docket_dir = os.path.join(self.download_dir, 'dockets')
        elif hasattr(self.file_handler, 'download_dir') and self.file_handler.download_dir:
            docket_dir = os.path.join(self.file_handler.download_dir, 'dockets')

        if not docket_dir or not os.path.isdir(docket_dir):
            self.log_error(f"Dockets directory does not exist: {docket_dir}")
            return []

        self.log_info(f"Scanning for JSON files in: {docket_dir}")
        try:
            all_files = os.listdir(docket_dir)
            json_paths = [os.path.join(docket_dir, f) for f in all_files if f.endswith('.json')]
            self.log_info(f"Found {len(json_paths)} JSON files.")
            return json_paths
        except OSError as e:
            self.log_error(f"Error listing files in {docket_dir}: {e}")
            return []

    async def _extract_text_for_summary(self, mdl_num: str) -> Optional[str]:
        """Extract text content for MDL summary generation."""
        try:
            # Get JSON file paths for this MDL
            json_paths = await self._get_json_file_paths()

            # Filter paths that might contain this MDL number
            relevant_paths = [
                path for path in json_paths
                if mdl_num in os.path.basename(path) or 'mdl' in os.path.basename(path).lower()
            ]

            if not relevant_paths:
                self.log_debug(f"No relevant files found for MDL {mdl_num}")
                return None

            # Try to extract text from the first relevant file
            for path in relevant_paths[:3]:  # Limit to first 3 files
                try:
                    if path.endswith('.json'):
                        # Load JSON and look for text content
                        data = self.file_handler.load_json(path)
                        if data and isinstance(data, dict):
                            # Look for text fields
                            for field in ['allegations', 'title', 'nature_of_suit', 'summary']:
                                content = data.get(field)
                                if content and isinstance(content, str) and len(content.strip()) > 20:
                                    return content.strip()
                    elif path.endswith('.pdf'):
                        # Extract PDF text
                        text = await self._extract_pdf_text(path)
                        if text and len(text.strip()) > 50:
                            return text.strip()[:2000]  # Limit length

                except Exception as e:
                    self.log_debug(f"Error processing file {path}: {e}")
                    continue

            return None

        except Exception as e:
            self.log_error(f"Error extracting text for MDL {mdl_num}: {e}")
            return None

    async def _extract_pdf_text(self, pdf_path: str) -> Optional[str]:
        """Extract text from PDF file."""
        try:
            if not os.path.exists(pdf_path):
                return None

            # Use file handler if it has PDF text extraction capability
            if hasattr(self.file_handler, 'extract_pdf_text'):
                return self.file_handler.extract_pdf_text(pdf_path)

            # Fallback: try to read with basic text extraction
            self.log_debug(f"No PDF text extraction available for {pdf_path}")
            return None

        except Exception as e:
            self.log_error(f"Error extracting PDF text from {pdf_path}: {e}")
            return None

    async def _process_docket_files_for_descriptions(self, json_paths: List[str]) -> Dict[str, Any]:
        """Process docket files to extract description content."""
        report = {
            'files_processed': 0,
            'descriptions_found': 0,
            'errors': [],
            'mdl_data': {}
        }

        for json_path in json_paths:
            try:
                data = self.file_handler.load_json(json_path)
                if not data or not isinstance(data, dict):
                    continue

                report['files_processed'] += 1

                # Extract MDL number
                mdl_num = data.get('mdl_num')
                if mdl_num and str(mdl_num) not in ['', 'NA', 'None']:
                    mdl_num_str = str(mdl_num)

                    # Look for description content
                    description_content = None
                    for field in ['allegations', 'title', 'nature_of_suit']:
                        content = data.get(field)
                        if content and isinstance(content, str) and len(content.strip()) > 20:
                            description_content = content.strip()
                            break

                    if description_content:
                        report['descriptions_found'] += 1
                        if mdl_num_str not in report['mdl_data']:
                            report['mdl_data'][mdl_num_str] = []
                        report['mdl_data'][mdl_num_str].append(description_content)

            except Exception as e:
                report['errors'].append(f"Error processing {json_path}: {str(e)}")

        return report

    def _needs_summary_update(self, mdl_entry: Dict) -> bool:
        """Check if MDL entry needs summary update."""
        try:
            # Check if description exists but summary is missing/short
            description = mdl_entry.get('description', '')
            summary = mdl_entry.get('short_summary', '')

            has_description = (
                    description and
                    description not in self.NULL_CONDITIONS and
                    len(str(description).strip()) > 50
            )

            has_good_summary = (
                    summary and
                    summary not in self.NULL_CONDITIONS and
                    len(str(summary).strip()) > 10
            )

            return has_description and not has_good_summary

        except Exception as e:
            self.log_error(f"Error checking summary update need: {e}")
            return False
