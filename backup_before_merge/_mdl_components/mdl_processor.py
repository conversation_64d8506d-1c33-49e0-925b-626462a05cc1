# /src/services/transformer/mdl_processor.py
"""

Refactored MDL processor following orchestrator pattern.

This module has been refactored in Phase 3.3 to delegate to specialized components
while maintaining backward compatibility with the original interface.
"""
import logging
import os
from typing import List, Dict, Any, Optional

import pandas as pd

# Direct async imports - no compatibility layer needed
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from .afff_calculator import AfffCalculator
from .mdl_data_processor import MDLDataProcessor
from .mdl_description_manager import MDLDescriptionManager
# Import the new modular components
from .mdl_lookup_manager import MDLLookupManager
from .mdl_persistence_manager import MDLPersistenceManager


class MDLProcessor(AsyncServiceBase):
    """
    Orchestrates MDL-related operations using modular components.
    
    This class has been refactored in Phase 3.3 to delegate to specialized components
    while maintaining backward compatibility.
    """
    NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
    def __init__(self,
                 mdl_litigations: pd.DataFrame =None,
                 mdl_path: str =None,
                 file_handler=None,
                 gpt=None,
                 config: Optional[Dict[str, Any]] =None,
                 litigation_classifier=None,
                 pdf_cache=None,
                 download_dir: Optional[str] =None,
                 district_court_db=None,
                 logger: Optional[logging.Logger] =None):
        """
        Initialize MDLProcessor with modular components.
        
        Args:
            mdl_litigations: DataFrame containing MDL litigation data
            mdl_path: Path to MDL data file
            file_handler: File handling interface
            gpt: LLM client for text processing
            config: Optional configuration dictionary
            litigation_classifier: Optional litigation classifier
            pdf_cache: Optional PDF cache
            download_dir: Optional download directory
            district_court_db: Optional district court database interface
            logger: Optional logger instance
        """
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config or {})

        # Store original dependencies for backward compatibility
        self.mdl_litigations = mdl_litigations
        self.mdl_path = mdl_path
        self.file_handler = file_handler
        self.gpt = gpt
        self.litigation_classifier = litigation_classifier
        self.pdf_cache = pdf_cache
        self.download_dir = download_dir
        self.district_court_db = district_court_db

        # Initialize modular components
        self._initialize_components()

        # Maintain backward compatibility cache attributes
        self._mdl_lookup_cache = None
        self._mdl_lookup_loaded = False

    async def _execute_action(self, data: Any) -> Any:
        """Execute MDLProcessor actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            if action == 'load_mdl_litigation_async':
                return await self._load_mdl_litigation_async()
            elif action == 'create_mdl_lookup_async':
                return await self._create_mdl_lookup_async()
            elif action == 'calculate_afff_num_plaintiffs_async':
                docket_data = action_data.get('docket_data', {})
                return await self.calculate_afff_num_plaintiffs_async(docket_data)
            elif action == 'manual_match_title_to_mdl_litigation_async':
                await self.manual_match_title_to_mdl_litigation_async()
                return {'status': 'completed'}
            elif action == 'update_mdl_description_async':
                await self.update_mdl_description_async()
                return {'status': 'completed'}
            elif action == 'summarize_description_async':
                await self.summarize_description_async()
                return {'status': 'completed'}
            elif action == 'save_mdl_litigation_async':
                return await self.save_mdl_litigation_async()
            elif action == 'update_and_save_mdl_litigations_async':
                mdl_number = action_data.get('mdl_number', '')
                litigation_name = action_data.get('litigation_name', '')
                additional_data = action_data.get('additional_data')
                return await self.update_and_save_mdl_litigations_async(mdl_number, litigation_name, additional_data)
            elif action == 'add_mdl_info_async':
                docket_data = action_data.get('docket_data', {})
                return await self.add_mdl_info_async(docket_data)
            elif action == 'validate_processing_results_async':
                processing_data = action_data.get('processing_data', {})
                return await self.validate_processing_results_async(processing_data)
            elif action == 'validate_processing_results':
                processing_data = action_data.get('processing_data', {})
                return self.validate_processing_results(processing_data)
            elif action == 'get_processing_summary':
                processing_data = action_data.get('processing_data', {})
                return self.get_processing_summary(processing_data)
            elif action == 'get_component_summary':
                return self.get_component_summary()
            elif action == 'update_district_court_db':
                district_court_db = action_data.get('district_court_db')
                self.update_district_court_db(district_court_db)
                return {'status': 'updated'}
            elif action == 'clear_lookup_cache':
                self._clear_lookup_cache()
                return {'status': 'cleared'}
            elif action == 'get_mdl_litigation_status':
                return self._get_mdl_litigation_status()
            elif action == 'get_component_status':
                return self._get_component_status()
            elif action == 'initialize_components':
                self._initialize_components()
                return {'status': 'initialized'}
            elif action == 'get_json_file_paths':
                return await self._get_json_file_paths()
        raise TransformerServiceError("Invalid action data provided to MDLProcessor")

    def _initialize_components(self):
        """Initialize the modular components for MDL processing."""
        try:
            # Initialize MDL lookup manager
            self.lookup_manager = MDLLookupManager(
                district_court_db=self.district_court_db,
                config=self.config,
                logger=self.logger
            )

            # Initialize AFFF calculator
            self.afff_calculator = AfffCalculator(
                config=self.config,
                logger=self.logger
            )

            # Initialize description manager
            self.description_manager = MDLDescriptionManager(
                file_handler=self.file_handler,
                gpt_client=self.gpt,
                config=self.config,
                download_dir=self.download_dir,
                logger=self.logger
            )

            # Initialize persistence manager
            self.persistence_manager = MDLPersistenceManager(
                mdl_path=self.mdl_path,
                config=self.config,
                logger=self.logger
            )

            # Initialize data processor
            self.data_processor = MDLDataProcessor(
                config=self.config,
                logger=self.logger
            )

            self.log_debug("Initialized all MDL processor components")

        except Exception as e:
            self.log_error(f"Error initializing MDL processor components: {e}")
            raise

    def update_district_court_db(self, district_court_db):
        """Update district court database and re-initialize components that depend on it."""
        self.district_court_db = district_court_db
        # Re-initialize the lookup manager with the new database
        self.lookup_manager = MDLLookupManager(
            district_court_db=self.district_court_db,
            config=self.config,
            logger=self.logger
        )
        self.log_debug("Updated MDLProcessor with new district court database")

    # === Async-First Methods ===

    async def _load_mdl_litigation_async(self) -> pd.DataFrame:
        """
        Load MDL litigation data asynchronously using persistence manager.
        
        Returns:
            DataFrame containing MDL litigation data
        """
        try:
            # Clear lookup cache when data is reloaded
            self._clear_lookup_cache()

            # Delegate to persistence manager
            df = await self.persistence_manager.load_mdl_litigation_async()

            # Update internal reference
            self.mdl_litigations = df

            return df

        except Exception as e:
            self.log_error(f"Error loading MDL litigation data: {e}")
            return pd.DataFrame()

    async def _create_mdl_lookup_async(self) -> Dict[str, str]:
        """
        Create MDL lookup table asynchronously using lookup manager.
        
        Returns:
            Dict mapping MDL numbers to court IDs
        """
        try:
            lookup_dict = await self.lookup_manager.get_mdl_lookup_async()

            # Update backward compatibility cache
            self._mdl_lookup_cache = lookup_dict
            self._mdl_lookup_loaded = True

            return lookup_dict

        except Exception as e:
            self.log_error(f"Error creating async MDL lookup: {e}")
            return {}

    async def calculate_afff_num_plaintiffs_async(self, data: Dict) -> str:
        """
        Calculate number of plaintiffs for AFFF cases (MDL 2873) asynchronously.
        
        Args:
            data: Docket data dictionary (modified in place)
            
        Returns:
            Number of plaintiffs as string, or "NA" if not applicable
        """
        return await self.afff_calculator.calculate_afff_num_plaintiffs(data)

    async def manual_match_title_to_mdl_litigation_async(self):
        """
        Manually match titles to MDL numbers asynchronously, filtering out certain types of files.
        """
        # Get list of JSON files, filtering out MDL patterns
        json_paths = await self._get_json_file_paths()

        if not json_paths:
            self.log_error("No JSON files found for title matching.")
            return

        # Filter out MDL pattern files
        filtered_paths = await self.data_processor.filter_mdl_docket_patterns(json_paths)

        self.log_info(f"Processing {len(filtered_paths)} non-MDL-pattern files for title matching.")

        # Delegate to description manager
        updated_df, update_count = await self.description_manager.match_titles_to_mdl_litigation(
            self.mdl_litigations, filtered_paths
        )

        # Update internal reference and save if changes were made
        if update_count > 0:
            self.mdl_litigations = updated_df
            await self.save_mdl_litigation_async()

    async def update_mdl_description_async(self):
        """
        Update MDL descriptions asynchronously by processing all relevant docket files.
        """
        self.log_info("Starting async MDL description update process...")

        # Ensure mdl_litigations is loaded
        if self.mdl_litigations is None or self.mdl_litigations.empty:
            self.log_info("MDL litigation data not loaded, loading now...")
            self.mdl_litigations = await self._load_mdl_litigation_async()
            if self.mdl_litigations.empty:
                self.log_error("Failed to load MDL litigation data. Aborting update.")
                return

        # Delegate to description manager
        updated_df, update_count = await self.description_manager.update_mdl_descriptions_from_dockets(
            self.mdl_litigations
        )

        # Update internal reference and save if changes were made
        if update_count > 0:
            self.mdl_litigations = updated_df
            await self.save_mdl_litigation_async()

    async def summarize_description_async(self):
        """
        Summarize MDL descriptions asynchronously using the configured LLM client.
        """
        self.log_info("Starting async summarization of existing MDL descriptions...")

        # Ensure mdl_litigations is loaded
        if self.mdl_litigations is None or self.mdl_litigations.empty:
            self.log_info("MDL litigation data not loaded, loading now...")
            self.mdl_litigations = await self._load_mdl_litigation_async()
            if self.mdl_litigations.empty:
                self.log_error("Failed to load MDL litigation data. Aborting summarization.")
                return

        # Delegate to description manager
        updated_df, update_count = await self.description_manager.summarize_existing_descriptions(
            self.mdl_litigations
        )

        # Update internal reference and save if changes were made
        if update_count > 0:
            self.mdl_litigations = updated_df
            await self.save_mdl_litigation_async()

    async def save_mdl_litigation_async(self) -> bool:
        """
        Save MDL litigation DataFrame asynchronously using persistence manager.
        
        Returns:
            True if save was successful, False otherwise
        """
        try:
            success = await self.persistence_manager.save_mdl_litigation_async(self.mdl_litigations)

            if success:
                # Clear lookup cache as data has changed
                self._clear_lookup_cache()

            return success

        except Exception as e:
            self.log_error(f"Error saving MDL litigation data: {e}")
            return False

    async def update_and_save_mdl_litigations_async(self, mdl_number: str, litigation_name: str,
                                                    additional_data: Optional[Dict] = None) -> bool:
        """
        Add or update an entry in the MDL litigation data and save it asynchronously.
        
        Args:
            mdl_number: The MDL number (as string)
            litigation_name: The name of the litigation
            additional_data: Optional dictionary of other fields to add/update
            
        Returns:
            True if update and save were successful, False otherwise
        """
        try:
            updated_df, success = await self.persistence_manager.update_and_save_mdl_entry(
                mdl_number, litigation_name, additional_data, self.mdl_litigations
            )

            if success:
                self.mdl_litigations = updated_df
                self._clear_lookup_cache()

            return success

        except Exception as e:
            self.log_error(f"Error updating and saving MDL entry {mdl_number}: {e}")
            return False

    async def add_mdl_info_async(self, data: Dict) -> bool:
        """
        Add MDL details asynchronously using data processor.
        
        Args:
            data: The docket data dictionary to update
            
        Returns:
            True if MDL info was added, False otherwise
        """
        return await self.data_processor.add_mdl_info_to_docket(data, self.mdl_litigations)

    async def _get_json_file_paths(self) -> List[str]:
        """Get list of JSON file paths from dockets directory."""
        # Determine the dockets directory
        docket_dir = None
        if self.download_dir:
            docket_dir = os.path.join(self.download_dir, 'dockets')
        elif hasattr(self.file_handler, 'download_dir') and self.file_handler.download_dir:
            docket_dir = os.path.join(self.file_handler.download_dir, 'dockets')

        if not docket_dir or not os.path.isdir(docket_dir):
            self.log_error(f"Dockets directory does not exist: {docket_dir}")
            return []

        self.log_info(f"Scanning for JSON files in: {docket_dir}")
        try:
            all_files = os.listdir(docket_dir)
            json_paths = [os.path.join(docket_dir, f) for f in all_files if f.endswith('.json')]
            self.log_info(f"Found {len(json_paths)} JSON files.")
            return json_paths
        except OSError as e:
            self.log_error(f"Error listing files in {docket_dir}: {e}")
            return []

    # === Orchestrator Methods ===

    def validate_processing_results(self, data: Dict) -> Dict[str, Any]:
        """
        Validate MDL processing results and return comprehensive report.
        
        This is a sync method that provides basic validation.
        For comprehensive async validation, use validate_processing_results_async().
        
        Args:
            data: Processed data dictionary to validate
            
        Returns:
            Validation report with status and component results
        """
        report = {
            'overall_status': 'success',
            'component_results': {},
            'summary': ''
        }

        try:
            # Validate with data processor (sync method)
            data_validation = self.data_processor.validate_mdl_processing_data(data)
            report['component_results']['data_processor'] = data_validation

            # Basic AFFF validation (sync fallback)
            mdl_num = str(data.get('mdl_num', ''))
            if mdl_num == '2873':  # AFFF case
                afff_validation = {
                    'status': 'success',
                    'is_afff_case': True,
                    'message': 'Basic AFFF validation passed (use async method for full validation)'
                }
            else:
                afff_validation = {
                    'status': 'success',
                    'is_afff_case': False,
                    'message': f'Not an AFFF case (MDL {mdl_num})'
                }
            report['component_results']['afff_calculator'] = afff_validation

            # Determine overall status
            component_statuses = [result.get('status', 'unknown') for result in report['component_results'].values()]

            if any(status == 'error' for status in component_statuses):
                report['overall_status'] = 'error'
            elif any(status == 'warning' for status in component_statuses):
                report['overall_status'] = 'warning'

            # Generate summary
            report['summary'] = self.get_processing_summary(data)

        except Exception as e:
            self.log_error(f"Error validating processing results: {e}")
            report['overall_status'] = 'error'
            report['component_results']['validation_error'] = {'status': 'error', 'message': str(e)}

        return report

    async def validate_processing_results_async(self, data: Dict) -> Dict[str, Any]:
        """
        Validate MDL processing results asynchronously with comprehensive validation.
        
        Args:
            data: Processed data dictionary to validate
            
        Returns:
            Validation report with status and component results
        """
        report = {
            'overall_status': 'success',
            'component_results': {},
            'summary': ''
        }

        try:
            # Validate with data processor
            data_validation = self.data_processor.validate_mdl_processing_data(data)
            report['component_results']['data_processor'] = data_validation

            # Validate AFFF data if applicable (async)
            afff_validation = await self.afff_calculator.validate_afff_data(data)
            report['component_results']['afff_calculator'] = afff_validation

            # Validate docket MDL data (async)
            docket_validation = await self.data_processor.validate_docket_mdl_data(data)
            report['component_results']['docket_validation'] = docket_validation

            # Determine overall status
            component_statuses = [result.get('status', 'unknown') for result in report['component_results'].values()]

            if any(status == 'error' for status in component_statuses):
                report['overall_status'] = 'error'
            elif any(status == 'warning' for status in component_statuses):
                report['overall_status'] = 'warning'

            # Generate summary
            report['summary'] = self.get_processing_summary(data)

        except Exception as e:
            self.log_error(f"Error validating processing results: {e}")
            report['overall_status'] = 'error'
            report['component_results']['validation_error'] = {'status': 'error', 'message': str(e)}

        return report

    def get_processing_summary(self, data: Dict) -> str:
        """
        Get comprehensive processing summary from all components.
        
        Args:
            data: Processed data dictionary
            
        Returns:
            Human-readable summary
        """
        summary_parts = []

        try:
            # Get summary from data processor
            data_summary = self.data_processor.get_mdl_processing_summary(data)
            if data_summary:
                summary_parts.append(data_summary)

            # Get AFFF summary if applicable
            afff_summary = self.afff_calculator.get_afff_summary(data)
            if afff_summary and 'Not an AFFF case' not in afff_summary:
                summary_parts.append(afff_summary)

            # Get description summary if available
            if hasattr(self, 'mdl_litigations') and self.mdl_litigations is not None:
                desc_summary = self.description_manager.get_description_summary(self.mdl_litigations)
                if desc_summary and 'No MDL data available' not in desc_summary:
                    summary_parts.append(desc_summary)

            return ' | '.join(summary_parts) if summary_parts else 'No processing summary available'

        except Exception as e:
            self.log_error(f"Error generating processing summary: {e}")
            return f"Summary generation error: {str(e)}"

    def get_component_summary(self) -> str:
        """
        Get summary of all initialized components.
        
        Returns:
            Human-readable summary of component status
        """
        components = {
            'lookup_manager': self.lookup_manager,
            'afff_calculator': self.afff_calculator,
            'description_manager': self.description_manager,
            'persistence_manager': self.persistence_manager,
            'data_processor': self.data_processor
        }

        active_components = [name for name, component in components.items() if component is not None]

        return f"MDLProcessor with {len(active_components)} components: {', '.join(active_components)}"

    # === Backward Compatibility Methods (Deprecated) ===

    def _load_mdl_litigation(self) -> pd.DataFrame:
        """
        Load MDL litigation data synchronously using persistence manager.
        
        This method is deprecated and will be removed in Phase 4.
        Use _load_mdl_litigation_async() instead.
        """
        self.log_warning("Using deprecated sync MDL loading method. Please migrate to async version.")

        try:
            # Clear lookup cache when data is reloaded
            self._clear_lookup_cache()

            # Delegate to persistence manager
            df = self.persistence_manager.load_mdl_litigation_sync()

            # Update internal reference
            self.mdl_litigations = df

            return df

        except Exception as e:
            self.log_error(f"Error loading MDL litigation data: {e}")
            return pd.DataFrame()

    def _create_mdl_lookup(self) -> Dict[str, str]:
        """
        Create MDL lookup table synchronously using lookup manager.
        
        This method is deprecated and will be removed in Phase 4.
        Use _create_mdl_lookup_async() instead.
        """
        self.log_warning("Using deprecated sync MDL lookup method. Please migrate to async version.")

        try:
            lookup_dict = self.lookup_manager.get_mdl_lookup_sync()

            # Update backward compatibility cache
            self._mdl_lookup_cache = lookup_dict
            self._mdl_lookup_loaded = True

            return lookup_dict

        except Exception as e:
            self.log_error(f"Error creating sync MDL lookup: {e}")
            return {}

    def _create_mdl_lookup_legacy_sync(self) -> Dict[str, str]:
        """
        Legacy sync method for backward compatibility with transfer_handler.
        
        This method is deprecated and will be removed in Phase 3.
        Use _create_mdl_lookup_async() instead.
        """
        self.log_warning("Using deprecated legacy sync MDL lookup method. Please migrate to async version.")
        return self._create_mdl_lookup()

    def calculate_afff_num_plaintiffs(self, data: Dict) -> str:
        """
        Calculate number of plaintiffs for AFFF cases (MDL 2873) synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use calculate_afff_num_plaintiffs_async() instead.
        """
        self.log_warning("Using deprecated sync AFFF calculation method. Please migrate to async version.")
        return self.afff_calculator.calculate_afff_num_plaintiffs_sync(data)

    def manual_match_title_to_mdl_litigation(self):
        """
        Manually match titles to MDL numbers synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use manual_match_title_to_mdl_litigation_async() instead.
        """
        self.log_warning("Using deprecated sync title matching method. Please migrate to async version.")

        import asyncio
        asyncio.run(self.manual_match_title_to_mdl_litigation_async())

    def update_mdl_description(self):
        """
        Update MDL descriptions synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use update_mdl_description_async() instead.
        """
        self.log_warning("Using deprecated sync description update method. Please migrate to async version.")

        import asyncio
        asyncio.run(self.update_mdl_description_async())

    def summarize_description(self):
        """
        Summarize MDL descriptions synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use summarize_description_async() instead.
        """
        self.log_warning("Using deprecated sync description summarization method. Please migrate to async version.")

        import asyncio
        asyncio.run(self.summarize_description_async())

    def save_mdl_litigation(self) -> None:
        """
        Save MDL litigation DataFrame synchronously using persistence manager.
        
        This method is deprecated and will be removed in Phase 4.
        Use save_mdl_litigation_async() instead.
        """
        self.log_warning("Using deprecated sync MDL saving method. Please migrate to async version.")

        try:
            self.persistence_manager.save_mdl_litigation_sync(self.mdl_litigations)

            # Clear lookup cache as data has changed
            self._clear_lookup_cache()

        except Exception as e:
            self.log_error(f"Error saving MDL litigation data: {e}")

    def update_and_save_mdl_litigations(self, mdl_number: str, litigation_name: str,
                                        additional_data: Optional[Dict] = None):
        """
        Update and save MDL litigation data synchronously.
        
        This method is deprecated and will be removed in Phase 4.
        Use update_and_save_mdl_litigations_async() instead.
        """
        self.log_warning("Using deprecated sync update and save method. Please migrate to async version.")

        import asyncio
        return asyncio.run(self.update_and_save_mdl_litigations_async(mdl_number, litigation_name, additional_data))

    def add_mdl_info(self, data: dict) -> None:
        """
        Add MDL details synchronously using data processor.
        
        This method is deprecated and will be removed in Phase 4.
        Use add_mdl_info_async() instead.
        """
        self.log_warning("Using deprecated sync MDL info addition method. Please migrate to async version.")

        try:
            import asyncio
            # Check if we're already in an event loop
            try:
                loop = asyncio.get_running_loop()
                # We're in an event loop, so we can't use asyncio.run()
                self.log_error(
                    "add_mdl_info called from async context - this will not work properly. Use add_mdl_info_async() instead.")
                # Ensure keys exist to prevent further errors
                data.setdefault('title', '')
                data.setdefault('allegations', '')
                data.setdefault('mdl_cat', 'NA')
                data.setdefault('summary', '')
                return
            except RuntimeError:
                # No event loop running, we can use asyncio.run()
                asyncio.run(self.data_processor.add_mdl_info_to_docket(data, self.mdl_litigations))
        except Exception as e:
            self.log_error(f"Error adding MDL info: {e}")
            # Ensure keys exist on error
            data.setdefault('title', '')
            data.setdefault('allegations', '')
            data.setdefault('mdl_cat', 'NA')
            data.setdefault('summary', '')

    # === Utility Methods ===

    def _clear_lookup_cache(self):
        """Clear MDL lookup cache."""
        self._mdl_lookup_cache = None
        self._mdl_lookup_loaded = False
        if hasattr(self, 'lookup_manager'):
            self.lookup_manager.clear_cache()

    # Legacy placeholder methods for match_title_to_mdl_num functionality
    def match_title_to_mdl_num(self, json_paths: List[str]) -> None:
        """Legacy method - use async version instead."""
        self.log_warning("Using deprecated match_title_to_mdl_num method. Please migrate to async version.")

        import asyncio
        updated_df, _ = asyncio.run(
            self.description_manager.match_titles_to_mdl_litigation(self.mdl_litigations, json_paths)
        )

        self.mdl_litigations = updated_df
        self.save_mdl_litigation()

    def _get_mdl_litigation_status(self) -> Dict[str, Any]:
        """Get status of MDL litigation data."""
        return {
            'is_loaded': self.mdl_litigations is not None and not self.mdl_litigations.empty,
            'record_count': len(self.mdl_litigations) if self.mdl_litigations is not None else 0,
            'columns': list(self.mdl_litigations.columns) if self.mdl_litigations is not None else [],
            'cache_status': {
                'lookup_loaded': self._mdl_lookup_loaded,
                'lookup_cached': self._mdl_lookup_cache is not None
            },
            'data_path': self.mdl_path
        }

    def _get_component_status(self) -> Dict[str, Any]:
        """Get status of all initialized components."""
        components = {
            'lookup_manager': {
                'available': hasattr(self, 'lookup_manager') and self.lookup_manager is not None,
                'type': type(self.lookup_manager).__name__ if hasattr(self,
                                                                      'lookup_manager') and self.lookup_manager else None
            },
            'afff_calculator': {
                'available': hasattr(self, 'afff_calculator') and self.afff_calculator is not None,
                'type': type(self.afff_calculator).__name__ if hasattr(self,
                                                                       'afff_calculator') and self.afff_calculator else None
            },
            'description_manager': {
                'available': hasattr(self, 'description_manager') and self.description_manager is not None,
                'type': type(self.description_manager).__name__ if hasattr(self,
                                                                           'description_manager') and self.description_manager else None
            },
            'persistence_manager': {
                'available': hasattr(self, 'persistence_manager') and self.persistence_manager is not None,
                'type': type(self.persistence_manager).__name__ if hasattr(self,
                                                                           'persistence_manager') and self.persistence_manager else None
            },
            'data_processor': {
                'available': hasattr(self, 'data_processor') and self.data_processor is not None,
                'type': type(self.data_processor).__name__ if hasattr(self,
                                                                      'data_processor') and self.data_processor else None
            }
        }

        active_count = sum(1 for comp in components.values() if comp['available'])

        return {
            'components': components,
            'active_components': active_count,
            'total_components': len(components),
            'all_initialized': active_count == len(components),
            'dependencies': {
                'mdl_litigations': self.mdl_litigations is not None,
                'mdl_path': self.mdl_path is not None,
                'file_handler': self.file_handler is not None,
                'gpt': self.gpt is not None,
                'district_court_db': self.district_court_db is not None
            }
        }
