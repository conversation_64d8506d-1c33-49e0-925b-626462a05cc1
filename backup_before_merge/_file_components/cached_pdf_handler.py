import logging
import re
from typing import Optional, List, Tuple, Dict, Any

import pandas as pd
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
from .litigation_classifier import LitigationClassifier


class CachedPdfHandler(AsyncServiceBase):
    """Cache for PDF data to avoid repeated processing (Simple version)."""

    def __init__(self,
                 config: Optional[Dict],
                 logger: Optional[logging.Logger],
                 litigation_classifier: Optional[LitigationClassifier]):
        super().__init__(logger, config or {})
        self.full_text: Optional[str] = None
        self.pdf_path: Optional[str] = None
        self.s3_link: Optional[str] = None
        self.md_path: Optional[str] = None
        self.litigation_classifier = litigation_classifier

    async def _execute_action(self, data: Any) -> Any:
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'is_valid':
                return self.is_valid()
            elif action == 'matches_source':
                return self.matches_source(
                    pdf_path=data.get('pdf_path'),
                    s3_link=data.get('s3_link')
                )
            elif action == 'update':
                self.update(
                    full_text=data['full_text'],
                    pdf_path=data.get('pdf_path'),
                    s3_link=data.get('s3_link'),
                    md_path=data.get('md_path')
                )
                return None
            elif action == 'get_signature_pages':
                return self.get_signature_pages()
            elif action == 'identify_litigation':
                return self.identify_litigation(data['mdl_litigations'])
            elif action == 'clear':
                self.clear()
                return None
            elif action == 'get_cache_stats':
                return self.get_cache_stats()
        raise TransformerServiceError("Invalid action data provided to CachedPdfHandler")

    def is_valid(self) -> bool:
        return bool(self.full_text)

    def matches_source(self, pdf_path: str = None, s3_link: str = None) -> bool:
        return (pdf_path and pdf_path == self.pdf_path) or \
            (s3_link and s3_link == self.s3_link)

    def update(self, full_text: str, pdf_path: str = None, s3_link: str = None, md_path: str = None) -> None:
        self.full_text = full_text
        self.pdf_path = pdf_path
        self.s3_link = s3_link
        self.md_path = md_path
        self.log_debug(f"PDF Cache updated. Source: {pdf_path or s3_link or md_path}")

    def get_signature_pages(self) -> List[str]:
        self.log_warning("get_signature_pages is a placeholder.")
        return [self.full_text] if self.full_text else []

    def identify_litigation(self, mdl_litigations: pd.DataFrame) -> Tuple[Optional[str], Optional[str]]:
        if not self.is_valid() or self.full_text is None: return None, None
        if mdl_litigations is None or mdl_litigations.empty:
            self.log_warning("MDL litigation data not provided for identification.")
            return None, None

        mdl_pattern = r'MDL\s*No\.?\s*(\d+)'
        try:
            for mdl_match in re.finditer(mdl_pattern, self.full_text):
                mdl_number_raw = mdl_match.group(1)
                mdl_number = mdl_number_raw.lstrip('0') if len(
                    mdl_number_raw) > 1 else mdl_number_raw
                if mdl_number:
                    match = mdl_litigations[mdl_litigations['mdl_num'] == str(mdl_number)]
                    if not match.empty:
                        litigation_name = match['litigation'].iloc[0]
                        self.log_debug(f"Identified MDL {mdl_number} ({litigation_name}) from text.")
                        return litigation_name, str(mdl_number)
        except Exception as e:
            self.log_error(f"Error searching for MDL number in text: {e}")

        if self.litigation_classifier is not None:
            try:
                result = self.litigation_classifier.identify_litigation_by_text(self.full_text)
                if result[0] or result[1]:
                    self.log_debug(f"Identified litigation via text classifier: {result}")
                    return result
            except Exception as e:
                self.log_warning(f"Error using litigation classifier: {e}")
        else:
            self.log_debug("LitigationClassifier not available - was not injected")

        return None, None

    def clear(self) -> None:
        self.full_text = None
        self.pdf_path = None
        self.s3_link = None
        self.md_path = None
        self.log_debug("PDF cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        return {
            'has_full_text': bool(self.full_text),
            'has_pdf_path': bool(self.pdf_path),
            'has_s3_link': bool(self.s3_link),
            'has_md_path': bool(self.md_path),
            'text_length': len(self.full_text) if self.full_text else 0,
            'is_valid': self.is_valid()
        }
