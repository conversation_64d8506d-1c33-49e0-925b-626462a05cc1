# Facebook Ads Library Network Analysis

## Key Network Activity Discovered

### GraphQL Requests Identified
Through browser performance monitoring, we captured **11 GraphQL requests** to the endpoint:
- **Endpoint**: `https://www.facebook.com/api/graphql/`
- **Timing**: Multiple requests during search execution (148-150 seconds)
- **Duration Range**: 45ms - 1165ms per request

### Network Request Timeline
1. **Initial Page Load**: Standard Facebook infrastructure requests
2. **Search Initiation**: Around timestamp 97993.5ms - First GraphQL request
3. **Search Results**: Burst of 10 GraphQL requests between 148554-149826ms
4. **Results Rendering**: Final data processing and display

### Critical Findings for Debugging

#### 1. **Multiple GraphQL Calls**
- Not a single API call, but a series of 11 GraphQL requests
- Suggests complex data aggregation and filtering process
- Longest request: 1164ms (likely main search query)
- Shorter requests: 45-64ms (likely metadata, filters, counts)

#### 2. **Session Management**
- **localStorage**: Contains signal flush timestamps, session IDs, heartbeat data
- **sessionStorage**: Stores recent search history with exact timestamps
- **Cookies**: Minimal (only viewport/display settings in Puppeteer)

#### 3. **Request Parameters** (from performance data)
Standard Facebook GraphQL parameters likely include:
- `__a=1` (AJAX flag)
- `__user=0` (Anonymous user)
- `__comet_req` (Comet request identifier)
- `__rev` (Facebook code revision)
- `__spin_r` (Deployment revision)
- `jazoest` (Anti-CSRF token)

#### 4. **Search Flow Analysis**
1. **User Input**: "Morgan & Morgan" entered
2. **Search Suggestions**: Initial GraphQL call for typeahead
3. **Exact Phrase Selection**: Triggers main search execution
4. **Results Loading**: Multiple GraphQL calls for:
   - Main ad results
   - Filtering options
   - Result counts
   - Ad metadata
   - Related suggestions

### Comparison with Automated Code Issues

#### Potential Null Payload Causes:
1. **Missing Session State**: Automated code may lack proper localStorage/sessionStorage
2. **Anti-Bot Detection**: Complex request timing and patterns required
3. **Multiple Request Dependency**: Single API call approach may be insufficient
4. **CSRF Token Management**: `jazoest` parameter appears in all requests
5. **Request Sequencing**: GraphQL calls may need specific ordering

#### Recommendations:
1. **Implement Session Persistence**: Maintain localStorage/sessionStorage state
2. **Multi-Request Strategy**: Execute sequence of GraphQL calls, not single request
3. **Request Timing**: Add realistic delays between requests (45-1165ms observed)
4. **Parameter Extraction**: Capture and reuse Facebook's dynamic parameters
5. **Header Matching**: Ensure all request headers match browser patterns

### Browser Fingerprinting Elements
- **User Agent**: `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36`
- **Viewport**: 800x600 (captured in cookies)
- **Language**: en-US
- **Platform**: MacIntel
- **DPR**: 1 (device pixel ratio)

### Next Steps for Implementation
1. **Capture GraphQL Bodies**: Use browser DevTools to extract actual request payloads
2. **Session Replication**: Implement proper session state management
3. **Request Sequence**: Recreate the 11-request sequence with proper timing
4. **Parameter Generation**: Implement Facebook's dynamic parameter generation
5. **Response Validation**: Ensure each GraphQL response contains expected data before proceeding

## Files Generated in this Analysis
- `comprehensive_session_data.json` - Complete session and performance data
- `process_summary.md` - Step-by-step navigation documentation
- `navigation_log.json` - Structured navigation process log
- 12 screenshots documenting the complete process