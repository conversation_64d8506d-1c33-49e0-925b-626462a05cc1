#!/usr/bin/env python3
"""
Script to find law firms with duplicate ID fields in the DynamoDB table
"""
import asyncio
import logging
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.law_firms_repository import LawFirmsRepository
from src.config_models.storage import StorageConfig


async def find_duplicate_ids() -> Dict[str, List[Tuple[str, str]]]:
    """
    Find all law firm records with duplicate IDs
    
    Returns:
        Dictionary mapping ID to list of (ID, Name) tuples for duplicates
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # Initialize configuration
    config = StorageConfig(aws_region="us-west-2")
    
    # Initialize storage and repository
    storage = AsyncDynamoDBStorage(config, logger)
    
    repository = LawFirmsRepository(storage=storage, logger=logger)
    
    async with storage:
        # Scan all records
        logger.info("Scanning all law firm records...")
        all_records = await repository.scan_all()
        logger.info(f"Found {len(all_records)} total records")
        
        # Group records by ID
        id_groups = defaultdict(list)
        for record in all_records:
            firm_id = record.get('id', '')
            firm_name = record.get('name', '')
            if firm_id:  # Only process records with valid IDs
                id_groups[firm_id].append((firm_id, firm_name))
        
        # Find duplicates (IDs that appear more than once)
        duplicates = {
            firm_id: entries 
            for firm_id, entries in id_groups.items() 
            if len(entries) > 1
        }
        
        return duplicates


async def main():
    """Main function to run the duplicate finder"""
    print("=" * 80)
    print("LAW FIRM DUPLICATE ID FINDER")
    print("=" * 80)
    print()
    
    duplicates = await find_duplicate_ids()
    
    if not duplicates:
        print("✅ No duplicate IDs found!")
    else:
        print(f"⚠️  Found {len(duplicates)} IDs with multiple entries:\n")
        
        # Sort by ID for consistent output
        for firm_id in sorted(duplicates.keys()):
            entries = duplicates[firm_id]
            print(f"ID: {firm_id}")
            print(f"   Appears {len(entries)} times with different names:")
            
            # Sort entries by name for consistent output
            for _, name in sorted(entries, key=lambda x: x[1]):
                print(f"   - Name: {name}")
            print()
        
        # Summary statistics
        total_duplicate_records = sum(len(entries) for entries in duplicates.values())
        print("=" * 80)
        print(f"SUMMARY:")
        print(f"- Total unique IDs with duplicates: {len(duplicates)}")
        print(f"- Total duplicate records: {total_duplicate_records}")
        print(f"- Records that should be deduplicated: {total_duplicate_records - len(duplicates)}")
        print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())