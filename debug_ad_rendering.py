#!/usr/bin/env python3
"""
Debug script to test ad page rendering with actual data.
"""

import asyncio
import json
import pandas as pd
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

# Test data based on the actual cached data
test_ad_data = {
    "ad_archive_id": "1437552473936525",
    "ad_creative_id": "1265114061890427",
    "page_id": "1589261434655726",
    "law_firm": "Advocate Alliance Group",
    "body": "⚠️ PFAS Chemicals May Have Caused Your Cancer. Drank contaminated tap water? PFAS exposure is linked to kidney, liver, testicular, and thyroid cancer. Check your eligibility in 1 minute.",
    "title": "✅ Check If You Qualify for Compensation",
    "caption": "advocatealliancegroup.com",
    "link_description": "PFAS, also known as 'forever chemicals,' have been linked to serious health issues. Learn how to file a claim",
    "cta_text": "Learn more",
    "link_url": "https://advocatealliancegroup.com/claims/pfas-groundwater",
    "publisher_platform": ["FACEBOOK", "INSTAGRAM", "AUDIENCE_NETWORK", "MESSENGER", "THREADS"],
    "is_active": True,
    "start_date": "07/09/25",
    "end_date": "07/17/25"
}

def simulate_check_and_format(value):
    """Simulate the _check_and_format method from ad_page_generator_service.py"""
    if pd.isna(value):
        return ''
    value_str = str(value).strip()
    return '' if not value_str or value_str.lower() in {'nan', 'na', 'none', '<na>'} else value_str

def simulate_filter_curly_braces(text):
    """Simulate the _filter_curly_braces method"""
    import re
    return re.sub(r'\\{\\{.*?\\}\\}', '', text) if isinstance(text, str) else text

def simulate_format_text_ad(value):
    """Simulate the _format_text_ad method"""
    import re
    if not isinstance(value, str):
        return ''
    value = value.strip()
    value = re.sub(r'\\r\\n', '\\n', value)
    value = re.sub(r'\\n{3,}', '\\n\\n', value)
    return value

def simulate_safe_process_platform(value):
    """Simulate the _safe_process_platform method"""
    if isinstance(value, list):
        cleaned_list = []
        for item in value:
            item_checked = simulate_check_and_format(item)
            if item_checked:
                filtered_item = simulate_filter_curly_braces(item_checked)
                if filtered_item:
                    cleaned_list.append(filtered_item)
        return cleaned_list
    elif isinstance(value, str):
        value_str_checked = simulate_check_and_format(value)
        if not value_str_checked:
            return []
        return [simulate_filter_curly_braces(value_str_checked)]
    return []

def create_ad_context_data(row_dict):
    """Simulate how ad_context_data is created in the service"""
    s3_prod_url = "https://cdn.lexgenius.ai"
    
    # Simulate image handling
    ad_archive_id = simulate_check_and_format(row_dict.get('ad_archive_id'))
    ad_creative_id = simulate_check_and_format(row_dict.get('ad_creative_id'))
    
    if ad_archive_id and ad_creative_id:
        ad_img_s3_key = f"adarchive/{ad_archive_id}/{ad_creative_id}.jpg"
        image_uri = f"{s3_prod_url}/{ad_img_s3_key}"
    else:
        fallback_s3_key = "assets/images/images/ad_unavailable.jpg"
        image_uri = f"{s3_prod_url}/{fallback_s3_key}"
    
    page_id = simulate_check_and_format(row_dict.get('page_id'))
    if page_id:
        logo_s3_key = f"assets/images/law-firm-logos/{page_id}.jpg"
    else:
        logo_s3_key = "assets/images/law-firm-logos/default.jpg"
    logo_uri = f"{s3_prod_url}/{logo_s3_key}"
    
    ad_context_data = {
        'page_name': simulate_check_and_format(row_dict.get('law_firm', 'Unknown Page')),
        'logo_uri': logo_uri,
        'image_uri': image_uri,
        'caption': simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('caption'))).upper(),
        'title': simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('title'))),
        'link_description': simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('link_description'))),
        'cta_text': simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('cta_text'))),
        'is_active': "Active" if row_dict.get("is_active", False) else "Inactive",
        'start_date': simulate_check_and_format(row_dict.get('start_date', '')),
        'end_date': simulate_check_and_format(row_dict.get('end_date', '')),
        'body': simulate_format_text_ad(simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('body')))),
        'link_url': simulate_filter_curly_braces(simulate_check_and_format(row_dict.get('link_url'))),
        'publisher_platform': simulate_safe_process_platform(row_dict.get('publisher_platform')),
        'landing_page': (simulate_check_and_format(row_dict.get('link_url')) or '').split('?')[0],
        'ad_archive_id': ad_archive_id
    }
    
    return ad_context_data

def test_template_rendering():
    """Test the template rendering with actual data"""
    
    print("🔍 Testing Ad Page Template Rendering")
    print("=" * 50)
    
    # Create context data
    ad_context = create_ad_context_data(test_ad_data)
    
    print("📋 Ad Context Data:")
    for key, value in ad_context.items():
        print(f"  {key}: {repr(value)}")
    print()
    
    # Test template conditions
    print("🧪 Testing Template Conditions:")
    print(f"  data.body: {repr(ad_context['body'])} -> bool({bool(ad_context['body'])})")
    print(f"  data.title: {repr(ad_context['title'])} -> bool({bool(ad_context['title'])})")
    print(f"  data.caption: {repr(ad_context['caption'])} -> bool({bool(ad_context['caption'])})")
    print(f"  data.link_description: {repr(ad_context['link_description'])} -> bool({bool(ad_context['link_description'])})")
    print(f"  data.cta_text: {repr(ad_context['cta_text'])} -> bool({bool(ad_context['cta_text'])})")
    print()
    
    # Test footer content detection
    has_footer_content = (ad_context['caption'] or 
                         ad_context['title'] or 
                         ad_context['link_description'] or
                         (ad_context['cta_text'] and ad_context['cta_text'] != '' and ad_context['cta_text'] != 'No button'))
    
    print(f"🎯 Footer Content Check: {has_footer_content}")
    print()
    
    # Load and render template
    try:
        template_dir = Path("src/assets/templates")
        env = Environment(loader=FileSystemLoader(template_dir))
        template = env.get_template('view_ad_v2.html')
        
        rendered_html = template.render(data=ad_context)
        
        # Save to file for inspection
        output_file = Path("debug_ad_output.html")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(rendered_html)
        
        print(f"✅ Template rendered successfully!")
        print(f"📁 Output saved to: {output_file.absolute()}")
        
        # Check for content in key sections
        if ad_context['body'] in rendered_html:
            print("✅ Body content found in rendered HTML")
        else:
            print("❌ Body content NOT found in rendered HTML")
            
        if ad_context['title'] in rendered_html:
            print("✅ Title content found in rendered HTML")
        else:
            print("❌ Title content NOT found in rendered HTML")
            
        if ad_context['caption'] in rendered_html:
            print("✅ Caption content found in rendered HTML")
        else:
            print("❌ Caption content NOT found in rendered HTML")
            
        if ad_context['link_description'] in rendered_html:
            print("✅ Link description found in rendered HTML")
        else:
            print("❌ Link description NOT found in rendered HTML")
        
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_template_rendering()