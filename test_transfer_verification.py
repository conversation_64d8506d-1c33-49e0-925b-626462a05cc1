#!/usr/bin/env python3
"""
Test to verify that the transfer handler logic fix is working correctly.
This simulates the exact flow that should happen with our fixes.
"""
import json
import re

def simulate_transfer_processing():
    """Simulate the complete transfer processing with our fixes."""
    
    # Load the actual JSON data
    json_path = "/Users/<USER>/PycharmProjects/lexgenius/data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    with open(json_path, 'r') as f:
        test_data = json.load(f)
    
    print("=" * 80)
    print("Transfer Processing Verification Test")
    print("=" * 80)
    
    print("\nInitial State from JSON:")
    print(f"  court_id: {test_data.get('court_id')}")
    print(f"  docket_num: {test_data.get('docket_num')}")
    print(f"  case_in_other_court: {test_data.get('case_in_other_court')}")
    print(f"  lead_case: {test_data.get('lead_case')}")
    print(f"  mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"  is_transferred: {test_data.get('is_transferred', 'NOT PRESENT')}")
    
    # Step 1: Extract MDL from lead_case (this happens in data_processing_engine)
    print("\n" + "-" * 80)
    print("Step 1: Extract MDL from lead_case")
    print("-" * 80)
    
    lead_case = test_data.get('lead_case', '')
    if lead_case:
        # Pattern for MDL court cases: X:XX-mn-XXXXX or X:XX-md-XXXXX
        lead_case_match = re.search(r'\d+:\d+-m[nd]-(\d+)', lead_case, re.IGNORECASE)
        if lead_case_match:
            mdl_num = lead_case_match.group(1).lstrip('0')
            test_data['mdl_num'] = mdl_num
            print(f"✅ Extracted mdl_num={mdl_num} from lead_case: {lead_case}")
        else:
            print(f"❌ Could not extract MDL from lead_case: {lead_case}")
    
    # Step 2: Process transfers (with our fix - parse case_in_other_court FIRST)
    print("\n" + "-" * 80)
    print("Step 2: Process transfers (with fixed logic)")
    print("-" * 80)
    
    # CRITICAL FIX: Parse case_in_other_court FIRST
    case_in_other_court = test_data.get('case_in_other_court')
    if case_in_other_court and case_in_other_court not in ['N/A', 'n/a', 'NA', 'None', '']:
        print(f"📋 Parsing case_in_other_court: '{case_in_other_court}'")
        
        parts = case_in_other_court.split(',', 1)
        if len(parts) == 2:
            transferor_name = parts[0].strip()
            transferor_docket = parts[1].strip()
            
            print(f"  Transferor name: '{transferor_name}'")
            print(f"  Transferor docket: '{transferor_docket}'")
            
            # Map court name to court ID
            court_name_map = {
                "Michigan Eastern": "mied",
                "Michigan Western": "miwd",
                "New York Southern": "nysd",
                "California Northern": "cand",
                # Add more mappings as needed
            }
            
            transferor_court_id = court_name_map.get(transferor_name, "unknown")
            
            test_data['transferor_court_id'] = transferor_court_id
            test_data['transferor_docket_num'] = transferor_docket
            
            print(f"✅ Set transferor_court_id: {transferor_court_id}")
            print(f"✅ Set transferor_docket_num: {transferor_docket}")
    
    # Now check transfer conditions (after parsing)
    print("\n" + "-" * 80)
    print("Step 3: Check transfer conditions")
    print("-" * 80)
    
    transferor_court_id = test_data.get('transferor_court_id')
    court_id = test_data.get('court_id')
    mdl_num = test_data.get('mdl_num')
    
    print(f"  Current court: {court_id}")
    print(f"  Transferor court: {transferor_court_id}")
    print(f"  MDL number: {mdl_num}")
    
    # Federal-to-federal transfer with MDL
    if transferor_court_id and transferor_court_id != court_id and mdl_num:
        test_data['is_transferred'] = True
        test_data['is_removal'] = False
        test_data['pending_cto'] = False
        print(f"✅ Federal-to-federal transfer detected (MDL case)")
        print(f"  is_transferred: True")
        print(f"  is_removal: False")
        print(f"  pending_cto: False")
    else:
        print("❌ Transfer conditions not met")
    
    # Step 4: Simulate DynamoDB query
    print("\n" + "-" * 80)
    print("Step 4: DynamoDB query simulation")
    print("-" * 80)
    
    if test_data.get('is_transferred') and test_data.get('transferor_court_id'):
        print(f"📊 Would query DynamoDB for transferor:")
        print(f"  Court ID: {test_data['transferor_court_id']}")
        print(f"  Docket Num: {test_data['transferor_docket_num']}")
        print(f"  Would inherit: mdl_num, s3_link, attorneys_gpt (if found)")
    else:
        print("⚠️  No DynamoDB query (not marked as transferred)")
    
    # Final results
    print("\n" + "=" * 80)
    print("FINAL RESULTS:")
    print("=" * 80)
    
    success_count = 0
    expected_fields = {
        'mdl_num': '2873',
        'transferor_court_id': 'mied',
        'transferor_docket_num': '1:25-cv-11890',
        'is_transferred': True,
        'is_removal': False,
        'pending_cto': False
    }
    
    for field, expected_value in expected_fields.items():
        actual_value = test_data.get(field, 'NOT SET')
        if actual_value == expected_value:
            print(f"✅ {field}: {actual_value}")
            success_count += 1
        else:
            print(f"❌ {field}: {actual_value} (expected: {expected_value})")
    
    print(f"\nSuccess rate: {success_count}/{len(expected_fields)} fields correctly set")
    
    if success_count == len(expected_fields):
        print("\n🎉 ALL TRANSFER FIELDS CORRECTLY SET!")
        print("The fix should work correctly in production!")
    else:
        print("\n⚠️  Some fields are not set correctly.")
        print("Review the logic and ensure all fixes are applied.")

if __name__ == "__main__":
    simulate_transfer_processing()