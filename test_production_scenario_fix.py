#!/usr/bin/env python3
"""
Test script to validate the production scenario with dependency injection and resets.

This test simulates the exact production scenario where:
1. Container is created with ThreadSafeSingleton
2. Config is overridden
3. Session managers are reset
4. Orchestrator is obtained and used

Usage:
    python test_production_scenario_fix.py
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any
from unittest.mock import MagicMock, patch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_production_scenario():
    """
    Test the exact production scenario with container lifecycle.
    """
    logger.info("🎯 Testing Production Scenario with Dependency Injection")
    
    try:
        # Simulate the production environment setup
        from dependency_injector import containers, providers
        from src.containers.storage import StorageContainer
        from src.containers.fb_ads import FbAdsContainer
        
        # Mock environment variables as in production
        test_env = {
            'OXYLABS_MOBILE_PASSWORD': 'test_mobile_password',
            'OXYLABS_PASSWORD': 'test_password',
            'AWS_ACCESS_KEY_ID': 'test_key_id',
            'AWS_SECRET_ACCESS_KEY': 'test_secret',
            'S3_BUCKET_NAME': 'test-bucket',
            'OPENAI_API_KEY': 'test_openai_key',
            'DEEPSEEK_API_KEY': 'test_deepseek_key',
        }
        
        for key, value in test_env.items():
            os.environ[key] = value
        
        # Configuration with expanded env vars
        test_config = {
            'headless': False,  # Production uses headless: false
            'humanize': True,
            'use_proxy': True,
            'mobile_proxy': True,
            'oxy_labs_mobile_password': '${OXYLABS_MOBILE_PASSWORD}',
            'oxy_labs_password': '${OXYLABS_PASSWORD}',
            'camoufox': {
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                }
            }
        }
        
        logger.info("📋 Phase 1: Creating containers with ThreadSafeSingleton")
        
        # Create mock storage container
        storage_container = MagicMock()
        storage_container.law_firms_repository = MagicMock()
        storage_container.fb_archive_repository = MagicMock()
        storage_container.fb_image_hash_repository = MagicMock()
        storage_container.s3_async_storage = MagicMock(return_value=MagicMock())
        storage_container.async_dynamodb_storage = MagicMock()
        
        # Create FB ads container
        fb_ads_container = FbAdsContainer()
        fb_ads_container.storage_container.override(storage_container)
        fb_ads_container.logger.override(logger)
        
        # Mock HTTP session
        mock_session = MagicMock()
        fb_ads_container.http_session.override(mock_session)
        
        logger.info("✅ Phase 1 PASSED: Containers created")
        
        logger.info("📋 Phase 2: Override config and reset session managers")
        
        # Override config (simulating production)
        fb_ads_container.config.from_dict(test_config)
        
        # Simulate resets as in production
        try:
            fb_ads_container.proxy_manager.reset()
            logger.info("✅ Proxy manager reset")
        except Exception as e:
            logger.debug(f"Proxy manager reset failed (expected): {e}")
        
        try:
            fb_ads_container.session_manager.reset()
            logger.info("✅ Session manager reset")
        except Exception as e:
            logger.debug(f"Session manager reset failed (expected): {e}")
        
        try:
            fb_ads_container.camoufox_session_manager.reset()
            logger.info("✅ Camoufox session manager reset")
        except Exception as e:
            logger.debug(f"Camoufox session manager reset failed (expected): {e}")
        
        logger.info("✅ Phase 2 PASSED: Config overridden and managers reset")
        
        logger.info("📋 Phase 3: Create session manager (triggers SharedBrowserManager)")
        
        # This is where the error typically occurs in production
        try:
            # Get the session manager - this triggers creation
            session_manager = fb_ads_container.session_manager()
            logger.info(f"✅ Session manager created: {type(session_manager).__name__}")
            
            # Check that it's the correct type
            from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
            assert isinstance(session_manager, CamoufoxSessionManager)
            logger.info("✅ Session manager is CamoufoxSessionManager")
            
            # Check that shared browser manager is accessible
            assert hasattr(session_manager, 'shared_browser_manager')
            logger.info("✅ Shared browser manager is accessible")
            
        except Exception as e:
            if "'dict' object has no attribute 'is_set'" in str(e):
                logger.error(f"❌ Phase 3 FAILED: AsyncCamoufox lock corruption: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return False
            else:
                logger.error(f"❌ Phase 3 FAILED: Other error: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return False
        
        logger.info("✅ Phase 3 PASSED: Session manager created without lock corruption")
        
        logger.info("📋 Phase 4: Test browser context creation")
        
        # Mock the actual Camoufox to avoid needing a real browser
        with patch('src.services.fb_ads.camoufox.shared_browser_manager.AsyncCamoufox') as MockCamoufox:
            # Setup mock browser
            mock_browser_instance = MagicMock()
            mock_browser = MagicMock()
            mock_context = MagicMock()
            mock_page = MagicMock()
            
            MockCamoufox.return_value = mock_browser_instance
            mock_browser_instance.start = MagicMock(return_value=asyncio.create_task(asyncio.sleep(0)))
            mock_browser_instance.browser = mock_browser
            mock_browser.new_context = MagicMock(return_value=asyncio.create_task(asyncio.coroutine(lambda: mock_context)()))
            mock_context.new_page = MagicMock(return_value=asyncio.create_task(asyncio.coroutine(lambda: mock_page)()))
            mock_context.pages = [mock_page]
            
            try:
                # Test getting browser context
                context, page = await session_manager.shared_browser_manager.get_browser_context(
                    job_id="test_job",
                    config=test_config
                )
                logger.info("✅ Browser context created successfully")
                
                # Cleanup
                await session_manager.shared_browser_manager.cleanup_job_context("test_job")
                logger.info("✅ Browser context cleaned up successfully")
                
            except Exception as e:
                if "'dict' object has no attribute 'is_set'" in str(e):
                    logger.error(f"❌ Phase 4 FAILED: AsyncCamoufox lock corruption during browser creation: {e}")
                    return False
                else:
                    logger.error(f"❌ Phase 4 FAILED: Other error during browser creation: {e}")
                    return False
        
        logger.info("✅ Phase 4 PASSED: Browser context lifecycle working")
        
        logger.info("🎉 ALL PHASES PASSED - Production scenario working!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Cannot import required modules: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def main():
    """Main test runner for production scenario validation."""
    logger.info("🎯 Starting Production Scenario Validation")
    logger.info("=" * 80)
    
    # Test production scenario
    test_passed = await test_production_scenario()
    
    # Final Results
    logger.info("\n" + "=" * 80)
    logger.info("FINAL PRODUCTION SCENARIO RESULTS")
    logger.info("=" * 80)
    
    if test_passed:
        logger.info("🎉 PRODUCTION SCENARIO TEST PASSED!")
        logger.info("✅ ThreadSafeSingleton with lazy initialization working")
        logger.info("✅ Config override and reset operations working")
        logger.info("✅ CamoufoxSessionManager class-level lock fixed")
        logger.info("✅ SharedBrowserManager asyncio locks working")
        logger.info("✅ Production FB ads processing should now work reliably")
        return 0
    else:
        logger.error("❌ PRODUCTION SCENARIO TEST FAILED")
        logger.error("Check the error logs for specific failure details")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)