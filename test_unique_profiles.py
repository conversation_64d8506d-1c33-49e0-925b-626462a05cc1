#!/usr/bin/env python3
"""
Quick test to verify unique browser profile functionality.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
import logging

# Set up basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_unique_profiles():
    """Test that unique profiles are generated correctly."""
    print("🧪 Testing unique browser profile generation...")
    
    # Mock config
    config = {
        'data_dir': './data',
        'camoufox': {
            'browser': {'headless': True},
            'session': {'min_duration_minutes': 3, 'max_duration_minutes': 5}
        },
        'cleanup': {'auto_cleanup_temp_files': True}
    }
    
    # Create session manager
    manager = CamoufoxSessionManager(config, logger)
    
    # Test profile generation for different firms
    test_firms = ['123456789', '987654321', 'firm_abc']
    profiles = []
    
    for firm_id in test_firms:
        manager._set_firm_context(firm_id)
        profiles.append(manager._profile_path)
        print(f"✅ Firm {firm_id} → Profile: {manager._profile_path}")
    
    # Verify all profiles are unique
    unique_profiles = set(profiles)
    if len(unique_profiles) == len(profiles):
        print("✅ All profiles are unique!")
    else:
        print("❌ Duplicate profiles found!")
        return False
    
    # Verify profile directories exist
    for profile_path in profiles:
        if os.path.exists(profile_path):
            print(f"✅ Profile directory exists: {profile_path}")
        else:
            print(f"❌ Profile directory missing: {profile_path}")
            return False
    
    print("🎉 Unique browser profile test PASSED!")
    return True

if __name__ == "__main__":
    success = test_unique_profiles()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Unique browser profiles working!")
    sys.exit(0 if success else 1)