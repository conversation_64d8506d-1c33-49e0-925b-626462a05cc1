#!/usr/bin/env python3
"""
Simple test to verify scrolling functionality works.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 8000,
                    'capture_wait': 10
                }
            }
        }

async def test_scrolling():
    """Test just the scrolling functionality."""
    logger.info("🧪 Testing scrolling functionality only")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Complete flow up to scrolling
        success = await session_manager.create_new_session()
        if not success:
            return False
        
        setup_success = await session_manager._setup_ad_library_search()
        if not setup_success:
            return False
        
        search_success = await session_manager._search_advertiser("Morgan & Morgan")
        if not search_success:
            return False
        
        logger.info("✅ Successfully reached dropdown search")
        
        # Now test the selection which includes scrolling
        selection_success = await session_manager._select_advertiser_from_suggestions("Morgan & Morgan")
        
        if selection_success:
            logger.info("✅ SUCCESS: Scrolling and selection worked!")
            return True
        else:
            logger.error("❌ FAILED: Scrolling/selection failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Scrolling Test")
    logger.info("This will test if the new scrolling mechanism works")
    logger.info("=" * 80)
    
    success = await test_scrolling()
    
    if success:
        logger.info("✅ Scrolling test PASSED")
    else:
        logger.error("❌ Scrolling test FAILED")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)