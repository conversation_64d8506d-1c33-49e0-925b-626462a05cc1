# Facebook Ads Navigation Timeout Fix Summary

## Issue
Browser navigation to Facebook Ads Library timing out after 30 seconds when using proxy:
```
ERROR    Failed to navigate to ads library home: Page.goto: Timeout 30000ms exceeded.
ERROR    FATAL: Could not establish initial session. Aborting run
```

## Root Causes
1. Proxy may be blocked or rate-limited by Facebook
2. Network latency with proxy connection
3. No retry logic for navigation failures
4. No proxy rotation on navigation failures

## Fixes Applied

### 1. Added Navigation Retry Logic
Modified `_navigate_to_ads_library_home()` in `camoufox_session_manager.py` to include:
- 3 retry attempts with exponential backoff
- Increasing timeout (30s → 60s) on retries
- Proxy rotation on timeout failures

### 2. Key Changes
```python
# BEFORE:
await self.page.goto(
    'https://www.facebook.com/ads/library/',
    wait_until='domcontentloaded',
    timeout=self.browser_timeout
)

# AFTER:
max_nav_retries = 3
for attempt in range(max_nav_retries):
    try:
        nav_timeout = 30000 if attempt == 0 else 60000
        await self.page.goto(url, wait_until='domcontentloaded', timeout=nav_timeout)
        return True
    except Exception as e:
        if "Timeout" in str(e) and self.proxy_manager:
            # Rotate proxy and recreate browser
            self.proxy_manager.mark_proxy_failed(self.current_proxy)
            await self.cleanup()
            await self._create_browser_and_page()
```

## Additional Improvements

### 1. Proxy Rotation on Failure
- Automatically marks current proxy as failed on timeout
- Rotates to next available proxy
- Recreates browser with new proxy

### 2. Better Error Logging
- Logs attempt number for each navigation try
- Indicates when proxy rotation is triggered
- Shows retry delays

### 3. Debug Script Created
Created `debug_fb_proxy_navigation.py` to test:
- Navigation with proxy
- Navigation without proxy (fallback test)
- Non-headless mode for visual debugging

## Testing

Run the debug script to verify the fix:
```bash
python debug_fb_proxy_navigation.py
```

This will:
1. Attempt navigation with retry logic
2. Show detailed logs of each attempt
3. Test with/without proxy to isolate issues
4. Keep browser open (non-headless) for inspection

## Expected Behavior

After the fix:
1. First navigation attempt at 30s timeout
2. If timeout → rotate proxy and retry with 60s timeout
3. Up to 3 total attempts before giving up
4. Clear logging of each retry attempt

## Monitoring

Watch for these log patterns:
- `🔄 Attempting to navigate to Facebook ads library (attempt X/3)`
- `🔄 Navigation timeout - possibly proxy-related. Rotating proxy...`
- `✅ Successfully navigated to Facebook ads library home`

## Next Steps

If navigation still fails after retries:
1. Check proxy health and credentials
2. Test different proxy providers or types
3. Consider implementing captcha detection
4. Add user-agent rotation
5. Implement session persistence to reuse working sessions