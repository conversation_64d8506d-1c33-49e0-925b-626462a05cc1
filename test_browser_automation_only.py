#!/usr/bin/env python3
"""
Test only the browser automation part without the full pipeline.
"""

import asyncio
import logging
from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_browser_automation():
    """Test browser automation directly."""
    config = {
        'camoufox': {
            'browser': {
                'headless': False,
                'timeout': 120000,
                'viewport': {'width': 1920, 'height': 1080}
            },
            'anti_bot': {
                'humanize': True,
                'mouse_curves': True,
                'typing_variation': True
            },
            'search': {
                'typing_delay': 120,
                'suggestion_wait': 5000,
                'capture_wait': 5
            }
        }
    }
    
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        logger.info("Creating new session...")
        success = await session_manager.create_new_session()
        
        if not success:
            logger.error("Failed to create session")
            return False
            
        logger.info("Session created successfully")
        
        # Now try to capture GraphQL for Wisner Baum
        logger.info("Attempting GraphQL capture for Wisner Baum...")
        result = await session_manager.capture_graphql_responses("295549338384")
        
        if result.get('success'):
            logger.info(f"✅ Success! Captured {result.get('total_responses')} responses")
            return True
        else:
            logger.error(f"❌ Failed: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    finally:
        await session_manager.cleanup()

if __name__ == "__main__":
    result = asyncio.run(test_browser_automation())
    print(f"\nTest {'PASSED' if result else 'FAILED'}")