#!/usr/bin/env python3
"""
Test script for comprehensive cache optimization fixes.

This script validates the FB ads cache improvements including:
1. Enhanced URL normalization
2. Disk persistence
3. Cache analytics
4. Performance monitoring

Run with: python test_cache_optimization_fixes.py
"""

import asyncio
import logging
import tempfile
import time
from pathlib import Path
from typing import List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our enhanced modules
from src.services.fb_ads.browser_image_cache_extractor import BrowserImageCacheExtractor, CachedImageResource
from src.services.fb_ads.url_normalization_utils import FacebookURL<PERSON>ormalizer, analyze_cache_miss_urls
from src.services.fb_ads.bandwidth_logger import BandwidthLogger


def test_url_normalization():
    """Test the enhanced URL normalization functionality."""
    print("\n🔗 TESTING URL NORMALIZATION")
    print("=" * 50)
    
    normalizer = FacebookURLNormalizer()
    
    # Test URLs with different parameter variations (same image)
    test_urls = [
        # Same image with different size parameters
        "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s60x60_tt6&_nc_cat=106&_nc_sid=18de74&_nc_ohc=abc123&oh=def456&oe=789xyz",
        "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s600x600_tt6&_nc_cat=106&_nc_sid=18de74&_nc_ohc=different&oh=values&oe=changed",
        "https://scontent.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s200x200_tt6&_nc_gid=new_gid&ccb=12-3",
        
        # Different image 
        "https://scontent.fbcdn.net/v/t39.35426-6/999999999_9999999999_9999999999999999999_n.jpg?stp=dst-jpg_s400x400_tt6&_nc_cat=107",
        
        # Profile image variations
        "https://scontent.fbcdn.net/v/t1.6435-9/123456789_987654321_1234567890123456789_n.jpg?_nc_cat=108&ccb=1-7&_nc_sid=5f2048&_nc_ohc=different",
        "https://scontent.fbcdn.net/v/t1.6435-9/123456789_987654321_1234567890123456789_n.jpg?_nc_cat=108&ccb=2-8&_nc_sid=changed&oh=new_value",
    ]
    
    print("Testing URL normalization...")
    normalized_groups = normalizer.analyze_url_variations(test_urls)
    
    for normalized, urls in normalized_groups.items():
        print(f"\n🎯 Normalized: {normalized}")
        print(f"   Original URLs ({len(urls)}):")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
    
    stats = normalizer.get_normalization_stats()
    print(f"\n📊 Normalization Stats:")
    print(f"   Successful: {stats['successful']}")
    print(f"   Failed: {stats['failed']}")
    print(f"   Pattern Matches: {stats['pattern_matches']}")
    
    # Validate that similar URLs normalize to the same key
    first_three = test_urls[:3]
    normalized_keys = [normalizer.normalize_facebook_url(url) for url in first_three]
    
    if len(set(normalized_keys)) == 1:
        print("✅ SUCCESS: First 3 URLs (same image) normalize to same key")
    else:
        print("❌ FAILURE: First 3 URLs should normalize to same key")
        print(f"   Keys: {normalized_keys}")
    
    return len(set(normalized_keys)) == 1


async def test_cache_persistence():
    """Test disk persistence functionality."""
    print("\n💾 TESTING DISK PERSISTENCE")
    print("=" * 50)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'camoufox': {
                'image_cache': {
                    'enabled': True,
                    'max_cache_size_mb': 10,
                    'cache_ttl_minutes': 60
                }
            }
        }
        
        # Create cache extractor with disk persistence
        cache_extractor = BrowserImageCacheExtractor(
            logger=logger,
            config=config,
            max_cache_size_mb=10,
            cache_ttl_minutes=60,
            enable_disk_persistence=True
        )
        
        # Change cache directory to our temp directory for testing
        cache_extractor._disk_cache_dir = Path(temp_dir) / '.image_cache'
        cache_extractor._disk_cache_dir.mkdir(exist_ok=True)
        (cache_extractor._disk_cache_dir / 'images').mkdir(exist_ok=True)
        (cache_extractor._disk_cache_dir / 'metadata').mkdir(exist_ok=True)
        
        # Create test image
        test_image = CachedImageResource(
            url="https://scontent.fbcdn.net/test_persistence.jpg",
            content=b"fake_test_image_content_for_persistence",
            content_type="image/jpeg",
            content_length=35,
            timestamp=time.time(),
            ad_archive_id="test_ad_123",
            ad_creative_id="test_creative_456",
            source="test"
        )
        
        # Store in cache
        await cache_extractor._store_cached_image(test_image)
        
        # Verify in-memory cache
        cached = await cache_extractor.get_cached_image(test_image.url)
        if cached:
            print("✅ SUCCESS: Image stored in memory cache")
        else:
            print("❌ FAILURE: Image not found in memory cache")
            return False
        
        # Check if saved to disk
        metadata_files = list((cache_extractor._disk_cache_dir / 'metadata').glob('*.json'))
        image_files = list((cache_extractor._disk_cache_dir / 'images').glob('*.jpg'))
        
        if metadata_files and image_files:
            print("✅ SUCCESS: Image saved to disk (metadata and content)")
        else:
            print("❌ FAILURE: Image not saved to disk properly")
            print(f"   Metadata files: {len(metadata_files)}")
            print(f"   Image files: {len(image_files)}")
            return False
        
        # Simulate session restart - clear memory cache
        await cache_extractor.clear_cache()
        
        # Verify memory cache is empty
        cached = await cache_extractor.get_cached_image(test_image.url)
        if not cached:
            print("✅ SUCCESS: Memory cache cleared")
        else:
            print("❌ FAILURE: Memory cache not cleared properly")
            return False
        
        # Load from disk
        await cache_extractor._load_cache_from_disk()
        
        # Check if loaded back into memory
        cached = await cache_extractor.get_cached_image(test_image.url)
        if cached and cached.content == test_image.content:
            print("✅ SUCCESS: Image loaded from disk persistence")
            return True
        else:
            print("❌ FAILURE: Image not loaded from disk properly")
            return False


async def test_cache_analytics():
    """Test the enhanced cache analytics functionality."""
    print("\n📊 TESTING CACHE ANALYTICS")
    print("=" * 50)
    
    config = {'camoufox': {'image_cache': {'enabled': True}}}
    
    cache_extractor = BrowserImageCacheExtractor(
        logger=logger,
        config=config,
        max_cache_size_mb=50,
        cache_ttl_minutes=1440,  # 24 hours
        enable_disk_persistence=False  # Disable for testing
    )
    
    # Simulate some cache operations
    test_images = [
        CachedImageResource(
            url="https://scontent.fbcdn.net/image1.jpg?param=value1",
            content=b"fake_image_content_1" * 1000,  # ~20KB
            content_type="image/jpeg",
            content_length=20000,
            timestamp=time.time() - 3600,  # 1 hour ago
            source="test"
        ),
        CachedImageResource(
            url="https://scontent.fbcdn.net/image2.jpg?param=value2",
            content=b"fake_image_content_2" * 2000,  # ~40KB  
            content_type="image/jpeg",
            content_length=40000,
            timestamp=time.time() - 1800,  # 30 minutes ago
            source="test"
        )
    ]
    
    # Store test images
    for image in test_images:
        await cache_extractor._store_cached_image(image)
    
    # Simulate cache hits and misses
    await cache_extractor.get_cached_image("https://scontent.fbcdn.net/image1.jpg?param=value1")  # Hit
    await cache_extractor.get_cached_image("https://scontent.fbcdn.net/image2.jpg?param=value2")  # Hit  
    await cache_extractor.get_cached_image("https://scontent.fbcdn.net/missing.jpg")  # Miss
    await cache_extractor.get_cached_image("https://scontent.fbcdn.net/another_missing.jpg")  # Miss
    
    # Get analytics
    stats = cache_extractor.get_cache_stats()
    dashboard = cache_extractor.get_cache_analytics_dashboard()
    
    print("📈 Cache Statistics:")
    print(f"   Cache Hits: {stats['cache_hits']}")
    print(f"   Cache Misses: {stats['cache_misses']}")
    print(f"   Hit Rate: {stats['hit_rate_percent']}%")
    print(f"   Cached Images: {stats['cached_images']}")
    print(f"   Cache Size: {stats['cache_size_mb']} MB")
    
    print("\n📋 Analytics Dashboard:")
    print(dashboard)
    
    # Validate key metrics
    success = True
    
    if stats['cache_hits'] != 2:
        print(f"❌ Expected 2 cache hits, got {stats['cache_hits']}")
        success = False
    
    if stats['cache_misses'] != 2:
        print(f"❌ Expected 2 cache misses, got {stats['cache_misses']}")
        success = False
    
    if stats['hit_rate_percent'] != 50.0:
        print(f"❌ Expected 50% hit rate, got {stats['hit_rate_percent']}%")
        success = False
    
    if stats['cached_images'] != 2:
        print(f"❌ Expected 2 cached images, got {stats['cached_images']}")
        success = False
    
    if success:
        print("✅ SUCCESS: Cache analytics working correctly")
    
    return success


def test_bandwidth_logger_enhancements():
    """Test enhanced bandwidth logger cache tracking."""
    print("\n📊 TESTING BANDWIDTH LOGGER ENHANCEMENTS")
    print("=" * 50)
    
    config = {'bandwidth_log_frequency': 300}
    bandwidth_logger = BandwidthLogger(config=config, logger=logger)
    
    # Test enhanced cache hit logging
    bandwidth_logger.log_cache_hit(
        url="https://scontent.fbcdn.net/cached_image.jpg",
        cached_size=150000,
        content_type="image/jpeg"
    )
    
    bandwidth_logger.log_cache_hit(
        url="https://scontent.fbcdn.net/another_cached.jpg", 
        cached_size=200000,
        content_type="image/jpeg"
    )
    
    # Test enhanced cache miss logging
    bandwidth_logger.log_cache_miss(
        url="https://scontent.fbcdn.net/missed_image.jpg",
        downloaded_bytes=180000,
        miss_reason="not_cached"
    )
    
    bandwidth_logger.log_cache_miss(
        url="https://scontent.fbcdn.net/expired_image.jpg",
        downloaded_bytes=160000, 
        miss_reason="expired"
    )
    
    # Get cache efficiency stats
    cache_stats = bandwidth_logger.get_cache_efficiency_stats()
    
    print("📈 Bandwidth Logger Cache Stats:")
    print(f"   Cache Enabled: {cache_stats.get('cache_enabled', False)}")
    print(f"   Cache Hits: {cache_stats.get('cache_hits', 0)}")
    print(f"   Cache Misses: {cache_stats.get('cache_misses', 0)}")
    print(f"   Hit Rate: {cache_stats.get('hit_rate_percent', 0)}%")
    print(f"   Total Saved: {cache_stats.get('total_saved_mb', 0)} MB")
    
    # Validate
    success = True
    
    if cache_stats.get('cache_hits', 0) != 2:
        print(f"❌ Expected 2 cache hits, got {cache_stats.get('cache_hits', 0)}")
        success = False
        
    if cache_stats.get('cache_misses', 0) != 2:
        print(f"❌ Expected 2 cache misses, got {cache_stats.get('cache_misses', 0)}")
        success = False
    
    if cache_stats.get('hit_rate_percent', 0) != 50.0:
        print(f"❌ Expected 50% hit rate, got {cache_stats.get('hit_rate_percent', 0)}%")
        success = False
    
    if success:
        print("✅ SUCCESS: Bandwidth logger enhancements working correctly")
    
    return success


async def main():
    """Run all cache optimization tests."""
    print("🚀 COMPREHENSIVE CACHE OPTIMIZATION TEST SUITE")
    print("=" * 60)
    print("Testing the fixes for ~99% cache miss rate issue")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: URL Normalization
    try:
        result = test_url_normalization()
        test_results.append(("URL Normalization", result))
    except Exception as e:
        print(f"❌ URL Normalization test failed: {e}")
        test_results.append(("URL Normalization", False))
    
    # Test 2: Disk Persistence
    try:
        result = await test_cache_persistence()
        test_results.append(("Disk Persistence", result))
    except Exception as e:
        print(f"❌ Disk Persistence test failed: {e}")
        test_results.append(("Disk Persistence", False))
    
    # Test 3: Cache Analytics
    try:
        result = await test_cache_analytics()
        test_results.append(("Cache Analytics", result))
    except Exception as e:
        print(f"❌ Cache Analytics test failed: {e}")
        test_results.append(("Cache Analytics", False))
    
    # Test 4: Bandwidth Logger Enhancements
    try:
        result = test_bandwidth_logger_enhancements()
        test_results.append(("Bandwidth Logger", result))
    except Exception as e:
        print(f"❌ Bandwidth Logger test failed: {e}")
        test_results.append(("Bandwidth Logger", False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Cache optimization fixes are working correctly.")
        print("\n🎯 EXPECTED IMPROVEMENTS:")
        print("   • Cache hit rate should improve from ~1% to 80-90%")
        print("   • URL normalization handles Facebook CDN parameter variations")
        print("   • Disk persistence survives browser session restarts") 
        print("   • Enhanced analytics provide detailed performance insights")
        print("   • TTL increased to 24 hours for better persistence")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)