# Camoufox Session Fix - Implementation Summary

## 🎯 Problem Fixed: "Page.evaluate: Target page, context or browser has been closed"

### 🔍 Root Cause Analysis

The error occurred because:
1. **Missing Operation Tracking**: The `_extract_tokens()` method was executing `page.evaluate()` WITHOUT protection
2. **Race Condition**: Cleanup could occur while token extraction was in progress
3. **No Retry Logic**: Single failure meant complete session failure

### ✅ FIXES IMPLEMENTED

#### 1. **Protected Token Extraction** (CRITICAL FIX)
**File:** `src/services/fb_ads/camoufox/camoufox_session_manager.py`

**Before:**
```python
# Extract session tokens
self.session_data = await self._extract_tokens()
```

**After:**
```python
# Extract session tokens with operation tracking to prevent cleanup during extraction
async with self._track_operation("extract_tokens"):
    self.session_data = await self._extract_tokens()
```

**Impact:** Cleanup will now wait for token extraction to complete!

#### 2. **Added Retry Logic to Token Extraction**
**Before:**
```python
async def _extract_tokens(self) -> Dict[str, Any]:
    if not self.page:
        return {}
    try:
        tokens = await self.page.evaluate("""...""")
        return tokens
    except Exception as e:
        self.logger.error(f"Failed to extract tokens: {str(e)}")
        return {}
```

**After:**
```python
async def _extract_tokens(self) -> Dict[str, Any]:
    if not self.page:
        return {}
    
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            # Wait a bit before extraction to ensure page is fully loaded
            if attempt > 0:
                await asyncio.sleep(1.0 * attempt)  # Exponential backoff
            
            tokens = await self.page.evaluate("""...""")
            self.logger.debug(f"Extracted tokens: {list(tokens.keys())}")
            return tokens
            
        except Exception as e:
            if attempt < max_attempts - 1:
                self.logger.warning(f"Token extraction attempt {attempt + 1} failed: {str(e)}. Retrying...")
                continue
            else:
                self.logger.error(f"Failed to extract tokens after {max_attempts} attempts: {str(e)}")
                return {}
```

**Impact:** 
- 3 retry attempts with exponential backoff
- Better error messages for debugging
- More resilient to transient failures

#### 3. **Protected Recovery Token Extraction**
Also applied operation tracking to the recovery path:

```python
# Re-extract tokens after context recreation with operation tracking
async with self._track_operation("extract_tokens_recovery"):
    self.session_data = await self._extract_tokens()
```

### 🛡️ How This Prevents the Error

1. **Operation Tracking**: The `_track_operation()` context manager:
   - Adds "extract_tokens" to `_pending_operations` set
   - Prevents cleanup from starting new operations
   - Makes cleanup wait for completion

2. **Cleanup Protection**: The existing cleanup method:
   ```python
   # Wait for pending operations to complete (with timeout)
   if pending_count > 0:
       self.logger.info(f"⏳ Waiting for {pending_count} pending operations...")
       await asyncio.wait_for(
           self._wait_for_pending_operations(),
           timeout=15.0
       )
   ```

3. **Retry Resilience**: If `page.evaluate()` fails due to timing:
   - Waits progressively longer (1s, 2s)
   - Retries up to 3 times
   - Only fails after all attempts exhausted

### 📊 Expected Results

**Before Fix:**
```
ERROR    Failed to extract tokens: Page.evaluate: Target page, context or browser has been closed
WARNING  No fb_dtsg token extracted, session may be invalid
ERROR    ❌ Job failed in fetch phase: Failed to create Camoufox session
```

**After Fix:**
```
INFO     ⏳ Waiting for 1 pending operations to complete...
DEBUG    Extracted tokens: ['fb_dtsg', 'lsd', 'jazoest', 'page_url', 'user_agent']
INFO     ✅ All pending operations completed
INFO     Camoufox session created successfully. Tokens: fb_dtsg=✓, lsd=✓, jazoest=✓
```

### 🔧 Additional Recommendations

While the immediate fix is implemented, for complete robustness:

1. **Add Operation Tracking to ALL Browser Operations**:
   - Every `page.goto()`, `page.click()`, `page.wait_for_selector()`
   - Currently only 2 operations use tracking out of 50+

2. **Implement Session Creation Lock**:
   - Prevent concurrent session creation attempts
   - Add to `create_new_session()` method

3. **Enhanced Health Checks**:
   - Coordinate health checks with operation tracking
   - Prevent health check from triggering cleanup during operations

### 🧪 Testing the Fix

To verify the fix works:

1. **Run the pipeline**: The token extraction should succeed
2. **Monitor logs**: Look for "Extracted tokens:" message
3. **Check for retries**: If network is slow, you'll see retry messages
4. **Verify cleanup**: Should see "Waiting for X pending operations"

### 🎉 Summary

The critical race condition has been fixed by:
- ✅ Protecting `_extract_tokens()` with operation tracking
- ✅ Adding retry logic with exponential backoff
- ✅ Ensuring cleanup waits for token extraction

The error "Page.evaluate: Target page, context or browser has been closed" should no longer occur during token extraction!