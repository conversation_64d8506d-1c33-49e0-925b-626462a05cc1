"""
Facebook Ads Bootstrap Module

This module handles the initialization of multiprocessing components and
other resources that need to be set up before the dependency injection
container is created.

It ensures that the shared cache manager is properly initialized and
available to all worker processes.
"""

import logging
from typing import Dict, Any, Optional

from src.infrastructure.multiprocessing import get_shared_cache_manager
from src.containers.fb_ads import FbAdsContainer


class FbAdsBootstrap:
    """
    Bootstrap class for Facebook Ads processing.
    
    Handles initialization of process-safe resources before container creation.
    """
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the bootstrap.
        
        Args:
            config: Application configuration
            logger: Optional logger instance
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self._shared_cache_manager = None
        self._shared_cache_dict = None
        
    def initialize_shared_resources(self) -> Dict[str, Any]:
        """
        Initialize shared resources that need to be available across processes.
        
        Returns:
            Dictionary of shared resources to inject into the container
        """
        self.logger.info("🚀 Initializing Facebook Ads shared resources...")
        
        # Initialize the shared cache manager
        self._shared_cache_manager = get_shared_cache_manager(self.logger)
        self._shared_cache_dict = self._shared_cache_manager.initialize()
        
        self.logger.info(f"✅ Shared cache initialized (type: {type(self._shared_cache_dict).__name__})")
        
        # Return resources to be injected into the container
        return {
            'shared_cache_dict': self._shared_cache_dict,
            'shared_cache_manager': self._shared_cache_manager
        }
        
    def create_container_with_shared_resources(self, **additional_config) -> FbAdsContainer:
        """
        Create the Facebook Ads container with shared resources pre-configured.
        
        Args:
            **additional_config: Additional configuration to pass to the container
            
        Returns:
            Configured FbAdsContainer instance
        """
        # Initialize shared resources
        shared_resources = self.initialize_shared_resources()
        
        # Create the container
        container = FbAdsContainer()
        
        # Configure the container with shared resources
        container.config.override({
            **self.config,
            **shared_resources,
            **additional_config
        })
        
        self.logger.info("✅ Facebook Ads container created with shared resources")
        
        return container
        
    def shutdown(self):
        """
        Shutdown and cleanup shared resources.
        """
        if self._shared_cache_manager:
            self._shared_cache_manager.shutdown()
            self.logger.info("✅ Shared resources shutdown complete")
            
    def __enter__(self):
        """Context manager entry."""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown()