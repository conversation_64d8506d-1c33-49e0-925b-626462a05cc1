"""
Shared Cache Manager for Multiprocessing

This module provides a process-safe cache manager using multiprocessing.Manager
to enable cache sharing across process boundaries in the Facebook Ads processing pipeline.

The manager ensures that all 83 cached images are accessible to all worker processes,
eliminating redundant downloads and improving performance.
"""

import multiprocessing
from typing import Dict, Any, Optional
import logging


class SharedCacheManager:
    """
    Manages a process-safe shared cache using multiprocessing.Manager.
    
    This class provides a singleton-like manager that creates and maintains
    a shared dictionary accessible across process boundaries. It's designed
    to be initialized once at application startup and passed through the
    dependency injection container.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the SharedCacheManager.
        
        Args:
            logger: Optional logger instance for debugging
        """
        self.logger = logger or logging.getLogger(__name__)
        self._manager: Optional[multiprocessing.Manager] = None
        self._shared_cache: Optional[Dict[str, Any]] = None
        self._initialized = False
        
    def initialize(self) -> Dict[str, Any]:
        """
        Initialize the multiprocessing Manager and create a shared dictionary.
        
        This method should be called once at application startup, before
        creating any worker processes.
        
        Returns:
            The managed dictionary that can be passed to worker processes
            
        Raises:
            RuntimeError: If already initialized
        """
        if self._initialized:
            raise RuntimeError("SharedCacheManager is already initialized")
            
        try:
            # Create the multiprocessing Manager
            self._manager = multiprocessing.Manager()
            
            # Create a managed dictionary for the cache
            self._shared_cache = self._manager.dict()
            
            self._initialized = True
            self.logger.info("✅ SharedCacheManager initialized successfully")
            
            return self._shared_cache
            
        except Exception as e:
            self.logger.error(f"Failed to initialize SharedCacheManager: {e}")
            raise
            
    def get_shared_cache(self) -> Dict[str, Any]:
        """
        Get the shared cache dictionary.
        
        Returns:
            The managed dictionary
            
        Raises:
            RuntimeError: If not initialized
        """
        if not self._initialized or self._shared_cache is None:
            raise RuntimeError("SharedCacheManager not initialized. Call initialize() first.")
            
        return self._shared_cache
        
    def shutdown(self):
        """
        Shutdown the multiprocessing Manager.
        
        This should be called when the application is shutting down to
        properly clean up resources.
        """
        if self._manager:
            try:
                self._manager.shutdown()
                self.logger.info("SharedCacheManager shutdown successfully")
            except Exception as e:
                self.logger.error(f"Error during SharedCacheManager shutdown: {e}")
            finally:
                self._manager = None
                self._shared_cache = None
                self._initialized = False
                
    def __enter__(self):
        """Context manager entry."""
        self.initialize()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown()
        

# Global instance for singleton-like behavior
_shared_cache_manager: Optional[SharedCacheManager] = None


def get_shared_cache_manager(logger: Optional[logging.Logger] = None) -> SharedCacheManager:
    """
    Get or create the global SharedCacheManager instance.
    
    Args:
        logger: Optional logger instance
        
    Returns:
        The global SharedCacheManager instance
    """
    global _shared_cache_manager
    
    if _shared_cache_manager is None:
        _shared_cache_manager = SharedCacheManager(logger)
        
    return _shared_cache_manager