"""
Factory for creating session managers based on feature flags.

Provides intelligent selection between legacy requests-based session
manager and Camoufox browser-based session manager.
"""

import logging
from typing import Dict, Any, Optional

from ..base.session_manager_base import SessionManagerBase
from ..feature_flag_controller import FeatureFlagController


class SessionManagerFactory:
    """Factory for creating appropriate session manager based on feature flags."""
    
    @staticmethod
    def create(config: Dict[str, Any], logger: logging.Logger, 
               firm_id: Optional[str] = None, **dependencies) -> SessionManagerBase:
        """
        Create session manager based on configuration and feature flags.
        
        Args:
            config: Configuration dictionary
            logger: Logger instance
            firm_id: Optional firm ID for feature flag routing
            **dependencies: Additional dependencies (fingerprint_manager, proxy_manager, law_firms_repository, etc.)
            
        Returns:
            SessionManagerBase: Appropriate session manager implementation
        """
        logger.info(f"🏭 SessionManagerFactory.create() called for firm: {firm_id}")
        logger.info(f"🔧 Available dependencies: {list(dependencies.keys())}")
        
        flag_controller = FeatureFlagController(config, logger)
        
        # Determine which implementation to use
        use_camoufox = flag_controller.should_use_camoufox(firm_id)
        implementation = flag_controller.get_implementation_name(firm_id)
        
        logger.info(f"🎯 Decision result: use_camoufox={use_camoufox}, implementation={implementation}")
        
        if use_camoufox:
            logger.info(f"🦊 Creating Camoufox session manager for firm {firm_id or 'default'}")
            try:
                session_manager = SessionManagerFactory._create_camoufox_session_manager(
                    config, logger, firm_id=firm_id, **dependencies
                )
                logger.info(f"✅ Successfully created Camoufox session manager: {type(session_manager).__name__}")
                return session_manager
            except Exception as e:
                logger.error(f"❌ CRITICAL: Failed to create Camoufox session manager: {e}")
                logger.error(f"❌ CRITICAL: System cannot continue without Camoufox session manager - exiting")
                raise RuntimeError(f"Failed to create Camoufox session manager: {e}")
        else:
            logger.warning(f"⚠️ Legacy session manager requested but no longer supported for firm {firm_id or 'default'}")
            logger.info(f"🦊 Forcing Camoufox session manager instead")
            try:
                session_manager = SessionManagerFactory._create_camoufox_session_manager(
                    config, logger, firm_id=firm_id, **dependencies
                )
                logger.info(f"✅ Successfully created Camoufox session manager as fallback: {type(session_manager).__name__}")
                return session_manager
            except Exception as e:
                logger.error(f"❌ CRITICAL: Failed to create Camoufox session manager: {e}")
                raise RuntimeError(f"Cannot create session manager - Camoufox is required: {e}")
    
    @staticmethod
    def _create_camoufox_session_manager(config: Dict[str, Any], logger: logging.Logger,
                                       firm_id: Optional[str] = None, **dependencies) -> SessionManagerBase:
        """
        Create Camoufox session manager.
        
        Args:
            config: Configuration dictionary
            logger: Logger instance
            firm_id: Optional firm ID to set context at creation time
            **dependencies: Dependencies including fingerprint_manager, proxy_manager, law_firms_repository
            
        Returns:
            CamoufoxSessionManager: Camoufox implementation
        """
        logger.info(f"🦊 Attempting to create CamoufoxSessionManager")
        logger.info(f"🔗 Dependencies provided: {list(dependencies.keys())}")
        
        # CRITICAL: Validate config contains camoufox settings
        camoufox_config = config.get('camoufox', {})
        force_clean_profile = camoufox_config.get('force_clean_profile', config.get('force_clean_profile'))
        logger.info(f"📋 Config validation - camoufox section present: {bool(camoufox_config)}")
        logger.info(f"🔧 Config validation - force_clean_profile: {force_clean_profile}")
        if camoufox_config:
            logger.info(f"🔍 Camoufox config contents: {list(camoufox_config.keys())}")
        else:
            logger.warning(f"⚠️ No camoufox config section found! Using fallback defaults.")
            # CRITICAL: Use sensible defaults if camoufox config is missing
            camoufox_config = {
                'force_clean_profile': True,  # Always clean profile to prevent ad blocker
                'browser': {
                    'headless': config.get('headless', False)
                },
                'anti_bot': {
                    'humanize': True,
                    'block_resources_for_performance': False  # CRITICAL: Don't block resources - triggers Facebook ad blocker detection
                }
            }
            config['camoufox'] = camoufox_config
            logger.info(f"✅ Applied fallback camoufox config with force_clean_profile=True")
        
        # CRITICAL FIX: Always check for top-level headless setting and use it as override
        if 'headless' in config:
            # Top-level headless setting takes precedence over nested camoufox.browser.headless
            original_headless = camoufox_config.get('browser', {}).get('headless')
            camoufox_config.setdefault('browser', {})['headless'] = config['headless']
            logger.info(f"🎯 HEADLESS OVERRIDE: Using top-level headless={config['headless']} (was: {original_headless})")
            logger.info(f"✅ Top-level 'headless' setting now overrides camoufox.browser.headless")
        
        # Ensure force_clean_profile is True if not explicitly set
        if not camoufox_config.get('force_clean_profile'):
            camoufox_config['force_clean_profile'] = True
            logger.info(f"🔧 Forced force_clean_profile=True to prevent ad blocker issues")
        
        try:
            logger.info(f"📦 Importing CamoufoxSessionManager...")
            from ..camoufox.camoufox_session_manager import CamoufoxSessionManager
            logger.info(f"✅ Successfully imported CamoufoxSessionManager")
            
            # Log dependency status
            fingerprint_manager = dependencies.get('fingerprint_manager')
            proxy_manager = dependencies.get('proxy_manager')
            law_firms_repository = dependencies.get('law_firms_repository')
            browser_image_cache_extractor = dependencies.get('browser_image_cache_extractor')
            logger.info(f"🔍 Fingerprint manager: {type(fingerprint_manager).__name__ if fingerprint_manager else 'None'}")
            logger.info(f"🌐 Proxy manager: {type(proxy_manager).__name__ if proxy_manager else 'None'}")
            logger.info(f"🏢 Law firms repository: {type(law_firms_repository).__name__ if law_firms_repository else 'None'}")
            logger.info(f"🖼️ Browser image cache extractor: {type(browser_image_cache_extractor).__name__ if browser_image_cache_extractor else 'None'}")
            
            # CRITICAL: Validate law_firms_repository is not None
            if not law_firms_repository:
                logger.error("❌ CRITICAL: law_firms_repository is None - dependency injection failed!")
                logger.error("❌ CRITICAL: System cannot continue without law_firms_repository - exiting")
                raise RuntimeError("law_firms_repository is None - dependency injection failed")
            
            logger.info(f"🏗️ Instantiating CamoufoxSessionManager with unified cache...")
            session_manager = CamoufoxSessionManager(
                config=config,
                logger=logger,
                fingerprint_manager=fingerprint_manager,
                proxy_manager=proxy_manager,
                law_firms_repository=law_firms_repository,
                browser_image_cache_extractor=browser_image_cache_extractor  # UNIFIED CACHE INJECTION
            )
            logger.info(f"✅ CamoufoxSessionManager created successfully")
            
            # CRITICAL FIX: Set firm context immediately after creation
            # This ensures profile path is set BEFORE any session operations
            if firm_id:
                logger.info(f"🎯 Setting firm context at creation time for firm: {firm_id}")
                session_manager._set_firm_context(firm_id)
                logger.info(f"✅ Firm context set at creation: {firm_id}")
            else:
                logger.warning(f"⚠️ No firm_id provided - profile path will be set later")
            
            return session_manager
            
        except ImportError as e:
            logger.error(f"❌ CRITICAL: Failed to import CamoufoxSessionManager: {e}")
            logger.error(f"❌ CRITICAL: System cannot continue without Camoufox session manager - exiting")
            raise ImportError(f"Failed to import CamoufoxSessionManager: {e}")
        except Exception as e:
            logger.error(f"❌ CRITICAL: Failed to create CamoufoxSessionManager: {e}")
            logger.error(f"❌ CRITICAL: System cannot continue without Camoufox session manager - exiting")
            raise RuntimeError(f"Failed to create CamoufoxSessionManager: {e}")
    
    @staticmethod
    def _create_legacy_session_manager(config: Dict[str, Any], logger: logging.Logger,
                                     **dependencies) -> SessionManagerBase:
        """
        Create legacy requests-based session manager.
        
        Args:
            config: Configuration dictionary
            logger: Logger instance
            **dependencies: Additional dependencies (currently unused)
            
        Returns:
            SessionManagerBase: Legacy implementation no longer supported
            
        Raises:
            RuntimeError: Always raises as legacy implementation has been removed
        """
        logger.error("Legacy session manager has been removed. Only Camoufox implementation is supported.")
        raise RuntimeError(
            "Legacy session manager (requests-based) has been removed. "
            "Please use Camoufox session manager instead."
        )
    
    @staticmethod
    def get_supported_implementations() -> Dict[str, bool]:
        """
        Get information about which implementations are available.
        
        Returns:
            Dict[str, bool]: Availability status for each implementation
        """
        implementations = {
            'legacy': False,
            'camoufox': False
        }
        
        # Legacy implementation has been removed
        implementations['legacy'] = False
        
        # Check Camoufox implementation
        try:
            from ..camoufox.camoufox_session_manager import CamoufoxSessionManager
            import camoufox
            implementations['camoufox'] = True
        except ImportError:
            pass
        
        return implementations
    
    @staticmethod
    def validate_dependencies(config: Dict[str, Any], implementation: str = 'auto') -> Dict[str, Any]:
        """
        Validate that all required dependencies are available for the given implementation.
        
        Args:
            config: Configuration dictionary
            implementation: 'auto', 'legacy', or 'camoufox'
            
        Returns:
            Dict[str, Any]: Validation results with status and messages
        """
        result = {
            'valid': False,
            'implementation': implementation,
            'messages': [],
            'missing_dependencies': []
        }
        
        supported = SessionManagerFactory.get_supported_implementations()
        
        if implementation == 'auto':
            flag_controller = FeatureFlagController(config)
            implementation = flag_controller.get_implementation_name()
        
        if implementation == 'camoufox':
            if not supported['camoufox']:
                result['messages'].append("Camoufox implementation not available")
                result['missing_dependencies'].extend(['camoufox', 'playwright'])
                
                # Check if we can fall back to legacy
                if supported['legacy']:
                    result['messages'].append("Falling back to legacy implementation")
                    implementation = 'legacy'
                else:
                    result['messages'].append("No fallback implementation available")
                    return result
            else:
                result['messages'].append("Camoufox implementation available")
        
        if implementation == 'legacy':
            if not supported['legacy']:
                result['messages'].append("Legacy implementation not available")
                result['missing_dependencies'].append('requests')
                return result
            else:
                result['messages'].append("Legacy implementation available")
        
        result['valid'] = True
        result['implementation'] = implementation
        
        return result