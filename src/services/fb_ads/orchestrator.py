#!/usr/bin/env python3
# /src/services/fb_ads/orchestrator.py
"""
Facebook Ads Orchestrator - Refactored

This is the refactored orchestrator that delegates responsibilities to focused services.
The orchestrator now serves as a lightweight coordinator rather than a monolithic processor.

Architecture:
- AdProcessingService: Core ad processing and database operations
- DataValidationService: Data validation and transformation logic
- ErrorHandlingService: Error management and reporting
- InteractiveService: User interaction and CLI operations
- WorkflowService: High-level workflow orchestration
"""

from __future__ import annotations

import logging
import os
import sys
from datetime import datetime
from typing import Any

import aiohttp
from dotenv import load_dotenv
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeRemainingColumn,
)

# Load environment variables
load_dotenv()

# Import feature flags and performance monitoring
try:
    from src.config_models.features import (
        FeatureFlags,
        get_feature_flags,
        should_use_orchestrator_async,
    )
    from src.infrastructure.monitoring.performance_monitor import (
        monitor_async_performance,
        monitor_performance,
    )

    _feature_flags_available = True
except ImportError:
    _feature_flags_available = False
    FeatureFlags = None

# Import async repositories for direct usage
try:
    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
    from src.repositories.fb_archive_repository import FBArchiveRepository
    from src.repositories.law_firms_repository import LawFirmsRepository

    _async_repos_available = True
except ImportError:
    _async_repos_available = False

# Using services architecture - no migration needed
_using_new_architecture = True

# Import infrastructure patterns
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError
from src.infrastructure.protocols.logger import LoggerProtocol

# Import infrastructure
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.services.ai.ai_orchestrator import AIOrchestrator
from src.utils.date import DateUtils
from src.utils.pdf_utils import PDFExtractor

from .ad_db_service import AdDBService

# Import services
from .ad_processing_service import AdProcessingService

# Import external dependencies
from .bandwidth_logger import BandwidthLogger
from .concurrent_workflow_service import ConcurrentWorkflowService
from .data_validation_service import DataValidationService
from .error_handling_service import ErrorHandlingService
from .failed_firms_manager import FailedFirmsManager
from .image_handler import ImageHandler
from .interactive_service import InteractiveService
from .jobs.job_orchestration_service import JobOrchestrationService
from .jobs.job_runner_service import JobRunnerService
from .local_image_queue import LocalImageQueue
# Removed FBAdsLogger - using injected logger per DI pattern
from .processing_tracker import ProcessingTracker
from .resource_monitor import ResourceMonitorService
from .workflow_service import WorkflowService

# Initialize console
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, console=console, show_path=False)],
)


def load_config(date_str: str = None) -> dict[str, Any]:
    """Load configuration - stub for compatibility.
    
    DEPRECATED: This function is kept for backward compatibility only.
    The actual config should come from the YAML files with proper environment variable expansion.
    """
    # Return minimal config - actual values should come from YAML/container injection
    return {
        "aws_region": "us-west-2",  # Default region
        "dynamodb": {
            "fb_ad_archive_table_name": "FBAdArchive",
            "law_firms_table_name": "LawFirms",
        },
        "fb_ad_archive_table_name": "FBAdArchive",
        "law_firms_table_name": "LawFirms",
        # DO NOT return proxy credentials here - they should come from YAML config
        # The YAML config loader will properly expand environment variables
    }


class FacebookAdsOrchestrator(AsyncServiceBase):
    """
    Refactored Facebook Ads Orchestrator.

    Now serves as a lightweight coordinator that delegates responsibilities
    to focused services rather than handling everything directly.
    """

    def __init__(
        self,
        logger: LoggerProtocol,
        config: dict[str, Any],
        session: aiohttp.ClientSession,
        s3_manager: S3AsyncStorage,
        async_storage: AsyncDynamoDBStorage,
        law_firms_repository: LawFirmsRepository,
        fb_archive_repository: FBArchiveRepository,
        processing_tracker: ProcessingTracker,
        session_manager,  # Could be FacebookSessionManager or CamoufoxSessionManager
        session_manager_factory,  # Factory for creating firm-specific session managers
        api_client,  # Could be FacebookAPIClient or CamoufoxAPIClient
        image_handler: ImageHandler,
        bandwidth_logger: BandwidthLogger,
        pdf_extractor: PDFExtractor | None,
        local_image_queue: LocalImageQueue,
        ad_processing_service: AdProcessingService,
        data_validation_service: DataValidationService,
        error_handling_service: ErrorHandlingService,
        interactive_service: InteractiveService,
        workflow_service: WorkflowService,
        job_orchestration_service: JobOrchestrationService,
        job_runner_service: JobRunnerService,
        failed_firms_manager: FailedFirmsManager,
        ad_db_service: AdDBService,
        ai_orchestrator: AIOrchestrator,
        concurrent_workflow_service: ConcurrentWorkflowService,
        skip_term_service=None,
        fingerprint_manager=None,  # Optional for Camoufox
        proxy_manager=None,  # Optional for Camoufox
        dropdown_search_helper=None,  # Optional for interactive dropdown workflows
        browser_image_cache_extractor=None,  # Optional shared browser image cache
        resource_monitor: ResourceMonitorService = None,  # Optional resource monitoring
    ):
        """Initialize the orchestrator with injected service dependencies."""
        # Convert config to dict for AsyncServiceBase and store original for business logic
        config_dict = config.model_dump() if hasattr(config, "model_dump") else config
        super().__init__(logger, config_dict)
        self.config = config

        # 🔧 FIX: Configure logging for FB ads modules to use the injected logger
        # Instead of creating a separate logger, configure all FB ads modules to use the injected logger
        try:
            self._configure_fb_ads_logging()
            self.log_info("✅ FB ads logging configured successfully")
            self.log_info(
                f"📁 Log output will use the injected logger from dependency injection"
            )
        except Exception as e:
            self.log_error(f"❌ Failed to configure FB ads logging: {e}")
            # Continue with default logging

        # CRITICAL: Validate LLM availability before proceeding
        self._validate_llm_availability(ai_orchestrator)

        # Store injected dependencies
        self.aiohttp_session = session
        self.console = console
        self.s3_manager = s3_manager
        self._async_storage = async_storage
        self._law_firms_repo = law_firms_repository
        self._fb_archive_repo = fb_archive_repository
        self.processing_tracker = processing_tracker
        self.session_manager = session_manager
        self.session_manager_factory = session_manager_factory
        self.api_client = api_client
        self.image_handler = image_handler
        self.bandwidth_logger = bandwidth_logger
        self.pdf_extractor_instance = pdf_extractor
        self.local_image_queue = local_image_queue
        self.ai_integrator = ai_orchestrator

        # Store service dependencies
        self.ad_processing_service = ad_processing_service
        self.data_validation_service = data_validation_service
        self.error_handling_service = error_handling_service
        self.interactive_service = interactive_service
        self.workflow_service = workflow_service
        self.job_orchestration_service = job_orchestration_service
        self.job_runner_service = job_runner_service
        self.failed_firms_manager = failed_firms_manager
        self.ad_db_service = ad_db_service
        self.concurrent_workflow_service = concurrent_workflow_service
        
        # Initialize resource monitoring
        self.resource_monitor = resource_monitor or ResourceMonitorService(logger, config_dict)
        self.log_info("🔍 Resource monitor initialized for crash prevention")
        self.skip_term_service = skip_term_service
        self.fingerprint_manager = fingerprint_manager
        self.proxy_manager = proxy_manager
        self.dropdown_search_helper = dropdown_search_helper
        self.browser_image_cache_extractor = browser_image_cache_extractor

        # Initialize feature flags
        self.feature_flags = get_feature_flags() if _feature_flags_available else None
        if self.feature_flags and self.feature_flags.debug_async_conversion:
            self.log_info("Feature flags loaded for async conversion")
            self.log_info(
                f"FB Ads async enabled: {self.feature_flags.enable_fb_ads_async}"
            )
            self.log_info(
                f"Use direct async repos: {self.feature_flags.use_direct_async_repos}"
            )

        # Setup dates and progress tracking (minimal initialization)
        self._setup_dates(self.config)
        self._setup_progress_tracking()

        # For test compatibility
        self._services_initialized = True
        self.law_firm_db = None  # Will use async repo
        self.fb_ad_db = None  # Will use async repo
        self.llava_extractor = None  # Set by container if available

        self.log_info(
            f"Facebook Ads Orchestrator initialized with iso_date: {config.get('iso_date', 'default')}"
        )
        
        # CRITICAL: Log camoufox config immediately
        camoufox_in_config = self.config.get('camoufox')
        if camoufox_in_config:
            self.log_info(f"🎯 CAMOUFOX CONFIG RECEIVED: {camoufox_in_config}")
        else:
            self.log_error("❌ CRITICAL: NO CAMOUFOX CONFIG IN ORCHESTRATOR!")
            
        self.log_info(
            "FacebookAdsOrchestrator initialized successfully with dependency injection."
        )

    def _validate_config(self):
        """Validate critical configuration sections for proper operation."""
        self.logger.info("🔍 Validating FacebookAdsOrchestrator configuration...")
        
        # Check for camoufox configuration
        camoufox_config = self.config.get('camoufox', {})
        if not camoufox_config:
            self.logger.warning("⚠️ CRITICAL: No 'camoufox' section found in config! This will cause ad blocker issues.")
            self.logger.warning("⚠️ Session managers will use fallback defaults, but config should include camoufox settings.")
        else:
            self.logger.info(f"✅ Camoufox config section found with keys: {list(camoufox_config.keys())}")
            force_clean = camoufox_config.get('force_clean_profile')
            self.logger.info(f"🔧 force_clean_profile setting: {force_clean}")
            if not force_clean:
                self.logger.warning("⚠️ force_clean_profile is not True! This may cause ad blocker detection.")
        
        # Check for feature flags
        feature_flags = self.config.get('feature_flags', {})
        use_camoufox = feature_flags.get('use_camoufox', False)
        self.logger.info(f"🚀 use_camoufox feature flag: {use_camoufox}")
        if not use_camoufox:
            self.logger.warning("⚠️ use_camoufox is False! Camoufox browser won't be used.")
        
        # Log critical config sections for debugging
        self.logger.info(f"📋 Config has following top-level keys: {list(self.config.keys())[:20]}...")
    
    def _validate_headless_config(self):
        """Validate and log headless configuration to ensure proper propagation."""
        self.logger.info("🔍 Validating headless configuration propagation...")
        
        # Check top-level headless setting
        top_level_headless = self.config.get('headless')
        self.logger.info(f"📋 Top-level 'headless' setting: {top_level_headless}")
        
        # Check nested camoufox.browser.headless setting
        camoufox_config = self.config.get('camoufox', {})
        browser_config = camoufox_config.get('browser', {})
        nested_headless = browser_config.get('headless')
        self.logger.info(f"📋 Nested 'camoufox.browser.headless' setting: {nested_headless}")
        
        # Log which one will be used
        if top_level_headless is not None:
            self.logger.info(f"✅ TOP-LEVEL HEADLESS WILL BE USED: headless={top_level_headless}")
            self.logger.info(f"🎯 This overrides any nested camoufox.browser.headless setting")
        else:
            self.logger.info(f"⚠️ No top-level headless setting found, will use nested setting: {nested_headless}")
        
        # Log sample_firms if present (for debugging test runs)
        if 'development' in self.config:
            sample_firms = self.config.get('development', {}).get('sample_firms')
            if sample_firms:
                self.logger.info(f"🧪 TEST MODE: Using sample_firms: {sample_firms}")
        
    async def _execute_action(self, data: Any) -> Any:
        """Execute FacebookAdsOrchestrator actions."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "run_full_scrape":
                return await self.run_full_scrape()
            elif action == "run_single_firm_scrape":
                return await self.run_single_firm_scrape(data["firm_id"])
            elif action == "run_ignore_list_processing":
                return await self.run_ignore_list_processing()
            elif action == "add_law_firm_interactive":
                await self.add_law_firm_interactive()
                return None
            elif action == "add_attorney_by_page_id":
                await self.add_attorney_by_page_id()
                return None
            elif action == "search_law_firm_in_db":
                self.search_law_firm_in_db(data["firm_id"])
                return None
            elif action == "lookup_ad_by_id":
                await self.lookup_ad_by_id(data["ad_id"])
                return None
            elif action == "show_failed_firms":
                self.show_failed_firms(data.get("date"), data.get("add_to_skip", False))
                return None
            elif action == "show_failure_summary":
                self.show_failure_summary(data.get("start_date"), data.get("end_date"))
                return None
            elif action == "get_bandwidth_usage":
                return self.get_bandwidth_usage()
            elif action == "cleanup":
                await self.cleanup()
                return None
            elif action == "run":
                return await self.run()
        raise FBAdServiceError(
            "Invalid action data provided to FacebookAdsOrchestrator"
        )

    def _setup_dates(self, config: dict[str, Any]):
        """Setup and validate date configuration."""
        # Handle both dict and config object access patterns
        if isinstance(config, dict):
            # Dictionary access
            self.end_date_iso = config.get("iso_date")
            default_date_range_days = config.get("default_date_range_days", 14)
        elif hasattr(config, "iso_date"):
            # Object attribute access
            self.end_date_iso = getattr(config, "iso_date", None)
            default_date_range_days = getattr(config, "default_date_range_days", 14)
        else:
            raise ValueError(
                f"Invalid config type: {type(config)}. Expected dict or config object with iso_date attribute."
            )

        if not self.end_date_iso:
            raise ValueError("Config missing 'iso_date'.")

        try:
            datetime.strptime(str(self.end_date_iso), "%Y%m%d")
        except ValueError:
            raise ValueError(f"Invalid 'iso_date': {self.end_date_iso}.")

        self.start_date_iso = DateUtils.get_date_before_n_days(
            int(default_date_range_days), self.end_date_iso
        )

    def _setup_progress_tracking(self):
        """Setup progress tracking components."""
        self.use_rich_progress = sys.stdout.isatty()
        if not self.use_rich_progress:
            self.log_info("Not a TTY, disabling Rich progress bar.")

        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            MofNCompleteColumn(),
            TimeRemainingColumn(),
            console=self.console,
            transient=True,
            disable=not self.use_rich_progress,
        )

        # Validate critical configuration sections
        self._validate_config()
        
        # CRITICAL: Log headless configuration to confirm propagation
        self._validate_headless_config()
        
        # Build job global dependencies
        self.job_global_dependencies = {
            "api_client": self.api_client,
            "image_handler": self.image_handler,
            "ai_integrator": self.ai_integrator,
            "ad_db_service": self.ad_db_service,
            # REMOVED session_manager - jobs create their own via factory
            "session_manager_factory": self.session_manager_factory,  # Factory for firm-specific session managers
            "local_image_queue": self.local_image_queue,
            "skip_term_service": self.skip_term_service,
            "fb_archive_repo": self._fb_archive_repo,
            "law_firms_repo": self._law_firms_repo,
            "law_firms_repository": self._law_firms_repo,  # Also provide with full name for session manager factory
            "logger": self.logger,
            "config": self.config,  # Needed for factory
            "fingerprint_manager": self.fingerprint_manager,
            "proxy_manager": self.proxy_manager,
            "browser_image_cache_extractor": self.browser_image_cache_extractor,  # Shared browser image cache
        }

    async def async_init(self):
        """Async initialization for test compatibility."""
        if not self._services_initialized:
            # All services are already injected via DI
            self._services_initialized = True
        return self

    @property
    def law_firm_repo(self):
        """Get law firms repository for test access."""
        return self._law_firms_repo

    @property
    def fb_archive_repo(self):
        """Get FB archive repository for test access."""
        return self._fb_archive_repo

    # ========================
    # PUBLIC WORKFLOW METHODS
    # ========================

    async def run_full_scrape(self):
        """Run the full ad scraping process for all relevant law firms."""
        # Update job global dependencies with injected repositories
        self.job_global_dependencies["fb_archive_repo"] = self._fb_archive_repo
        self.job_global_dependencies["law_firms_repo"] = self._law_firms_repo
        # CRITICAL FIX: Add law_firms_repository to match what session manager expects
        self.job_global_dependencies["law_firms_repository"] = self._law_firms_repo

        # Pass the dependencies to the workflow service
        self.workflow_service.job_global_dependencies = self.job_global_dependencies

        return await self.workflow_service.run_full_scrape_workflow()

    async def run_single_firm_scrape(self, firm_id: str):
        """Run the ad scraping process for a single specified law firm ID."""
        # Update job global dependencies with injected repositories
        self.job_global_dependencies["fb_archive_repo"] = self._fb_archive_repo
        self.job_global_dependencies["law_firms_repo"] = self._law_firms_repo
        # CRITICAL FIX: Add law_firms_repository to match what session manager expects
        self.job_global_dependencies["law_firms_repository"] = self._law_firms_repo

        # Pass the dependencies to the workflow service
        self.workflow_service.job_global_dependencies = self.job_global_dependencies

        return await self.workflow_service.run_single_firm_workflow(firm_id)

    async def run_ignore_list_processing(self):
        """Process only the firms listed in the loaded ignore_firm_data."""
        # Update job global dependencies with injected repositories
        self.job_global_dependencies["fb_archive_repo"] = self._fb_archive_repo
        self.job_global_dependencies["law_firms_repo"] = self._law_firms_repo
        # CRITICAL FIX: Add law_firms_repository to match what session manager expects
        self.job_global_dependencies["law_firms_repository"] = self._law_firms_repo

        # Pass the dependencies to the workflow service
        self.workflow_service.job_global_dependencies = self.job_global_dependencies

        return await self.workflow_service.run_ignore_list_workflow()

    # ==========================
    # INTERACTIVE/CLI METHODS
    # ==========================

    async def add_law_firm_interactive(self):
        """Interactively search and add a law firm by name."""
        # Update interactive service dependencies with injected repositories
        self.interactive_service.set_dependencies(
            self.session_manager,
            self.api_client,
            self.processing_tracker,
            self.ad_processing_service,
            self._law_firms_repo,
            self._fb_archive_repo,
            self,  # Pass orchestrator reference
            self.image_handler,  # Pass image handler for profile picture upload
            self.dropdown_search_helper,  # Pass dropdown helper for interactive workflows
        )

        await self.interactive_service.add_law_firm_interactive()

    async def add_attorney_by_page_id(self):
        """Interactively add a law firm by Facebook Page ID."""
        # Update interactive service dependencies with injected repositories
        self.interactive_service.set_dependencies(
            self.session_manager,
            self.api_client,
            self.processing_tracker,
            self.ad_processing_service,
            self._law_firms_repo,
            self._fb_archive_repo,
            self,  # Pass orchestrator reference
            self.image_handler,  # Pass image handler for profile picture upload
            self.dropdown_search_helper,  # Pass dropdown helper for interactive workflows
        )

        await self.interactive_service.add_attorney_by_page_id()

    async def add_law_firm_with_dropdown(self):
        """
        Add law firm using interactive dropdown navigation workflow.
        
        Uses arrow key navigation to search dropdown results.
        """
        # Update interactive service dependencies with injected repositories
        self.interactive_service.set_dependencies(
            self.session_manager,
            self.api_client,
            self.processing_tracker,
            self.ad_processing_service,
            self._law_firms_repo,
            self._fb_archive_repo,
            self,  # Pass orchestrator reference
            self.image_handler,  # Pass image handler for profile picture upload
            self.dropdown_search_helper,  # Pass dropdown helper for interactive workflows
        )

        await self.interactive_service.add_law_firm_with_dropdown()

    def search_law_firm_in_db(self, firm_id: str):
        """Search for a law firm by ID in the local database."""
        self.interactive_service.search_law_firm_in_db(firm_id)

    async def lookup_ad_by_id(self, ad_id):
        """Look up a Facebook ad by its archive ID."""
        await self.interactive_service.lookup_ad_by_id(ad_id)

    # ==========================
    # ERROR MANAGEMENT METHODS
    # ==========================

    def show_failed_firms(self, date: str | None = None, add_to_skip: bool = False):
        """Show failed firms, optionally by date and add to skip list."""
        self.error_handling_service.show_failed_firms(date, add_to_skip)

    def show_failure_summary(
        self, start_date: str | None = None, end_date: str | None = None
    ):
        """Show summary of processing failures."""
        self.error_handling_service.show_failure_summary(start_date, end_date)

    def add_firms_to_skip_list(self, firm_ids: list[str]):
        """Add firms to the skip list."""
        self.interactive_service.manage_skip_list("add", firm_ids)

    def remove_firms_from_skip_list(self, firm_ids: list[str]):
        """Remove firms from the skip list."""
        self.interactive_service.manage_skip_list("remove", firm_ids)

    def show_skip_list(self):
        """Display the current skip list."""
        self.interactive_service.manage_skip_list("show")

    # ==========================
    # UTILITY METHODS
    # ==========================

    def get_bandwidth_usage(self):
        """Return the current bandwidth usage statistics."""
        if hasattr(self, "bandwidth_logger"):
            stats = self.bandwidth_logger.get_total_bandwidth()
            stats["downloaded_mb"] = stats["downloaded"] / (1024 * 1024)
            stats["uploaded_mb"] = stats["uploaded"] / (1024 * 1024)
            stats["images_mb"] = stats["images"] / (1024 * 1024)
            stats["html_mb"] = stats["html"] / (1024 * 1024)
            stats["api_mb"] = stats["api"] / (1024 * 1024)
            stats["other_mb"] = stats["other"] / (1024 * 1024)
            return stats
        return None

    async def cleanup(self):
        """Close sessions and perform cleanup."""
        self.log_info("Performing cleanup...")

        # Cleanup bandwidth logger
        if hasattr(self, "bandwidth_logger"):
            self.bandwidth_logger.log_summary()

        # Cleanup LLaVA extractor
        if self.llava_extractor:
            try:
                await self.llava_extractor.close_session()
            except Exception as e:
                self.log_error(f"Error closing LLaVA session: {e}")

        # Cleanup session manager
        if self.session_manager:
            # Check if it's a legacy session manager with requests
            if hasattr(self.session_manager, 'reqs') and self.session_manager.reqs:
                try:
                    self.session_manager.reqs.close()
                except Exception as e:
                    self.log_error(f"Error closing requests session: {e}")

        # Cleanup progress bar
        if hasattr(self, "progress") and self.progress.live.is_started:
            try:
                self.progress.stop()
            except Exception as e:
                self.log_error(f"Error stopping progress bar: {e}")

        # Cleanup async storage
        if self._async_storage:
            try:
                await self._async_storage.__aexit__(None, None, None)
            except Exception as e:
                self.log_error(f"Error closing async storage: {e}")

        # IMPORTANT: Don't close aiohttp session here - it's managed by DI container
        # The container will handle proper cleanup when it shuts down
        # This prevents "Cannot write to closing transport" errors during concurrent operations
        if self.aiohttp_session:
            self.log_info("aiohttp session cleanup will be handled by DI container")

        self.log_info("Cleanup complete.")

    # ==========================
    # MAIN ENTRY POINT
    # ==========================

    async def run(self):
        """
        Main entry point for Facebook Ads processing.
        This is the method called by main.py.
        """
        self.log_info(f"Starting Facebook Ads processing for {self.end_date_iso}")
        
        # Start resource monitoring
        await self.resource_monitor.start_monitoring()

        try:
            # Log initial system resources
            metrics = await self.resource_monitor.get_resource_metrics()
            self.log_info(f"🔍 Initial system resources: CPU={metrics.cpu_percent:.1f}%, "
                         f"Memory={metrics.memory_percent:.1f}%, "
                         f"Available={metrics.memory_available_gb:.1f}GB")
            
            # Get recommended workers and adjust if needed
            recommended_workers = self.resource_monitor.get_recommended_workers()
            if hasattr(self.concurrent_workflow_service, 'num_workers'):
                current_workers = getattr(self.concurrent_workflow_service, 'num_workers', 4)
                if current_workers > recommended_workers:
                    self.log_warning(f"⚠️ Current workers ({current_workers}) exceed recommended ({recommended_workers}) for system capacity")
            
            # Run the full scrape workflow
            success = await self.run_full_scrape()

            if success:
                self.log_info("Facebook Ads processing completed successfully")
            else:
                self.log_error("Facebook Ads processing failed")

            return success

        except Exception as e:
            import traceback

            full_traceback = traceback.format_exc()
            self.log_error(f"Error in Facebook Ads processing: {e}")
            self.logger.error("Full Facebook Ads processing traceback:", exc_info=True)
            print(f"FULL FB ADS ERROR TRACEBACK:\n{full_traceback}", file=sys.stderr)
            return False
        
        finally:
            # Stop resource monitoring and cleanup
            try:
                await self.resource_monitor.stop_monitoring()
                self.log_info("🔍 Resource monitoring stopped")
                
                # Cleanup any zombie Camoufox processes
                cleanup_result = await self.resource_monitor.cleanup_zombie_processes()
                if cleanup_result == 0:
                    self.log_info("🧹 Zombie process cleanup completed successfully")
                    
            except Exception as cleanup_error:
                self.log_warning(f"Error during cleanup: {cleanup_error}")

    def _validate_llm_availability(self, ai_orchestrator):
        """
        Validate that required LLM services are available.
        Exits immediately with status code 1 if no LLM is available.
        """
        if not ai_orchestrator:
            self.log_error(
                "❌ CRITICAL: No AI orchestrator available for FB ads processing"
            )
            self.log_error(
                "Required: AI orchestrator with LLM services for ad summarization"
            )
            raise RuntimeError("No AI orchestrator available for FB ads processing")

        # Check if AI orchestrator has required dependencies
        dependencies = getattr(ai_orchestrator, "_dependencies", {})
        deepseek_service = dependencies.get("deepseek")
        gpt4_service = dependencies.get("gpt4")

        # Check service availability
        deepseek_available = False
        gpt4_available = False

        if deepseek_service:
            deepseek_available = (
                hasattr(deepseek_service, "client")
                and deepseek_service.client is not None
            )

        if gpt4_service:
            gpt4_available = (
                hasattr(gpt4_service, "chat_completion")
                and gpt4_service.chat_completion is not None
            )

        if not (deepseek_available or gpt4_available):
            self.log_error(
                "❌ CRITICAL: No working LLM service available for FB ads processing"
            )
            self.log_error(
                "Required: DeepSeek service with client OR GPT-4 service with chat_completion"
            )
            self.log_error("Check your .env file for API keys:")
            self.log_error("  - DEEPSEEK_API_KEY for DeepSeek")
            self.log_error("  - OPENAI_API_KEY for GPT-4")
            self.log_error("FB ads processing cannot proceed without LLM services.")
            raise RuntimeError("No working LLM service available - check API keys in .env file")

        # Log successful validation
        self.log_info("✅ LLM availability validated for FB ads processing")
        if deepseek_available:
            self.log_info("  - DeepSeek service: ✅ Available")
        if gpt4_available:
            self.log_info("  - GPT-4 service: ✅ Available")

    def _configure_fb_ads_logging(self):
        """Configure all FB ads modules to use the injected logger."""
        # CRITICAL FIX: The logging is already configured in main.py 
        # The injected logger from DI container should be automatically used by all services
        # Just verify that our logger is properly configured
        
        # Log a test message to verify the logger is working
        self.log_info("🔧 FB ads logging configuration verification:")
        self.log_info(f"   Logger type: {type(self.logger).__name__}")
        self.log_info(f"   Logger handlers: {len(self.logger._logger.handlers) if hasattr(self.logger, '_logger') else 'unknown'}")
        
        # Test that logs are going to the right place
        import logging
        fb_ads_logger = logging.getLogger("src.services.fb_ads")
        self.log_info(f"   FB ads root logger level: {fb_ads_logger.level}")
        self.log_info(f"   FB ads root logger propagate: {fb_ads_logger.propagate}")
        self.log_info(f"   FB ads root logger handlers: {len(fb_ads_logger.handlers)}")
        
        # The dependency injection system should ensure all services use the same logger instance
