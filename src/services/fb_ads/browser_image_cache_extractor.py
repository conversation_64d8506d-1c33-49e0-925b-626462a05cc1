"""
Browser Image Cache Extractor Service

This service eliminates double bandwidth usage by extracting images directly from 
browser cache during GraphQL response capture, preventing redundant HTTP downloads.

The service intercepts image resources during browser navigation and provides a 
cache-first approach for image retrieval, with fallback to network downloads.

Author: System Architecture Designer
Date: 2025-07-30
"""

import asyncio
import time
from dataclasses import dataclass
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from src.infrastructure.patterns.component_base import AsyncServiceBase


@dataclass
class CachedImageResource:
    """Represents an image extracted from browser cache during page navigation."""
    
    url: str
    content: bytes
    content_type: str
    content_length: int
    timestamp: float
    ad_archive_id: Optional[str] = None
    ad_creative_id: Optional[str] = None
    source: str = "browser_cache"  # Track where image came from


class BrowserImageCacheExtractor(AsyncServiceBase):
    """
    Service for extracting images directly from browser cache during page navigation.
    
    This service eliminates redundant HTTP downloads by capturing images during 
    initial GraphQL capture. It provides a cache-first approach for image retrieval
    with automatic fallback mechanisms.
    
    Key Features:
    - Intercepts Facebook image resources during browser navigation
    - Provides cache-first image retrieval with network fallback
    - Manages memory usage with configurable limits and TTL
    - Tracks bandwidth savings and cache efficiency metrics
    - Thread-safe operations with async locking
    """
    
    def __init__(
        self, 
        logger: Any,
        config: Dict[str, Any],
        max_cache_size_mb: int = 500, 
        cache_ttl_minutes: int = 1440,  # ENHANCED: 24 hours TTL for better persistence
        bandwidth_logger: Optional[Any] = None,
        enable_disk_persistence: bool = True,  # NEW: Enable disk-based cache persistence
        shared_cache_dict: Optional[Dict[str, Any]] = None  # NEW: Process-safe shared cache
    ):
        """
        Initialize the Browser Image Cache Extractor.
        
        Args:
            logger: Logger instance from DI container
            config: Configuration dictionary from DI container
            max_cache_size_mb: Maximum cache size in megabytes (default: 500MB)
            cache_ttl_minutes: Cache time-to-live in minutes (default: 30 minutes)
            bandwidth_logger: Optional bandwidth logger for tracking initial downloads
            shared_cache_dict: Optional multiprocessing.Manager dict for process-safe caching
        """
        super().__init__(logger, config)
        
        # Cache configuration
        self._max_cache_size = max_cache_size_mb * 1024 * 1024  # Convert to bytes
        self._cache_ttl = cache_ttl_minutes * 60  # Convert to seconds
        
        # Cache storage - ENHANCED with multiprocessing support
        if shared_cache_dict is not None:
            # Use the provided managed dictionary for process-safe caching
            self._image_cache = shared_cache_dict
            self._normalized_url_index = {}  # This can remain local as it's for indexing
            self.logger.info("✅ Using shared multiprocessing cache dictionary")
        else:
            # Fall back to regular dictionary (single process mode)
            self._image_cache: Dict[str, CachedImageResource] = {}
            self._normalized_url_index: Dict[str, List[str]] = {}  # Map normalized URLs to actual URLs
            self.logger.info("ℹ️ Using local cache dictionary (single process mode)")
            
        self._cache_lock = asyncio.Lock()
        self._current_cache_size = 0
        
        # DISK PERSISTENCE - NEW
        self._enable_disk_persistence = enable_disk_persistence
        self._disk_cache_dir = None
        if self._enable_disk_persistence:
            self._setup_disk_persistence()
        
        # Statistics tracking - ENHANCED
        self._cache_hits = 0
        self._cache_misses = 0
        self._total_saved_bytes = 0
        self._cache_miss_reasons = {}  # NEW: Track reasons for cache misses
        self._url_collision_count = 0  # NEW: Track URL collisions
        self._normalization_stats = {'successful': 0, 'failed': 0}  # NEW: Track normalization success
        
        # Bandwidth tracking
        self.bandwidth_logger = bandwidth_logger
        self._total_cached_images = 0
        
        # Facebook image domains for filtering
        self._facebook_image_domains = [
            'fbcdn.net', 'facebook.com', 'fbsbx.com', 
            'xx.fbcdn.net', 'scontent', 'static.xx.fbcdn.net',
            'video.xx.fbcdn.net', 'z-m-scontent', 'z-m-static.xx.fbcdn.net'
        ]
        
        # Image content types to cache
        self._image_content_types = [
            'image/', 'img/', 'png', 'jpg', 'jpeg', 'gif', 'webp', 'avif'
        ]
        
        self.log_info(
            f"🖼️ Browser Image Cache Extractor initialized (Max: {max_cache_size_mb}MB, TTL: {cache_ttl_minutes}min, "
            f"Disk Persistence: {self._enable_disk_persistence})"
        )

    async def _execute_action(self, action_data: Any) -> Any:
        """Execute cache extractor action (base class requirement)."""
        return await self.get_cache_stats()
    
    def _setup_disk_persistence(self):
        """ENHANCED: Set up disk-based cache persistence."""
        try:
            from pathlib import Path
            import os
            
            # Create cache directory in project root
            project_root = Path.cwd()
            self._disk_cache_dir = project_root / '.image_cache'
            self._disk_cache_dir.mkdir(exist_ok=True)
            
            # Create subdirectories for organization
            (self._disk_cache_dir / 'images').mkdir(exist_ok=True)
            (self._disk_cache_dir / 'metadata').mkdir(exist_ok=True)
            
            self.log_info(f"💾 Disk cache persistence enabled: {self._disk_cache_dir}")
            
            # Load existing cache from disk on startup
            asyncio.create_task(self._load_cache_from_disk())
            
        except Exception as e:
            self.log_warning(f"Failed to setup disk persistence: {e}")
            self._enable_disk_persistence = False
    
    async def _load_cache_from_disk(self):
        """Load existing cache entries from disk."""
        if not self._enable_disk_persistence or not self._disk_cache_dir:
            return
            
        try:
            import json
            import os
            from pathlib import Path
            
            metadata_dir = self._disk_cache_dir / 'metadata'
            images_dir = self._disk_cache_dir / 'images'
            
            loaded_count = 0
            current_time = time.time()
            
            # Load metadata files
            for metadata_file in metadata_dir.glob('*.json'):
                try:
                    with open(metadata_file, 'r') as f:
                        cache_entry = json.load(f)
                    
                    # Check if entry is still valid (TTL)
                    if current_time - cache_entry['timestamp'] > self._cache_ttl:
                        # Remove expired entry
                        os.remove(metadata_file)
                        image_file = images_dir / cache_entry['image_filename']
                        if image_file.exists():
                            os.remove(image_file)
                        continue
                    
                    # Load image content
                    image_file = images_dir / cache_entry['image_filename']
                    if not image_file.exists():
                        os.remove(metadata_file)
                        continue
                    
                    with open(image_file, 'rb') as f:
                        image_content = f.read()
                    
                    # Recreate cached image resource
                    cached_image = CachedImageResource(
                        url=cache_entry['url'],
                        content=image_content,
                        content_type=cache_entry['content_type'],
                        content_length=len(image_content),
                        timestamp=cache_entry['timestamp'],
                        ad_archive_id=cache_entry.get('ad_archive_id'),
                        ad_creative_id=cache_entry.get('ad_creative_id'),
                        source="disk_cache"
                    )
                    
                    # Add to memory cache
                    async with self._cache_lock:
                        self._image_cache[cache_entry['url']] = cached_image
                        self._current_cache_size += len(image_content)
                        
                        # Update normalized URL index
                        normalized_url = self._normalize_facebook_image_url(cache_entry['url'])
                        if normalized_url not in self._normalized_url_index:
                            self._normalized_url_index[normalized_url] = []
                        if cache_entry['url'] not in self._normalized_url_index[normalized_url]:
                            self._normalized_url_index[normalized_url].append(cache_entry['url'])
                    
                    loaded_count += 1
                    
                except Exception as e:
                    self.log_debug(f"Failed to load cache entry {metadata_file}: {e}")
                    continue
            
            if loaded_count > 0:
                self.log_info(f"💾 Loaded {loaded_count} cached images from disk ({self._current_cache_size / (1024*1024):.1f}MB)")
            
        except Exception as e:
            self.log_warning(f"Failed to load cache from disk: {e}")
    
    async def _save_cache_to_disk(self, cached_image: CachedImageResource):
        """Save cache entry to disk for persistence."""
        if not self._enable_disk_persistence or not self._disk_cache_dir:
            return
        
        try:
            import json
            import hashlib
            from pathlib import Path
            
            # Create filename from URL hash
            url_hash = hashlib.md5(cached_image.url.encode()).hexdigest()
            image_filename = f"{url_hash}.jpg"
            metadata_filename = f"{url_hash}.json"
            
            images_dir = self._disk_cache_dir / 'images'
            metadata_dir = self._disk_cache_dir / 'metadata'
            
            # Save image content
            image_file = images_dir / image_filename
            with open(image_file, 'wb') as f:
                f.write(cached_image.content)
            
            # Save metadata
            metadata = {
                'url': cached_image.url,
                'content_type': cached_image.content_type,
                'content_length': cached_image.content_length,
                'timestamp': cached_image.timestamp,
                'ad_archive_id': cached_image.ad_archive_id,
                'ad_creative_id': cached_image.ad_creative_id,
                'image_filename': image_filename,
                'normalized_url': self._normalize_facebook_image_url(cached_image.url)
            }
            
            metadata_file = metadata_dir / metadata_filename
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f)
            
            self.log_debug(f"💾 Saved cache entry to disk: {image_filename}")
            
        except Exception as e:
            self.log_debug(f"Failed to save cache entry to disk: {e}")

    async def setup_image_interception(self, page) -> None:
        """
        Setup image resource interception on the browser page.
        
        This method registers response handlers to capture Facebook image resources
        during page navigation and GraphQL response capture.
        
        Args:
            page: Playwright page object to setup interception on
        """
        if not page:
            self.log_error("❌ Cannot setup image interception: page is None")
            return
        
        # ENHANCED DEBUG LOGGING
        self.log_info(f"🎯 CACHE_SETUP: Setting up image interception on page {id(page)}")
            
        async def handle_image_response(response):
            """Handle intercepted image responses from Facebook."""
            try:
                # Only intercept Facebook image resources
                if not self._is_facebook_image(response.url):
                    return
                    
                content_type = response.headers.get('content-type', '')
                if not self._is_image_content_type(content_type):
                    return
                
                # Check response status
                if response.status != 200:
                    self.log_debug(f"⚠️ Skipping image with status {response.status}: {response.url}")
                    return
                
                # Extract image content
                try:
                    image_content = await response.body()
                except Exception as body_error:
                    self.log_debug(f"Failed to get response body for {response.url}: {body_error}")
                    return
                    
                if not image_content:
                    self.log_debug(f"Empty image content for {response.url}")
                    return
                
                # Create cached resource
                cached_image = CachedImageResource(
                    url=response.url,
                    content=image_content,
                    content_type=content_type,
                    content_length=len(image_content),
                    timestamp=time.time(),
                    source="browser_cache"
                )
                
                # Store in cache
                await self._store_cached_image(cached_image)
                
                # ENHANCED DEBUG LOGGING
                self.log_info(f"🖼️ CACHE_INTERCEPT: Stored {len(image_content)} bytes from {response.url[:80]}...")
                
                # Track bandwidth for initial download
                if self.bandwidth_logger:
                    # Estimate request size for the image request
                    request_size = 500  # Average HTTP request size
                    response_size = len(image_content)
                    
                    # Log the bandwidth for this initial download
                    self.bandwidth_logger.log_request(
                        url=response.url,
                        request_size=request_size,
                        response_size=response_size,
                        method="GET",
                        headers={"content-type": content_type}
                    )
                    self.log_debug(f"📊 Logged bandwidth for initial image download: {response_size} bytes")
                
                self.log_info(f"🖼️ CACHED: Extracted {len(image_content)} bytes from {response.url}")
                
            except Exception as e:
                self.log_warning(f"Failed to cache image from {response.url}: {e}")
        
        try:
            # Register response handler for images
            page.on("response", handle_image_response)
            self.log_info(f"🎯 CACHE_SETUP: Image interception setup complete on page {id(page)}")
            self.log_info(f"📊 CACHE_STATUS: Currently {len(self._image_cache)} images in cache")
            
            # Log current cache contents for debugging
            if self._image_cache:
                self.log_debug(f"CACHE_CONTENTS: {list(self._image_cache.keys())[:3]}... (showing first 3)")
            
        except Exception as e:
            self.log_error(f"Failed to setup image interception: {e}")
            raise

    async def get_cached_image(self, image_url: str) -> Optional[CachedImageResource]:
        """
        ENHANCED: Retrieve image from cache if available and valid with detailed miss tracking.
        Now includes prefix-based fallback matching for better cache hit rates.
        
        Args:
            image_url: URL of the image to retrieve
            
        Returns:
            CachedImageResource if found and valid, None otherwise
        """
        async with self._cache_lock:
            # Track normalization success
            normalized_url = None
            try:
                normalized_url = self._normalize_facebook_image_url(image_url)
                self._normalization_stats['successful'] += 1
            except Exception as e:
                self._normalization_stats['failed'] += 1
                self.log_debug(f"URL normalization failed for {image_url}: {e}")
                normalized_url = image_url  # Fallback to original URL
            
            # Try exact match first
            cached_image = self._image_cache.get(image_url)
            
            # If no exact match, try normalized URL lookup with enhanced matching
            if not cached_image:
                
                # Check normalized URL index for potential matches
                if normalized_url in self._normalized_url_index:
                    # Get all URLs that normalize to the same base
                    candidate_urls = self._normalized_url_index[normalized_url]
                    
                    # Find the best match with enhanced selection criteria
                    best_match = None
                    best_size = 0
                    best_score = 0
                    
                    for candidate_url in candidate_urls:
                        if candidate_url in self._image_cache:
                            candidate = self._image_cache[candidate_url]
                            
                            # Check TTL before considering as candidate
                            if time.time() - candidate.timestamp > self._cache_ttl:
                                continue
                            
                            # Enhanced scoring: prefer larger images and newer timestamps
                            size_score = candidate.content_length / 1024  # KB as base score
                            freshness_score = max(0, self._cache_ttl - (time.time() - candidate.timestamp)) / 3600  # Hours fresh
                            total_score = size_score + (freshness_score * 10)  # Weight freshness
                            
                            if total_score > best_score:
                                best_match = candidate
                                best_size = candidate.content_length
                                best_score = total_score
                    
                    if best_match:
                        cached_image = best_match
                        self.log_info(f"🎯 CACHE HIT (normalized): Found {best_size} byte image for {image_url}")
                        self.log_debug(f"   Normalized: {normalized_url}")
                        self.log_debug(f"   Score: {best_score:.1f}, Age: {(time.time() - best_match.timestamp)/3600:.1f}h")
                else:
                    # Track miss reasons for analytics
                    self._cache_miss_reasons['no_normalized_match'] = self._cache_miss_reasons.get('no_normalized_match', 0) + 1
            
            # ENHANCED: If still no match, try prefix-based search as last resort
            if not cached_image and normalized_url:
                self.log_debug(f"🔍 CACHE PREFIX SEARCH: Trying prefix match for {normalized_url}")
                
                # Extract base URL without query parameters for prefix matching
                base_url = normalized_url.split('?')[0] if '?' in normalized_url else normalized_url
                
                # Search through all cached URLs for prefix matches
                prefix_candidates = []
                for cached_url, cached_resource in self._image_cache.items():
                    # Skip expired entries
                    if time.time() - cached_resource.timestamp > self._cache_ttl:
                        continue
                    
                    # Check if the cached URL starts with our base URL
                    cached_base = cached_url.split('?')[0] if '?' in cached_url else cached_url
                    if cached_base.startswith(base_url) or base_url.startswith(cached_base):
                        prefix_candidates.append((cached_url, cached_resource))
                        self.log_debug(f"   Found prefix candidate: {cached_url}")
                
                # Select best prefix match based on size and freshness
                if prefix_candidates:
                    best_match = None
                    best_size = 0
                    best_score = 0
                    
                    for cached_url, candidate in prefix_candidates:
                        # Enhanced scoring for prefix matches
                        size_score = candidate.content_length / 1024  # KB as base score
                        freshness_score = max(0, self._cache_ttl - (time.time() - candidate.timestamp)) / 3600
                        # Add URL similarity bonus
                        similarity_score = 10 if cached_url == image_url else 5  # Exact match gets bonus
                        total_score = size_score + (freshness_score * 10) + similarity_score
                        
                        if total_score > best_score:
                            best_match = candidate
                            best_size = candidate.content_length
                            best_score = total_score
                    
                    if best_match:
                        cached_image = best_match
                        self.log_info(f"🎯 CACHE HIT (prefix): Found {best_size} byte image for {image_url}")
                        self.log_debug(f"   Base URL: {base_url}")
                        self.log_debug(f"   Score: {best_score:.1f}, Age: {(time.time() - best_match.timestamp)/3600:.1f}h")
                        self.log_debug(f"   Matched from {len(prefix_candidates)} prefix candidates")
                        # Track this as a special hit type
                        self._cache_miss_reasons['prefix_match_recovery'] = self._cache_miss_reasons.get('prefix_match_recovery', 0) + 1
                else:
                    self.log_debug(f"   No prefix matches found for base URL: {base_url}")
            
            if not cached_image:
                self._cache_misses += 1
                
                # Enhanced miss tracking - categorize miss reasons
                miss_reason = 'unknown'
                if image_url in self._image_cache:
                    miss_reason = 'expired'
                elif normalized_url and normalized_url in self._normalized_url_index:
                    miss_reason = 'all_candidates_expired' 
                elif normalized_url and normalized_url != image_url:
                    miss_reason = 'normalization_mismatch'
                else:
                    miss_reason = 'not_cached'
                
                self._cache_miss_reasons[miss_reason] = self._cache_miss_reasons.get(miss_reason, 0) + 1
                
                # ENHANCED DEBUG LOGGING FOR PRODUCTION DIAGNOSIS
                self.log_info(f"❌ CACHE_MISS ({miss_reason}): {image_url[:80]}...")
                self.log_info(f"   Normalized URL: {normalized_url[:80]}...")
                self.log_info(f"   Cache contains {len(self._image_cache)} images, {len(self._normalized_url_index)} normalized groups")
                
                # Log sample of what's in cache for comparison
                if self._image_cache and len(self._image_cache) < 10:
                    sample_urls = list(self._image_cache.keys())[:3]
                    self.log_debug(f"   Sample cached URLs: {[url[:60] + '...' for url in sample_urls]}")
                
                return None
                
            # Check TTL
            if time.time() - cached_image.timestamp > self._cache_ttl:
                self.log_debug(f"⏰ CACHE EXPIRED: {image_url}")
                await self._remove_from_cache(cached_image.url)
                self._cache_misses += 1
                self._cache_miss_reasons['expired'] = self._cache_miss_reasons.get('expired', 0) + 1
                return None
            
            # Update statistics
            self._cache_hits += 1
            self._total_saved_bytes += cached_image.content_length
            
            # ENHANCED DEBUG LOGGING
            self.log_info(f"✅ CACHE_HIT_SUCCESS: Found {cached_image.content_length} bytes for {image_url[:80]}...")
            self.log_debug(f"   Cache stats: {self._cache_hits} hits, {self._cache_misses} misses, {len(self._image_cache)} total images")
            return cached_image

    async def _store_cached_image(self, cached_image: CachedImageResource) -> None:
        """
        ENHANCED: Store image in cache with size management and disk persistence.
        
        Args:
            cached_image: Image resource to store in cache
        """
        async with self._cache_lock:
            # Check if we need to evict old images
            await self._ensure_cache_space(cached_image.content_length)
            
            # Store the image
            self._image_cache[cached_image.url] = cached_image
            
            # Update normalized URL index with collision detection
            normalized_url = self._normalize_facebook_image_url(cached_image.url)
            if normalized_url not in self._normalized_url_index:
                self._normalized_url_index[normalized_url] = []
            if cached_image.url not in self._normalized_url_index[normalized_url]:
                self._normalized_url_index[normalized_url].append(cached_image.url)
                
                # Track URL collisions for analysis
                if len(self._normalized_url_index[normalized_url]) > 1:
                    self._url_collision_count += 1
                    self.log_debug(f"🔄 URL collision detected: {len(self._normalized_url_index[normalized_url])} URLs map to {normalized_url}")
            
            self._current_cache_size += cached_image.content_length
            self._total_cached_images += 1
            
            self.log_debug(
                f"💾 STORED: {cached_image.url} ({cached_image.content_length} bytes) "
                f"[Cache: {len(self._image_cache)} images, {self._current_cache_size / (1024*1024):.1f}MB] "
                f"[Normalized: {normalized_url}]"
            )
            
            # Save to disk asynchronously if persistence is enabled
            if self._enable_disk_persistence:
                try:
                    await self._save_cache_to_disk(cached_image)
                except Exception as e:
                    self.log_debug(f"Failed to save to disk (continuing): {e}")

    async def _ensure_cache_space(self, required_size: int) -> None:
        """
        Ensure sufficient cache space by evicting old images.
        
        Args:
            required_size: Number of bytes needed for new image
        """
        if self._current_cache_size + required_size <= self._max_cache_size:
            return
            
        self.log_info(f"🧹 Cache full, evicting old images to make space for {required_size} bytes")
        
        # Sort by timestamp (oldest first) and evict until we have space
        sorted_images = sorted(
            self._image_cache.items(), 
            key=lambda x: x[1].timestamp
        )
        
        evicted_count = 0
        evicted_bytes = 0
        
        for url, cached_image in sorted_images:
            evicted_bytes += cached_image.content_length
            await self._remove_from_cache(url)
            evicted_count += 1
            
            if self._current_cache_size + required_size <= self._max_cache_size:
                break
        
        self.log_info(f"🧹 Evicted {evicted_count} images ({evicted_bytes / (1024*1024):.1f}MB)")

    async def _remove_from_cache(self, url: str) -> None:
        """
        Remove image from cache.
        
        Args:
            url: URL of image to remove
        """
        if url in self._image_cache:
            cached_image = self._image_cache.pop(url)
            self._current_cache_size -= cached_image.content_length
            
            # Clean up normalized URL index
            normalized_url = self._normalize_facebook_image_url(url)
            if normalized_url in self._normalized_url_index:
                self._normalized_url_index[normalized_url].remove(url)
                if not self._normalized_url_index[normalized_url]:
                    del self._normalized_url_index[normalized_url]

    def _is_facebook_image(self, url: str) -> bool:
        """
        Check if URL is a Facebook image resource.
        
        Args:
            url: URL to check
            
        Returns:
            True if URL is from Facebook image domain
        """
        return any(domain in url for domain in self._facebook_image_domains)

    def _is_image_content_type(self, content_type: str) -> bool:
        """
        Check if content type is an image.
        
        Args:
            content_type: HTTP content-type header value
            
        Returns:
            True if content type indicates an image
        """
        content_type_lower = content_type.lower()
        return any(img_type in content_type_lower for img_type in self._image_content_types)
    
    def _normalize_facebook_image_url(self, url: str) -> str:
        """
        ENHANCED: Normalize Facebook image URL to handle variations between thumbnail and full-size images.
        
        Facebook image URLs often contain varying query parameters for different sizes:
        - stp=dst-jpg_s60x60_tt6 (thumbnail)
        - stp=dst-jpg_s600x600_tt6 (full size)
        - _nc_gid, oh, oe, ccb parameters change frequently
        
        This method extracts the stable image identifier and creates a normalized cache key.
        
        Args:
            url: Facebook image URL to normalize
            
        Returns:
            Normalized URL for cache key comparison
        """
        try:
            # Parse URL
            from urllib.parse import urlparse, parse_qs
            import re
            parsed = urlparse(url)
            
            # FIXED: Normalize Facebook CDN hosts to a common base
            # Instead of keeping specific hosts, use a normalized Facebook host
            normalized_host = "scontent.fbcdn.net"
            
            # Check if this is a Facebook CDN URL
            if not any(domain in parsed.netloc for domain in [
                'fbcdn.net', 'facebook.com', 'fbsbx.com'
            ]):
                # Not a Facebook URL, return as-is
                return url
            
            # For Facebook images, extract the stable image identifier from the path
            # Example paths:
            # /v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg
            # /v/t1.6435-9/123456789_987654321_1234567890123456789_n.jpg
            
            # Extract image ID pattern: numbers_numbers_numbers_n.extension
            id_match = re.search(r'(\d+_\d+_\d+_[no]\.)([a-z]+)$', parsed.path)
            if id_match:
                # Use the stable image ID as the primary cache key component
                image_id = id_match.group(1) + id_match.group(2)
                # FIXED: Create a normalized base using common Facebook host and image ID
                normalized_base = f"{parsed.scheme}://{normalized_host}/{image_id}"
            else:
                # Fallback to path normalization with normalized host
                normalized_base = f"{parsed.scheme}://{normalized_host}{parsed.path}"
            
            # Handle query parameters - AGGRESSIVELY filter dynamic ones
            if parsed.query:
                params = parse_qs(parsed.query)
                # CRITICAL: These parameters change frequently and cause cache misses
                dynamic_params = {
                    # Size and transformation parameters
                    'stp', 'w', 'h', 'q', 'quality',
                    # Facebook tracking parameters that change frequently
                    '_nc_cat', '_nc_sid', '_nc_ohc', '_nc_oc', '_nc_zt', '_nc_ht', 
                    '_nc_gid', 'oh', 'oe', 'ccb', '_nc_aid', '_nc_ad', '_nc_cid',
                    # CDN and caching parameters
                    'tp', 'nc_pid', 'efg', 'dl', 'nc_eui2', 'hash',
                    # Time-based parameters
                    'ts', 'time', 'timestamp', 'cache_buster', 'cb',
                    # Session-specific parameters
                    'session', 'token', 'nonce', 'sig', 'signature'
                }
                
                # Keep only stable, essential identifying parameters
                essential_params = []
                for key, values in params.items():
                    if key.lower() not in dynamic_params and not key.startswith('_'):
                        # Additional filter: skip numeric-only parameter names (often dynamic)
                        if not key.replace('_', '').isdigit():
                            essential_params.append(f"{key}={values[0]}")
                
                if essential_params:
                    return f"{normalized_base}?{'&'.join(sorted(essential_params))}"
            
            return normalized_base
            
        except Exception as e:
            self.log_debug(f"Failed to normalize URL {url}: {e}")
            # Return simplified fallback normalization
            try:
                # At minimum, strip common dynamic parameters and normalize host
                from urllib.parse import urlparse, parse_qs, urlencode
                parsed = urlparse(url)
                
                # Use normalized Facebook host
                normalized_host = "scontent.fbcdn.net"
                if any(domain in parsed.netloc for domain in ['fbcdn.net', 'facebook.com', 'fbsbx.com']):
                    host = normalized_host
                else:
                    host = parsed.netloc
                
                if parsed.query:
                    params = parse_qs(parsed.query)
                    # Remove the most common dynamic parameters
                    for param in ['_nc_gid', 'oh', 'oe', 'ccb', '_nc_aid', 'stp']:
                        params.pop(param, None)
                    if params:
                        clean_query = urlencode(sorted(params.items()))
                        return f"{parsed.scheme}://{host}{parsed.path}?{clean_query}"
                return f"{parsed.scheme}://{host}{parsed.path}"
            except:
                return url

    async def clear_cache(self) -> None:
        """Clear all cached images and reset statistics."""
        async with self._cache_lock:
            cleared_count = len(self._image_cache)
            cleared_size = self._current_cache_size
            
            self._image_cache.clear()
            self._normalized_url_index.clear()
            self._current_cache_size = 0
            
            self.log_info(f"🧹 Image cache cleared: {cleared_count} images ({cleared_size / (1024*1024):.1f}MB)")

    async def cleanup_expired_images(self) -> int:
        """
        Remove expired images from cache.
        
        Returns:
            Number of images removed
        """
        current_time = time.time()
        expired_urls = []
        
        async with self._cache_lock:
            for url, cached_image in self._image_cache.items():
                if current_time - cached_image.timestamp > self._cache_ttl:
                    expired_urls.append(url)
            
            for url in expired_urls:
                await self._remove_from_cache(url)
        
        if expired_urls:
            self.log_info(f"🧹 Removed {len(expired_urls)} expired images from cache")
        
        return len(expired_urls)

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        ENHANCED: Get comprehensive cache statistics with detailed analytics.
        
        Returns:
            Dictionary containing cache performance metrics and diagnostic data
        """
        total_requests = self._cache_hits + self._cache_misses
        hit_rate = self._cache_hits / total_requests if total_requests > 0 else 0
        
        # Calculate cache age distribution
        current_time = time.time()
        age_buckets = {'<1h': 0, '1-6h': 0, '6-12h': 0, '12-24h': 0, '>24h': 0}
        for cached_image in self._image_cache.values():
            age_hours = (current_time - cached_image.timestamp) / 3600
            if age_hours < 1:
                age_buckets['<1h'] += 1
            elif age_hours < 6:
                age_buckets['1-6h'] += 1
            elif age_hours < 12:
                age_buckets['6-12h'] += 1
            elif age_hours < 24:
                age_buckets['12-24h'] += 1
            else:
                age_buckets['>24h'] += 1
        
        # Calculate potential bandwidth if all misses were hits
        potential_additional_savings = 0
        if self._cache_misses > 0 and self._cache_hits > 0:
            avg_image_size = self._total_saved_bytes / self._cache_hits if self._cache_hits > 0 else 150000  # 150KB default
            potential_additional_savings = self._cache_misses * avg_image_size
        
        return {
            # Cache utilization
            'cached_images': len(self._image_cache),
            'cache_size_mb': round(self._current_cache_size / (1024 * 1024), 2),
            'max_cache_size_mb': round(self._max_cache_size / (1024 * 1024), 2),
            'cache_utilization_percent': round(
                (self._current_cache_size / self._max_cache_size * 100) if self._max_cache_size > 0 else 0, 1
            ),
            
            # Performance metrics
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_rate_percent': round(hit_rate * 100, 1),
            'total_requests': total_requests,
            
            # ENHANCED: Miss analysis
            'miss_rate_percent': round((1 - hit_rate) * 100, 1),
            'cache_miss_reasons': dict(self._cache_miss_reasons),
            'top_miss_reason': max(self._cache_miss_reasons.items(), key=lambda x: x[1])[0] if self._cache_miss_reasons else 'none',
            'prefix_recoveries': self._cache_miss_reasons.get('prefix_match_recovery', 0),
            
            # Index statistics
            'normalized_url_groups': len(self._normalized_url_index),
            'avg_urls_per_group': round(
                sum(len(urls) for urls in self._normalized_url_index.values()) / len(self._normalized_url_index)
                if self._normalized_url_index else 0, 1
            ),
            'url_collision_count': self._url_collision_count,
            'normalization_success_rate': round(
                self._normalization_stats['successful'] / 
                (self._normalization_stats['successful'] + self._normalization_stats['failed']) * 100
                if (self._normalization_stats['successful'] + self._normalization_stats['failed']) > 0 else 0, 1
            ),
            
            # Bandwidth savings
            'total_saved_bytes': self._total_saved_bytes,
            'total_saved_mb': round(self._total_saved_bytes / (1024 * 1024), 2),
            'total_cached_images': self._total_cached_images,
            'avg_image_size_kb': round(
                (self._current_cache_size / len(self._image_cache) / 1024) if len(self._image_cache) > 0 else 0, 1
            ),
            
            # ENHANCED: Efficiency analysis
            'cache_efficiency_score': round(hit_rate * 100, 1),  # Hit rate as efficiency score
            'potential_additional_savings_mb': round(potential_additional_savings / (1024 * 1024), 2),
            'cache_age_distribution': age_buckets,
            
            # Disk persistence status
            'disk_persistence_enabled': self._enable_disk_persistence,
            'disk_cache_dir': str(self._disk_cache_dir) if self._disk_cache_dir else None,
            
            # Configuration
            'cache_ttl_minutes': self._cache_ttl / 60,
            'cache_ttl_hours': round(self._cache_ttl / 3600, 1),
            'enabled': True,
            
            # DIAGNOSTIC: Problem identification
            'needs_attention': self._identify_cache_problems(hit_rate, total_requests),
            'recommendations': self._generate_cache_recommendations(hit_rate, total_requests)
        }
    
    def _identify_cache_problems(self, hit_rate: float, total_requests: int) -> List[str]:
        """Identify cache performance problems."""
        problems = []
        
        if hit_rate < 0.3 and total_requests > 10:  # Less than 30% hit rate
            problems.append("Very low cache hit rate - check URL normalization")
        
        if self._cache_miss_reasons.get('normalization_mismatch', 0) > self._cache_hits:
            problems.append("High normalization mismatches - review URL normalization logic")
        
        if self._cache_miss_reasons.get('expired', 0) > self._cache_hits * 0.5:
            problems.append("High expiration rate - consider increasing TTL")
        
        if self._normalization_stats['failed'] > self._normalization_stats['successful'] * 0.1:
            problems.append("URL normalization failures detected")
        
        if len(self._image_cache) == 0 and total_requests > 5:
            problems.append("No images in cache despite requests - check image interception")
        
        return problems
    
    def _generate_cache_recommendations(self, hit_rate: float, total_requests: int) -> List[str]:
        """Generate recommendations to improve cache performance."""
        recommendations = []
        
        if hit_rate < 0.5 and total_requests > 10:
            recommendations.append("Increase cache TTL to 24+ hours for ad images")
            recommendations.append("Review and improve URL normalization patterns")
        
        if self._cache_miss_reasons.get('not_cached', 0) > 0:
            recommendations.append("Ensure image interception is working during page navigation")
        
        if self._url_collision_count > 0:
            recommendations.append(f"Good: {self._url_collision_count} URL variations were normalized successfully")
        
        if self._cache_miss_reasons.get('prefix_match_recovery', 0) > 0:
            recoveries = self._cache_miss_reasons['prefix_match_recovery']
            recommendations.append(f"Excellent: Prefix matching recovered {recoveries} cache misses - feature working well!")
        
        if not self._enable_disk_persistence:
            recommendations.append("Enable disk persistence for better cache survival across sessions")
        
        if self._current_cache_size < self._max_cache_size * 0.1:
            recommendations.append("Cache is underutilized - increase interception coverage")
        
        return recommendations

    def get_detailed_cache_info(self) -> List[Dict[str, Any]]:
        """
        Get detailed information about cached images.
        
        Returns:
            List of dictionaries with detailed cache information
        """
        current_time = time.time()
        cache_info = []
        
        for url, cached_image in self._image_cache.items():
            age_seconds = current_time - cached_image.timestamp
            cache_info.append({
                'url': url,
                'content_length': cached_image.content_length,
                'content_type': cached_image.content_type,
                'age_seconds': round(age_seconds, 1),
                'age_minutes': round(age_seconds / 60, 1),
                'expires_in_seconds': max(0, self._cache_ttl - age_seconds),
                'ad_archive_id': cached_image.ad_archive_id,
                'ad_creative_id': cached_image.ad_creative_id,
                'source': cached_image.source
            })
        
        # Sort by age (newest first)
        cache_info.sort(key=lambda x: x['age_seconds'])
        return cache_info

    def get_normalized_url_index_info(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get information about the normalized URL index for debugging.
        
        Returns:
            Dict mapping normalized URLs to lists of cached URL info
        """
        index_info = {}
        
        for normalized_url, urls in self._normalized_url_index.items():
            url_infos = []
            for url in urls:
                if url in self._image_cache:
                    cached = self._image_cache[url]
                    url_infos.append({
                        'url': url,
                        'size': cached.content_length,
                        'type': cached.content_type,
                        'age_seconds': round(time.time() - cached.timestamp, 1)
                    })
            
            if url_infos:
                index_info[normalized_url] = url_infos
        
        return index_info
    
    async def preload_images_from_urls(self, image_urls: List[str], page) -> int:
        """
        Preload images from a list of URLs using the browser.
        
        This can be used to populate the cache with specific images
        before they are needed for processing.
        
        Args:
            image_urls: List of image URLs to preload  
            page: Playwright page object for navigation
            
        Returns:
            Number of images successfully preloaded
        """
        if not image_urls:
            return 0
            
        self.log_info(f"🔄 Preloading {len(image_urls)} images into cache")
        preload_count = 0
        
        for image_url in image_urls:
            try:
                # Navigate to image URL to trigger cache
                response = await page.goto(image_url, wait_until='load', timeout=10000)
                
                if response and response.status == 200:
                    # Image should now be in cache via response handler
                    cached_image = await self.get_cached_image(image_url)
                    if cached_image:
                        preload_count += 1
                        self.log_debug(f"✅ Preloaded: {image_url}")
                    else:
                        self.log_debug(f"❌ Failed to cache after preload: {image_url}")
                else:
                    self.log_debug(f"❌ Preload failed for {image_url}: status {response.status if response else 'None'}")
                    
            except Exception as e:
                self.log_debug(f"❌ Preload error for {image_url}: {e}")
                continue
        
        self.log_info(f"🔄 Preload complete: {preload_count}/{len(image_urls)} images cached")
        return preload_count
    
    def get_cache_analytics_dashboard(self) -> str:
        """
        ENHANCED: Get a formatted analytics dashboard showing cache performance.
        
        Returns:
            Formatted string with comprehensive cache analytics
        """
        stats = self.get_cache_stats()
        
        dashboard = []
        dashboard.append("🖼️  BROWSER IMAGE CACHE ANALYTICS DASHBOARD")
        dashboard.append("=" * 55)
        
        # Performance Summary
        hit_rate = stats['hit_rate_percent']
        if hit_rate >= 80:
            performance_emoji = "🟢"
            performance_status = "EXCELLENT"
        elif hit_rate >= 60:
            performance_emoji = "🟡"
            performance_status = "GOOD"
        elif hit_rate >= 30:
            performance_emoji = "🟠"
            performance_status = "NEEDS IMPROVEMENT"
        else:
            performance_emoji = "🔴"
            performance_status = "CRITICAL"
        
        dashboard.append(f"{performance_emoji} CACHE PERFORMANCE: {performance_status} ({hit_rate}% hit rate)")
        dashboard.append("")
        
        # Core Metrics
        dashboard.append("📊 CORE METRICS:")
        dashboard.append(f"  Cache Hits: {stats['cache_hits']:,}")
        dashboard.append(f"  Cache Misses: {stats['cache_misses']:,}")
        dashboard.append(f"  Total Requests: {stats['total_requests']:,}")
        dashboard.append(f"  Bandwidth Saved: {stats['total_saved_mb']} MB")
        dashboard.append("")
        
        # Cache Status
        dashboard.append("💾 CACHE STATUS:")
        dashboard.append(f"  Images Cached: {stats['cached_images']:,}")
        dashboard.append(f"  Cache Size: {stats['cache_size_mb']} MB / {stats['max_cache_size_mb']} MB")
        dashboard.append(f"  Utilization: {stats['cache_utilization_percent']}%")
        dashboard.append(f"  Average Image Size: {stats['avg_image_size_kb']} KB")
        dashboard.append("")
        
        # URL Normalization
        dashboard.append("🔗 URL NORMALIZATION:")
        dashboard.append(f"  Normalized Groups: {stats['normalized_url_groups']:,}")
        dashboard.append(f"  URLs per Group: {stats['avg_urls_per_group']}")
        dashboard.append(f"  Success Rate: {stats['normalization_success_rate']}%")
        dashboard.append(f"  Collisions Handled: {stats['url_collision_count']:,}")
        dashboard.append("")
        
        # Miss Analysis
        if stats['cache_miss_reasons']:
            dashboard.append("❌ CACHE MISS BREAKDOWN:")
            for reason, count in sorted(stats['cache_miss_reasons'].items(), key=lambda x: x[1], reverse=True):
                percentage = (count / stats['cache_misses'] * 100) if stats['cache_misses'] > 0 else 0
                if reason == 'prefix_match_recovery':
                    dashboard.append(f"  🔍 {reason.replace('_', ' ').title()}: {count:,} ({percentage:.1f}%) - RECOVERED!")
                else:
                    dashboard.append(f"  {reason.replace('_', ' ').title()}: {count:,} ({percentage:.1f}%)")
            dashboard.append("")
        
        # Cache Age Distribution
        dashboard.append("⏰ CACHE AGE DISTRIBUTION:")
        for age_range, count in stats['cache_age_distribution'].items():
            if count > 0:
                percentage = (count / stats['cached_images'] * 100) if stats['cached_images'] > 0 else 0
                dashboard.append(f"  {age_range}: {count:,} images ({percentage:.1f}%)")
        dashboard.append("")
        
        # Disk Persistence
        dashboard.append("💿 DISK PERSISTENCE:")
        if stats['disk_persistence_enabled']:
            dashboard.append(f"  Status: ✅ ENABLED")
            dashboard.append(f"  Directory: {stats['disk_cache_dir']}")
        else:
            dashboard.append(f"  Status: ❌ DISABLED")
        dashboard.append("")
        
        # Problems and Recommendations
        if stats['needs_attention']:
            dashboard.append("⚠️  ISSUES DETECTED:")
            for problem in stats['needs_attention']:
                dashboard.append(f"  • {problem}")
            dashboard.append("")
        
        if stats['recommendations']:
            dashboard.append("💡 RECOMMENDATIONS:")
            for rec in stats['recommendations']:
                dashboard.append(f"  • {rec}")
            dashboard.append("")
        
        # Configuration
        dashboard.append("⚙️  CONFIGURATION:")
        dashboard.append(f"  TTL: {stats['cache_ttl_hours']} hours")
        dashboard.append(f"  Max Size: {stats['max_cache_size_mb']} MB")
        dashboard.append(f"  Disk Persistence: {'✅' if stats['disk_persistence_enabled'] else '❌'}")
        
        return "\n".join(dashboard)