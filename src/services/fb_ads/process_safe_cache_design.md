# Process-Safe Cache Solution Design

## Executive Summary

This design document outlines a robust process-safe caching solution for the BrowserImageCacheExtractor that will work seamlessly in both the current asyncio-based architecture and potential future multiprocessing implementations.

## Current Architecture Analysis

### Key Findings

1. **Concurrency Model**: The system currently uses asyncio concurrency, NOT multiprocessing
   - JobOrchestrationService manages concurrent firms using `asyncio.Semaphore`
   - All firms run in the same process with async/await coordination
   - No process boundaries to cross currently

2. **Current Cache Implementation**:
   - Uses standard Python `dict` with `asyncio.Lock` for thread safety
   - Stores 83+ cached images in memory
   - Has disk persistence for cache survival across restarts
   - Works perfectly in current single-process architecture

3. **Future Risk**: If multiprocessing is added later, the current dict-based cache won't work across process boundaries

## Proposed Solution: Hybrid Cache Architecture

### Design Principles

1. **Backward Compatibility**: Must work identically in current asyncio architecture
2. **Forward Compatibility**: Must scale to multiprocessing without code changes
3. **Performance**: Minimal overhead in single-process mode
4. **Reliability**: Graceful fallback to disk cache if shared memory fails

### Architecture Components

```python
class ProcessSafeCacheManager:
    """
    Manages cache storage with automatic selection of best available backend:
    1. Shared memory dict (multiprocessing.Manager) if available
    2. Local dict with asyncio locks (current behavior) 
    3. Disk-based fallback for cross-process sharing
    """
    
    def __init__(self):
        self._cache_backend = self._initialize_backend()
        self._lock = asyncio.Lock()  # Always needed for async safety
        self._process_lock = None  # Only if multiprocessing detected
```

### Implementation Strategy

#### Phase 1: Detection Layer
```python
def _detect_multiprocessing_context(self) -> bool:
    """Detect if we're running in a multiprocessing context."""
    # Check 1: Is multiprocessing.current_process() not MainProcess?
    # Check 2: Is there a shared Manager in the environment?
    # Check 3: Check for process pool executor in config
    return is_multiprocessing_active
```

#### Phase 2: Backend Selection
```python
def _initialize_backend(self) -> CacheBackend:
    """Select appropriate cache backend based on context."""
    if self._detect_multiprocessing_context():
        try:
            # Try to get shared manager from environment
            manager = self._get_or_create_manager()
            return SharedDictBackend(manager.dict())
        except Exception as e:
            self.log_warning(f"Shared memory unavailable: {e}")
            return DiskCacheBackend(self._disk_cache_dir)
    else:
        # Single process - use fast local dict
        return LocalDictBackend({})
```

#### Phase 3: Unified Interface
```python
class CacheBackend(ABC):
    """Abstract base for cache backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[CachedImageResource]:
        pass
    
    @abstractmethod  
    async def set(self, key: str, value: CachedImageResource) -> None:
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> None:
        pass
    
    @abstractmethod
    async def keys(self) -> List[str]:
        pass
```

### Cache Backends

#### 1. LocalDictBackend (Current Behavior)
- Uses standard Python dict
- Fastest performance in single-process mode
- Protected by asyncio.Lock
- Zero overhead

#### 2. SharedDictBackend (Multiprocessing)
- Uses multiprocessing.Manager().dict()
- Automatically shared across processes
- Slight performance overhead
- Seamless cross-process access

#### 3. DiskCacheBackend (Fallback)
- SQLite or file-based storage
- Works across any architecture
- Slower but reliable
- Already implemented for persistence

### Initialization Flow

```python
class BrowserImageCacheExtractor:
    def __init__(self, ..., process_safe_mode: bool = None):
        # Auto-detect if not specified
        if process_safe_mode is None:
            process_safe_mode = self._should_use_process_safe_cache()
        
        # Initialize appropriate cache manager
        self._cache_manager = ProcessSafeCacheManager(
            enable_process_safe=process_safe_mode,
            disk_cache_dir=self._disk_cache_dir,
            logger=self.logger
        )
        
        # Rest of initialization remains the same
```

### Manager Lifecycle

```python
class ManagerRegistry:
    """Singleton to manage shared Manager instance."""
    _instance = None
    _manager = None
    
    @classmethod
    def get_or_create_manager(cls):
        if cls._manager is None:
            cls._manager = multiprocessing.Manager()
        return cls._manager
    
    @classmethod
    def cleanup(cls):
        if cls._manager:
            cls._manager.shutdown()
            cls._manager = None
```

### Integration Points

1. **Container Initialization**:
```python
# In FbAdsContainer
browser_image_cache_extractor = providers.Singleton(
    BrowserImageCacheExtractor,
    logger=logger,
    config=config,
    process_safe_mode=config.get("enable_process_safe_cache", None),
    shared_manager=providers.Resource(ManagerRegistry.get_or_create_manager)
)
```

2. **Job Runner Integration**:
```python
# In JobRunnerService
async def initialize_shared_resources(self):
    """Initialize resources that need to be shared across processes."""
    if self.config.get("enable_multiprocessing", False):
        # Ensure manager is started before jobs
        ManagerRegistry.get_or_create_manager()
```

3. **Cleanup Integration**:
```python
# In main shutdown
def cleanup_resources():
    """Clean shutdown of shared resources."""
    ManagerRegistry.cleanup()
```

## Implementation Plan

### Step 1: Create Cache Backend Abstraction
1. Define CacheBackend interface
2. Implement LocalDictBackend (extract current logic)
3. Add comprehensive tests

### Step 2: Implement Process-Safe Backends
1. Create SharedDictBackend with Manager.dict()
2. Enhance DiskCacheBackend for cross-process use
3. Add backend selection logic

### Step 3: Integrate ProcessSafeCacheManager
1. Replace direct dict usage in BrowserImageCacheExtractor
2. Add configuration options
3. Maintain backward compatibility

### Step 4: Testing Strategy
1. Unit tests for each backend
2. Integration tests with mock multiprocessing
3. Performance benchmarks
4. Stress test with 83+ cached images

### Step 5: Rollout Plan
1. Feature flag: `enable_process_safe_cache`
2. Default to False (current behavior)
3. Enable in test environments first
4. Gradual rollout with monitoring

## Performance Considerations

### Single-Process Mode (Current)
- Zero overhead - uses LocalDictBackend
- Identical performance to current implementation
- No changes to async locking

### Multi-Process Mode (Future)
- ~10-15% overhead for shared memory access
- Disk fallback adds ~50ms per operation
- But enables true parallel processing gains

### Memory Usage
- Shared memory: Same as current (500MB max)
- Disk cache: Configurable, already implemented
- No memory duplication across processes

## Risk Mitigation

1. **Fallback Strategy**: If shared memory fails, fall back to disk cache
2. **Health Checks**: Monitor cache backend health
3. **Metrics**: Track hit rates per backend type
4. **Gradual Rollout**: Feature flag for easy rollback

## Success Metrics

1. **Compatibility**: Zero failures in current async mode
2. **Performance**: <5% overhead in single-process mode
3. **Reliability**: 99.9% cache availability
4. **Scalability**: Support 10+ concurrent processes

## Conclusion

This design provides a robust, future-proof caching solution that:
- Works perfectly in the current asyncio architecture
- Scales seamlessly to multiprocessing when needed
- Maintains excellent performance characteristics
- Provides multiple fallback options for reliability

The implementation can be done incrementally without disrupting current operations, making it a safe and practical solution for evolving system requirements.