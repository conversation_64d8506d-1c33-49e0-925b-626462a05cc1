import asyncio
import logging
import time
from typing import Any

from rich.console import Console
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
    TimeRemainingColumn,
)

from src.services.fb_ads.failed_firms_manager import FailedFirmsManager
from src.services.fb_ads.jobs.job_models import ProcessFirmJob
from src.services.fb_ads.jobs.job_runner_service import JobRunnerService

# Type hinting for dependencies passed through global_dependencies
# from src.services.fb_ads.api_client import FacebookAPIClient
# from src.services.fb_ads.image_handler import ImageHandler
# ... etc.


class JobOrchestrationService:
    """
    Orchestrates the creation and concurrent execution of ProcessFirmJob instances.
    This service manages a pool of workers (via an asyncio.Semaphore) to process
    multiple firms' ad data in parallel, delegating the execution of each individual
    job to the JobRunnerService.
    """

    def __init__(
        self,
        config: dict[str, Any] = None,
        job_runner_service: JobRunnerService = None,
        logger: logging.Logger = None,
        failed_firms_manager: FailedFirmsManager | None = None,
    ):
        """
        Initializes the JobOrchestrationService.

        Args:
            config: Application configuration dictionary. Expected to contain
                    'fb_ads_job_workers' for concurrency control.
            job_runner_service: An instance of JobRunnerService that will execute individual jobs.
            logger: Logger instance to use for this service.
            failed_firms_manager: Optional manager for tracking failed firms
        """
        self.config = config
        self.job_runner_service = job_runner_service
        self.logger = logger
        self.failed_firms_manager = failed_firms_manager

        # CRITICAL: Validate LLM availability before proceeding
        self._validate_llm_availability()
        
        # CRITICAL: Use correct configuration keys for concurrency control
        # Check both top-level and performance section for max_concurrent_firms
        performance_config = self.config.get("performance", {})
        max_concurrent_firms_value = performance_config.get("max_concurrent_firms") or self.config.get("max_concurrent_firms", 1)
        num_workers_value = self.config.get("num_workers", 1)
        
        # Ensure values are integers (YAML config may load as strings)
        max_concurrent_firms = int(max_concurrent_firms_value)
        num_workers = int(num_workers_value)
        enable_concurrent_processing = self.config.get("enable_concurrent_processing", True)
        run_parallel = self.config.get("run_parallel", True)
        
        # Force single worker if concurrent processing is disabled
        if not enable_concurrent_processing or not run_parallel:
            self.num_workers = 1
            self.logger.info(f"🔧 Concurrent processing disabled - forcing single worker mode")
        else:
            # Use the minimum of max_concurrent_firms and num_workers
            self.num_workers = min(max_concurrent_firms, num_workers)
        
        # Ensure at least 1 worker
        if self.num_workers <= 0:
            self.logger.warning(f"Invalid worker count {self.num_workers}. Setting to 1.")
            self.num_workers = 1
            
        # Log the actual configuration being used
        self.logger.info(f"🔧 JobOrchestrationService configured:")
        self.logger.info(f"   num_workers: {self.num_workers}")
        self.logger.info(f"   max_concurrent_firms: {max_concurrent_firms}")
        self.logger.info(f"   enable_concurrent_processing: {enable_concurrent_processing}")
        self.logger.info(f"   run_parallel: {run_parallel}")
        
        if self.num_workers == 1:
            self.logger.info(f"✅ SINGLE FIRM MODE: Processing one firm at a time")

        # Initialize console for Rich output
        self.console = Console()
        
        # 🎯 FIRM-LEVEL DEDUPLICATION: Track active firms to prevent multiple browsers per firm
        self._active_firms: set[str] = set()  # Track firms currently being processed
        self._firm_locks: dict[str, asyncio.Lock] = {}  # Per-firm locks for synchronization
        self._firm_locks_mutex = asyncio.Lock()  # Protects _firm_locks dict access
        
        self.logger.info("🔒 Firm-level deduplication initialized - ONE browser per law firm guaranteed")
        
        # 🎯 BROWSER SESSION TRACKING: Monitor browser instances per firm
        self._active_browser_sessions: dict[str, str] = {}  # firm_identifier -> session_info
        self._browser_session_lock = asyncio.Lock()  # Protects browser session tracking

    async def process_firms_as_jobs(
        self,
        firms_to_process: list[dict[str, Any]],
        current_process_date: str,
        global_dependencies: dict[str, Any],
    ) -> list[ProcessFirmJob]:
        """
        Creates ProcessFirmJob instances for a list of firms and processes them concurrently.

        Args:
            firms_to_process: A list of firm data dictionaries. Each dictionary is expected
                              to have at least 'ID' (for firm_id) and 'Name' (for firm_name).
            current_process_date: The ISO date string (YYYYMMDD) for which this batch of
                                  processing is being run. This is passed to each job.
            global_dependencies: A dictionary holding shared service instances and configurations
                                 needed by the JobRunnerService for executing each job. This typically
                                 includes clients (API, AI), handlers (image), other services (DB),
                                 and repositories.
        Returns:
            A list of ProcessFirmJob objects, each updated with its final status,
            metrics, and any results or errors encountered during processing.
        """
        if not firms_to_process:
            self.logger.info(
                "JobOrchestrationService: No firms provided for processing."
            )
            return []

        # 🎯 INPUT VALIDATION: Check for duplicate firms in input data
        firm_identifiers_in_input = []
        for firm_data in firms_to_process:
            firm_id = str(firm_data.get("id", "")) if firm_data.get("id") else ""
            firm_name = str(firm_data.get("name", "")) if firm_data.get("name") else ""
            if firm_id and firm_name:
                firm_identifiers_in_input.append(f"{firm_id}_{firm_name}")
        
        duplicate_firms = len(firm_identifiers_in_input) - len(set(firm_identifiers_in_input))
        if duplicate_firms > 0:
            self.logger.warning(f"🚨 INPUT DATA CONTAINS {duplicate_firms} DUPLICATE FIRMS - will be filtered to prevent multiple browsers!")
        
        self.logger.info(f"📊 INPUT VALIDATION: {len(firms_to_process)} firms provided, {len(set(firm_identifiers_in_input))} unique firms identified")

        jobs: list[ProcessFirmJob] = []
        for firm_data in firms_to_process:
            firm_id_raw = firm_data.get("id")
            firm_name_raw = firm_data.get("name")

            # Ensure firm_id is always a string
            if isinstance(firm_id_raw, (list, tuple)):
                firm_id = str(firm_id_raw[0]) if firm_id_raw else None
                self.logger.warning(
                    f"JobOrchestrationService: firm_id was a {type(firm_id_raw).__name__} with {len(firm_id_raw)} items, using first item: {firm_id}"
                )
            else:
                firm_id = str(firm_id_raw) if firm_id_raw else None

            # Ensure firm_name is always a string (fix for SQL binding error)
            if isinstance(firm_name_raw, (list, tuple)):
                firm_name = str(firm_name_raw[0]) if firm_name_raw else "Unknown"
                self.logger.warning(
                    f"JobOrchestrationService: firm_name was a {type(firm_name_raw).__name__} with {len(firm_name_raw)} items, using first item: {firm_name}"
                )
            else:
                firm_name = str(firm_name_raw) if firm_name_raw else None

            if not firm_id or not firm_name:
                self.logger.warning(
                    f"JobOrchestrationService: Skipping firm data due to missing id or name: {str(firm_data)[:100]}"
                )
                continue

            # Snapshot relevant parts of the main config for this job.
            # This ensures that if the main config changes mid-run (not typical), jobs use their initial config.
            job_config_snapshot = {
                "max_ad_pages": self.config.get("max_ad_pages", 50),
                "default_date_range_days": self.config.get(
                    "default_date_range_days", 14
                ),
                "rotate_proxy_per_page": self.config.get("rotate_proxy_per_page", True),
                "defer_image_processing": self.config.get(
                    "defer_image_processing", False
                ),
                "feature_flags": self.config.get("feature_flags", {}),
                # CRITICAL: Include camoufox config for profile cleaning
                "camoufox": self.config.get("camoufox", {}),
                # Add other job-relevant config flags as needed
            }

            # Ensure current_process_date is a string
            if isinstance(current_process_date, (list, tuple)):
                process_date_str = (
                    str(current_process_date[0]) if current_process_date else "20250113"
                )
                self.logger.warning(
                    f"JobOrchestrationService: current_process_date was a {type(current_process_date).__name__} with {len(current_process_date)} items, using first item: {process_date_str}"
                )
            else:
                process_date_str = (
                    str(current_process_date) if current_process_date else "20250113"
                )

            # 🎯 FIRM DEDUPLICATION: Check if this firm is already being processed
            firm_identifier = f"{firm_id}_{firm_name}"  # Use both ID and name for uniqueness
            if await self._is_firm_already_processing(firm_identifier):
                self.logger.warning(f"🚨 SKIPPING DUPLICATE FIRM: {firm_name} (ID: {firm_id}) - already being processed to prevent multiple browsers!")
                continue
                
            # Mark firm as active to prevent future duplicates
            if not await self._mark_firm_as_active(firm_identifier):
                self.logger.warning(f"🚨 FAILED to mark firm as active: {firm_name} (ID: {firm_id}) - skipping to prevent race condition!")
                continue

            job = ProcessFirmJob(
                firm_id=firm_id,
                firm_name=firm_name,
                firm_data=firm_data,
                config_snapshot=job_config_snapshot,
                current_process_date=process_date_str,
            )
            # Store firm identifier in job for cleanup later
            job.firm_identifier = firm_identifier
            jobs.append(job)

        if not jobs:
            self.logger.info(
                "JobOrchestrationService: No valid jobs were created from the provided firm list."
            )
            return []

        self.logger.info(
            f"JobOrchestrationService: Created {len(jobs)} jobs. Starting processing with up to {self.num_workers} concurrent workers."
        )

        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TextColumn("•"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TimeRemainingColumn(),
            console=self.console,
            transient=False,
        ) as progress:
            # Create main task for overall progress
            main_task = progress.add_task(
                f"[cyan]Processing {len(jobs)} law firms...[/cyan]", total=len(jobs)
            )

            # Track job statuses
            completed_jobs = 0
            failed_jobs = 0
            start_time = time.time()

            semaphore = asyncio.Semaphore(self.num_workers)
            
            # Browser startup delay to prevent proxy overload
            # Note: We use a small fixed delay instead of progressive delay to enable true parallel execution
            # The original progressive delay (0.8s * counter) would cause sequential startup
            browser_startup_delay = self.config.get("camoufox", {}).get("browser", {}).get("startup_delay", 0.8)
            startup_counter = 0
            startup_lock = asyncio.Lock()

            async def run_job_with_progress(
                job_instance: ProcessFirmJob,
            ) -> ProcessFirmJob:
                """Run a job and update progress."""
                # Apply small fixed startup delay to prevent proxy overload
                # Only apply delay for the first batch of workers to avoid thundering herd
                nonlocal startup_counter
                async with startup_lock:
                    if startup_counter < self.num_workers:
                        # Small fixed delay for first batch only
                        delay = 0.2  # 200ms fixed delay
                        self.logger.info(f"🕐 Browser startup delay: {delay:.1f}s for job {job_instance.job_id} ({job_instance.firm_name})")
                        await asyncio.sleep(delay)
                    startup_counter += 1
                
                result = await self._run_job_with_semaphore(
                    job_instance, semaphore, global_dependencies, progress
                )

                # Update counters
                nonlocal completed_jobs, failed_jobs
                if result.status == "COMPLETED":
                    completed_jobs += 1
                    # Remove successful firm from failed firms JSON
                    if self.failed_firms_manager and result.firm_id:
                        self.failed_firms_manager.remove_successful_firm(
                            current_process_date, result.firm_id
                        )
                else:
                    failed_jobs += 1

                # Update progress bar
                progress.update(main_task, advance=1)

                # Update description with current stats
                elapsed = time.time() - start_time
                avg_time_per_job = (
                    elapsed / (completed_jobs + failed_jobs)
                    if (completed_jobs + failed_jobs) > 0
                    else 0
                )
                remaining_jobs = len(jobs) - (completed_jobs + failed_jobs)
                eta_seconds = avg_time_per_job * remaining_jobs

                progress.update(
                    main_task,
                    description=f"[cyan]Processing law firms... [green]{completed_jobs} completed[/green], [red]{failed_jobs} failed[/red][/cyan]",
                )

                return result

            # Create tasks for all jobs
            tasks = [run_job_with_progress(job) for job in jobs]

            # Wait for all tasks to complete
            processed_jobs_results = await asyncio.gather(*tasks)

            # Final summary
            progress.update(
                main_task,
                description=f"[bold green]✓ Completed processing {len(jobs)} firms: {completed_jobs} successful, {failed_jobs} failed[/bold green]",
            )

        self.logger.info(
            f"JobOrchestrationService: All {len(jobs)} job processing tasks have concluded."
        )
        self.logger.info(
            f"JobOrchestrationService: Processing Summary - Success: {completed_jobs}, Failed: {failed_jobs} out of {len(processed_jobs_results)} jobs."
        )

        return processed_jobs_results

    async def _get_firm_lock(self, firm_identifier: str) -> asyncio.Lock:
        """Get or create a lock for the specified firm to ensure single browser per firm."""
        async with self._firm_locks_mutex:
            if firm_identifier not in self._firm_locks:
                self._firm_locks[firm_identifier] = asyncio.Lock()
                self.logger.debug(f"🔐 Created new lock for firm: {firm_identifier}")
            return self._firm_locks[firm_identifier]

    async def _is_firm_already_processing(self, firm_identifier: str) -> bool:
        """Check if a firm is already being processed to prevent duplicate browsers."""
        return firm_identifier in self._active_firms

    async def _mark_firm_as_active(self, firm_identifier: str) -> bool:
        """Mark a firm as active for processing. Returns True if successfully marked, False if already active."""
        if firm_identifier in self._active_firms:
            self.logger.warning(f"🚨 DUPLICATE FIRM DETECTED: {firm_identifier} is already being processed - SKIPPING to prevent multiple browsers!")
            return False
        
        self._active_firms.add(firm_identifier)
        self.logger.info(f"🎯 Marked firm as active: {firm_identifier} - browser session will be created")
        return True

    async def _mark_firm_as_completed(self, firm_identifier: str):
        """Mark a firm as completed and clean up its resources."""
        self._active_firms.discard(firm_identifier)
        
        # Clean up the firm's lock to prevent memory leaks
        async with self._firm_locks_mutex:
            if firm_identifier in self._firm_locks:
                del self._firm_locks[firm_identifier]
                self.logger.debug(f"🧹 Cleaned up lock for completed firm: {firm_identifier}")
        
        self.logger.info(f"✅ Firm processing completed and cleaned up: {firm_identifier}")

    async def _track_browser_session(self, firm_identifier: str, session_info: str):
        """Track an active browser session for a firm."""
        async with self._browser_session_lock:
            if firm_identifier in self._active_browser_sessions:
                existing_session = self._active_browser_sessions[firm_identifier]
                self.logger.error(f"🚨 MULTIPLE BROWSER SESSIONS DETECTED for {firm_identifier}!")
                self.logger.error(f"   Existing: {existing_session}")
                self.logger.error(f"   New: {session_info}")
                # Don't overwrite - this indicates a problem that needs investigation
                return False
            
            self._active_browser_sessions[firm_identifier] = session_info
            self.logger.info(f"🖥️ Browser session created for {firm_identifier}: {session_info}")
            return True

    async def _untrack_browser_session(self, firm_identifier: str):
        """Remove tracking for a browser session when it's cleaned up."""
        async with self._browser_session_lock:
            if firm_identifier in self._active_browser_sessions:
                session_info = self._active_browser_sessions.pop(firm_identifier)
                self.logger.info(f"🧹 Browser session cleaned up for {firm_identifier}: {session_info}")
                return True
            else:
                self.logger.warning(f"⚠️ Attempted to clean up browser session for {firm_identifier}, but no session was tracked")
                return False

    async def _run_job_with_semaphore(
        self,
        job: ProcessFirmJob,
        semaphore: asyncio.Semaphore,
        global_dependencies: dict[str, Any],
        progress: Progress | None = None,
    ) -> ProcessFirmJob:
        """
        A wrapper method to execute a single ProcessFirmJob under the control of an asyncio.Semaphore.
        This ensures that the number of concurrently running jobs does not exceed `self.num_workers`.

        Args:
            job: The ProcessFirmJob instance to execute.
            semaphore: The asyncio.Semaphore instance controlling concurrency.
            global_dependencies: Dictionary of shared dependencies to be passed to the JobRunnerService.

        Returns:
            The processed ProcessFirmJob instance, with its status and metrics updated.
        """
        async with semaphore:
            # 🔍 RESOURCE CHECK: Verify system resources before processing
            if hasattr(global_dependencies.get('orchestrator'), 'resource_monitor'):
                resource_monitor = global_dependencies['orchestrator'].resource_monitor
                if not resource_monitor.can_spawn_worker():
                    self.logger.warning(f"⏳ Resource exhaustion detected for job {job.job_id}. Waiting for resources...")
                    
                    # Wait for resources to become available (timeout after 5 minutes)
                    if not await resource_monitor.wait_for_resources(timeout_seconds=300):
                        self.logger.error(f"❌ Resource timeout for job {job.job_id}. Marking as failed.")
                        job.status = "failed"
                        job.error_message = "Resource exhaustion - system overloaded"
                        return job
                    
                    self.logger.info(f"✅ Resources available for job {job.job_id}. Proceeding...")
            
            # Create a sub-task in progress bar for this specific firm
            firm_task = None
            if progress:
                firm_task = progress.add_task(
                    f"[yellow]↳ {(job.firm_name or 'Unknown')[:50]}...[/yellow]",
                    total=3,  # 3 phases: fetch, process, save
                )

            self.logger.info(
                f"JobOrchestrationService: Worker acquired for job {job.job_id} ({job.firm_name or 'Unknown'}). Starting execution."
            )
            
            # 🔍 DIAGNOSTIC: Log job details at acquisition
            self.logger.info(
                f"🔍 DIAGNOSTIC - Job details at acquisition: job_id={job.job_id}, firm_id={job.firm_id}, firm_name={job.firm_name}"
            )
            
            try:
                # CRITICAL FIX: Capture job details in local variables to avoid closure issues
                # This ensures the callback uses the correct firm name even in concurrent execution
                captured_job_id = job.job_id
                captured_firm_id = job.firm_id
                captured_firm_name = job.firm_name
                
                # Create a callback function to update progress
                def update_firm_progress(phase: str, job_status: ProcessFirmJob):
                    if progress and firm_task is not None:
                        # 🔍 DIAGNOSTIC: Log callback invocation details
                        self.logger.info(
                            f"🔍 DIAGNOSTIC - Progress callback: phase={phase}, "
                            f"captured_firm_name={captured_firm_name}, "
                            f"job_status.firm_name={job_status.firm_name}, "
                            f"outer_job.firm_name={job.firm_name}"
                        )
                        
                        phase_map = {
                            "FETCHING_PAYLOADS": (
                                1,
                                "[yellow]↳ Fetching ads for {name}...[/yellow]",
                            ),
                            "PROCESSING_ADS": (
                                2,
                                "[cyan]↳ Processing ads for {name}...[/cyan]",
                            ),
                            "SAVING_TO_DB": (
                                3,
                                "[blue]↳ Saving to DB for {name}...[/blue]",
                            ),
                        }
                        if phase in phase_map:
                            completed, desc_template = phase_map[phase]
                            # CRITICAL FIX: Use captured firm name instead of outer job reference
                            firm_name_safe = (captured_firm_name or "Unknown")[:40]
                            progress.update(
                                firm_task,
                                completed=completed,
                                description=desc_template.format(name=firm_name_safe),
                            )

                # Add the progress callback to dependencies
                dependencies_with_progress = global_dependencies.copy()
                dependencies_with_progress["progress_callback"] = update_firm_progress
                # Remove default session manager to force new creation
                dependencies_with_progress.pop("session_manager", None)

                # CRITICAL: Create BRAND NEW firm-specific session manager for EACH firm
                session_manager = None  # Start with None to ensure we create new
                if "session_manager_factory" in dependencies_with_progress:
                    try:
                        # Use factory to create BRAND NEW session manager for this specific firm
                        factory = dependencies_with_progress["session_manager_factory"]
                        
                        # CRITICAL FIX: Merge job's config_snapshot with global config
                        # This ensures camoufox settings from job config are used
                        merged_config = dependencies_with_progress.get("config", {}).copy()
                        if hasattr(job, 'config_snapshot') and job.config_snapshot:
                            # Job config_snapshot takes precedence
                            merged_config.update(job.config_snapshot)
                            self.logger.info(f"🔧 Using job config_snapshot with camoufox settings: {job.config_snapshot.get('camoufox', {})}")
                        
                        session_manager = factory.create(
                            config=merged_config,
                            logger=dependencies_with_progress.get("logger"),
                            firm_id=job.firm_id,
                            fingerprint_manager=dependencies_with_progress.get("fingerprint_manager"),
                            proxy_manager=dependencies_with_progress.get("proxy_manager"),
                            law_firms_repository=dependencies_with_progress.get("law_firms_repository"),
                            browser_image_cache_extractor=dependencies_with_progress.get("browser_image_cache_extractor")
                        )
                        self.logger.info(f"🆕 Created BRAND NEW session manager for firm {job.firm_id}: {type(session_manager).__name__}")
                        
                        # 🎯 TRACK BROWSER SESSION: Monitor browser creation per firm
                        firm_identifier = getattr(job, 'firm_identifier', f"{job.firm_id}_{job.firm_name}")
                        session_info = f"{type(session_manager).__name__}[{id(session_manager)}]"
                        await self._track_browser_session(firm_identifier, session_info)
                        
                    except Exception as e:
                        self.logger.error(f"❌ CRITICAL: Failed to create firm-specific session manager: {e}")
                        self.logger.error(f"❌ CRITICAL: Failing job {job.job_id} due to session manager creation failure")
                        job.set_error(f"Critical failure: Unable to create session manager - {str(e)}")
                        return job
                
                if not session_manager:
                    self.logger.error(f"❌ CRITICAL: No session manager available for firm {job.firm_id}")
                    self.logger.error(f"❌ CRITICAL: Failing job {job.job_id} due to missing session manager")
                    job.set_error(f"Critical failure: No session manager available for firm {job.firm_id}")
                    return job
                
                # Update dependencies with the BRAND NEW firm-specific session manager
                dependencies_with_progress["session_manager"] = session_manager
                
                # 🔍 DIAGNOSTIC: Log repository injection status before job execution
                fb_archive_repo = dependencies_with_progress.get("fb_archive_repo")
                law_firms_repo = dependencies_with_progress.get("law_firms_repo")
                law_firms_repository = dependencies_with_progress.get("law_firms_repository")
                
                self.logger.info(
                    f"🔍 JobOrchestration DIAGNOSTIC - Repository injection for job {job.job_id}:"
                )
                self.logger.info(
                    f"  - fb_archive_repo: {'✅ Available' if fb_archive_repo else '❌ Missing'} (type: {type(fb_archive_repo).__name__})"
                )
                self.logger.info(
                    f"  - law_firms_repo: {'✅ Available' if law_firms_repo else '❌ Missing'} (type: {type(law_firms_repo).__name__})"
                )
                self.logger.info(
                    f"  - law_firms_repository: {'✅ Available' if law_firms_repository else '❌ Missing'} (type: {type(law_firms_repository).__name__ if law_firms_repository else 'None'})"
                )
                self.logger.info(f"  - All dependency keys: {list(dependencies_with_progress.keys())}")
                
                if not fb_archive_repo or not law_firms_repo:
                    self.logger.error(
                        f"❌ CRITICAL: Missing repositories in JobOrchestrationService for job {job.job_id}"
                    )
                    job.set_error("Critical repositories missing for database operations")
                    return job

                # Add firm-specific session manager to dependencies for GraphQL workflow
                dependencies_with_session = dependencies_with_progress.copy()
                dependencies_with_session["session_manager"] = session_manager
                
                # Extract required positional arguments from dependencies
                updated_job = await self.job_runner_service.run_job(
                    job,
                    dependencies_with_session["api_client"],
                    dependencies_with_session["image_handler"],
                    dependencies_with_session["ai_integrator"],
                    dependencies_with_session["ad_db_service"],
                    session_manager,  # Use firm-specific session manager
                    **{
                        k: v
                        for k, v in dependencies_with_session.items()
                        if k
                        not in [
                            "api_client",
                            "image_handler",
                            "ai_integrator",
                            "ad_db_service",
                            "session_manager",
                            "session_manager_factory",  # Exclude factory from kwargs
                        ]
                    },
                )

                # Update progress to show completion
                if progress and firm_task is not None:
                    if updated_job.status == "COMPLETED":
                        ads_count = updated_job.metrics.get("ads_fetched", 0)
                        duration = updated_job.metrics.get("duration_sec", 0) or 0
                        progress.update(
                            firm_task,
                            completed=3,
                            description=f"[green]✓ {(captured_firm_name or 'Unknown')[:40]} - {ads_count} ads in {duration:.1f}s[/green]",
                        )
                    else:
                        progress.update(
                            firm_task,
                            completed=3,
                            description=f"[red]✗ {(captured_firm_name or 'Unknown')[:40]} - Failed[/red]",
                        )

                return updated_job
            except Exception as e:
                self.logger.error(
                    f"JobOrchestrationService: Uncaught exception during execution of job {job.job_id} ({job.firm_name or 'Unknown'}): {e}",
                    exc_info=True,
                )
                job.set_error(
                    f"Orchestration level error during job execution: {str(e)}"
                )
                job.end_timer()  # Ensure timer is stopped on such errors

                # Update progress to show failure
                if progress and firm_task is not None:
                    progress.update(
                        firm_task,
                        completed=3,
                        description=f"[red]✗ {(captured_firm_name or 'Unknown')[:40]} - Error: {str(e)[:30]}[/red]",
                    )

                return job
            finally:
                # 🎯 FIRM CLEANUP: Mark firm as completed and clean up resources to allow future processing
                firm_identifier = getattr(job, 'firm_identifier', f"{job.firm_id}_{job.firm_name}")
                await self._untrack_browser_session(firm_identifier)  # Clean up browser session tracking
                await self._mark_firm_as_completed(firm_identifier)  # Mark firm as completed
                
                # CRITICAL: Do NOT clean up session manager here - it's still needed by job runner phases!
                # The job runner will handle cleanup after all phases are complete.
                # if 'session_manager' in dependencies_with_progress:
                #     session_manager = dependencies_with_progress.get('session_manager')
                #     if session_manager and hasattr(session_manager, 'cleanup'):
                #         try:
                #             await session_manager.cleanup()
                #             self.logger.info(f"✅ Cleaned up session manager for firm {job.firm_id}")
                #         except Exception as e:
                #             self.logger.error(f"⚠️ Session cleanup failed for {job.firm_id}: {e}")
                
                self.logger.info(
                    f"JobOrchestrationService: Worker released for job {job.job_id} ({job.firm_name or 'Unknown'}). Final status: {job.status}"
                )

                # Remove the firm task after a short delay so it's visible
                if progress and firm_task is not None:
                    await asyncio.sleep(0.5)  # Brief pause so status is visible
                    progress.remove_task(firm_task)

    def _validate_llm_availability(self):
        """
        Validate that required LLM services are available via job runner service.
        Exits immediately with status code 1 if no LLM is available.
        """
        import sys

        if not self.job_runner_service:
            error_msg = "❌ CRITICAL: No job runner service available for FB ads job orchestration"
            if self.logger:
                self.logger.error(error_msg)
            print(error_msg, file=sys.stderr)
            raise RuntimeError("No job runner service available for FB ads job orchestration")

        # Check if job runner has AI orchestrator
        ai_orchestrator = getattr(self.job_runner_service, "ai_orchestrator", None)
        if not ai_orchestrator:
            error_msg = "❌ CRITICAL: No AI orchestrator available in job runner for FB ads processing"
            if self.logger:
                self.logger.error(error_msg)
            print(error_msg, file=sys.stderr)
            raise RuntimeError("No AI orchestrator available in job runner for FB ads processing")

        # Check if AI orchestrator has working LLM services
        dependencies = getattr(ai_orchestrator, "_dependencies", {})
        deepseek_service = dependencies.get("deepseek")
        gpt4_service = dependencies.get("gpt4")

        deepseek_available = False
        gpt4_available = False

        if deepseek_service:
            deepseek_available = (
                hasattr(deepseek_service, "client")
                and deepseek_service.client is not None
            )

        if gpt4_service:
            gpt4_available = (
                hasattr(gpt4_service, "chat_completion")
                and gpt4_service.chat_completion is not None
            )

        if not (deepseek_available or gpt4_available):
            error_msg = "❌ CRITICAL: No working LLM service available for FB ads job orchestration"
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error(
                    "Check your .env file for API keys: DEEPSEEK_API_KEY or OPENAI_API_KEY"
                )
            print(error_msg, file=sys.stderr)
            print(
                "Check your .env file for API keys: DEEPSEEK_API_KEY or OPENAI_API_KEY",
                file=sys.stderr,
            )
            raise RuntimeError("No working LLM service available - check API keys in .env file")

        # Log successful validation
        if self.logger:
            self.logger.info(
                "✅ LLM availability validated for FB ads job orchestration"
            )
