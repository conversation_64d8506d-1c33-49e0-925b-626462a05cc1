import asyncio
import json
import logging
import os
import re
import traceback
from datetime import datetime
from typing import Any
from functools import wraps

from bs4 import BeautifulSoup  # For HTML stripping

from src.services.fb_ads.jobs.job_models import ProcessFirmJob
from src.utils.date import DateUtils

# Type hints for dependencies (actual instances passed in)
# from src.services.fb_ads.api_client import FacebookAPIClient
# from src.services.fb_ads.image_handler import ImageHandler
# from src.services.ai.ai_orchestrator import AIOrchestrator
# from src.services.fb_ads.ad_db_service import AdDBService
# from src.services.fb_ads.session_manager import FacebookSessionManager
# from src.services.fb_ads.local_image_queue import LocalImageQueue


def deprecated(reason):
    """Decorator to mark methods as deprecated."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logging.getLogger(__name__).warning(
                f"DEPRECATED: {func.__name__} is deprecated. {reason}"
            )
            return func(*args, **kwargs)
        return wrapper
    return decorator


class JobRunnerService:
    """
    Enhanced service responsible for executing a single ProcessFirmJob through its defined lifecycle phases.
    It encapsulates the logic for fetching, transforming, and saving ad data for one firm.

    Supports both legacy API and GraphQL workflows for Facebook Ad Library data retrieval.

    MIGRATION NOTES:
    - Original API-only methods preserved with @deprecated decorators
    - GraphQL integration merged from job_runner_service_graphql_integration.py
    - Use run_job() for unified workflow (auto-detects GraphQL vs API)
    - Legacy methods kept for reference and debugging
    """

    def __init__(
        self,
        config: dict[str, Any] = None,
        logger: logging.Logger = None,
        ai_orchestrator: Any = None,
        graphql_parser: Any = None,
    ):
        """
        Initializes the JobRunnerService with optional GraphQL support.

        Args:
            config: Application configuration dictionary.
            logger: Logger instance to use for this service.
            ai_orchestrator: AI orchestrator instance for job validation.
            graphql_parser: Optional GraphQL response parser for NDJSON processing.
        """
        self.config = config or {}
        self._logger = logger or logging.getLogger(__name__)
        self.ai_orchestrator = ai_orchestrator
        self.graphql_parser = graphql_parser

        # CRITICAL DEBUGGING: Log GraphQL parser injection status
        parser_type = type(graphql_parser).__name__ if graphql_parser else "None"
        self._logger.info(f"🔍 CRITICAL DEBUG: JobRunnerService initialized with graphql_parser={parser_type}")
        if graphql_parser:
            self._logger.info(f"✅ GraphQL parser available: {parser_type}")
        else:
            self._logger.error(f"❌ CRITICAL: GraphQL parser is NULL - this will prevent GraphQL processing!")

    async def run_job(
        self,
        job: ProcessFirmJob,
        api_client: Any,  # Actual type: FacebookAPIClient
        image_handler: Any,  # Actual type: ImageHandler
        ai_integrator: Any,  # Actual type: AIOrchestrator
        ad_db_service: Any,  # Actual type: AdDBService
        session_manager: Any,  # Actual type: FacebookSessionManager or CamoufoxSessionManager
        local_image_queue: Any | None = None,  # Actual type: LocalImageQueue
        # Allow passing other dependencies like repositories if AdDBService doesn't own them
        **kwargs: Any,  # For fb_archive_repo, law_firms_repo if passed directly
    ) -> ProcessFirmJob:
        """
        Enhanced job runner with GraphQL and API support.

        Executes a ProcessFirmJob through its lifecycle phases: fetching, transforming, and saving.
        Automatically detects and uses GraphQL capabilities when available, falling back to API.

        Args:
            job: The ProcessFirmJob instance to run.
            api_client: Instance of FacebookAPIClient for fetching ad data.
            image_handler: Instance of ImageHandler for image processing and S3 uploads.
            ai_integrator: Instance of AIOrchestrator for AI-based data enhancement.
            ad_db_service: Instance of AdDBService for database operations.
            session_manager: Instance of FacebookSessionManager or CamoufoxSessionManager.
            local_image_queue: Optional instance of LocalImageQueue if deferring image text extraction.
            **kwargs: Additional dependencies, potentially including 'fb_archive_repo' and 'law_firms_repo'.
                      These are passed to phases that might need them (e.g., save phase).

        Returns:
            The job instance, updated with its final status, metrics, and any results or errors.
        """
        # Check if GraphQL is available and session manager supports it
        if self._should_use_graphql(session_manager):
            self._logger.info(
                f"🚀 JobRunner: Using GraphQL workflow for job {job.job_id} for firm {job.firm_name or 'Unknown'} ({job.firm_id})"
            )
            return await self.run_job_with_graphql(
                job, session_manager, api_client, image_handler,
                ai_integrator, ad_db_service, local_image_queue=local_image_queue, **kwargs
            )
        else:
            self._logger.info(
                f"🔧 JobRunner: Using legacy API workflow for job {job.job_id} for firm {job.firm_name or 'Unknown'} ({job.firm_id})"
            )
            return await self._run_job_legacy(
                job, api_client, image_handler, ai_integrator,
                ad_db_service, session_manager, local_image_queue, **kwargs
            )

    @deprecated("Use run_job() method which auto-detects GraphQL vs API")
    async def _run_job_legacy(
        self,
        job: ProcessFirmJob,
        api_client: Any,  # Actual type: FacebookAPIClient
        image_handler: Any,  # Actual type: ImageHandler
        ai_integrator: Any,  # Actual type: AIOrchestrator
        ad_db_service: Any,  # Actual type: AdDBService
        session_manager: Any,  # Actual type: FacebookSessionManager
        local_image_queue: Any | None = None,  # Actual type: LocalImageQueue
        **kwargs: Any,  # For fb_archive_repo, law_firms_repo if passed directly
    ) -> ProcessFirmJob:
        """
        DEPRECATED: Legacy job execution method using API-only workflow.
        Preserved for reference and fallback when GraphQL is not available.

        Returns:
            The job instance, updated with its final status, metrics, and any results or errors.
        """
        self._logger.info(
            f"JobRunner (Legacy): Starting job {job.job_id} for firm {job.firm_name or 'Unknown'} ({job.firm_id})"
        )
        self._logger.info(f"🚀 LEGACY PATH: Job execution using API workflow (not GraphQL)")

        # CRITICAL FIX: Update ImageHandler with firm-specific session manager
        if image_handler and session_manager:
            old_session = type(image_handler.session_manager).__name__ if image_handler.session_manager else "None"
            new_session = type(session_manager).__name__
            self._logger.info(f"🔧 CRITICAL FIX: Updating ImageHandler session manager: {old_session} → {new_session}")
            image_handler.session_manager = session_manager
            self._logger.info(f"✅ ImageHandler now has firm-specific session manager for image downloads")

        job.start_timer()

        # Consolidate dependencies for easy passing to phase methods
        dependencies = {
            "api_client": api_client,
            "image_handler": image_handler,
            "ai_integrator": ai_integrator,
            "ad_db_service": ad_db_service,
            "session_manager": session_manager,
            "local_image_queue": local_image_queue,
            "logger": self._logger,  # Provide logger to phases
            "fb_archive_repo": kwargs.get("fb_archive_repo"),  # Extract from kwargs
            "law_firms_repo": kwargs.get("law_firms_repo"),  # Extract from kwargs
        }

        # Extract progress callback if provided
        progress_callback = kwargs.get("progress_callback")

        try:
            # Phase 1: Fetch Ad Payloads
            job.update_status("FETCHING_PAYLOADS")
            if progress_callback:
                progress_callback("FETCHING_PAYLOADS", job)
            await self._phase_fetch_ad_payloads_legacy(job, dependencies)
            if job.status == "ERROR":
                self._logger.error(
                    f"Job {job.job_id} failed in FETCHING_PAYLOADS phase: {job.error_message or 'Unknown error'}"
                )
                return job

            # Phase 2: Transform Ad Data
            job.update_status("PROCESSING_ADS")
            if progress_callback:
                progress_callback("PROCESSING_ADS", job)
            self._logger.info(f"🔄 Phase 2 STARTING: Transform Ad Data for job {job.job_id} - firm {job.firm_name or 'Unknown'}")
            await self._phase_transform_ad_data(job, dependencies)
            self._logger.info(f"✅ Phase 2 COMPLETED: Transform Ad Data for job {job.job_id}")
            if job.status == "ERROR":
                self._logger.error(
                    f"Job {job.job_id} failed in PROCESSING_ADS phase: {job.error_message or 'Unknown error'}"
                )
                return job

            # Phase 3: Save to Database
            job.update_status("SAVING_TO_DB")
            if progress_callback:
                progress_callback("SAVING_TO_DB", job)
            self._logger.info(f"💾 Phase 3 STARTING: Save to Database for job {job.job_id} - firm {job.firm_name or 'Unknown'}")
            await self._phase_save_to_database(job, dependencies)
            self._logger.info(f"✅ Phase 3 COMPLETED: Save to Database for job {job.job_id}")
            if job.status == "ERROR":
                self._logger.error(
                    f"Job {job.job_id} failed in SAVING_TO_DB phase: {job.error_message or 'Unknown error'}"
                )
                return job

            job.update_status("COMPLETED")
            self._logger.info(f"🏁 Phase 4: Final Status Update - job {job.job_id} marked as COMPLETED")

            # Log comprehensive job summary with all metrics
            self._logger.info(
                f"🎉🎉 COMPLETE SUCCESS: JobRunner finished ALL PHASES for job {job.job_id} - "
                f"Firm: {job.firm_name or 'Unknown'} ({job.firm_id}) ✅✅ TOTAL SUCCESS"
            )
            self._logger.info(
                f"📊 Job Summary - Ads fetched: {job.metrics.get('ads_fetched', 0)}, "
                f"Ads processed: {job.metrics.get('ads_processed_in_transform_phase', 0)}, "
                f"DB operations: {job.metrics.get('db_items_processed_for_save', 0)} "
                f"(inserts: {job.metrics.get('db_inserts', 0)}, updates: {job.metrics.get('db_updates', 0)})"
            )

            # Log validation metrics if any
            ads_skipped_wrong_page = job.metrics.get("ads_skipped_wrong_page_id", 0)
            ads_skipped_terms = job.metrics.get("ads_skipped_by_terms", 0)
            if ads_skipped_wrong_page > 0 or ads_skipped_terms > 0:
                self._logger.info(
                    f"📊 Validation Summary - Page ID mismatches: {ads_skipped_wrong_page}, "
                    f"Skip terms: {ads_skipped_terms}"
                )

        except Exception as e:
            self._logger.error(
                f"JobRunner: Unhandled exception in job {job.job_id} for firm {job.firm_name or 'Unknown'}: {e}",
                exc_info=True,
            )
            job.set_error(f"Unhandled job execution error: {str(e)}")
        finally:
            job.end_timer()

            # CRITICAL FIX: Clear image cache after advertiser processing completes (Legacy)
            # This ensures no cross-advertiser cache contamination
            if session_manager and hasattr(session_manager, 'image_cache_extractor') and session_manager.image_cache_extractor:
                try:
                    await session_manager.image_cache_extractor.clear_cache()
                    self._logger.info(f"🧹 CACHE CLEARED (Legacy): Completed advertiser {job.firm_name} ({job.firm_id}) - cache reset for next advertiser")
                except Exception as cache_error:
                    self._logger.error(f"⚠️ Failed to clear image cache after legacy job completion: {cache_error}")

            # CRITICAL: Clean up session manager to prevent state contamination
            if session_manager and hasattr(session_manager, 'cleanup'):
                try:
                    await session_manager.cleanup()
                    self._logger.info(f"✅ Cleaned up session manager for job {job.job_id}")
                except Exception as cleanup_error:
                    self._logger.error(f"⚠️ Failed to cleanup session manager: {cleanup_error}")

            status_indicator = "🎉 SUCCESS" if job.status == "COMPLETED" else "⚠️" if job.status == "ERROR" else "ℹ️"
            self._logger.info(
                f"{status_indicator} JobRunner: Finished job {job.job_id} for firm {job.firm_name or 'Unknown'}. "
                f"Status: {job.status}, Duration: {job.metrics.get('duration_sec', 0):.2f}s"
            )

        return job

    def _should_use_graphql(self, session_manager=None) -> bool:
        """
        Determine if GraphQL capture should be used based on configuration and session manager capability.

        Args:
            session_manager: The session manager instance to check for GraphQL support

        Returns:
            bool: True if GraphQL should be used
        """
        # CRITICAL: Only use GraphQL if session manager actually supports it
        has_graphql_support = session_manager is not None and hasattr(session_manager, 'capture_graphql_responses')

        config_enabled = (
            self.graphql_parser is not None
            and self.config.get('use_graphql_capture', True)
            and self.config.get('feature_flags', {}).get('graphql_enabled', True)
        )

        result = has_graphql_support and config_enabled

        if session_manager:
            session_type = type(session_manager).__name__
            self._logger.info(f"🔍 GraphQL decision: session_manager={session_type}, has_support={has_graphql_support}, config_enabled={config_enabled}, result={result}")

        return result

    async def run_job_with_graphql(
        self,
        job: ProcessFirmJob,
        session_manager: Any,  # CamoufoxSessionManager
        api_client: Any = None,  # FacebookAPIClient (fallback)
        image_handler: Any = None,
        ai_integrator: Any = None,
        ad_db_service: Any = None,
        **kwargs
    ) -> ProcessFirmJob:
        """
        Execute a job with GraphQL capture capability.

        Args:
            job: ProcessFirmJob instance
            session_manager: Camoufox session manager for GraphQL capture
            api_client: Legacy API client (fallback)
            image_handler: Image processing service
            ai_integrator: AI enhancement service
            ad_db_service: Database service

        Returns:
            Updated job with results
        """
        self._logger.info(
            f"🚀 Starting GraphQL-enabled job {job.job_id} for firm {job.firm_name or 'Unknown'} ({job.firm_id})"
        )
        self._logger.info(f"🌐 GRAPHQL PATH: Job execution using GraphQL capture workflow")

        # CRITICAL FIX: Update ImageHandler with firm-specific session manager
        if image_handler and session_manager:
            old_session = type(image_handler.session_manager).__name__ if image_handler.session_manager else "None"
            new_session = type(session_manager).__name__
            self._logger.info(f"🔧 CRITICAL FIX: Updating ImageHandler session manager: {old_session} → {new_session}")
            image_handler.session_manager = session_manager
            self._logger.info(f"✅ ImageHandler now has firm-specific session manager for image downloads")

        job.start_timer()

        # Create bandwidth logger for this advertiser
        bandwidth_logger = None
        if self.config.get('enable_bandwidth_logging', True):
            try:
                from ..advertiser_bandwidth_logger import AdvertiserBandwidthLogger
                data_dir = self.config.get('data_dir', './data')
                bandwidth_logger = AdvertiserBandwidthLogger(
                    firm_id=job.firm_id,
                    page_name=job.firm_name or 'unknown',
                    base_dir=data_dir,
                    logger=self._logger
                )

                # Set bandwidth logger on session manager
                if hasattr(session_manager, 'set_advertiser_bandwidth_logger'):
                    session_manager.set_advertiser_bandwidth_logger(bandwidth_logger)
                    self._logger.info(f"📊 Bandwidth logging enabled for {job.firm_name} ({job.firm_id})")

                # Store on job for later use
                job.bandwidth_logger = bandwidth_logger
            except Exception as e:
                self._logger.warning(f"Failed to create bandwidth logger: {e}")

        # Consolidate dependencies
        dependencies = {
            "session_manager": session_manager,
            "api_client": api_client,
            "image_handler": image_handler,
            "ai_integrator": ai_integrator,
            "ad_db_service": ad_db_service,
            "fb_archive_repo": kwargs.get("fb_archive_repo"),  # CRITICAL: Extract from kwargs
            "law_firms_repo": kwargs.get("law_firms_repo"),    # CRITICAL: Extract from kwargs
            "logger": self._logger,
            **kwargs
        }

        try:
            # Phase 1: Fetch Ad Payloads using GraphQL or API
            job.update_status("FETCHING_PAYLOADS")
            await self._phase_fetch_ads_with_graphql(job, dependencies)

            if job.status == "ERROR":
                self._logger.error(f"❌ Job {job.job_id} failed in fetch phase: {job.error_message}")
                return job

            # Phase 2: Transform Ad Data (existing logic)
            job.update_status("PROCESSING_ADS")
            self._logger.info(f"🔄 Phase 2 STARTING (GraphQL): Transform Ad Data for job {job.job_id} - firm {job.firm_name or 'Unknown'}")
            await self._phase_transform_ad_data(job, dependencies)
            self._logger.info(f"✅ Phase 2 COMPLETED (GraphQL): Transform Ad Data for job {job.job_id}")

            if job.status == "ERROR":
                self._logger.error(f"❌ Job {job.job_id} failed in transform phase: {job.error_message}")
                return job

            # Phase 3: Save to Database (existing logic)
            job.update_status("SAVING_TO_DB")
            self._logger.info(f"💾 Phase 3 STARTING (GraphQL): Save to Database for job {job.job_id} - firm {job.firm_name or 'Unknown'}")
            await self._phase_save_to_database(job, dependencies)
            self._logger.info(f"✅ Phase 3 COMPLETED (GraphQL): Save to Database for job {job.job_id}")

            if job.status == "ERROR":
                self._logger.error(f"❌ Job {job.job_id} failed in save phase: {job.error_message}")
                return job

            job.update_status("COMPLETED")
            self._logger.info(f"🏁 Phase 4 (GraphQL): Final Status Update - job {job.job_id} marked as COMPLETED")

            # Log comprehensive job summary with all metrics
            self._logger.info(
                f"🎉 Job {job.job_id} completed successfully! "
                f"Summary - Firm: {job.firm_name or 'Unknown'} ({job.firm_id}), "
                f"Ads fetched: {job.metrics.get('ads_fetched', 0)}, "
                f"Ads processed: {job.metrics.get('ads_processed_in_transform_phase', 0)}, "
                f"DB operations: {job.metrics.get('db_items_processed_for_save', 0)} "
                f"(inserts: {job.metrics.get('db_inserts', 0)}, updates: {job.metrics.get('db_updates', 0)})"
            )

            # Log validation metrics if any
            ads_skipped_wrong_page = job.metrics.get("ads_skipped_wrong_page_id", 0)
            ads_skipped_terms = job.metrics.get("ads_skipped_by_terms", 0)
            if ads_skipped_wrong_page > 0 or ads_skipped_terms > 0:
                self._logger.info(
                    f"📊 Validation Summary - Page ID mismatches: {ads_skipped_wrong_page}, "
                    f"Skip terms: {ads_skipped_terms}"
                )

        except Exception as e:
            self._logger.error(f"❌ Unhandled exception in job {job.job_id}: {e}", exc_info=True)
            job.set_error(f"Job execution error: {str(e)}")
        finally:
            job.end_timer()

            # Finalize bandwidth logging
            if bandwidth_logger:
                try:
                    bandwidth_logger.finalize()
                    self._logger.info(f"📊 Bandwidth log saved to: {bandwidth_logger.get_log_file_path()}")

                    # Log summary
                    summary = bandwidth_logger.get_summary()
                    self._logger.info(
                        f"📊 Bandwidth summary for {job.firm_name}: "
                        f"{summary['total_requests']} requests, "
                        f"{summary['total_downloaded'] / (1024*1024):.1f} MB downloaded, "
                        f"{summary['ads_processed']} ads processed"
                    )
                except Exception as e:
                    self._logger.error(f"Failed to finalize bandwidth logging: {e}")

            # CRITICAL FIX: Clear image cache after advertiser processing completes
            # This ensures no cross-advertiser cache contamination
            if session_manager and hasattr(session_manager, 'image_cache_extractor') and session_manager.image_cache_extractor:
                try:
                    await session_manager.image_cache_extractor.clear_cache()
                    self._logger.info(f"🧹 CACHE CLEARED: Completed advertiser {job.firm_name} ({job.firm_id}) - cache reset for next advertiser")
                except Exception as cache_error:
                    self._logger.error(f"⚠️ Failed to clear image cache after job completion: {cache_error}")

            # CRITICAL: Clean up session manager to prevent state contamination
            if session_manager and hasattr(session_manager, 'cleanup'):
                try:
                    await session_manager.cleanup()
                    self._logger.info(f"✅ Cleaned up session manager for GraphQL job {job.job_id}")
                except Exception as cleanup_error:
                    self._logger.error(f"⚠️ Failed to cleanup session manager: {cleanup_error}")

        return job

    async def _phase_fetch_ads_with_graphql(
        self, job: ProcessFirmJob, dependencies: dict[str, Any]
    ) -> None:
        """
        Phase 1: Fetch ads using GraphQL NDJSON capture or fallback to API.
        """
        session_manager = dependencies["session_manager"]
        api_client = dependencies.get("api_client")
        logger = dependencies["logger"]

        # Check if GraphQL capture is enabled and available
        use_graphql_capture = self.config.get('use_graphql_capture', True)

        # CRITICAL DEBUGGING: Log decision factors
        parser_available = self.graphql_parser is not None
        session_manager_available = session_manager is not None
        session_manager_type = type(session_manager).__name__ if session_manager else "None"

        logger.info(f"🔍 CRITICAL DEBUG: GraphQL decision factors for job {job.job_id}:")
        logger.info(f"   ├─ use_graphql_capture: {use_graphql_capture}")
        logger.info(f"   ├─ graphql_parser available: {parser_available}")
        logger.info(f"   ├─ session_manager available: {session_manager_available}")
        logger.info(f"   ├─ session_manager type: {session_manager_type}")
        logger.info(f"   └─ GraphQL decision: {use_graphql_capture and parser_available and session_manager_available}")

        if use_graphql_capture and self.graphql_parser and session_manager:
            logger.info(f"🚀 TAKING GRAPHQL PATH: Job {job.job_id} using GraphQL NDJSON capture method")
            await self._fetch_ads_via_graphql(job, session_manager, logger)
        elif api_client:
            logger.warning(f"🔄 TAKING API FALLBACK PATH: Job {job.job_id} falling back to legacy API method")
            await self._fetch_ads_via_api_legacy(job, api_client, logger)
        else:
            logger.error(f"❌ NO VALID PATH: Job {job.job_id} has no valid fetch method available (GraphQL or API)")
            job.set_error("No valid fetch method available (GraphQL or API)")
            return

    async def _fetch_ads_via_graphql(
        self, job: ProcessFirmJob, session_manager: Any, logger: logging.Logger
    ) -> None:
        """
        Fetch ads using GraphQL NDJSON capture from Camoufox.
        """
        logger.info(
            f"🌐 Job {job.job_id}: Starting GraphQL capture for firm {job.firm_name} (ID: {job.firm_id})"
        )

        try:
            # Ensure valid session - reuse existing session if valid, create new only if needed
            # NOTE: Firm context is now set at session manager creation time in factory
            if session_manager.is_session_valid():
                logger.info(f"♻️ Job {job.job_id}: Reusing existing valid Camoufox session")
            else:
                logger.info(f"📱 Job {job.job_id}: Creating new Camoufox session")
                session_created = await session_manager.create_new_session()
                if not session_created:
                    job.set_error("Failed to create Camoufox session for GraphQL capture")
                    return
                logger.info(f"✅ Job {job.job_id}: Camoufox session created successfully")

            # Capture GraphQL responses
            logger.info(f"🔍 Job {job.job_id}: Capturing GraphQL responses from Facebook Ad Library...")
            capture_result = await session_manager.capture_graphql_responses(job.firm_id)

            if not capture_result.get('success'):
                error_msg = capture_result.get('error', 'Unknown GraphQL capture error')
                logger.error(f"❌ Job {job.job_id}: GraphQL capture failed: {error_msg}")
                job.set_error(f"GraphQL capture failed: {error_msg}")
                return

            responses = capture_result.get('responses', [])
            logger.info(f"📊 Job {job.job_id}: Captured {len(responses)} GraphQL responses")

            if not responses:
                logger.warning(f"⚠️ Job {job.job_id}: No GraphQL responses captured")
                job.raw_ad_groups = []
                job.metrics["ads_fetched"] = 0
                return

            # Process each response
            all_ads = []
            total_ads_found = 0

            for i, response_text in enumerate(responses):
                logger.info(f"🔍 Job {job.job_id}: Processing response {i+1}/{len(responses)} ({len(response_text):,} chars)")

                try:
                    # Parse NDJSON response
                    parsed_result = await self.graphql_parser.parse_ndjson_response(response_text)

                    ads_in_response = parsed_result.get('ads', [])
                    page_info = parsed_result.get('page_info', {})
                    errors = parsed_result.get('errors', [])

                    logger.info(f"📈 Job {job.job_id}: Response {i+1} - {len(ads_in_response)} ads found")

                    if page_info:
                        page_name = page_info.get('name', 'Unknown')
                        page_id = page_info.get('page_id', 'Unknown')
                        logger.info(f"📄 Job {job.job_id}: Page - {page_name} (ID: {page_id})")

                    if errors:
                        logger.warning(f"⚠️ Job {job.job_id}: {len(errors)} parsing errors in response {i+1}")
                        for error in errors[:3]:  # Log first 3 errors
                            logger.debug(f"   Parse error: {error}")

                    # Log sample ad structure for debugging
                    if ads_in_response:
                        sample_ad = ads_in_response[0]
                        ad_id = sample_ad.get('ad_archive_id', 'Unknown')
                        logger.debug(f"📝 Job {job.job_id}: Sample ad {ad_id} keys: {list(sample_ad.keys())}")

                        # Check for required fields
                        snapshot = sample_ad.get('snapshot', {})
                        if snapshot:
                            logger.debug(f"   Snapshot keys: {list(snapshot.keys())}")
                        else:
                            logger.warning(f"   Missing snapshot in ad {ad_id}")

                    # Add ads to collection
                    all_ads.extend(ads_in_response)
                    total_ads_found += len(ads_in_response)

                except Exception as parse_error:
                    logger.error(f"❌ Job {job.job_id}: Error parsing response {i+1}: {parse_error}")
                    logger.debug(f"   Response preview: {response_text[:200]}...")
                    continue

            # Convert to expected format (list of lists for compatibility)
            job.raw_ad_groups = [[ad] for ad in all_ads]  # Wrap each ad in a list
            job.metrics["ads_fetched"] = total_ads_found
            job.metrics["graphql_responses_processed"] = len(responses)
            job.metrics["graphql_responses_with_ads"] = sum(1 for r in responses if any(ads for ads in all_ads))

            # CRITICAL DEBUGGING: Log job state after GraphQL processing
            logger.info(f"🎉 SUCCESS: Job {job.job_id} GraphQL capture completed!")
            logger.info(f"📊 CRITICAL DEBUG: Post-GraphQL job state:")
            logger.info(f"   ├─ job.raw_ad_groups length: {len(job.raw_ad_groups)}")
            logger.info(f"   ├─ job.metrics['ads_fetched']: {job.metrics['ads_fetched']}")
            logger.info(f"   ├─ job.status: {job.status}")
            logger.info(f"   ├─ Total responses: {len(responses)}")
            logger.info(f"   ├─ Responses with ads: {job.metrics.get('graphql_responses_with_ads', 0)}")
            logger.info(f"   ├─ Total ad groups: {len(job.raw_ad_groups)}")
            logger.info(f"   ├─ Total ads: {total_ads_found}")
            logger.info(f"   └─ Avg ads/response: {total_ads_found / len(responses) if responses else 0:.1f}")

            # Sample first ad for verification
            if job.raw_ad_groups and job.raw_ad_groups[0]:
                sample_ad = job.raw_ad_groups[0][0]  # First ad in first group
                ad_id = sample_ad.get('adArchiveID', sample_ad.get('ad_archive_id', 'Unknown'))
                logger.info(f"🔍 SAMPLE AD DEBUG: First ad ID={ad_id}, keys={list(sample_ad.keys())}")
            else:
                logger.warning(f"⚠️ NO ADS: job.raw_ad_groups is empty after GraphQL processing!")

        except Exception as e:
            logger.error(f"❌ Job {job.job_id}: GraphQL capture exception: {e}", exc_info=True)
            job.set_error(f"GraphQL capture failed: {str(e)}")

    async def _fetch_ads_via_api_legacy(
        self, job: ProcessFirmJob, api_client: Any, logger: logging.Logger
    ) -> None:
        """
        Fallback method: Fetch ads using legacy API client.
        """
        logger.info(f"🔄 Job {job.job_id}: Using legacy API fetch method")

        # Delegate to the original method
        dependencies = {
            "api_client": api_client,
            "session_manager": None,  # Not needed for API method
            "logger": logger
        }
        await self._phase_fetch_ad_payloads_legacy(job, dependencies)

    # ============================
    # DEPRECATED METHODS (Legacy Reference)
    # ============================

    @deprecated("Use run_job_with_graphql() or unified run_job() instead")
    async def _phase_fetch_ad_payloads(
        self, job: ProcessFirmJob, dependencies: dict[str, Any]
    ) -> None:
        """
        Phase 1: Fetches raw ad payloads from the Facebook API for the job's firm.
        Updates `job.raw_ad_groups` and `job.metrics["ads_fetched"]`.
        Sets `job.status` to "ERROR" and `job.error_message` on failure.
        """
        api_client = dependencies["api_client"]
        session_manager = dependencies["session_manager"]
        logger = dependencies["logger"]

        logger.info(
            f"Job {job.job_id} (Phase 1): Fetching ad payloads for firm {job.firm_name or 'Unknown'}"
        )

        all_ads_raw_groups: list[list[dict[str, Any]]] = []
        forward_cursor: str | None = None
        page_num = 1
        # Use config from the job's snapshot for consistency
        max_pages = int(job.config_snapshot.get("max_ad_pages", 50))

        # Determine date range for API query from job's context
        start_date_iso = DateUtils.get_date_before_n_days(
            int(job.config_snapshot.get("default_date_range_days", 14)),
            job.current_process_date,
        )
        end_date_iso = job.current_process_date

        try:
            while page_num <= max_pages:
                await session_manager.random_sleep(
                    short=True
                )  # Simulate human-like delay

                if session_manager.use_proxy and job.config_snapshot.get(
                    "rotate_proxy_per_page", True
                ):
                    session_manager.rotate_proxy()

                payload_response = await api_client.fetch_ads_page(
                    job.firm_id, start_date_iso, end_date_iso, forward_cursor
                )

                if payload_response is None:  # Indicates an error within the API client
                    last_error = getattr(
                        api_client, "last_error_type", "Unknown API Client Error"
                    )
                    job.set_error(
                        f"Failed to fetch ad payloads (page {page_num}). API client error: {last_error}"
                    )
                    return

                payload_data = payload_response.get("payload")
                if not isinstance(payload_data, dict):
                    logger.info(
                        f"Job {job.job_id} (Phase 1): ✅ Pagination complete - no more ads available at page {page_num}. Successfully ending fetch."
                    )
                    break

                results_data = payload_data.get("results")
                if not isinstance(
                    results_data, list
                ):  # API might return empty results, but 'results' key should be a list
                    logger.info(
                        f"Job {job.job_id} (Phase 1): ✅ Pagination complete - results format indicates end of data at page {page_num}. Successfully ending fetch."
                    )
                    break

                if results_data:  # Add valid groups (non-empty lists of dicts)
                    valid_groups = [
                        group
                        for group in results_data
                        if isinstance(group, list)
                        and group
                        and isinstance(group[0], dict)
                    ]
                    all_ads_raw_groups.extend(valid_groups)

                forward_cursor = payload_data.get("forwardCursor")
                if not forward_cursor or not payload_data.get(
                    "hasNextPage", bool(forward_cursor)
                ):
                    logger.info(f"Job {job.job_id} (Phase 1): ✅ All available pages fetched successfully.")
                    break

                page_num += 1
                if page_num > max_pages:
                    logger.info(
                        f"Job {job.job_id} (Phase 1): ✅ Reached configured max_ad_pages limit ({max_pages}). Fetch completed successfully."
                    )

            job.raw_ad_groups = all_ads_raw_groups
            # Correctly count individual ads, assuming each item in the inner list is an ad
            job.metrics["ads_fetched"] = sum(
                len(group) for group in all_ads_raw_groups if isinstance(group, list)
            )
            logger.info(
                f"🎉 SUCCESS: Job {job.job_id} (Phase 1) COMPLETED - Fetched {len(all_ads_raw_groups)} groups "
                f"containing {job.metrics['ads_fetched']} total ads for firm {job.firm_name or 'Unknown'}. ✅ PHASE 1 SUCCESS"
            )
        except Exception as e:
            logger.error(
                f"Job {job.job_id} (Phase 1): Exception during ad payload fetching for {job.firm_name or 'Unknown'}: {e}",
                exc_info=True,
            )
            job.set_error(f"Payload fetching failed: {str(e)}")

    async def _phase_fetch_ad_payloads_legacy(
        self, job: ProcessFirmJob, dependencies: dict[str, Any]
    ) -> None:
        """
        LEGACY: Phase 1 method - Fetches raw ad payloads from the Facebook API for the job's firm.
        This is the original implementation preserved for reference and fallback.
        Updates `job.raw_ad_groups` and `job.metrics["ads_fetched"]`.
        Sets `job.status` to "ERROR" and `job.error_message` on failure.
        """
        api_client = dependencies["api_client"]
        session_manager = dependencies.get("session_manager")
        logger = dependencies["logger"]

        logger.info(
            f"Job {job.job_id} (Phase 1 - Legacy): Fetching ad payloads for firm {job.firm_name or 'Unknown'}"
        )

        all_ads_raw_groups: list[list[dict[str, Any]]] = []
        forward_cursor: str | None = None
        page_num = 1
        # Use config from the job's snapshot for consistency
        max_pages = int(job.config_snapshot.get("max_ad_pages", 50))

        # Determine date range for API query from job's context
        start_date_iso = DateUtils.get_date_before_n_days(
            int(job.config_snapshot.get("default_date_range_days", 14)),
            job.current_process_date,
        )
        end_date_iso = job.current_process_date

        try:
            while page_num <= max_pages:
                if session_manager:
                    await session_manager.random_sleep(
                        short=True
                    )  # Simulate human-like delay

                    if session_manager.use_proxy and job.config_snapshot.get(
                        "rotate_proxy_per_page", True
                    ):
                        session_manager.rotate_proxy()

                payload_response = await api_client.fetch_ads_page(
                    job.firm_id, start_date_iso, end_date_iso, forward_cursor
                )

                if payload_response is None:  # Indicates an error within the API client
                    last_error = getattr(
                        api_client, "last_error_type", "Unknown API Client Error"
                    )
                    job.set_error(
                        f"Failed to fetch ad payloads (page {page_num}). API client error: {last_error}"
                    )
                    return

                payload_data = payload_response.get("payload")
                if not isinstance(payload_data, dict):
                    logger.info(
                        f"Job {job.job_id} (Phase 1 - Legacy): ✅ Pagination complete - no more ads available at page {page_num}. Successfully ending fetch."
                    )
                    break

                results_data = payload_data.get("results")
                if not isinstance(
                    results_data, list
                ):  # API might return empty results, but 'results' key should be a list
                    logger.info(
                        f"Job {job.job_id} (Phase 1 - Legacy): ✅ Pagination complete - results format indicates end of data at page {page_num}. Successfully ending fetch."
                    )
                    break

                if results_data:  # Add valid groups (non-empty lists of dicts)
                    valid_groups = [
                        group
                        for group in results_data
                        if isinstance(group, list)
                        and group
                        and isinstance(group[0], dict)
                    ]
                    all_ads_raw_groups.extend(valid_groups)

                forward_cursor = payload_data.get("forwardCursor")
                if not forward_cursor or not payload_data.get(
                    "hasNextPage", bool(forward_cursor)
                ):
                    logger.info(f"Job {job.job_id} (Phase 1 - Legacy): ✅ All available pages fetched successfully.")
                    break

                page_num += 1
                if page_num > max_pages:
                    logger.info(
                        f"Job {job.job_id} (Phase 1 - Legacy): ✅ Reached configured max_ad_pages limit ({max_pages}). Fetch completed successfully."
                    )

            job.raw_ad_groups = all_ads_raw_groups
            # Correctly count individual ads, assuming each item in the inner list is an ad
            job.metrics["ads_fetched"] = sum(
                len(group) for group in all_ads_raw_groups if isinstance(group, list)
            )
            logger.info(
                f"🎉 SUCCESS: Job {job.job_id} (Phase 1 - Legacy) COMPLETED - Fetched {len(all_ads_raw_groups)} groups "
                f"containing {job.metrics['ads_fetched']} total ads for firm {job.firm_name or 'Unknown'}. ✅ PHASE 1 SUCCESS"
            )
        except Exception as e:
            logger.error(
                f"Job {job.job_id} (Phase 1 - Legacy): Exception during ad payload fetching for {job.firm_name or 'Unknown'}: {e}",
                exc_info=True,
            )
            job.set_error(f"Payload fetching failed: {str(e)}")

    async def _phase_transform_ad_data(
        self, job: ProcessFirmJob, dependencies: dict[str, Any]
    ) -> None:
        """
        Phase 2: Transforms raw ad data into a structured and enriched format.
        This adapts logic from the deprecated AdProcessor class. It includes:
        - Structuring raw ad data using `_structure_single_ad`.
        - Handling images: S3 upload, PHash generation, and queuing for deferred processing if enabled.
        - AI-based enhancement: Generating summaries, classifications via AIOrchestrator.
        Updates `job.processed_ads` and `job.metrics["ads_processed_in_transform_phase"]`.
        Sets job error status on failure.
        """
        image_handler: Any = dependencies["image_handler"]
        ai_integrator: Any = dependencies["ai_integrator"]
        local_image_queue: Any | None = dependencies["local_image_queue"]
        logger: logging.Logger = dependencies["logger"]

        # CRITICAL DEBUGGING: Log transform phase inputs
        logger.info(f"🔍 CRITICAL DEBUG: Transform phase starting for job {job.job_id}")
        logger.info(f"   ├─ job.raw_ad_groups length: {len(job.raw_ad_groups) if job.raw_ad_groups else 0}")
        logger.info(f"   ├─ job.metrics['ads_fetched']: {job.metrics.get('ads_fetched', 0)}")
        logger.info(f"   └─ firm: {job.firm_name or 'Unknown'}")

        if not job.raw_ad_groups:
            logger.info(f"ℹ️ No raw_ad_groups found for job {job.job_id} - firm may have no active ads")
        else:
            logger.info(f"✅ Raw ad groups available - starting transformation")

        logger.info(
            f"Job {job.job_id} (Phase 2): Transforming ad data for firm {job.firm_name or 'Unknown'}"
        )
        logger.info(f"🔍 Phase 2 DETAIL: Starting transformation of {len(job.raw_ad_groups) if job.raw_ad_groups else 0} ad groups")

        if (
            job.raw_ad_groups is None
        ):  # Should have been set by phase 1 or be an empty list
            logger.error(
                f"Job {job.job_id} (Phase 2): raw_ad_groups is None. Cannot transform ads."
            )
            job.set_error(
                "Cannot transform ads: raw_ad_groups not available (fetch phase likely failed)."
            )
            return
        if not job.raw_ad_groups:
            logger.info(
                f"Job {job.job_id} (Phase 2): No raw ad groups to process for firm {job.firm_name or 'Unknown'}."
            )
            job.processed_ads = []
            job.metrics["ads_processed_in_transform_phase"] = 0
            return

        structured_ads_for_ai_pipeline: list[dict[str, Any]] = []
        ad_ids_processed_in_job = (
            set()
        )  # To avoid processing duplicate ad_archive_ids within the same job

        # Determine if image processing should be deferred based on job's config snapshot
        defer_image_processing_job_config = job.config_snapshot.get(
            "defer_image_processing", False
        )

        # Get FB archive repository for existence checks
        fb_archive_repo = dependencies.get("fb_archive_repo")
        if not fb_archive_repo:
            logger.warning(
                f"Job {job.job_id} (Phase 2): No FB archive repository available - skipping existence checks"
            )

        try:
            # Enhanced logging for ad processing
            logger.info(f"📊 Job {job.job_id} (Phase 2): Processing {len(job.raw_ad_groups)} ad groups")

            for group_index, ad_group_list in enumerate(job.raw_ad_groups):
                if not (
                    isinstance(ad_group_list, list)
                    and ad_group_list
                    and isinstance(ad_group_list[0], dict)
                ):
                    logger.warning(
                        f"Job {job.job_id} (Phase 2): Skipping invalid ad group at index {group_index}."
                    )
                    continue

                ad_raw_input_dict = ad_group_list[
                    0
                ]  # Assuming first item is the primary ad data

                # Log ad being processed
                ad_id_preview = ad_raw_input_dict.get('adArchiveID', ad_raw_input_dict.get('ad_archive_id', 'Unknown'))
                logger.info(f"🔍 Processing ad {group_index + 1}/{len(job.raw_ad_groups)}: ID={ad_id_preview}")

                # 1. Structure Ad Data
                ad_structured = self._structure_single_ad(
                    ad_raw_input_dict, job, logger
                )
                if not ad_structured:
                    logger.warning(
                        f"Job {job.job_id} (Phase 2): Failed to structure ad from group {group_index}."
                    )
                    continue

                ad_archive_id = ad_structured.get("ad_archive_id")
                if (
                    not ad_archive_id
                ):  # Should be guaranteed by _structure_single_ad if successful
                    logger.error(
                        f"Job {job.job_id} (Phase 2): Ad missing ad_archive_id after structuring (Group {group_index})."
                    )
                    continue

                if ad_archive_id in ad_ids_processed_in_job:
                    logger.debug(
                        f"Job {job.job_id} (Phase 2): Skipping duplicate ad_archive_id {ad_archive_id} in this job."
                    )
                    continue
                ad_ids_processed_in_job.add(ad_archive_id)

                # 2. S3 Image Existence Check - Check if image exists in S3 and process if missing
                ad_creative_id = ad_structured.get("ad_creative_id")
                if ad_creative_id:
                    # Build S3 key using the standard pattern: adarchive/fb/{ad_archive_id}/{ad_creative_id}.jpg
                    s3_prefix = self.config.get("s3_ad_archive_prefix", "adarchive/fb").strip("/")
                    s3_image_key = f"{s3_prefix}/{ad_archive_id}/{ad_creative_id}.jpg"

                    # Check if image exists in S3
                    s3_manager = dependencies.get("s3_manager")
                    if s3_manager:
                        try:
                            s3_exists = await s3_manager.check_file_exists(s3_image_key)
                            logger.info(f"Job {job.job_id} (Phase 2): S3 image check for ad {ad_archive_id}: key={s3_image_key}, exists={s3_exists}")

                            if not s3_exists:
                                # Get image URL from ad data
                                source_image_url = (
                                    ad_structured.get("original_image_url") or
                                    ad_structured.get("video_preview_image_url")
                                )

                                if source_image_url:
                                    logger.info(f"Job {job.job_id} (Phase 2): Downloading missing S3 image for ad {ad_archive_id} from {source_image_url[:100]}")

                                    # Use existing image handler to download and upload
                                    image_handler = dependencies.get("image_handler")
                                    if image_handler:
                                        try:
                                            s3_key_result, _ = await image_handler.process_and_upload_ad_image(
                                                ad_archive_id, ad_creative_id, source_image_url
                                            )

                                            if s3_key_result:
                                                logger.info(f"Job {job.job_id} (Phase 2): Successfully uploaded missing image to S3: {s3_key_result}")

                                                # Add to deferred processing queue
                                                local_image_queue = dependencies.get("local_image_queue")
                                                if local_image_queue:
                                                    local_image_queue.add_to_queue(
                                                        image_hash=f"{ad_archive_id}_{ad_creative_id}",
                                                        ad_archive_id=ad_archive_id,
                                                        start_date=ad_structured.get("start_date"),
                                                        s3_key=s3_key_result,
                                                        image_text=""  # Empty string indicates deferred processing
                                                    )
                                                    logger.info(f"Job {job.job_id} (Phase 2): Added missing image to deferred processing queue")
                                            else:
                                                logger.error(f"Job {job.job_id} (Phase 2): Failed to upload missing image for ad {ad_archive_id}")
                                        except Exception as upload_error:
                                            logger.error(f"Job {job.job_id} (Phase 2): Error uploading missing image for ad {ad_archive_id}: {upload_error}")
                                else:
                                    logger.warning(f"Job {job.job_id} (Phase 2): No source image URL found for missing S3 image - ad {ad_archive_id}")
                        except Exception as s3_check_error:
                            logger.error(f"Job {job.job_id} (Phase 2): Error checking S3 image existence for ad {ad_archive_id}: {s3_check_error}")

                # 3. Database Existence Check - Check if ad already exists in DynamoDB
                if fb_archive_repo:
                    start_date = ad_structured.get("start_date")
                    if ad_archive_id and start_date:
                        try:
                            logger.info(
                                f"🔍 Job {job.job_id} (Phase 2): Checking database existence for Ad {ad_archive_id} with StartDate {start_date}"
                            )
                            # Check if ad exists in database (using PascalCase keys for DynamoDB)
                            existing_key = {
                                "AdArchiveID": str(ad_archive_id),
                                "StartDate": str(start_date)
                            }
                            existing_ad = await fb_archive_repo.get_item(existing_key)

                            if existing_ad:
                                # Ad exists - check if we need to update based on certain conditions
                                # For now, we'll process it anyway to check for updates
                                logger.info(
                                    f"✅ Job {job.job_id} (Phase 2): Ad {ad_archive_id} already exists in DB - will check for updates"
                                )
                                # Store the existing ad data for comparison later
                                ad_structured["_existing_in_db"] = True
                                ad_structured["_existing_db_data"] = existing_ad
                            else:
                                logger.info(
                                    f"🆕 Job {job.job_id} (Phase 2): Ad {ad_archive_id} is new - not in database"
                                )
                                ad_structured["_existing_in_db"] = False
                        except Exception as db_check_error:
                            logger.warning(
                                f"Job {job.job_id} (Phase 2): Error checking DB existence for ad {ad_archive_id}: {db_check_error}"
                            )
                            # Continue processing even if DB check fails
                            ad_structured["_existing_in_db"] = None

                # 4. Skip Term Checking - Early exit before expensive operations
                skip_term_service = dependencies.get("skip_term_service")
                if skip_term_service:
                    skip_check_result = skip_term_service.check_ad_for_skip_terms(
                        ad_structured
                    )
                    if skip_check_result["should_skip"]:
                        # Log the skipped ad
                        await skip_term_service.log_skipped_ad(
                            ad_dict=ad_structured,
                            matched_terms=skip_check_result["matched_terms"],
                            iso_date=job.current_process_date,
                            reason=f"Skip terms matched: {', '.join(skip_check_result['matched_terms'])}",
                        )
                        logger.info(
                            f"Job {job.job_id} (Phase 2): Skipping ad {ad_archive_id} due to skip terms: "
                            f"{', '.join(skip_check_result['matched_terms'])}"
                        )
                        # Update metrics for skipped ads
                        job.metrics.setdefault("ads_skipped_by_terms", 0)
                        job.metrics["ads_skipped_by_terms"] += 1
                        continue  # Skip this ad - no image processing or AI enhancement

                # 5. Image Handling & PHash
                ad_creative_id = ad_structured.get("ad_creative_id")
                source_image_url = ad_structured.get(
                    "original_image_url"
                ) or ad_structured.get("video_preview_image_url")

                # Check if this is an existing DB item missing s3_image_key
                existing_db_data = ad_structured.get("_existing_db_data", {})
                existing_s3_key = existing_db_data.get("S3ImageKey") if existing_db_data else None
                is_missing_s3_key = ad_structured.get("_existing_in_db") and not existing_s3_key

                # Initialize s3_key to avoid UnboundLocalError
                s3_key = None

                # ENHANCED DEBUG: Log S3 image processing conditions
                logger.info(
                    f"Job {job.job_id} (Phase 2) S3 DEBUG for ad {ad_archive_id}: "
                    f"ad_creative_id={ad_creative_id}, source_image_url={'None' if not source_image_url else f'{source_image_url[:100]}...'}, "
                    f"is_missing_s3_key={is_missing_s3_key}, existing_s3_key={existing_s3_key}"
                )

                # CRITICAL DEBUG: Log the actual image URLs from structured ad
                original_url = ad_structured.get("original_image_url")
                preview_url = ad_structured.get("video_preview_image_url")
                logger.info(
                    f"Job {job.job_id} (Phase 2) IMAGE URLS: original_image_url={'None' if not original_url else f'{original_url[:100]}...'}, "
                    f"video_preview_image_url={'None' if not preview_url else f'{preview_url[:100]}...'}"
                )

                # Process image if:
                # 1. We have a creative_id and source URL (new ads or updates)
                # 2. OR this is an existing DB item without an s3_image_key
                if ad_creative_id and (source_image_url or is_missing_s3_key):
                    # For existing items without s3_key, try to get image URL from existing data
                    if is_missing_s3_key and not source_image_url:
                        source_image_url = (existing_db_data.get("OriginalImageUrl") or
                                          existing_db_data.get("VideoPreviewImageUrl"))
                        if source_image_url:
                            logger.info(
                                f"Job {job.job_id} (Phase 2): Found missing S3 key for existing ad {ad_archive_id}, "
                                f"will download image from: {source_image_url[:100]}..."
                            )

                    try:
                        if source_image_url:  # Only proceed if we have a URL
                            logger.info(f"Job {job.job_id} (Phase 2) S3 PROCESSING: Starting image upload for ad {ad_archive_id}, creative_id={ad_creative_id}, url={source_image_url[:100]}...")

                            # Set current ad for bandwidth tracking
                            if hasattr(job, 'bandwidth_logger') and job.bandwidth_logger:
                                job.bandwidth_logger.set_current_ad(ad_archive_id)

                            try:
                                s3_key, s3_exists = await image_handler.process_and_upload_ad_image(
                                    ad_archive_id, ad_creative_id, source_image_url
                                )
                                ad_structured["s3_image_key"] = s3_key

                                logger.info(
                                    f"Job {job.job_id} (Phase 2) S3 RESULT: ad {ad_archive_id} - "
                                    f"s3_key='{s3_key}', s3_exists={s3_exists}"
                                )

                                if not s3_key:
                                    logger.error(
                                        f"Job {job.job_id} (Phase 2) S3 FAILURE: No S3 key generated for ad {ad_archive_id}! "
                                        f"Image processing failed for {source_image_url[:100]}"
                                    )
                            except Exception as e:
                                logger.error(
                                    f"Job {job.job_id} (Phase 2) S3 EXCEPTION: Failed to process image for ad {ad_archive_id}: {e}",
                                    exc_info=True
                                )
                                s3_key = None
                                ad_structured["s3_image_key"] = None

                            # Clear current ad after processing
                            if hasattr(job, 'bandwidth_logger') and job.bandwidth_logger:
                                job.bandwidth_logger.clear_current_ad()

                            # If this was an existing item missing s3_key, mark that we need to update it
                            if is_missing_s3_key:
                                ad_structured["_needs_s3_key_update"] = True
                                logger.info(
                                    f"Job {job.job_id} (Phase 2): Successfully processed missing image for {ad_archive_id}, "
                                    f"S3 key: {s3_key}"
                                )

                        # Get PHash and check for existing ImageText from FBImageHash table
                        (
                            phash_str,
                            existing_image_text,
                        ) = await image_handler.get_phash_and_existing_text(
                            s3_key,
                            ad_archive_id,
                            ad_creative_id,
                            job.current_process_date,
                        )
                        ad_structured["phash"] = phash_str
                        ad_structured["PHash"] = phash_str  # Also set PascalCase for AI orchestrator

                        if existing_image_text:
                            ad_structured["ImageText"] = existing_image_text
                        elif (
                            defer_image_processing_job_config
                            and local_image_queue
                            and s3_key
                        ):
                            local_image_queue.add_to_queue(
                                image_hash=phash_str
                                or f"{ad_archive_id}_{ad_creative_id}",  # Use PHash if available
                                ad_archive_id=ad_archive_id,
                                start_date=ad_structured.get(
                                    "start_date", job.current_process_date
                                ),
                                s3_path=s3_key,
                                scrape_date=job.current_process_date,
                                creative_id=ad_creative_id,
                                law_firm_name=job.firm_name or "Unknown",
                            )
                            ad_structured["ImageText"] = (
                                ""  # Empty string indicates deferred
                            )
                        elif (
                            s3_key
                        ):  # Not deferring, no existing text, and S3 key exists
                            ad_structured["ImageText"] = (
                                None  # None triggers AI integrator for image text
                            )

                    except Exception as img_e:
                        logger.error(
                            f"Job {job.job_id} (Phase 2): Error in image handling for ad {ad_archive_id}: {img_e}",
                            exc_info=True,
                        )
                else:
                    # ENHANCED DEBUG: Log when image processing is skipped
                    logger.info(
                        f"Job {job.job_id} (Phase 2) S3 SKIPPED for ad {ad_archive_id}: "
                        f"Image processing bypassed - ad_creative_id={ad_creative_id}, "
                        f"source_image_url_exists={bool(source_image_url)}, "
                        f"is_missing_s3_key={is_missing_s3_key}"
                    )

                # Add defensive check to prevent None values from entering AI pipeline
                if ad_structured is not None:
                    structured_ads_for_ai_pipeline.append(ad_structured)
                else:
                    logger.warning(
                        f"Job {job.job_id} (Phase 2): Skipping None ad_structured from group {group_index} - prevents NoneType errors in AI enhancement"
                    )

            if not structured_ads_for_ai_pipeline:
                logger.info(
                    f"Job {job.job_id} (Phase 2): No ads were structured for AI pipeline for {job.firm_name or 'Unknown'}."
                )
                job.processed_ads = []
                job.metrics["ads_processed_in_transform_phase"] = 0
                # Continue to allow Phase 3 to run and update the last_updated date

            # 5. AI Enhancement (Batch) - Includes Summary Generation
            ai_enhanced_ads: list[dict[str, Any]] = []

            # Only proceed with AI enhancement if we have ads to process
            if structured_ads_for_ai_pipeline:
                logger.info(
                    f"🤖 Job {job.job_id} (Phase 2): Starting AI Enhancement step for {len(structured_ads_for_ai_pipeline)} ads"
                )
                ai_enhancement_enabled = job.config_snapshot.get("feature_flags", {}).get(
                    "enable_ai_enhancement", True
                )

                if ai_enhancement_enabled:
                    # Additional validation to prevent NoneType errors in AI enhancement
                    if structured_ads_for_ai_pipeline is None:
                        logger.error(
                            f"Job {job.job_id} (Phase 2): structured_ads_for_ai_pipeline is None - cannot proceed with AI enhancement"
                        )
                        job.set_error(
                            "AI enhancement failed: structured ads pipeline is None"
                        )
                        return

                    # Filter out any None values that might have slipped through
                    valid_ads = [
                        ad for ad in structured_ads_for_ai_pipeline if ad is not None
                    ]
                    if len(valid_ads) != len(structured_ads_for_ai_pipeline):
                        logger.warning(
                            f"Job {job.job_id} (Phase 2): Filtered out {len(structured_ads_for_ai_pipeline) - len(valid_ads)} None ads from AI pipeline"
                        )
                        structured_ads_for_ai_pipeline = valid_ads

                    logger.info(
                        f"Job {job.job_id} (Phase 2): AI enhancing {len(structured_ads_for_ai_pipeline)} ads for {job.firm_name or 'Unknown'}..."
                    )
                    ai_tasks = [
                        ai_integrator.enhance_ad_data(ad_data)
                        for ad_data in structured_ads_for_ai_pipeline
                    ]
                    results_with_exceptions = await asyncio.gather(
                        *ai_tasks, return_exceptions=True
                    )

                    logger.info(
                        f"✅ Job {job.job_id} (Phase 2): AI tasks completed. Processing {len(results_with_exceptions)} results..."
                    )

                    for i, result_or_exc in enumerate(results_with_exceptions):
                        original_ad_data = structured_ads_for_ai_pipeline[i]
                        ad_id_log = original_ad_data.get(
                            "ad_archive_id", f"unknown_ad_at_index_{i}"
                        )
                        if isinstance(result_or_exc, Exception):
                            logger.error(
                                f"Job {job.job_id} (Phase 2): AI enhancement failed for ad {ad_id_log}: {result_or_exc}"
                            )
                            original_ad_data["ai_error"] = str(
                                result_or_exc
                            )  # Add error info to the ad dict
                            ai_enhanced_ads.append(original_ad_data)
                        elif isinstance(result_or_exc, dict):
                            # Log summary generation status
                            summary = result_or_exc.get("summary") or result_or_exc.get("Summary")
                            if summary and summary != "NA":
                                logger.info(
                                    f"✅ Job {job.job_id} (Phase 2): AI generated summary for ad {ad_id_log}: '{summary[:100]}...'"
                                )
                            else:
                                logger.info(
                                    f"ℹ️ Job {job.job_id} (Phase 2): No summary generated for ad {ad_id_log} (may already exist)"
                                )
                            ai_enhanced_ads.append(result_or_exc)
                        else:
                            logger.error(
                                f"Job {job.job_id} (Phase 2): AI enhancement for ad {ad_id_log} returned unexpected type: {type(result_or_exc)}"
                            )
                            ai_enhanced_ads.append(
                                original_ad_data
                            )  # Fallback to original if result is malformed
                else:
                    logger.info(
                        f"Job {job.job_id} (Phase 2): AI enhancement disabled by feature flag. Using structured ads."
                    )
                    ai_enhanced_ads = structured_ads_for_ai_pipeline
            else:
                # No ads to process - set empty list for ai_enhanced_ads
                ai_enhanced_ads = []
                logger.info(
                    f"Job {job.job_id} (Phase 2): No ads to enhance - firm has no active ads."
                )

            # Log AI enhancement completion
            logger.info(
                f"🎯 Job {job.job_id} (Phase 2): AI enhancement completed. Enhanced {len(ai_enhanced_ads)} ads with summaries"
            )

            # Default campaign if not set (VectorClusterer logic was here, now deprecated)
            # Also ensure field name consistency for DynamoDB (PascalCase)
            for ad in ai_enhanced_ads:
                ad.setdefault("campaign", "General")

                # Map lowercase field names to PascalCase for DynamoDB compatibility
                if "summary" in ad and "Summary" not in ad:
                    ad["Summary"] = ad.get("summary")
                    ad.pop("summary", None)  # Remove snake_case version

                # Ensure Summary field exists and is not None
                if not ad.get("Summary") and not ad.get("summary"):
                    ad["Summary"] = "NA"  # Default if no summary was generated
                    logger.warning(
                        f"Job {job.job_id} (Phase 2): No summary for ad {ad.get('ad_archive_id', 'unknown')} - defaulting to 'NA'"
                    )

                # Ensure other AI fields have proper casing
                # Keep data in snake_case format - let repository decorators handle PascalCase conversion
                # EXCEPTIONS: Some fields must always be saved in PascalCase
                if "phash" in ad and "PHash" not in ad:
                    ad["PHash"] = ad.get("phash")
                    ad.pop("phash", None)  # Remove snake_case version

                if "image_text" in ad and "ImageText" not in ad:
                    ad["ImageText"] = ad.get("image_text")
                    ad.pop("image_text", None)  # Remove snake_case version

                # Remove temporary flags and duplicate snake_case fields
                ad.pop("_existing_in_db", None)
                ad.pop("_existing_db_data", None)
                ad.pop("_needs_s3_key_update", None)
                ad.pop("phash", None)  # Keep only PHash (PascalCase)
                ad.pop("ai_error", None)  # Remove temporary error field

            job.processed_ads = ai_enhanced_ads
            job.metrics["ads_processed_in_transform_phase"] = len(ai_enhanced_ads)

            # Enhanced logging with validation metrics
            logger.info(
                f"Job {job.job_id} (Phase 2): Transformation complete. {len(ai_enhanced_ads)} ads for {job.firm_name or 'Unknown'}."
            )
            logger.info(f"📦 Phase 2 RESULT: Processed {len(ai_enhanced_ads)} ads, {len(structured_ads_for_ai_pipeline)} structured for AI")

            # Log validation metrics if any ads were skipped
            ads_skipped_wrong_page = job.metrics.get("ads_skipped_wrong_page_id", 0)
            if ads_skipped_wrong_page > 0:
                logger.warning(
                    f"⚠️ Page ID Validation: Skipped {ads_skipped_wrong_page} ads due to page_id mismatch "
                    f"(expected: {job.firm_id}, firm: {job.firm_name or 'Unknown'})"
                )

            # Log sample of processed ads for verification
            if ai_enhanced_ads:
                logger.info(f"📋 Sample of processed ads (first 5):")
                for i, ad in enumerate(ai_enhanced_ads[:5]):
                    ad_id = ad.get("ad_archive_id", ad.get("AdArchiveID", "Unknown"))
                    page_name = ad.get("page_name", ad.get("PageName", "Unknown"))
                    logger.info(f"   {i+1}. ID: {ad_id}, Page: {page_name}")
                if len(ai_enhanced_ads) > 5:
                    logger.info(f"   ... and {len(ai_enhanced_ads) - 5} more ads")

        except Exception as e:
            logger.error(
                f"Job {job.job_id} (Phase 2): Exception during ad transformation for {job.firm_name or 'Unknown'}: {e}",
                exc_info=True,
            )
            job.set_error(f"Ad transformation failed: {str(e)}")

    @staticmethod
    def _static_extract_media_info(
        snapshot: dict[str, Any], card: dict[str, Any], media_type: str
    ) -> dict[str, str | None]:
        media_data = {}
        keys_map = {
            "videos": ["video_hd_url", "video_sd_url", "video_preview_url"],
            "images": ["original_image_url", "resized_image_url"],
        }
        keys_to_find = keys_map.get(media_type, [])
        if not keys_to_find:
            return {}

        # DEBUG: Log media extraction start
        logging.getLogger(__name__).debug(
            f"🔍 MEDIA EXTRACTION DEBUG - Starting {media_type} extraction: "
            f"snapshot_keys={list(snapshot.keys()) if snapshot else 'None'}, "
            f"card_keys={list(card.keys()) if card else 'None'}, "
            f"keys_to_find={keys_to_find}"
        )

        if isinstance(card, dict):
            for key in keys_to_find:
                card_value = card.get(key)
                if card_value:
                    media_data[key] = card_value

        if not all(k in media_data for k in keys_to_find):
            # CRITICAL FIX: Add defensive null check for snapshot
            if snapshot is None:
                logging.getLogger(__name__).debug(f"🔍 MEDIA EXTRACTION DEBUG - snapshot is None, returning: {media_data}")
                return media_data  # Return what we have so far if snapshot is None

            snapshot_media_source = snapshot.get(media_type)

            # DEBUG: Log snapshot media source
            logging.getLogger(__name__).debug(
                f"🔍 MEDIA EXTRACTION DEBUG - snapshot_media_source for {media_type}: "
                f"type={type(snapshot_media_source)}, "
                f"is_list={isinstance(snapshot_media_source, list)}, "
                f"length={len(snapshot_media_source) if isinstance(snapshot_media_source, list) else 'N/A'}, "
                f"content={snapshot_media_source}"
            )

            source_to_check = None
            if (
                isinstance(snapshot_media_source, list)
                and snapshot_media_source
                and isinstance(snapshot_media_source[0], dict)
            ):
                source_to_check = snapshot_media_source[0]
                logging.getLogger(__name__).debug(
                    f"🔍 MEDIA EXTRACTION DEBUG - Using first item from list: {source_to_check}"
                )
            elif isinstance(snapshot_media_source, dict):
                source_to_check = snapshot_media_source
                logging.getLogger(__name__).debug(
                    f"🔍 MEDIA EXTRACTION DEBUG - Using dict directly: {source_to_check}"
                )

            if source_to_check:
                for key in keys_to_find:
                    if key not in media_data and (
                        source_val := source_to_check.get(key)
                    ):
                        media_data[key] = source_val
                        logging.getLogger(__name__).debug(
                            f"🔍 MEDIA EXTRACTION DEBUG - Found {key}={source_val}"
                        )

        # DEBUG: Log final result
        logging.getLogger(__name__).debug(
            f"🔍 MEDIA EXTRACTION DEBUG - Final result for {media_type}: {media_data}"
        )

        for key in keys_to_find:
            media_data.setdefault(key, None)
        return media_data

    @staticmethod
    def _static_strip_html_tags(html: str | None) -> str | None:
        if not html or not isinstance(html, str) or not ("<" in html and ">" in html):
            return html
        try:
            soup = BeautifulSoup(html, "lxml")
        except ImportError:  # Fallback if lxml is not installed
            soup = BeautifulSoup(html, "html.parser")
        except Exception as e:  # Catch other BeautifulSoup errors
            # Using a generic logger here as this is a static method
            logging.getLogger(__name__).warning(
                f"BeautifulSoup init error: {e}. Trying html.parser."
            )
            soup = BeautifulSoup(html, "html.parser")  # Fallback parser
        text = soup.get_text(separator=" ", strip=True)
        return text if text else None

    def _structure_single_ad(
        self, record: dict[str, Any], job: ProcessFirmJob, logger: logging.Logger
    ) -> dict[str, Any] | None:
        """
        Structures a single raw ad data dictionary into a consistent format.
        Adapted from the deprecated `AdProcessor._create_ad_dictionary`.
        """
        ad_archive_id_raw = record.get("adArchiveID") or record.get("ad_archive_id")
        if not ad_archive_id_raw:
            logger.error(
                f"Job {job.job_id}: _structure_single_ad: Returning None - Missing 'adArchiveID'/'ad_archive_id'. Record: {record}"
            )
            return None
        ad_archive_id = str(ad_archive_id_raw)
        log_prefix = f"Job {job.job_id} Ad {ad_archive_id}: "

        snapshot = record.get("snapshot")

        # ENHANCED DEBUG: Track snapshot lifecycle to identify NoneType source
        logger.debug(f"{log_prefix}DEBUG snapshot lifecycle: type={type(snapshot)}, is_dict={isinstance(snapshot, dict)}")
        if snapshot is None:
            logger.error(f"{log_prefix}DEBUG snapshot is None - record keys: {list(record.keys()) if record else 'No record'}")
        elif not isinstance(snapshot, dict):
            logger.error(f"{log_prefix}DEBUG snapshot is not dict: {type(snapshot)} = {snapshot}")

        if not isinstance(snapshot, dict):
            logger.error(
                f"{log_prefix}Returning None - Missing or invalid 'snapshot'. Record: {record}"
            )
            return None

        ad_creative_id_raw = snapshot.get("ad_creative_id", record.get("collation_id"))
        # If ad_creative_id is missing, use ad_archive_id so images will download
        ad_creative_id = str(ad_creative_id_raw) if ad_creative_id_raw else ad_archive_id

        try:
            cards = snapshot.get("cards", [])
            card = cards[0] if cards and isinstance(cards[0], dict) else {}

            video_info = JobRunnerService._static_extract_media_info(
                snapshot, card, "videos"
            )
            image_info = JobRunnerService._static_extract_media_info(
                snapshot, card, "images"
            )
            original_fb_url = image_info.get("original_image_url")
            preview_fb_url = video_info.get(
                "video_preview_url"
            )  # Used for video_preview_image_url

            # CRITICAL DEBUG: Log image URL extraction results
            logger.debug(
                f"{log_prefix}IMAGE URL DEBUG: video_info={video_info}, image_info={image_info}, "
                f"original_fb_url={original_fb_url}, preview_fb_url={preview_fb_url}"
            )

            start_ts_raw = record.get("start_date")
            end_ts_raw = record.get("end_date")
            start_date_iso = DateUtils.convert_unix_to_date_str(start_ts_raw, "%Y%m%d")
            scheduled_end_date_iso = DateUtils.convert_unix_to_date_str(
                end_ts_raw, "%Y%m%d"
            )

            if not start_date_iso:
                logger.error(
                    f"{log_prefix}Returning None - Missing valid start date (Raw: {start_ts_raw})."
                )
                return None

            is_active_raw = record.get("isActive") or record.get("is_active")
            if isinstance(is_active_raw, str):
                is_active = is_active_raw.lower() in ["true", "1", "yes", "on"]
            elif isinstance(is_active_raw, (int, float)):
                is_active = bool(is_active_raw)
            elif isinstance(is_active_raw, bool):
                is_active = is_active_raw
            else:
                is_active = False  # Default to False if type is unexpected or None

            effective_end_date = scheduled_end_date_iso
            current_process_date_str = job.current_process_date  # From ProcessFirmJob
            if is_active:
                if not scheduled_end_date_iso or (
                    scheduled_end_date_iso < current_process_date_str
                ):
                    effective_end_date = current_process_date_str
            elif not scheduled_end_date_iso:  # Not active and no scheduled end date
                effective_end_date = start_date_iso  # Default to start_date if not active and no end_date

            body_from_card = card.get("body")

            # CRITICAL FIX: Add defensive null checks for snapshot to prevent NoneType errors
            if snapshot is None:
                logger.error(f"{log_prefix}CRITICAL: snapshot is None despite earlier validation! Record snapshot field: {record.get('snapshot', 'MISSING')}")
                body_from_snapshot_text = None
                body_html = None
            else:
                body_from_snapshot_text = snapshot.get("body", {}).get("text") if isinstance(snapshot.get("body"), dict) else None
                body_markup = snapshot.get("body")
                if isinstance(body_markup, dict) and body_markup:
                    markup_data = body_markup.get("markup")
                    body_html = markup_data.get("__html") if isinstance(markup_data, dict) else None
                else:
                    body_html = None
            body_from_html_stripped = (
                JobRunnerService._static_strip_html_tags(body_html)
                if body_html
                else None
            )
            body_text = (
                body_from_card or body_from_snapshot_text or body_from_html_stripped
            )

            # Extract page_id for validation
            ad_page_id = str(record.get("page_id")) if record.get("page_id") else None

            # CRITICAL: Validate page_id matches the firm being processed
            if ad_page_id and job.firm_id and ad_page_id != job.firm_id:
                logger.warning(
                    f"{log_prefix}Page ID mismatch - expected: {job.firm_id}, "
                    f"got: {ad_page_id} for ad {ad_archive_id}. "
                    f"Page name: {record.get('page_name', 'Unknown')}. "
                    f"Discarding ad as it belongs to a different firm."
                )
                # Update metrics for tracking
                job.metrics.setdefault("ads_skipped_wrong_page_id", 0)
                job.metrics["ads_skipped_wrong_page_id"] += 1
                return None

            ad = {
                "law_firm": job.firm_name or "Unknown",
                "ad_archive_id": ad_archive_id,
                "ad_creative_id": ad_creative_id,
                "page_name": record.get("page_name"),
                "page_id": ad_page_id,
                "is_active": is_active,
                "start_date": start_date_iso,
                "end_date": effective_end_date,  # Calculated effective end date
                "publisher_platform": record.get("publisher_platform"),
                "link_url": card.get("link_url") or snapshot.get("link_url"),
                "video_hd_url": video_info.get("video_hd_url"),
                "video_sd_url": video_info.get("video_sd_url"),
                "video_preview_image_url": preview_fb_url,  # From video_info extraction
                "original_image_url": original_fb_url,  # From image_info extraction
                "resized_image_url": image_info.get("resized_image_url"),
                "link_description": snapshot.get("link_description")
                or card.get("link_description"),
                "body": body_text,
                "caption": snapshot.get("caption") or card.get("caption"),
                "cta_text": snapshot.get("cta_text") or card.get("cta_text"),
                "title": card.get("title") or snapshot.get("title"),
                # Don't set last_updated here - it will be set when saving to DB
                # Initialize fields expected by subsequent processing or DB schema
                "s3_image_key": None,
                "phash": None,  # Will be populated during image handling
                "ImageText": None,
                "Summary": None,
                "LLM": None,
                "IsForbidden403": False,  # Default, might be updated by image handler
                "Category": None,
                "Company": None,
                "Product": None,
                "Injuries": None,
                "LitigationType": None,
                "LitigationName": None,
                "MdlName": None,
                "campaign": "General",  # Default campaign
            }

            # Clean placeholder values from text fields
            for key in ["link_description", "body", "title", "caption", "cta_text"]:
                value = ad.get(key)
                if isinstance(value, str) and "{{" in value and "}}" in value:
                    cleaned_value = re.sub(r"\{\{.*?\}\}", "", value).strip()
                    ad[key] = cleaned_value if cleaned_value else None

            logger.debug(f"{log_prefix}Successfully structured ad.")
            return ad
        except Exception as e:
            # Capture complete ad data for analysis
            error_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "error_type": type(e).__name__,
                "error_message": str(e),
                "job_id": job.job_id,
                "firm_id": job.firm_id,
                "firm_name": job.firm_name,
                "ad_id": ad_archive_id,
                "raw_record": record,  # Complete GraphQL response
                "snapshot_analysis": {
                    "type": str(type(snapshot)),
                    "is_none": snapshot is None,
                    "is_dict": isinstance(snapshot, dict),
                    "fields": list(snapshot.keys()) if isinstance(snapshot, dict) else None,
                    "body_field": snapshot.get("body") if isinstance(snapshot, dict) else "N/A",
                    "body_type": str(type(snapshot.get("body"))) if isinstance(snapshot, dict) else "N/A"
                },
                "processing_state": {
                    "body_markup": body_markup if 'body_markup' in locals() else "not_reached",
                    "body_markup_type": str(type(body_markup)) if 'body_markup' in locals() else "not_reached",
                    "card": card if 'card' in locals() else "not_reached",
                    "cards": cards if 'cards' in locals() else "not_reached",
                    "image_info": image_info if 'image_info' in locals() else "not_reached",
                    "video_info": video_info if 'video_info' in locals() else "not_reached"
                },
                "stack_trace": traceback.format_exc()
            }

            # Log to error file
            asyncio.create_task(self._log_ad_error(error_data, job, logger))

            logger.error(
                f"{log_prefix}Exception during dictionary creation: {e}\n"
                f"Ad Archive ID: {ad_archive_id_raw}\n"
                f"Snapshot present: {snapshot is not None}\n"
                f"Body markup type: {type(body_markup) if 'body_markup' in locals() else 'not set'}\n"
                f"Record keys: {list(record.keys())}",
                exc_info=True
            )
            return None

    async def _phase_save_to_database(self, job: ProcessFirmJob, dependencies: dict):
        """
        Phase 3: Saves processed ad data to the database using AdDBService.
        Updates `job.metrics` related to database operations.
        Sets job error status on failure.
        """
        ad_db_service = dependencies["ad_db_service"]
        logger = dependencies["logger"]

        # CRITICAL DEBUGGING: Log save phase inputs
        logger.info(f"🔍 CRITICAL DEBUG: Save phase starting for job {job.job_id}")
        logger.info(f"   ├─ job.processed_ads length: {len(job.processed_ads) if job.processed_ads else 0}")
        logger.info(f"   ├─ job.metrics['ads_processed_in_transform_phase']: {job.metrics.get('ads_processed_in_transform_phase', 0)}")
        logger.info(f"   └─ firm: {job.firm_name or 'Unknown'}")

        logger.info(
            f"Job {job.job_id} (Phase 3): Saving data to database for firm {job.firm_name or 'Unknown'}"
        )
        logger.info(f"📊 Phase 3 DETAIL: Preparing to save {len(job.processed_ads) if job.processed_ads else 0} processed ads")

        try:
            # Repositories are expected to be in the dependencies dict
            fb_archive_repo = dependencies.get("fb_archive_repo")
            law_firms_repo = dependencies.get("law_firms_repo")

            # 🔍 DIAGNOSTIC: Log repository injection status
            logger.info(
                f"Job {job.job_id} (Phase 3): 🔍 DIAGNOSTIC - Repository injection status:"
            )
            logger.info(
                f"  - fb_archive_repo: {'✅ Available' if fb_archive_repo else '❌ Missing'} (type: {type(fb_archive_repo).__name__})"
            )
            logger.info(
                f"  - law_firms_repo: {'✅ Available' if law_firms_repo else '❌ Missing'} (type: {type(law_firms_repo).__name__})"
            )
            logger.info(f"  - dependencies keys: {list(dependencies.keys())}")

            if not fb_archive_repo or not law_firms_repo:
                job.set_error("Database repositories not available for saving.")
                logger.error(
                    f"Job {job.job_id} (Phase 3): ❌ CRITICAL - Repository injection failed!"
                )
                logger.error(
                    f"Job {job.job_id} (Phase 3): Error - {job.error_message or 'Unknown error'}"
                )
                return

            # Check if we have ads to save
            if job.processed_ads is None or not job.processed_ads:
                logger.info(
                    f"Job {job.job_id} (Phase 3): No processed ads to save for {job.firm_name or 'Unknown'}."
                )
                job.metrics["db_items_processed_for_save"] = 0
                job.metrics["db_inserts"] = 0
                job.metrics["db_updates"] = 0

                # CRITICAL FIX: Still update law firm stats even with no ads
                # This ensures ad_archive_last_update is set, preventing reprocessing
                logger.info(
                    f"Job {job.job_id} (Phase 3): ✅ UPDATING law firm stats despite having no ads - prevents reprocessing"
                )
                await ad_db_service.update_law_firm_stats(
                    firm_id=job.firm_id,
                    firm_name=job.firm_name,
                    num_ads_processed_in_job=0,  # No ads processed
                    last_updated_date=job.current_process_date,
                    law_firms_repo=law_firms_repo,
                )
                logger.info(
                    f"Job {job.job_id} (Phase 3): ✅ Law firm stats updated with last_updated_date={job.current_process_date}"
                )
                return

            # Process ads if we have any
            # 🔍 DIAGNOSTIC: Log S3 image key presence before saving
            logger.info(
                f"Job {job.job_id} (Phase 3): 🔍 S3 KEY DIAGNOSTIC - Checking s3_image_key in {len(job.processed_ads)} ads before save"
            )
            s3_key_count = 0
            for idx, ad in enumerate(job.processed_ads[:5]):  # Check first 5 ads
                s3_key = ad.get("s3_image_key")
                ad_id = ad.get("ad_archive_id", "unknown")
                logger.info(
                    f"  Ad {idx+1} (ID: {ad_id}): s3_image_key = {s3_key if s3_key else 'NOT SET'}"
                )
                if s3_key:
                    s3_key_count += 1
            logger.info(
                f"Job {job.job_id} (Phase 3): Total ads with s3_image_key: {s3_key_count}/{len(job.processed_ads[:5])} (first 5 checked)"
            )

            (
                items_compared,
                updates,
                inserts,
            ) = await ad_db_service.batch_save_processed_ads(
                ads_list=job.processed_ads,
                firm_id=job.firm_id,
                firm_name=job.firm_name,
                processing_date=job.current_process_date,
                fb_archive_repo=fb_archive_repo,
            )
            job.metrics["db_items_processed_for_save"] = items_compared
            job.metrics["db_updates"] = updates
            job.metrics["db_inserts"] = inserts

            # Update law firm stats
            num_ads_for_firm_in_this_run = len(
                job.processed_ads
            )  # Or a more specific count if some ads were invalid for DB
            await ad_db_service.update_law_firm_stats(
                firm_id=job.firm_id,
                firm_name=job.firm_name,
                num_ads_processed_in_job=num_ads_for_firm_in_this_run,
                last_updated_date=job.current_process_date,
                law_firms_repo=law_firms_repo,
            )
            logger.info(
                f"Job {job.job_id} (Phase 3): Database operations complete for {job.firm_name or 'Unknown'}."
            )
            logger.info(f"📊 Phase 3 RESULT: DB operations - {job.metrics.get('db_inserts', 0)} inserts, {job.metrics.get('db_updates', 0)} updates, {job.metrics.get('db_items_processed_for_save', 0)} items processed")

        except Exception as e:
            logger.error(
                f"Job {job.job_id} (Phase 3): Error during database operations for {job.firm_name or 'Unknown'}: {e}",
                exc_info=True,
            )
            job.set_error(f"Database operation failed: {str(e)}")

    async def _log_ad_error(self, error_data: dict, job: ProcessFirmJob, logger: logging.Logger):
        """Log ad processing errors to dedicated JSON error file with full ad data for analysis."""
        try:
            # Get iso_date from job or config
            iso_date = job.current_process_date if job.current_process_date else self.config.get('iso_date', datetime.now().strftime('%Y%m%d'))
            error_log_path = f"data/{iso_date}/logs/ad_errors.json"

            # Extract key info for logging - fix key mapping issues
            ad_id = error_data.get('ad_id', error_data.get('ad_archive_id', 'unknown'))
            firm_name = error_data.get('firm_name', 'unknown')
            firm_id = error_data.get('firm_id', 'unknown')
            error_type = error_data.get('error_type', 'unknown')
            error_msg = error_data.get('error_message', 'unknown')

            # Log to standard error logger
            logger.error(f"Failed to process ad {ad_id} for firm {firm_name} ({firm_id}): {error_type} - {error_msg}")

            # Ensure directory exists
            os.makedirs(os.path.dirname(error_log_path), exist_ok=True)

            # Enhanced error data structure for comprehensive debugging
            comprehensive_error = {
                "timestamp": datetime.now().isoformat(),
                "job_id": job.job_id,
                "iso_date": iso_date,
                "ad_archive_id": ad_id,
                "firm_info": {
                    "name": firm_name,
                    "id": firm_id,
                    "page_id": job.firm_id
                },
                "error_details": {
                    "type": error_type,
                    "message": error_msg,
                    "stack_trace": error_data.get('stack_trace', ''),
                    "method": error_data.get('processing_method', '_structure_single_ad')
                },
                # CRITICAL FIX: Map correct key names from error_data
                "ad_raw_data": error_data.get('raw_record', error_data.get('raw_ad_record', {})),
                "snapshot_analysis": error_data.get('snapshot_analysis', {}),
                "processing_state": error_data.get('processing_state', {}),
                "config_snapshot": {
                    "mobile_proxy": self.config.get('mobile_proxy', False),
                    "use_graphql_capture": self.config.get('use_graphql_capture', False),
                    "defer_image_processing": self.config.get('defer_image_processing', False)
                }
            }

            # Write detailed error data as JSON line for easy parsing
            with open(error_log_path, 'a', encoding='utf-8') as f:
                json.dump(comprehensive_error, f, default=str, ensure_ascii=False, indent=None, separators=(',', ':'))
                f.write('\n')

            logger.info(f"📝 Comprehensive ad error logged to {error_log_path} for ad {ad_id}")

        except Exception as log_error:
            logger.error(f"Failed to write to ad error log file: {log_error}", exc_info=True)


# Helper class from AdProcessingService, slightly adapted for use here if needed for date logic
# Note: Using imported DateUtils from src.utils.date instead of local implementation


# Placeholder for utility functions that would be ported from AdProcessor
# TODO: Create a new utility module e.g., src.services.fb_ads.jobs.job_utils.py for these
class AdProcessorUtils:
    """Utility functions adapted from the deprecated AdProcessor, for use in JobRunnerService."""

    @staticmethod
    def convert_to_boolean(value: Any) -> bool:
        """Converts a value to boolean, handling common string representations."""
        if isinstance(value, str):
            return value.lower() in ["true", "1", "yes", "on"]
        return bool(value)

    @staticmethod
    def extract_text(snapshot_field: str | None, card_field: Any | None) -> str | None:
        # Simplified placeholder for text extraction logic from AdProcessor._create_ad_dictionary
        return snapshot_field or (
            card_field.get("text") if isinstance(card_field, dict) else None
        )

    # Add _extract_media_info_util, _strip_html_tags_util etc. here by porting from AdProcessor
    # These are complex and need careful adaptation.
    # For now, they are placeholders for the full implementation.
    extract_media_url = extract_text  # Placeholder
    extract_body_text = extract_text  # Placeholder
