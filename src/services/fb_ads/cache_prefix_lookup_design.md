# Prefix-Based Cache Lookup Architecture Design

## Problem Statement
The current cache implementation has a ~99% miss rate because Facebook image URLs contain dynamic parameters that change between requests for the same image. The existing `get_cached_image()` method tries an exact URL match first, which almost always fails.

## Root Cause Analysis

### Current Flawed Approach:
```python
# Step 1: Try exact match (FAILS 99% of time)
cached_image = self._image_cache.get(image_url)  

# Step 2: Only if exact match fails, try normalized lookup
if not cached_image:
    normalized_url = self._normalize_facebook_image_url(image_url)
    # ... complex lookup logic
```

### URL Variation Examples:
```
Request 1: https://scontent.xx.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s60x60&_nc_cat=106&oh=abc123&oe=def456
Request 2: https://scontent.xx.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s600x600&_nc_cat=107&oh=xyz789&oe=uvw123
```

Both URLs point to the same image but have different query parameters.

## Proposed Solution: Prefix-Based Cache Lookup

### Core Algorithm Design

#### 1. **Normalize-First Strategy**
```python
async def get_cached_image(self, image_url: str) -> Optional[CachedImageResource]:
    """Enhanced cache lookup using prefix matching."""
    
    # Step 1: Normalize the incoming URL FIRST
    normalized_url = self._normalize_facebook_image_url(image_url)
    
    # Step 2: Generate cache key prefix (base URL without params)
    cache_prefix = self._extract_cache_prefix(normalized_url)
    
    # Step 3: Iterate through cache keys and find matches
    async with self._cache_lock:
        for cached_url, cached_resource in self._image_cache.items():
            # Check if cached URL starts with our prefix
            if cached_url.startswith(cache_prefix):
                # Validate TTL and return if valid
                if self._is_cache_entry_valid(cached_resource):
                    self._cache_hits += 1
                    return cached_resource
        
        # No match found
        self._cache_misses += 1
        return None
```

#### 2. **Optimized Prefix Index Structure**
To avoid O(n) iteration through all cache entries, maintain a prefix index:

```python
class BrowserImageCacheExtractor:
    def __init__(self):
        # Main cache storage
        self._image_cache: Dict[str, CachedImageResource] = {}
        
        # NEW: Prefix index for O(1) lookups
        # Maps base URL prefix -> List of full URLs in cache
        self._prefix_index: Dict[str, List[str]] = {}
        
        # Example:
        # _prefix_index = {
        #     "https://scontent.xx.fbcdn.net/v/t39.35426-6/514343966": [
        #         "https://scontent.xx.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s60x60&_nc_cat=106",
        #         "https://scontent.xx.fbcdn.net/v/t39.35426-6/514343966_1462920898395104_3170171672333121812_n.jpg?stp=dst-jpg_s600x600&_nc_cat=107"
        #     ]
        # }
```

#### 3. **Efficient Prefix Extraction**
```python
def _extract_cache_prefix(self, url: str) -> str:
    """Extract stable prefix from Facebook image URL."""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        
        # For Facebook images, extract up to the image ID
        # Pattern: /v/t{version}/{image_id}_{timestamp}_{hash}_n.{ext}
        import re
        
        # Match Facebook image pattern
        match = re.search(r'(/v/t[\d.-]+/\d+)', parsed.path)
        if match:
            # Return scheme + netloc + matched prefix
            return f"{parsed.scheme}://{parsed.netloc}{match.group(1)}"
        
        # Fallback: use full path without query params
        return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        
    except Exception:
        # Ultimate fallback
        return url.split('?')[0]
```

#### 4. **Cache Storage Enhancement**
```python
async def _store_cached_image(self, cached_image: CachedImageResource) -> None:
    """Store image with prefix indexing."""
    async with self._cache_lock:
        # Store in main cache
        self._image_cache[cached_image.url] = cached_image
        
        # Update prefix index
        prefix = self._extract_cache_prefix(cached_image.url)
        if prefix not in self._prefix_index:
            self._prefix_index[prefix] = []
        
        if cached_image.url not in self._prefix_index[prefix]:
            self._prefix_index[prefix].append(cached_image.url)
        
        # Update size tracking
        self._current_cache_size += cached_image.content_length
```

#### 5. **Optimized Lookup with Scoring**
```python
async def get_cached_image(self, image_url: str) -> Optional[CachedImageResource]:
    """Get cached image using prefix-based lookup with intelligent scoring."""
    
    # Extract prefix for lookup
    prefix = self._extract_cache_prefix(image_url)
    
    async with self._cache_lock:
        # O(1) prefix lookup
        if prefix not in self._prefix_index:
            self._cache_misses += 1
            self._track_miss_reason('no_prefix_match')
            return None
        
        # Get all URLs with this prefix
        candidate_urls = self._prefix_index[prefix]
        
        # Score and select best match
        best_match = None
        best_score = -1
        
        for candidate_url in candidate_urls:
            if candidate_url not in self._image_cache:
                continue
                
            cached_resource = self._image_cache[candidate_url]
            
            # Check TTL
            if not self._is_cache_entry_valid(cached_resource):
                continue
            
            # Calculate match score
            score = self._calculate_match_score(
                requested_url=image_url,
                cached_url=candidate_url,
                cached_resource=cached_resource
            )
            
            if score > best_score:
                best_score = score
                best_match = cached_resource
        
        if best_match:
            self._cache_hits += 1
            self._total_saved_bytes += best_match.content_length
            return best_match
        else:
            self._cache_misses += 1
            self._track_miss_reason('no_valid_candidates')
            return None
```

#### 6. **Intelligent Scoring Algorithm**
```python
def _calculate_match_score(self, requested_url: str, cached_url: str, 
                          cached_resource: CachedImageResource) -> float:
    """Calculate match score for cache candidate selection."""
    score = 0.0
    
    # 1. Exact URL match (highest priority)
    if requested_url == cached_url:
        score += 1000.0
    
    # 2. Size preference from URL params
    requested_size = self._extract_size_hint(requested_url)
    cached_size = self._extract_size_hint(cached_url)
    
    if requested_size and cached_size:
        if requested_size == cached_size:
            score += 100.0  # Exact size match
        else:
            # Prefer larger cached images (can be downscaled)
            if cached_size > requested_size:
                score += 50.0
            else:
                score += 10.0
    
    # 3. Content size (prefer larger images)
    score += (cached_resource.content_length / 1024) * 0.1  # KB as minor factor
    
    # 4. Freshness (prefer newer cache entries)
    age_hours = (time.time() - cached_resource.timestamp) / 3600
    freshness_score = max(0, 24 - age_hours) * 2  # Up to 48 points for fresh entries
    score += freshness_score
    
    return score

def _extract_size_hint(self, url: str) -> Optional[int]:
    """Extract size hint from Facebook URL parameters."""
    import re
    
    # Look for patterns like s60x60, s600x600
    size_match = re.search(r's(\d+)x\d+', url)
    if size_match:
        return int(size_match.group(1))
    
    # Look for w= or width= parameters
    width_match = re.search(r'[?&]w(?:idth)?=(\d+)', url)
    if width_match:
        return int(width_match.group(1))
    
    return None
```

### Performance Optimizations

1. **Lazy Prefix Index Cleanup**
   - Remove entries when cache items expire
   - Batch cleanup operations during low activity

2. **Memory-Efficient Prefix Generation**
   - Use consistent prefix lengths to reduce index size
   - Share prefix strings between entries (string interning)

3. **Fast Path for Common Cases**
   - Check exact URL match first (for repeated requests)
   - Cache the last N successful lookups

### Fallback Strategy

If prefix matching fails:
1. Log detailed miss information for analysis
2. Attempt fuzzy matching on image ID patterns
3. Return None to trigger fresh download
4. Store the newly downloaded image with proper indexing

### Implementation Benefits

1. **Dramatic Cache Hit Rate Improvement**
   - From ~1% to 80-90% expected hit rate
   - Handles all URL parameter variations

2. **O(1) Average Lookup Time**
   - Prefix index enables fast lookups
   - No need to iterate entire cache

3. **Intelligent Selection**
   - Returns best available cached version
   - Prefers size-appropriate images

4. **Maintainable & Debuggable**
   - Clear separation of concerns
   - Comprehensive logging and metrics

### Testing Strategy

1. **Unit Tests**
   - Verify prefix extraction for various URL formats
   - Test scoring algorithm with different scenarios
   - Validate index maintenance operations

2. **Integration Tests**
   - Simulate real Facebook URL variations
   - Measure actual hit rates with production-like data
   - Performance benchmarks for large caches

3. **Monitoring**
   - Track prefix index size and growth
   - Monitor lookup performance metrics
   - Alert on unusual miss patterns

## Implementation Checklist

- [ ] Implement `_extract_cache_prefix()` method
- [ ] Add `_prefix_index` data structure
- [ ] Update `_store_cached_image()` with prefix indexing
- [ ] Rewrite `get_cached_image()` with prefix-based lookup
- [ ] Implement `_calculate_match_score()` algorithm
- [ ] Add `_extract_size_hint()` helper
- [ ] Update cache removal to maintain prefix index
- [ ] Add comprehensive logging for debugging
- [ ] Write unit tests for all new methods
- [ ] Update cache statistics to track prefix performance
- [ ] Document the new lookup algorithm