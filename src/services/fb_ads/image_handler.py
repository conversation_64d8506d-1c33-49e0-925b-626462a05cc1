# /src/services/fb_ads/image_handler.py

import asyncio
import os
import random
import time
from io import BytesIO
from typing import Any

import requests
from botocore.exceptions import ClientError
from PIL import Image

# Removed dependency_injector imports - using container-based injection
from requests.exceptions import RequestException

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import FBAdServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.s3_async import S3AsyncStorage

from .bandwidth_logger import BandwidthLogger, calculate_request_size
from .image_utils import FBImageHashManager, calculate_image_hash
# from .session_manager import FacebookSessionManager  # Removed - legacy implementation


class ImageHandler(AsyncServiceBase):
    """Handles downloading, processing, and uploading of ad images."""

    def __init__(
        self,
        logger: LoggerProtocol,
        config: dict[str, Any],
        s3_manager: S3AsyncStorage,
        session_manager: Any,  # Session manager instance (CamoufoxSessionManager or compatible)
        hash_manager: FBImageHashManager | None,
        bandwidth_logger: BandwidthLogger,
        browser_image_cache_extractor=None,  # Injected unified cache instance
    ):
        """
        Initializes the ImageHandler with injected dependencies.

        Args:
            logger: Logger instance from DI container.
            config: Application configuration from DI container.
            s3_manager: Instance of S3AsyncStorage from DI container.
            session_manager: Session manager instance from DI container (CamoufoxSessionManager or compatible).
            hash_manager: Instance of FBImageHashManager from DI container.
            bandwidth_logger: Instance of BandwidthLogger from DI container.
            browser_image_cache_extractor: Shared browser image cache extractor instance.
        """
        super().__init__(logger, config)
        # Initialize S3 manager with fallback handling
        if s3_manager is None:
            self.log_warning(
                "S3 manager not properly injected - S3 operations will be disabled"
            )
        self.s3_manager = s3_manager
        self.session_manager = session_manager
        self.temp_download_dir = config.get("temp_image_dir", "./temp_images")

        # Store injected dependencies
        self.hash_manager = hash_manager
        self.bandwidth_logger = bandwidth_logger
        self.browser_image_cache_extractor = browser_image_cache_extractor

        if self.hash_manager:
            self.log_info("Using injected FBImageHashManager")
        else:
            self.log_warning("FBImageHashManager not provided by DI container")

        if self.browser_image_cache_extractor:
            self.log_info("Using injected BrowserImageCacheExtractor for unified caching")
        else:
            self.log_warning("BrowserImageCacheExtractor not provided - cache-first image loading disabled")

        self.log_debug("Using injected bandwidth logger instance")

        # Error recovery and circuit breaker configuration
        self._session_failure_count = 0
        self._last_session_failure_time = 0
        self._circuit_breaker_open = False
        self._circuit_breaker_open_time = 0
        self._max_session_failures = config.get("session_max_failures", 3)
        self._circuit_breaker_timeout = config.get("circuit_breaker_timeout", 300)  # 5 minutes
        self._base_retry_delay = config.get("retry_base_delay", 1.0)  # Base delay for exponential backoff
        self._max_retry_delay = config.get("retry_max_delay", 30.0)  # Max delay for exponential backoff
        
        self.log_debug(
            f"Error recovery initialized: max_failures={self._max_session_failures}, "
            f"circuit_timeout={self._circuit_breaker_timeout}s, "
            f"retry_delays={self._base_retry_delay}-{self._max_retry_delay}s"
        )

        try:
            os.makedirs(self.temp_download_dir, exist_ok=True)
        except OSError as e:
            self.log_error(
                f"Failed to create temporary image directory {self.temp_download_dir}: {e}"
            )

    async def _execute_action(self, data: Any) -> Any:
        """Execute image handler actions."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "process_upload_image":
                return await self.process_and_upload_ad_image(
                    data["ad_archive_id"], data["ad_creative_id"], data["image_url"]
                )
        raise FBAdServiceError("Invalid action data provided to ImageHandler")
    
    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open (should block operations)."""
        if not self._circuit_breaker_open:
            return False
        
        # Check if timeout has passed to close circuit breaker
        elapsed = time.time() - self._circuit_breaker_open_time
        if elapsed >= self._circuit_breaker_timeout:
            self._circuit_breaker_open = False
            self._session_failure_count = 0
            self.log_info(f"🔄 Circuit breaker reset after {elapsed:.1f}s timeout")
            return False
        
        return True
    
    def _record_session_failure(self):
        """Record a session failure and potentially open circuit breaker."""
        self._session_failure_count += 1
        self._last_session_failure_time = time.time()
        
        if self._session_failure_count >= self._max_session_failures:
            self._circuit_breaker_open = True
            self._circuit_breaker_open_time = time.time()
            self.log_warning(
                f"🚨 Circuit breaker opened after {self._session_failure_count} consecutive session failures"
            )
    
    def _record_session_success(self):
        """Record a successful session operation."""
        if self._session_failure_count > 0:
            self.log_info(f"✅ Session recovery successful after {self._session_failure_count} failures")
        self._session_failure_count = 0
        self._circuit_breaker_open = False
    
    async def _exponential_backoff_delay(self, attempt: int, base_delay: float = None) -> None:
        """Apply exponential backoff delay with jitter."""
        if base_delay is None:
            base_delay = self._base_retry_delay
        
        # Calculate exponential delay with jitter
        delay = min(base_delay * (2 ** attempt), self._max_retry_delay)
        jitter = random.uniform(0, delay * 0.1)  # Add up to 10% jitter
        total_delay = delay + jitter
        
        self.log_debug(f"⏳ Applying exponential backoff: {total_delay:.2f}s (attempt {attempt + 1})")
        await asyncio.sleep(total_delay)
    
    async def _ensure_session_with_recovery(self) -> bool:
        """Ensure session is valid with recovery and circuit breaker protection."""
        # Check circuit breaker first
        if self._is_circuit_breaker_open():
            remaining_time = self._circuit_breaker_timeout - (time.time() - self._circuit_breaker_open_time)
            self.log_warning(f"🚨 Circuit breaker is open, session recovery blocked for {remaining_time:.1f}s more")
            return False
        
        # Check if session is already valid
        if hasattr(self.session_manager, 'is_session_valid') and self.session_manager.is_session_valid():
            self._record_session_success()
            return True
        
        # Attempt session recovery with exponential backoff
        max_recovery_attempts = 3
        for attempt in range(max_recovery_attempts):
            try:
                self.log_info(f"🔄 Attempting session recovery (attempt {attempt + 1}/{max_recovery_attempts})")
                
                if attempt > 0:
                    await self._exponential_backoff_delay(attempt - 1)
                
                # Try to create new session
                if hasattr(self.session_manager, 'create_new_session'):
                    session_created = await self.session_manager.create_new_session()
                    if session_created:
                        self.log_info("✅ Session recovery successful")
                        self._record_session_success()
                        return True
                    else:
                        self.log_warning(f"❌ Session creation failed on attempt {attempt + 1}")
                else:
                    self.log_error("Session manager doesn't support create_new_session method")
                    break
                    
            except Exception as e:
                self.log_error(f"Session recovery attempt {attempt + 1} failed: {e}")
                
                if attempt == max_recovery_attempts - 1:
                    # Last attempt failed, record failure
                    self._record_session_failure()
        
        self.log_error("Session recovery failed after all attempts")
        return False

    async def process_and_upload_ad_image(
        self, ad_archive_id: str, ad_creative_id: str, image_url: str | None
    ) -> tuple[str | None, bool | None]:
        """
        Process and upload ad image with enhanced error recovery and circuit breaker protection.
        
        Features:
        - Circuit breaker pattern for repeated session failures
        - Exponential backoff for proxy and session errors
        - Automatic session recovery with validation
        - Enhanced retry logic for 407 proxy authentication errors
        """
        # Check circuit breaker first
        if self._is_circuit_breaker_open():
            self.log_warning(f"🚨 Circuit breaker is open, skipping image processing for {ad_archive_id}")
            return None, None
        
        max_retries = 3  # Increased retries with better error handling
        last_error = None
        
        for attempt in range(max_retries):
            try:
                # Ensure session is valid before attempting download
                if not await self._ensure_session_with_recovery():
                    self.log_error(f"Failed to ensure valid session for image {ad_archive_id} (attempt {attempt + 1})")
                    if attempt < max_retries - 1:
                        await self._exponential_backoff_delay(attempt)
                        continue
                    else:
                        return None, None
                
                # Attempt image processing
                result = await self._process_ad_image_internal(ad_archive_id, ad_creative_id, image_url)
                
                # Check for success
                if result[0] is not None or result[1] is True:
                    self._record_session_success()  # Record successful operation
                    return result
                
                # Check if the last error was a 407 that we should retry
                if hasattr(self, '_last_proxy_error_was_407') and self._last_proxy_error_was_407:
                    self.log_info(f"🔄 Retrying image download after 407 error (attempt {attempt + 1}/{max_retries})")
                    self._last_proxy_error_was_407 = False  # Reset flag
                    
                    # Apply exponential backoff for proxy errors
                    if attempt < max_retries - 1:
                        await self._exponential_backoff_delay(attempt, base_delay=2.0)
                    continue
                else:
                    # Not a retryable error
                    if result[0] is None and result[1] is None:
                        # Complete failure - apply circuit breaker logic
                        if attempt == max_retries - 1:
                            self._record_session_failure()
                    return result
                    
            except Exception as e:
                last_error = e
                self.log_error(f"Error in image processing attempt {attempt + 1}/{max_retries}: {e}")
                
                # Apply exponential backoff for exceptions
                if attempt < max_retries - 1:
                    await self._exponential_backoff_delay(attempt)
        
        # All attempts failed
        self._record_session_failure()
        self.log_error(f"Failed to process image after {max_retries} attempts with enhanced recovery. Last error: {last_error}")
        return None, None

    async def _process_ad_image_internal(
        self, ad_archive_id: str, ad_creative_id: str, image_url: str | None
    ) -> tuple[str | None, bool | None]:
        """
        Internal method that checks S3 existence, downloads if needed (with explicit proxy handling),
        processes, uploads, and CONSISTENTLY returns (s3_key|None, s3_exists_bool|None).
        - Returns (key, True) if file exists on S3.
        - Returns (key, False) if file did NOT exist but was successfully downloaded/uploaded.
        - Returns (None, False) if file did NOT exist and download/upload FAILED.
        - Returns (None, None) if S3 check failed or process was skipped early (e.g., bad URL).
        - Returns (None, check_result) if blocked or proxy error during download after check.
        """
        s3_exists_result: bool | None = None  # Variable to store the check result
        local_temp_path = (
            None  # Initialize to prevent potential UnboundLocalError in finally
        )

        if (
            not image_url
            or not isinstance(image_url, str)
            or not image_url.startswith("http")
        ):
            self.log_debug(
                f"IH: No valid image URL provided for ad {ad_archive_id}/{ad_creative_id}. Skipping."
            )
            return None, None  # Return two values

        # Use the correct S3 key format: adarchive/{ad_archive_id}/{ad_creative_id}.jpg
        s3_key = f"adarchive/{ad_archive_id}/{ad_creative_id}.jpg"
        # Define temp path using creative_id to avoid collisions in parallel runs
        local_temp_path = os.path.join(
            self.temp_download_dir, f"{ad_creative_id}_temp_{os.getpid()}.jpg"
        )

        # --- Step 1: Check S3 Existence ---
        try:
            self.log_debug(f"IH_CHECK: Checking S3 existence for key: '{s3_key}'")
            s3_exists_result = await self.s3_manager.file_exists(s3_key)
            self.log_debug(
                f"IH_CHECK: s3_manager.file_exists reported: {s3_exists_result} for key: '{s3_key}'"
            )

            if s3_exists_result is True:
                self.log_info(f"IH: Image exists in S3, skipping download: {s3_key}")
                
                # Log bandwidth savings
                if self.bandwidth_logger:
                    # Estimate image size based on typical Facebook ad images
                    estimated_size = 150 * 1024  # 150KB average
                    self.bandwidth_logger.log_s3_skip(estimated_size)
                
                if os.path.exists(local_temp_path):
                    try:
                        os.remove(local_temp_path)
                    except OSError as e:
                        self.log_warning(
                            f"IH_CHECK: Could not remove pre-existing temp file {local_temp_path}: {e}"
                        )
                return s3_key, True  # Return key and True for exists

            self.log_info(
                f"IH: Image not found in S3 (or check failed: {s3_exists_result}), proceeding to download: {s3_key}"
            )

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            self.log_error(
                f"IH_CHECK: S3 ClientError during existence check for {s3_key} (Code: {error_code}): {e}. Skipping image."
            )
            return None, None
        except Exception as e_generic_s3_check:
            self.log_error(
                f"IH_CHECK: Generic error during S3 file existence check for {s3_key}: {e_generic_s3_check}. Skipping image."
            )
            return None, None

        # PHash handling has been moved to processor.py

        # --- Step 2: Try Browser Cache First (NEW) ---
        # CACHE-FIRST APPROACH: Try unified browser image cache before any network downloads
        if self.browser_image_cache_extractor:
            self.log_debug(f"IH_CACHE: Attempting unified cache extraction for {image_url}")
            
            try:
                # Use the injected unified cache instance
                cached_image = await self.browser_image_cache_extractor.get_cached_image(image_url)
                if cached_image:
                    image_content = cached_image.content
                    self.log_info(f"🎯 UNIFIED_CACHE_HIT: Retrieved {len(image_content)} bytes from shared cache for {image_url}")
                else:
                    image_content = None
                    self.log_debug(f"❌ UNIFIED_CACHE_MISS: {image_url}")
                
                if image_content:
                    # Process cached image
                    image_content_buffer = BytesIO(image_content)
                    content_length = len(image_content)
                    
                    self.log_info(f"🎯 CACHE SUCCESS: Using cached image {content_length} bytes for {s3_key}")
                    
                    # Log cache hit to bandwidth logger
                    if self.bandwidth_logger:
                        self.bandwidth_logger.log_cache_hit(
                            url=image_url,
                            cached_size=content_length,
                            content_type="image"
                        )
                    
                    self.log_debug(f"💾 Cache hit saved downloading {content_length} bytes")
                    
                    # Process and save cached image
                    saved_locally = self._save_image_to_jpeg(image_content_buffer, local_temp_path)
                    if saved_locally:
                        # Upload to S3 (existing logic)
                        self.log_debug(f"IH_UPLOAD: Attempting upload to S3 for key: {s3_key} from {local_temp_path}")
                        upload_result = await self.s3_manager.upload_file(local_temp_path, s3_key)
                        
                        if isinstance(upload_result, tuple) and len(upload_result) == 2:
                            status = upload_result[1]
                            if status in ["uploaded", "reuploaded", "already exists"]:
                                self.log_info(f"IH_UPLOAD: Upload successful (Status: {status}) to S3: {s3_key}")
                                
                                # Calculate and save PHash to FBImageHash table (existing logic)
                                if self.hash_manager and local_temp_path and os.path.exists(local_temp_path):
                                    try:
                                        with open(local_temp_path, 'rb') as f:
                                            image_content_for_hash = f.read()
                                        
                                        phash = calculate_image_hash(image_content_for_hash)
                                        if phash:
                                            phash_str = str(phash)
                                            hash_record = {
                                                "PHash": phash_str,
                                                "AdArchiveID": ad_archive_id,
                                                "ImageUrl": image_url,
                                                "S3Key": s3_key,
                                                "LastUpdated": self.config.get("iso_date", ""),
                                            }
                                            await self.hash_manager.add_hash_record(hash_record)
                                            self.log_info(f"IH_UPLOAD: Saved PHash {phash_str} for {ad_archive_id} to FBImageHash table")
                                    except Exception as hash_e:
                                        self.log_error(f"IH_UPLOAD: Error saving PHash for {ad_archive_id}: {hash_e}")
                                
                                return s3_key, False  # Return key, False (didn't exist initially)
                            else:
                                self.log_error(f"IH_UPLOAD: Upload FAILED to S3 for key: {s3_key}. Status: {status}")
                                return None, False
                        else:
                            self.log_error(f"IH_UPLOAD: Upload to S3 for key {s3_key} returned unexpected result: {upload_result}")
                            return None, False
                    else:
                        self.log_error(f"IH_CACHE: Failed to save cached image locally: {local_temp_path} for {s3_key}")
                        # Continue to fallback download logic below
                else:
                    self.log_debug(f"🌐 CACHE MISS: Image not in cache, proceeding to network download: {image_url}")
                    # Note: Cache miss will be logged after we know the download size
                    
            except Exception as e:
                self.log_warning(f"IH_CACHE: Cache extraction failed, falling back to download: {e}")
        
        # --- Step 3, 4, 5: Fallback Download, Process, Upload (Only if cache failed) ---
        try:
            # Check if this is a Facebook CDN URL that we should download directly via HTTP
            is_cdn_url = any(cdn in image_url for cdn in ['fbcdn.net', 'facebook.com', 'fbsbx.com'])
            
            # Check if session manager supports Playwright-based downloads (Camoufox)
            # BUT skip Playwright for CDN URLs to avoid double bandwidth usage
            if hasattr(self.session_manager, 'download_image') and not is_cdn_url:
                # Use enhanced session recovery instead of simple validation
                if not await self._ensure_session_with_recovery():
                    self.log_warning(f"IH_DOWNLOAD: Session recovery failed for {image_url}, falling back to direct HTTP")
                    is_cdn_url = True  # Force CDN path for fallback
                
                if not is_cdn_url:  # Only use Playwright if we didn't fall back
                    # Use Playwright-based download for Camoufox
                    self.log_debug(f"IH_DOWNLOAD: Using Playwright-based download for Camoufox")
                    timeout = int(self.config.get("image_download_timeout", 30))
                    
                    # Download image using Playwright
                    image_content = await self.session_manager.download_image(image_url, timeout=timeout)
                
                if not image_content:
                    self.log_error(f"IH_DOWNLOAD: Failed to download image via Playwright: {image_url}")
                    return None, s3_exists_result
                
                # Save to local file
                image_content_buffer = BytesIO(image_content)
                content_length = len(image_content)
                
                self.log_debug(f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key}")
                
                # Log bandwidth usage
                if self.bandwidth_logger:
                    request_size = calculate_request_size({"User-Agent": "Mozilla/5.0"})
                    self.bandwidth_logger.log_request(
                        url=image_url,
                        request_size=request_size,
                        response_size=content_length,
                        method="GET"
                    )
                    self.log_debug(
                        f"Logged bandwidth for Playwright image download: {content_length} bytes from {image_url}"
                    )
                    # Note: We don't log direct HTTP savings here because this IS using Playwright
                
                # Process the image
                saved_locally = self._save_image_to_jpeg(
                    image_content_buffer, local_temp_path
                )
                if not saved_locally:
                    self.log_error(
                        f"IH_DOWNLOAD: Failed to save/convert image locally: {local_temp_path} for {s3_key}"
                    )
                    return None, s3_exists_result
                
            else:
                # Use legacy requests-based download (or forced for CDN URLs)
                if is_cdn_url:
                    self.log_info(f"IH_DOWNLOAD: Detected Facebook CDN URL, using direct HTTP download to avoid double bandwidth: {image_url}")
                
                # CRITICAL DEBUG: Log session manager details for troubleshooting
                session_type = type(self.session_manager).__name__ if self.session_manager else "None"
                self.log_info(f"🔍 IH_DOWNLOAD DEBUG: Session manager type: {session_type}")
                if hasattr(self.session_manager, '_is_session_valid'):
                    is_valid = self.session_manager._is_session_valid
                    self.log_info(f"🔍 IH_DOWNLOAD DEBUG: Camoufox session valid: {is_valid}")
                
                # Enhanced session validation with recovery
                session = self.session_manager.get_session()
                if not session:
                    # Use enhanced session recovery
                    self.log_warning(f"IH_DOWNLOAD: Session not available for {s3_key}. Using enhanced recovery...")
                    
                    if not await self._ensure_session_with_recovery():
                        # Session recovery failed
                        if is_cdn_url:
                            self.log_info("IH_DOWNLOAD: Facebook CDN detected - proceeding with direct HTTP download without session")
                            session = None  # Use None to trigger direct HTTP download
                        else:
                            self.log_error(f"IH_DOWNLOAD: Cannot download non-CDN image {s3_key} without valid session after recovery")
                            return None, s3_exists_result
                    else:
                        # Recovery successful, get the session
                        session = self.session_manager.get_session()
                        self.log_info("IH_DOWNLOAD: Enhanced session recovery successful")
                        
                        # Final validation
                        if not session and not is_cdn_url:
                            self.log_error(f"IH_DOWNLOAD: Session still not available after successful recovery for {s3_key}")
                            return None, s3_exists_result

                # --- Explicit Proxy Setup for Download ---
                proxies = None
                # Try to use proxy for Facebook CDN downloads when available
                if self.session_manager:
                    # Get proxy settings dictionary WITH AUTHENTICATION from session manager
                    # ASSUMES session_manager has a method returning {'http': '*********************:port', 'https': '...'}
                    try:
                        proxy_settings = self.session_manager.get_current_proxy_settings()
                        if proxy_settings:
                            proxies = proxy_settings
                            proxy_host = (
                                list(proxies.values())[0].split("@")[-1] if proxies else "N/A"
                            )  # Log host only
                            self.log_info(
                                f"IH_DOWNLOAD: Using proxy {proxy_host} for Facebook CDN image download."
                            )
                        else:
                            self.log_warning(
                                "IH_DOWNLOAD: Failed to get proxy settings from session manager - proceeding with direct connection."
                            )
                    except Exception as e:
                        self.log_warning(f"IH_DOWNLOAD: Error getting proxy settings: {e} - proceeding with direct connection.")
                else:
                    self.log_warning(
                        "IH_DOWNLOAD: Session manager not available - proceeding with direct connection."
                    )
                # --- End Explicit Proxy Setup ---

                self.log_debug(f"IH_DOWNLOAD: Attempting download: {image_url}")
                headers = {"User-Agent": "Mozilla/5.0"}  # Simple user agent
                timeout = int(self.config.get("image_download_timeout", 30))

                # Calculate request size before making the request
                request_size = calculate_request_size(headers)

                # Initialize variables for both paths
                image_content_buffer = None
                content_length = 0
                
                # Check if this is a CamoufoxSessionManager (has download_image method)
                if hasattr(session, 'download_image') and callable(getattr(session, 'download_image')):
                    # Use Camoufox's download_image method
                    self.log_debug(f"IH_DOWNLOAD: Using Camoufox download_image method")
                    try:
                        # Camoufox download_image is async
                        import asyncio
                        try:
                            loop = asyncio.get_running_loop()
                            image_content = await session.download_image(image_url, timeout=timeout)
                        except RuntimeError:
                            # No running loop, create one
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            image_content = loop.run_until_complete(
                                session.download_image(image_url, timeout=timeout)
                            )
                        
                        if not image_content:
                            self.log_error(f"IH_DOWNLOAD: Camoufox download_image returned None for {image_url}")
                            return None, s3_exists_result
                        
                        # Create a BytesIO buffer from the content
                        image_content_buffer = BytesIO(image_content)
                        content_length = len(image_content)
                        
                        self.log_debug(
                            f"IH_DOWNLOAD: Downloaded {content_length} bytes via Camoufox for {s3_key}"
                        )
                        
                        # Log bandwidth usage for Camoufox download
                        if self.bandwidth_logger:
                            self.bandwidth_logger.log_request(
                                url=image_url,
                                request_size=request_size,
                                response_size=content_length,
                                content_type="image/jpeg",  # Assume JPEG for FB images
                            )
                            self.log_debug(
                                f"Logged bandwidth for Camoufox image download: {content_length} bytes from {image_url}"
                            )
                        
                    except Exception as e:
                        self.log_error(f"IH_DOWNLOAD: Error using Camoufox download_image: {e}")
                        return None, s3_exists_result
                
                elif hasattr(session, 'get'):
                    # Standard requests.Session - use the get method
                    response = session.get(
                        image_url,
                        timeout=timeout,
                        stream=True,
                        headers=headers,
                        proxies=proxies,  # Pass the explicit proxy dict here
                    )
                    
                    # Check for FB blocks... (keep this logic)
                    response_text_preview = "[Binary Content]"
                    is_likely_text = "text" in response.headers.get("Content-Type", "").lower()
                    if is_likely_text:
                        try:
                            response_text_preview = next(
                                response.iter_content(chunk_size=512, decode_unicode=True), ""
                            )
                        except Exception:
                            pass
                    elif response.status_code != 200:
                        try:
                            response_text_preview = response.text[:500]
                        except Exception:
                            pass

                    if (
                        response.status_code in [403, 429, 503]
                        or "Temporarily Blocked" in response_text_preview
                        or "Try again later" in response_text_preview
                    ):
                        self.log_warning(
                            f"IH_DOWNLOAD: Block/Rate limit ({response.status_code}) downloading image: {image_url} for {s3_key}"
                        )
                        self.session_manager.handle_block_or_rate_limit("Image download block")
                        return None, s3_exists_result

                    response.raise_for_status()  # Check for other HTTP errors AFTER block checks

                    # --- Process downloaded content (keep this logic) ---
                    image_content_buffer = BytesIO()
                    content_length = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        image_content_buffer.write(chunk)
                        content_length += len(chunk)
                    self.log_debug(
                        f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key}"
                    )

                    # Log bandwidth usage for image download
                    content_type = response.headers.get("content-type", "")
                    if self.bandwidth_logger:
                        self.bandwidth_logger.log_request(
                            url=image_url,
                            request_size=request_size,
                            response_size=content_length,
                            method="GET",
                            headers=response.headers
                        )
                        self.log_debug(
                            f"Logged bandwidth for image download: {content_length} bytes from {image_url}"
                        )
                        
                        # If this was a CDN URL, log the savings from using direct HTTP
                        if is_cdn_url:
                            self.bandwidth_logger.log_direct_http_save(content_length)
                    else:
                        self.log_debug(
                            f"Downloaded {content_length} bytes from {image_url} (bandwidth logging disabled)"
                        )

                    if content_length == 0:
                        self.log_warning(
                            f"IH_DOWNLOAD: Downloaded empty content from {image_url} for {s3_key}. Skipping."
                        )
                        return None, s3_exists_result
                
                elif session is None:
                    # CRITICAL FIX: Handle case where session is None - use direct requests
                    self.log_info("IH_DOWNLOAD: No session available - using direct HTTP requests for Facebook CDN")
                    try:
                        response = requests.get(
                            image_url,
                            timeout=timeout,
                            stream=True,
                            headers=headers,
                            proxies=proxies,  # Pass the explicit proxy dict here (may be None)
                        )
                        
                        # Check for FB blocks
                        response_text_preview = "[Binary Content]"
                        is_likely_text = "text" in response.headers.get("Content-Type", "").lower()
                        if is_likely_text:
                            try:
                                response_text_preview = next(
                                    response.iter_content(chunk_size=512, decode_unicode=True), ""
                                )
                            except Exception:
                                pass
                        elif response.status_code != 200:
                            try:
                                response_text_preview = response.text[:500]
                            except Exception:
                                pass

                        if (
                            response.status_code in [403, 429, 503]
                            or "Temporarily Blocked" in response_text_preview
                            or "Try again later" in response_text_preview
                        ):
                            self.log_warning(
                                f"IH_DOWNLOAD: Block/Rate limit ({response.status_code}) downloading image: {image_url} for {s3_key}"
                            )
                            return None, s3_exists_result

                        response.raise_for_status()  # Check for other HTTP errors AFTER block checks

                        # Process downloaded content
                        image_content_buffer = BytesIO()
                        content_length = 0
                        for chunk in response.iter_content(chunk_size=8192):
                            image_content_buffer.write(chunk)
                            content_length += len(chunk)
                        self.log_debug(
                            f"IH_DOWNLOAD: Downloaded {content_length} bytes for {s3_key} using direct requests"
                        )

                        # Log bandwidth usage for image download
                        content_type = response.headers.get("content-type", "")
                        if self.bandwidth_logger:
                            self.bandwidth_logger.log_request(
                                url=image_url,
                                request_size=request_size,
                                response_size=content_length,
                                method="GET",
                                headers=response.headers
                            )
                            self.log_debug(
                                f"Logged bandwidth for direct image download: {content_length} bytes from {image_url}"
                            )
                            
                            # If this was a CDN URL, log the savings from using direct HTTP
                            if is_cdn_url:
                                self.bandwidth_logger.log_direct_http_save(content_length)

                        if content_length == 0:
                            self.log_warning(
                                f"IH_DOWNLOAD: Downloaded empty content from {image_url} for {s3_key}. Skipping."
                            )
                            return None, s3_exists_result
                            
                    except Exception as e:
                        self.log_error(f"IH_DOWNLOAD: Error using direct requests for {image_url}: {e}")
                        return None, s3_exists_result
                
                else:
                    self.log_error(f"IH_DOWNLOAD: Session object has neither 'get' nor 'download_image' method: {type(session).__name__}")
                    return None, s3_exists_result

                # Common processing for both paths (if we have content)
                if image_content_buffer and content_length > 0:
                    image_content_buffer.seek(0)
                saved_locally = self._save_image_to_jpeg(
                    image_content_buffer, local_temp_path
                )
                if not saved_locally:
                    self.log_error(
                        f"IH_DOWNLOAD: Failed to save/convert image locally: {local_temp_path} for {s3_key}"
                    )
                    return None, s3_exists_result

            # PHash calculation has been moved to processor.py after S3 upload

            # --- Upload to S3 (keep this logic) ---
            self.log_debug(
                f"IH_UPLOAD: Attempting upload to S3 for key: {s3_key} from {local_temp_path}"
            )
            # Assuming s3_manager.upload_file returns bool or similar indicating success
            upload_result = await self.s3_manager.upload_file(local_temp_path, s3_key)
            # Check the status string returned by upload_file
            if isinstance(upload_result, tuple) and len(upload_result) == 2:
                status = upload_result[1]
                if status in [
                    "uploaded",
                    "reuploaded",
                    "already exists",
                ]:  # Consider "already exists" a success here too? Adjust if needed.
                    self.log_info(
                        f"IH_UPLOAD: Upload successful (Status: {status}) to S3: {s3_key}"
                    )
                    
                    # Calculate and save PHash to FBImageHash table
                    if self.hash_manager and local_temp_path and os.path.exists(local_temp_path):
                        try:
                            # Read the image file to calculate PHash
                            with open(local_temp_path, 'rb') as f:
                                image_content = f.read()
                            
                            phash = calculate_image_hash(image_content)
                            if phash:
                                phash_str = str(phash)
                                
                                # Create FBImageHash record
                                hash_record = {
                                    "PHash": phash_str,
                                    "AdArchiveID": ad_archive_id,
                                    "ImageUrl": image_url,
                                    "S3Key": s3_key,  # Note: FBImageHashService expects 'S3Key' not 'S3ImageKey'
                                    "LastUpdated": self.config.get("iso_date", ""),  # Note: expects 'LastUpdated' not 'CreatedDate'/'LastSeen'
                                }
                                
                                # Save to FBImageHash table using the correct method name
                                await self.hash_manager.add_hash_record(hash_record)
                                self.log_info(
                                    f"IH_UPLOAD: Saved PHash {phash_str} for {ad_archive_id} to FBImageHash table"
                                )
                        except Exception as hash_e:
                            self.log_error(
                                f"IH_UPLOAD: Error saving PHash for {ad_archive_id}: {hash_e}"
                            )
                    
                    return (
                        s3_key,
                        False,
                    )  # Return key, and False (since it didn't exist initially)
                else:
                    self.log_error(
                        f"IH_UPLOAD: Upload FAILED to S3 for key: {s3_key}. Status: {status}"
                    )
                    return None, False
            else:  # Handle unexpected return type from upload_file
                self.log_error(
                    f"IH_UPLOAD: Upload to S3 for key {s3_key} returned unexpected result: {upload_result}"
                )
                return None, False

        # --- Exception Handling ---
        except requests.exceptions.ProxyError as pe:
            # Specific handling for ProxyError - includes the 407 Auth error
            self.log_error(
                f"IH_DOWNLOAD: ProxyError downloading image {image_url} (S3 key {s3_key}): {pe}"
            )
            if "407" in str(pe):
                self.log_error(
                    "IH_DOWNLOAD: *** Proxy Authentication Required (407). Triggering proxy type switch. ***"
                )
                # Trigger proxy rotation with 407 error code to switch proxy types
                self.session_manager.handle_block_or_rate_limit(
                    reason="Proxy Authentication Required",
                    error_code=407
                )
                # Set flag to indicate this was a 407 error for retry logic
                self._last_proxy_error_was_407 = True
                # After proxy switch, the wrapper will retry the download
            return (
                None,
                s3_exists_result,
            )  # Return None key and the original check result

        except RequestException as e:
            # Handle other network/HTTP errors
            status_code = (
                e.response.status_code
                if hasattr(e, "response") and e.response is not None
                else "N/A"
            )
            self.log_error(
                f"IH_DOWNLOAD: Download failed for image {image_url} (S3 key {s3_key}): {e} (Status: {status_code})"
            )
            if (
                hasattr(e, "response")
                and e.response is not None
                and e.response.status_code in [403, 429]
            ):
                self.session_manager.handle_block_or_rate_limit(
                    "Image download RequestException"
                )
            return None, s3_exists_result

        except OSError as e:
            self.log_error(
                f"IH_DOWNLOAD: File/Image processing IO error for {s3_key} ({local_temp_path}): {e}"
            )
            return None, s3_exists_result
        except Exception as e:
            self.log_error(
                f"IH_DOWNLOAD: Unexpected error processing image {s3_key} from {image_url}: {e}"
            )
            return None, s3_exists_result
        finally:
            # Ensure temp file is removed
            if local_temp_path and os.path.exists(local_temp_path):
                try:
                    os.remove(local_temp_path)
                    self.log_debug(
                        f"IH_CLEANUP: Removed temporary file {local_temp_path}"
                    )
                except OSError as e:
                    self.log_warning(
                        f"IH_CLEANUP: Could not remove temporary image file {local_temp_path}: {e}"
                    )

        # _save_image_to_jpeg remains the same...

    def _save_image_to_jpeg(
        self, image_content_stream: BytesIO, save_path: str
    ) -> bool:
        # (Keep the implementation from the previous response)
        try:
            with Image.open(image_content_stream) as image:
                image_to_save = None
                self.log_debug(
                    f"IH_SAVE: Processing image {os.path.basename(save_path)} (Mode: {image.mode}, Size: {image.size})"
                )
                if image.mode == "P":
                    image = image.convert("RGBA")
                    self.log_debug(
                        f"IH_SAVE: Converted image {os.path.basename(save_path)} from P to RGBA."
                    )
                if image.mode in ("RGBA", "LA"):
                    self.log_debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB with white background."
                    )
                    background = Image.new("RGB", image.size, (255, 255, 255))
                    try:
                        mask = image.getchannel("A")
                        background.paste(image, mask=mask)
                    except ValueError:
                        try:  # Try splitting as fallback
                            *_, mask = image.split()
                            background.paste(image, mask=mask)
                        except Exception:
                            self.log_warning(
                                f"IH_SAVE: Could not get or split alpha channel for {save_path}. Using simple convert."
                            )
                            image_to_save = image.convert("RGB")  # Fallback conversion

                    if image_to_save is None:
                        image_to_save = background
                elif image.mode != "RGB":
                    self.log_debug(
                        f"IH_SAVE: Converting image {os.path.basename(save_path)} from {image.mode} to RGB."
                    )
                    image_to_save = image.convert("RGB")
                else:
                    image_to_save = image
                if image_to_save:
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    image_to_save.save(save_path, format="JPEG", quality=85)
                    self.log_debug(
                        f"IH_SAVE: Saved image {os.path.basename(save_path)} as JPEG to {save_path}"
                    )
                    return True
                else:
                    self.log_error(
                        f"IH_SAVE: Image object became None during conversion for {save_path}"
                    )
                    return False
        except Exception as e:
            # Use PIL.UnidentifiedImageError if available/needed, else generic Exception
            # from PIL import UnidentifiedImageError
            # except UnidentifiedImageError: ...
            self.log_error(
                f"IH_SAVE: Pillow error processing/saving image to {save_path}: {e}"
            )
            return False

    # save_profile_picture remains the same...
    def save_profile_picture(
        self, law_firm_id: str, image_uri: str | None, local_dir: str
    ) -> bool | None:
        # (Keep the implementation from the previous response)
        if not law_firm_id or not image_uri:
            self.logger.warning(
                f"Missing ID ({law_firm_id}) or URI ({image_uri}) for profile pic."
            )
            return None
        local_img_dir = os.path.join(local_dir, "img")
        os.makedirs(local_img_dir, exist_ok=True)
        local_save_path = os.path.join(local_img_dir, f"{law_firm_id}.jpg")
        s3_save_path = f"assets/images/law-firm-logos/{law_firm_id}.jpg"
        s3_exists = None
        uploaded_to_s3 = False  # Track upload status
        try:
            self.logger.debug(f"PROFILE PIC: Checking S3 for {s3_save_path}")
            s3_exists = self.s3_manager.file_exists(s3_save_path)
            self.logger.debug(
                f"PROFILE PIC: s3_manager.file_exists returned: {s3_exists} for {s3_save_path}"
            )
            if s3_exists:
                self.logger.debug(
                    f"PROFILE PIC: Already exists in S3: {s3_save_path}. Skipping download."
                )
                return True
            self.logger.info(
                f"PROFILE PIC: Not found in S3 ({s3_save_path}). Proceeding with download."
            )
            
            # Check if this is a Facebook CDN URL
            is_cdn_url = any(cdn in image_uri for cdn in ['fbcdn.net', 'facebook.com', 'fbsbx.com'])
            
            # Check if session manager supports Playwright-based downloads (Camoufox)
            # BUT skip Playwright for CDN URLs to avoid double bandwidth usage
            if hasattr(self.session_manager, 'download_image') and not is_cdn_url:
                # Use Playwright-based download for Camoufox
                self.logger.debug(f"PROFILE PIC: Using Playwright-based download for profile picture")
                
                # Since save_profile_picture is not async, we need to run the async download in the event loop
                import asyncio
                try:
                    # Get or create event loop
                    try:
                        loop = asyncio.get_running_loop()
                    except RuntimeError:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    
                    # Run the async download
                    content = loop.run_until_complete(
                        self.session_manager.download_image(image_uri, timeout=15)
                    )
                except Exception as e:
                    self.logger.error(f"PROFILE PIC: Error running async download: {e}")
                    content = None
                
                if not content:
                    self.logger.error(f"PROFILE PIC: Failed to download profile pic via Playwright: {image_uri}")
                    return False
                
                content_length = len(content)
                content_type = "image/jpeg"  # Assume JPEG for profile pics
                request_size = calculate_request_size({"User-Agent": "Mozilla/5.0"})
                
            else:
                # Use legacy requests-based download (or forced for CDN URLs)
                if is_cdn_url:
                    self.logger.info(f"PROFILE PIC: Detected Facebook CDN URL, using direct HTTP download to avoid double bandwidth: {image_uri}")
                session = self.session_manager.get_session()
                if not session:
                    self.logger.error(
                        f"PROFILE PIC: Cannot download profile pic for {law_firm_id}: Session not available."
                    )
                    return False
                self.logger.debug(
                    f"PROFILE PIC: Attempting download via session: {image_uri}"
                )

                # Calculate request size before making the request
                request_size = calculate_request_size(
                    {"User-Agent": session.headers.get("User-Agent", "Mozilla/5.0")}
                )

                response = session.get(image_uri, timeout=15, stream=True)
                response.raise_for_status()

                # Get content and log bandwidth usage
                content = response.content
                content_length = len(content)
                content_type = response.headers.get("content-type", "")

            if self.bandwidth_logger:
                self.bandwidth_logger.log_request(
                    url=image_uri,
                    request_size=request_size,
                    response_size=content_length,
                    method="GET",
                    headers=response.headers if response else None
                )
                self.logger.debug(
                    f"Logged bandwidth for profile picture download: {content_length} bytes from {image_uri}"
                )
            else:
                self.logger.debug(
                    f"Downloaded profile picture {content_length} bytes from {image_uri} (bandwidth logging disabled)"
                )

            image_content_buffer = BytesIO(content)
            saved_locally = self._save_image_to_jpeg(
                image_content_buffer, local_save_path
            )
            if not saved_locally:
                self.logger.error(
                    f"PROFILE PIC: Failed to save profile pic locally to {local_save_path}"
                )
                return False
            self.logger.info(f"PROFILE PIC: Saved locally: {local_save_path}")
            self.logger.info(f"PROFILE PIC: Attempting upload to S3: {s3_save_path}")
            uploaded_to_s3 = self.s3_manager.upload_file(local_save_path, s3_save_path)
            if uploaded_to_s3:
                self.logger.info(f"PROFILE PIC: Uploaded to S3: {s3_save_path}")
                return True
            else:
                self.logger.error(
                    f"PROFILE PIC: Failed to upload profile pic to S3: {s3_save_path}"
                )
                return False
        except RequestException as e:
            err_msg = f"PROFILE PIC: Failed to download profile pic for {law_firm_id} from {image_uri}: {e}"
            if e.response is not None:
                err_msg += f" (Status: {e.response.status_code})"
            self.logger.error(err_msg)
            return False
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            status_code = e.response.get("ResponseMetadata", {}).get("HTTPStatusCode")
            self.logger.error(
                f"PROFILE PIC: S3 ClientError during processing for {law_firm_id} (Code: {error_code}, Status: {status_code}): {e}"
            )
            return False
        except OSError as e:
            self.logger.error(
                f"PROFILE PIC: IOError saving profile picture {local_save_path} for {law_firm_id}: {e}"
            )
            return False
        except Exception as e:
            self.logger.error(
                f"PROFILE PIC: Unexpected error saving profile picture for {law_firm_id}: {e}",
                exc_info=self.config.get("verbose", False),
            )
            return False
        finally:
            if os.path.exists(local_save_path):
                # Remove if S3 check was True OR if upload failed after local save
                should_remove = s3_exists is True or not uploaded_to_s3
                if should_remove:
                    try:
                        os.remove(local_save_path)
                        self.logger.debug(
                            f"PROFILE PIC: Removed local file {local_save_path}"
                        )
                    except OSError as e:
                        self.logger.warning(
                            f"PROFILE PIC: Could not remove local profile pic file {local_save_path}: {e}"
                        )

    async def get_phash_and_existing_text(
        self,
        s3_key: str,
        ad_archive_id: str,
        ad_creative_id: str,
        current_process_date: str,
    ) -> tuple[str | None, str | None]:
        """
        Get perceptual hash and check for existing ImageText in FBImageHash table.

        Args:
            s3_key: S3 key of the image
            ad_archive_id: Ad archive ID
            ad_creative_id: Ad creative ID
            current_process_date: Current processing date

        Returns:
            Tuple of (phash_str, existing_image_text)
        """
        phash_str = None
        existing_image_text = None

        try:
            # Download image from S3 to calculate phash
            if s3_key:
                image_content = await self.s3_manager.download_content(s3_key)
                if image_content:
                    # Calculate perceptual hash
                    phash = calculate_image_hash(image_content)
                    if phash:
                        phash_str = str(phash)

                        # Check if we have FBImageHashManager configured
                        if self.hash_manager:
                            # Query for existing record by phash
                            existing_records = await self.hash_manager.query_by_hash(
                                phash_str
                            )
                            if existing_records:
                                # Look for ImageText in any of the records
                                for record in existing_records:
                                    if "ImageText" in record and record["ImageText"]:
                                        existing_image_text = record["ImageText"]
                                        self.logger.debug(
                                            f"Found existing ImageText for phash {phash_str}: "
                                            f"{existing_image_text[:50]}..."
                                        )
                                        break
                else:
                    self.logger.warning(f"Could not download image from S3: {s3_key}")
            else:
                self.logger.debug("No S3 key provided for phash calculation")

        except Exception as e:
            self.logger.error(
                f"Error getting phash and existing text for {ad_archive_id}: {e}",
                exc_info=self.config.get("verbose", False),
            )

        return phash_str, existing_image_text
