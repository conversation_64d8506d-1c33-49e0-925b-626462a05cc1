# Process-Safe Cache Implementation Guide

## Quick Start for Implementation Agents

This guide provides step-by-step instructions for implementing the process-safe cache solution designed in `process_safe_cache_design.md`.

## Implementation Checklist

- [ ] Create cache backend abstraction layer
- [ ] Implement LocalDictBackend (current behavior)
- [ ] Implement SharedDictBackend (multiprocessing support)
- [ ] Enhance DiskCacheBackend for cross-process use
- [ ] Create ProcessSafeCacheManager
- [ ] Integrate with BrowserImageCacheExtractor
- [ ] Add configuration and feature flags
- [ ] Create comprehensive tests
- [ ] Update documentation

## Step 1: Create Cache Backend Infrastructure

### 1.1 Create Backend Interface

Create file: `src/services/fb_ads/cache_backends/__init__.py`

```python
"""Cache backend infrastructure for process-safe caching."""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

@dataclass
class CachedImageResource:
    """Image resource stored in cache."""
    url: str
    content: bytes
    content_type: str
    content_length: int
    timestamp: float
    ad_archive_id: Optional[str] = None
    ad_creative_id: Optional[str] = None
    source: str = "browser_cache"

class CacheBackend(ABC):
    """Abstract base class for cache backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[bytes]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: bytes) -> None:
        """Set value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> None:
        """Delete key from cache."""
        pass
    
    @abstractmethod
    async def keys(self) -> List[str]:
        """Get all keys in cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all entries from cache."""
        pass
    
    @abstractmethod
    async def size(self) -> int:
        """Get total size of cached data in bytes."""
        pass

class CacheSerializer:
    """Handles serialization of cache objects."""
    
    @staticmethod
    def serialize(obj: CachedImageResource) -> bytes:
        """Serialize CachedImageResource to bytes."""
        import pickle
        return pickle.dumps(obj)
    
    @staticmethod
    def deserialize(data: bytes) -> CachedImageResource:
        """Deserialize bytes to CachedImageResource."""
        import pickle
        return pickle.loads(data)
```

### 1.2 Implement LocalDictBackend

Create file: `src/services/fb_ads/cache_backends/local_dict_backend.py`

```python
"""Local dictionary backend for single-process caching."""

import asyncio
from typing import Optional, List, Dict
from . import CacheBackend

class LocalDictBackend(CacheBackend):
    """Fast local dictionary backend for single-process use."""
    
    def __init__(self):
        self._cache: Dict[str, bytes] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[bytes]:
        async with self._lock:
            return self._cache.get(key)
    
    async def set(self, key: str, value: bytes) -> None:
        async with self._lock:
            self._cache[key] = value
    
    async def delete(self, key: str) -> None:
        async with self._lock:
            self._cache.pop(key, None)
    
    async def keys(self) -> List[str]:
        async with self._lock:
            return list(self._cache.keys())
    
    async def clear(self) -> None:
        async with self._lock:
            self._cache.clear()
    
    async def size(self) -> int:
        async with self._lock:
            return sum(len(v) for v in self._cache.values())
```

### 1.3 Implement SharedDictBackend

Create file: `src/services/fb_ads/cache_backends/shared_dict_backend.py`

```python
"""Shared dictionary backend for multiprocessing support."""

import asyncio
import multiprocessing
from typing import Optional, List
from . import CacheBackend

class SharedDictBackend(CacheBackend):
    """Multiprocessing-safe shared dictionary backend."""
    
    def __init__(self, shared_dict: multiprocessing.managers.DictProxy):
        self._cache = shared_dict
        self._lock = asyncio.Lock()
        # Also need process-level lock for true safety
        self._process_lock = multiprocessing.Lock()
    
    async def get(self, key: str) -> Optional[bytes]:
        async with self._lock:
            with self._process_lock:
                return self._cache.get(key)
    
    async def set(self, key: str, value: bytes) -> None:
        async with self._lock:
            with self._process_lock:
                self._cache[key] = value
    
    async def delete(self, key: str) -> None:
        async with self._lock:
            with self._process_lock:
                if key in self._cache:
                    del self._cache[key]
    
    async def keys(self) -> List[str]:
        async with self._lock:
            with self._process_lock:
                return list(self._cache.keys())
    
    async def clear(self) -> None:
        async with self._lock:
            with self._process_lock:
                self._cache.clear()
    
    async def size(self) -> int:
        async with self._lock:
            with self._process_lock:
                return sum(len(v) for v in self._cache.values())
```

### 1.4 Enhance DiskCacheBackend

Create file: `src/services/fb_ads/cache_backends/disk_cache_backend.py`

```python
"""Disk-based cache backend for ultimate reliability."""

import asyncio
import json
import hashlib
from pathlib import Path
from typing import Optional, List
import aiofiles
import aiofiles.os
from . import CacheBackend

class DiskCacheBackend(CacheBackend):
    """File-based cache backend that works across any architecture."""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._lock = asyncio.Lock()
    
    def _get_cache_path(self, key: str) -> Path:
        """Get file path for cache key."""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    async def get(self, key: str) -> Optional[bytes]:
        cache_path = self._get_cache_path(key)
        if not cache_path.exists():
            return None
        
        async with self._lock:
            try:
                async with aiofiles.open(cache_path, 'rb') as f:
                    return await f.read()
            except Exception:
                return None
    
    async def set(self, key: str, value: bytes) -> None:
        cache_path = self._get_cache_path(key)
        
        async with self._lock:
            async with aiofiles.open(cache_path, 'wb') as f:
                await f.write(value)
    
    async def delete(self, key: str) -> None:
        cache_path = self._get_cache_path(key)
        
        async with self._lock:
            try:
                await aiofiles.os.remove(cache_path)
            except FileNotFoundError:
                pass
    
    async def keys(self) -> List[str]:
        # Store key mapping in index file
        index_file = self.cache_dir / "index.json"
        
        async with self._lock:
            if not index_file.exists():
                return []
            
            try:
                async with aiofiles.open(index_file, 'r') as f:
                    index = json.loads(await f.read())
                return list(index.keys())
            except Exception:
                return []
    
    async def clear(self) -> None:
        async with self._lock:
            for cache_file in self.cache_dir.glob("*.cache"):
                await aiofiles.os.remove(cache_file)
            
            index_file = self.cache_dir / "index.json"
            if index_file.exists():
                await aiofiles.os.remove(index_file)
    
    async def size(self) -> int:
        total_size = 0
        for cache_file in self.cache_dir.glob("*.cache"):
            total_size += cache_file.stat().st_size
        return total_size
```

## Step 2: Create ProcessSafeCacheManager

Create file: `src/services/fb_ads/process_safe_cache_manager.py`

```python
"""Manager for process-safe cache with automatic backend selection."""

import asyncio
import multiprocessing
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from .cache_backends import (
    CacheBackend, CacheSerializer, CachedImageResource,
    LocalDictBackend, SharedDictBackend, DiskCacheBackend
)

class ManagerRegistry:
    """Singleton registry for shared multiprocessing Manager."""
    _instance = None
    _manager: Optional[multiprocessing.Manager] = None
    
    @classmethod
    def get_or_create_manager(cls) -> multiprocessing.Manager:
        """Get or create shared manager instance."""
        if cls._manager is None:
            cls._manager = multiprocessing.Manager()
        return cls._manager
    
    @classmethod
    def cleanup(cls):
        """Clean up manager resources."""
        if cls._manager:
            try:
                cls._manager.shutdown()
            except Exception:
                pass
            cls._manager = None

class ProcessSafeCacheManager:
    """
    Manages cache storage with automatic backend selection.
    
    Selects the best available backend:
    1. Shared memory dict (multiprocessing.Manager) if in multiprocess mode
    2. Local dict with asyncio locks (fastest for single process)
    3. Disk-based fallback for ultimate reliability
    """
    
    def __init__(
        self,
        logger: logging.Logger,
        disk_cache_dir: Optional[Path] = None,
        enable_process_safe: Optional[bool] = None,
        shared_manager: Optional[multiprocessing.Manager] = None
    ):
        self.logger = logger
        self.disk_cache_dir = disk_cache_dir or Path(".cache/images")
        
        # Auto-detect if not specified
        if enable_process_safe is None:
            enable_process_safe = self._detect_multiprocessing_context()
        
        self.enable_process_safe = enable_process_safe
        self._shared_manager = shared_manager
        
        # Initialize backend
        self._backend = self._initialize_backend()
        self._normalized_url_index: Dict[str, list] = {}
        self._lock = asyncio.Lock()
        
        self.logger.info(
            f"ProcessSafeCacheManager initialized with backend: {type(self._backend).__name__}"
        )
    
    def _detect_multiprocessing_context(self) -> bool:
        """Detect if running in multiprocessing context."""
        # Check if we're in a child process
        current = multiprocessing.current_process()
        if current.name != 'MainProcess':
            return True
        
        # Check environment for multiprocessing flags
        import os
        if os.environ.get('LEXGENIUS_MULTIPROCESS_MODE'):
            return True
        
        return False
    
    def _initialize_backend(self) -> CacheBackend:
        """Initialize appropriate cache backend."""
        if self.enable_process_safe:
            try:
                # Try shared memory first
                manager = self._shared_manager or ManagerRegistry.get_or_create_manager()
                shared_dict = manager.dict()
                self.logger.info("Using SharedDictBackend for multiprocessing support")
                return SharedDictBackend(shared_dict)
            except Exception as e:
                self.logger.warning(
                    f"Failed to initialize shared memory: {e}. "
                    "Falling back to disk cache."
                )
                return DiskCacheBackend(self.disk_cache_dir)
        else:
            # Single process - use fast local dict
            self.logger.info("Using LocalDictBackend for single-process mode")
            return LocalDictBackend()
    
    async def get_cached_image(self, url: str) -> Optional[CachedImageResource]:
        """Get image from cache."""
        try:
            data = await self._backend.get(url)
            if data:
                return CacheSerializer.deserialize(data)
            return None
        except Exception as e:
            self.logger.error(f"Cache get error: {e}")
            return None
    
    async def store_cached_image(self, image: CachedImageResource) -> None:
        """Store image in cache."""
        try:
            data = CacheSerializer.serialize(image)
            await self._backend.set(image.url, data)
            
            # Update normalized URL index
            async with self._lock:
                normalized_url = self._normalize_url(image.url)
                if normalized_url not in self._normalized_url_index:
                    self._normalized_url_index[normalized_url] = []
                if image.url not in self._normalized_url_index[normalized_url]:
                    self._normalized_url_index[normalized_url].append(image.url)
                    
        except Exception as e:
            self.logger.error(f"Cache store error: {e}")
    
    async def remove_cached_image(self, url: str) -> None:
        """Remove image from cache."""
        try:
            await self._backend.delete(url)
            
            # Update normalized URL index
            async with self._lock:
                normalized_url = self._normalize_url(url)
                if normalized_url in self._normalized_url_index:
                    self._normalized_url_index[normalized_url].remove(url)
                    if not self._normalized_url_index[normalized_url]:
                        del self._normalized_url_index[normalized_url]
                        
        except Exception as e:
            self.logger.error(f"Cache delete error: {e}")
    
    async def clear_cache(self) -> None:
        """Clear all cached images."""
        await self._backend.clear()
        async with self._lock:
            self._normalized_url_index.clear()
    
    async def get_cache_size(self) -> int:
        """Get total cache size in bytes."""
        return await self._backend.size()
    
    async def get_all_keys(self) -> list:
        """Get all cache keys."""
        return await self._backend.keys()
    
    def _normalize_url(self, url: str) -> str:
        """Normalize URL for index lookup."""
        # Implement URL normalization logic here
        # This is a simplified version
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
```

## Step 3: Integration with BrowserImageCacheExtractor

### 3.1 Update BrowserImageCacheExtractor

Modify `src/services/fb_ads/browser_image_cache_extractor.py`:

```python
# Add imports
from .process_safe_cache_manager import ProcessSafeCacheManager

# In __init__ method, replace cache initialization:

# OLD CODE:
# self._image_cache: Dict[str, CachedImageResource] = {}
# self._normalized_url_index: Dict[str, List[str]] = {}
# self._cache_lock = asyncio.Lock()

# NEW CODE:
self._cache_manager = ProcessSafeCacheManager(
    logger=self.logger,
    disk_cache_dir=Path(self._disk_cache_dir) if self._disk_cache_dir else None,
    enable_process_safe=config.get("enable_process_safe_cache"),
    shared_manager=kwargs.get("shared_manager")  # Injected by container
)

# Update all cache operations to use cache manager:
# Replace self._image_cache[url] with await self._cache_manager.get_cached_image(url)
# Replace self._image_cache[url] = resource with await self._cache_manager.store_cached_image(resource)
```

### 3.2 Update Container Configuration

Modify `src/containers/fb_ads.py`:

```python
# Add manager provider
shared_manager = providers.Resource(
    ManagerRegistry.get_or_create_manager,
    condition=lambda config: config.get("enable_process_safe_cache", False)
)

# Update browser_image_cache_extractor
browser_image_cache_extractor = providers.Singleton(
    BrowserImageCacheExtractor,
    logger=logger,
    config=config,
    max_cache_size_mb=500,
    cache_ttl_minutes=1440,
    bandwidth_logger=None,
    enable_disk_persistence=True,
    shared_manager=shared_manager  # Add this
)
```

## Step 4: Configuration

Add to configuration file:

```yaml
# Cache configuration
cache:
  # Enable process-safe cache (auto-detected if not set)
  enable_process_safe_cache: null
  
  # Force specific backend
  force_cache_backend: null  # "local", "shared", or "disk"
  
  # Disk cache location
  disk_cache_dir: ".cache/images"
  
  # Performance tuning
  cache_operation_timeout: 5.0  # seconds
  enable_cache_compression: false
```

## Step 5: Testing Strategy

### 5.1 Unit Tests

Create `tests/unit/test_cache_backends.py`:

```python
import pytest
import asyncio
from src.services.fb_ads.cache_backends import *

@pytest.mark.asyncio
async def test_local_dict_backend():
    backend = LocalDictBackend()
    
    # Test basic operations
    await backend.set("key1", b"value1")
    assert await backend.get("key1") == b"value1"
    
    # Test delete
    await backend.delete("key1")
    assert await backend.get("key1") is None
    
    # Test clear
    await backend.set("key2", b"value2")
    await backend.clear()
    assert await backend.get("key2") is None

# Similar tests for SharedDictBackend and DiskCacheBackend
```

### 5.2 Integration Tests

Create `tests/integration/test_process_safe_cache.py`:

```python
import pytest
import multiprocessing
from src.services.fb_ads.process_safe_cache_manager import ProcessSafeCacheManager

def worker_process(cache_manager, results_queue):
    """Worker process for testing cross-process cache."""
    # Store image in cache
    image = CachedImageResource(
        url="http://example.com/image.jpg",
        content=b"image_data",
        content_type="image/jpeg",
        content_length=10,
        timestamp=time.time()
    )
    asyncio.run(cache_manager.store_cached_image(image))
    results_queue.put("stored")

@pytest.mark.asyncio
async def test_cross_process_cache():
    """Test cache sharing across processes."""
    manager = ProcessSafeCacheManager(
        logger=logger,
        enable_process_safe=True
    )
    
    # Start worker process
    queue = multiprocessing.Queue()
    process = multiprocessing.Process(
        target=worker_process,
        args=(manager, queue)
    )
    process.start()
    process.join()
    
    # Verify cache in main process
    cached = await manager.get_cached_image("http://example.com/image.jpg")
    assert cached is not None
    assert cached.content == b"image_data"
```

## Step 6: Rollout Plan

### 6.1 Feature Flag Implementation

```python
# In JobOrchestrationService
if self.config.get("features", {}).get("enable_process_safe_cache", False):
    # Initialize shared manager before spawning jobs
    ManagerRegistry.get_or_create_manager()
```

### 6.2 Monitoring

Add metrics collection:

```python
class CacheMetrics:
    def __init__(self):
        self.backend_type = None
        self.operations = {"get": 0, "set": 0, "delete": 0}
        self.errors = {"get": 0, "set": 0, "delete": 0}
        self.latencies = {"get": [], "set": [], "delete": []}
    
    def record_operation(self, op_type: str, duration: float, error: bool = False):
        self.operations[op_type] += 1
        if error:
            self.errors[op_type] += 1
        self.latencies[op_type].append(duration)
```

## Implementation Order

1. **Day 1**: Implement cache backends and tests
2. **Day 2**: Create ProcessSafeCacheManager
3. **Day 3**: Integrate with BrowserImageCacheExtractor
4. **Day 4**: Add configuration and monitoring
5. **Day 5**: Testing and performance validation

## Success Criteria

1. All existing tests pass without modification
2. Cache works identically in single-process mode
3. Cache successfully shares data in multiprocess tests
4. Performance overhead < 5% in single-process mode
5. No memory leaks or resource exhaustion

## Notes for Implementers

1. **Serialization**: Use pickle for now, but consider msgpack for better performance
2. **Compression**: Add optional compression for large images
3. **TTL**: Implement TTL cleanup in background task
4. **Monitoring**: Add detailed metrics for production debugging
5. **Testing**: Test with actual 83+ image workload

This implementation guide provides a clear path for other agents to implement the process-safe cache solution. Follow the steps in order and ensure each component is thoroughly tested before moving to the next.