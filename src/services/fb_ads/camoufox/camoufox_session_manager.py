"""
Camoufox-based session manager for Facebook.

Uses Camoufox browser automation for session management with anti-detection
capabilities, proxy support, and automatic session lifecycle management.

Configuration options for ad blocker detection avoidance:
- camoufox.anti_bot.disable_ad_blocker_detection: True (default) - Applies JavaScript
  to hide webdriver properties and avoid ad blocker detection
- camoufox.anti_bot.block_resources_for_performance: False (default) - Disables resource
  blocking to avoid triggering ad blocker detection on websites

The browser is configured with addons: [] to ensure no ad blockers are loaded.
"""

import asyncio
import json
import logging
import random
import shutil
import time
import urllib.parse
import os
import tempfile
import hashlib
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

import aiohttp
from aiohttp import ClientError, ClientConnectionResetError

from ..base.session_manager_base import SessionManagerBase
from src.infrastructure.decorators.async_decorators import retry_async


class CamoufoxSessionManager(SessionManagerBase):
    """Camoufox-based session manager for Facebook."""

    # Class-level lock to prevent concurrent browser starts
    _browser_start_lock = asyncio.Lock()

    def __init__(self, config: Dict[str, Any], logger: logging.Logger,
                 fingerprint_manager=None, proxy_manager=None, law_firms_repository=None,
                 browser_image_cache_extractor=None):
        """
        Initialize Camoufox session manager.

        Args:
            config: Configuration dictionary
            logger: Logger instance
            fingerprint_manager: Optional fingerprint management service
            proxy_manager: Optional proxy management service
            law_firms_repository: Optional law firms repository for page name lookups
            browser_image_cache_extractor: Optional shared browser image cache extractor instance
        """
        self.logger = logger
        self.advertiser_bandwidth_logger = None  # Will be set when processing specific advertiser
        self.logger.info(f"🦊 CamoufoxSessionManager.__init__() called")
        self.logger.info(f"🔧 Config keys: {list(config.keys()) if config else 'None'}")
        self.logger.info(f"🔍 Fingerprint manager: {type(fingerprint_manager).__name__ if fingerprint_manager else 'None'}")
        self.logger.info(f"🌐 Proxy manager: {type(proxy_manager).__name__ if proxy_manager else 'None'}")
        self.logger.info(f"🏢 Law firms repository: {type(law_firms_repository).__name__ if law_firms_repository else 'None'}")

        # Enhanced config logging for debugging
        if config:
            camoufox_config = config.get('camoufox', {})
            self.logger.info(f"📋 Camoufox config section: {camoufox_config}")
            force_clean_from_camoufox = camoufox_config.get('force_clean_profile')
            force_clean_from_root = config.get('force_clean_profile')
            self.logger.info(f"🔍 force_clean_profile from camoufox section: {force_clean_from_camoufox}")
            self.logger.info(f"🔍 force_clean_profile from root config: {force_clean_from_root}")

        self.config = config
        self.fingerprint_manager = fingerprint_manager
        self.proxy_manager = proxy_manager
        self.current_proxy = None  # Initialize current proxy
        self.law_firms_repository = law_firms_repository

        # Store injected browser image cache extractor
        self.image_cache_extractor = browser_image_cache_extractor
        self.image_cache_enabled = browser_image_cache_extractor is not None

        if self.image_cache_extractor:
            self.logger.info(f"🖼️ Using injected BrowserImageCacheExtractor instance")
        else:
            self.logger.info(f"🚫 No BrowserImageCacheExtractor instance provided")

        # Browser and page instances
        self.browser = None
        self.browser_cm = None  # Context manager for browser, needed for cleanup
        self.page = None
        self.context = None

        # Session data and timing
        self.session_data = {}
        self.session_start_time = None
        self.last_activity_time = None

        # Configuration
        if 'camoufox' not in config:
            self.logger.error("❌ CRITICAL: 'camoufox' section missing from config! This should never happen.")
            self.logger.error(f"Available config keys: {list(config.keys())}")
            raise ValueError("Configuration must include 'camoufox' section")

        camoufox_config = config.get('camoufox', {})
        session_config = camoufox_config.get('session', {})

        self.logger.info(f"📋 Camoufox config: {camoufox_config}")
        self.logger.info(f"⏱️ Session config: {session_config}")

        self.min_session_duration = session_config.get('min_duration_minutes', 3) * 60
        self.max_session_duration = session_config.get('max_duration_minutes', 5) * 60
        self.refresh_before_expiry = session_config.get('refresh_before_expiry_seconds', 30)

        self.logger.info(f"⏱️ Session duration: {self.min_session_duration}s - {self.max_session_duration}s")

        # Browser configuration
        browser_config = camoufox_config.get('browser', {})

        # CRITICAL FIX: Check for top-level headless override FIRST
        if 'headless' in config:
            self.headless = config['headless']
            self.logger.info(f"🎯 HEADLESS OVERRIDE: Using top-level headless={self.headless} (ignoring camoufox.browser.headless)")
        else:
            self.headless = browser_config.get('headless')
            self.logger.info(f"📋 Using camoufox.browser.headless={self.headless} (no top-level override found)")

        self.browser_timeout = browser_config.get('timeout', 30000)
        self.viewport = browser_config.get('viewport', {'width': 1920, 'height': 1080})
        self.max_browsers = browser_config.get('max_browsers', config.get('num_workers', 8))

        self.logger.info(f"🖥️ Browser config: headless={self.headless}, timeout={self.browser_timeout}ms, max_browsers={self.max_browsers}")
        self.logger.info(f"📐 Viewport: {self.viewport}")

        # Anti-bot configuration
        antibot_config = camoufox_config.get('anti_bot', {})
        self.humanize = antibot_config.get('humanize', True)
        self.mouse_curves = antibot_config.get('mouse_curves', True)
        self.typing_variation = antibot_config.get('typing_variation', True)

        # Ad blocker detection avoidance
        self.disable_ad_blocker_detection = antibot_config.get('disable_ad_blocker_detection', True)
        self.block_resources_for_performance = antibot_config.get('block_resources_for_performance', False)

        self.logger.info(f"🤖 Anti-bot config: humanize={self.humanize}, mouse_curves={self.mouse_curves}")
        self.logger.info(f"🛡️ Ad blocker detection disabled: {self.disable_ad_blocker_detection}")

        # Session state
        self._is_session_valid = False
        self._last_error = None

        # GraphQL response capture
        self._graphql_responses = []
        self._accumulated_ndjson = ""
        self._response_interception_active = False

        # Enhanced debugging for response sequence
        self._all_responses = []  # Capture ALL responses for debugging
        self._response_sequence_counter = 0

        # Track pending operations to prevent premature cleanup
        self._pending_operations = set()
        self._operation_lock = asyncio.Lock()
        self._is_closing = False
        self._target_page_id = None  # Store the target page ID for filtering

        # NOTE: Image cache extractor is now injected via constructor parameter
        # instead of being created here to enable unified caching across components

        self.logger.info(f"✅ CamoufoxSessionManager initialization complete")

        # Initialize unique profile management
        self._profile_path = None
        self._firm_id = None

    def _generate_unique_profile_path(self, firm_id: str) -> str:
        """
        Generate a unique browser profile directory for the given firm.

        Args:
            firm_id: Unique identifier for the law firm

        Returns:
            str: Path to unique profile directory
        """
        # Create a stable hash of the firm ID for consistent profile paths
        firm_hash = hashlib.md5(str(firm_id).encode()).hexdigest()[:12]

        # Use data directory structure
        base_dir = self.config.get('data_dir', './data')
        profiles_dir = Path(base_dir) / 'browser_profiles' / 'camoufox'

        # Create unique profile directory
        profile_path = profiles_dir / f"firm_{firm_hash}"

        # Log configuration status
        force_clean = self.config.get('camoufox', {}).get('force_clean_profile', self.config.get('force_clean_profile', True))
        self.logger.info(f"🔧 Profile cleaning configuration: force_clean_profile={force_clean}")

        # Force clear existing profile to remove any cached addon data, cookies, and contexts
        if profile_path.exists() and force_clean:
            self.logger.info(f"🧹 PROFILE CLEANUP: Starting aggressive profile cleanup for: {profile_path}")

            # First, try to remove the profile directory
            try:
                import time
                # Check if directory exists before deletion
                if profile_path.exists():
                    self.logger.info(f"📁 Profile directory exists, size: {sum(f.stat().st_size for f in profile_path.rglob('*') if f.is_file())} bytes")
                    shutil.rmtree(profile_path, ignore_errors=False)
                    # Wait a moment to ensure filesystem catches up
                    time.sleep(0.5)
                    # Verify deletion
                    if not profile_path.exists():
                        self.logger.info(f"✅ Profile directory successfully deleted: {profile_path}")
                    else:
                        self.logger.error(f"❌ Profile directory still exists after deletion attempt!")
                else:
                    self.logger.info(f"📁 Profile directory doesn't exist, nothing to clean")

                # Also clear any potential Camoufox cache directories
                cache_dir = profiles_dir / '.cache' / f"firm_{firm_hash}"
                if cache_dir.exists():
                    self.logger.info(f"🗑️ Clearing cache directory: {cache_dir}")
                    shutil.rmtree(cache_dir, ignore_errors=False)
                    if not cache_dir.exists():
                        self.logger.info(f"✅ Cache directory successfully cleared: {cache_dir}")
                    else:
                        self.logger.error(f"❌ Cache directory still exists after deletion!")

                # Clear parent-level Firefox cache that might contain addon data
                firefox_cache_dirs = [
                    profiles_dir.parent / '.mozilla',
                    profiles_dir.parent / '.cache' / 'mozilla',
                    Path.home() / '.cache' / 'camoufox',
                ]

                for cache_path in firefox_cache_dirs:
                    if cache_path.exists():
                        try:
                            self.logger.info(f"🗑️ Clearing Firefox cache: {cache_path}")
                            shutil.rmtree(cache_path, ignore_errors=True)
                            self.logger.info(f"✅ Cleared Firefox cache: {cache_path}")
                        except Exception as e:
                            self.logger.debug(f"Could not clear {cache_path}: {e}")

            except Exception as e:
                self.logger.error(f"❌ Failed to clear profile directory: {e}")
                # Try alternative approach - rename and delete
                try:
                    temp_path = profile_path.with_suffix('.tmp')
                    profile_path.rename(temp_path)
                    shutil.rmtree(temp_path, ignore_errors=True)
                    self.logger.info(f"✓ Profile cleared using rename strategy")
                except Exception as e2:
                    self.logger.error(f"❌ Alternative cleanup also failed: {e2}")

        profile_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"🗂️ Generated unique profile for firm {firm_id}: {profile_path}")
        return str(profile_path)

    def _set_firm_context(self, firm_id: str):
        """
        Set the firm context for this session manager instance.

        Args:
            firm_id: Unique identifier for the law firm
        """
        self._firm_id = firm_id
        self._profile_path = self._generate_unique_profile_path(firm_id)
        self.logger.info(f"🎯 Set firm context: {firm_id} → Profile: {self._profile_path}")

    def _cleanup_profile_if_needed(self):
        """Clean up temporary profile directories if needed."""
        if self._profile_path and os.path.exists(self._profile_path):
            try:
                # Only clean up if it's a temporary profile
                # Keep permanent profiles for session persistence
                cleanup_enabled = self.config.get('cleanup', {}).get('auto_cleanup_temp_files', True)
                if cleanup_enabled and 'temp_' in self._profile_path:
                    import shutil
                    shutil.rmtree(self._profile_path, ignore_errors=True)
                    self.logger.debug(f"🧹 Cleaned up temporary profile: {self._profile_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to cleanup profile {self._profile_path}: {e}")

    async def create_new_session(self) -> bool:
        """
        Create a new browser session.

        Returns:
            bool: True if session creation was successful
        """
        try:
            self.logger.info("Creating new Camoufox session")

            # Clean up any existing session
            await self.cleanup()

            # Create the browser and page without navigation
            if not await self._create_browser_and_page():
                return False

            # Navigate to Facebook ads library home page to establish session
            if not await self._navigate_to_ads_library_home():
                return False

            # Extract session tokens with operation tracking to prevent cleanup during extraction
            async with self._track_operation("extract_tokens"):
                self.session_data = await self._extract_tokens()

            # Validate session
            if not self.session_data.get('fb_dtsg'):
                self.logger.warning("No fb_dtsg token extracted, session may be invalid")
                self._is_session_valid = False
                return False

            # Update session timing
            self.session_start_time = time.time()
            self.last_activity_time = self.session_start_time
            self._is_session_valid = True
            self._last_error = None

            self.logger.info(
                f"Camoufox session created successfully. "
                f"Tokens: fb_dtsg={'✓' if self.session_data.get('fb_dtsg') else '✗'}, "
                f"lsd={'✓' if self.session_data.get('lsd') else '✗'}, "
                f"jazoest={'✓' if self.session_data.get('jazoest') else '✗'}"
            )

            return True

        except Exception as e:
            self.logger.error(f"Failed to create Camoufox session: {str(e)}")
            self._last_error = str(e)
            self._is_session_valid = False
            await self.cleanup()
            return False


    async def _configure_page(self):
        """Configure page settings for Facebook scraping."""
        if not self.page:
            return

        # Set user agent if not already set by fingerprint
        user_agent = self.session_data.get('user_agent')
        if user_agent:
            await self.page.set_extra_http_headers({'User-Agent': user_agent})

        # Conditional resource blocking based on configuration
        if self.block_resources_for_performance:
            self.logger.debug("Enabling resource blocking for performance")
            await self.page.route("**/*.{png,jpg,jpeg,gif,svg,ico,woff,woff2}",
                                 lambda route: route.abort())
        else:
            self.logger.debug("Resource blocking disabled to avoid ad blocker detection")

        # Set timeouts
        self.page.set_default_timeout(self.browser_timeout)
        self.page.set_default_navigation_timeout(self.browser_timeout)

    async def _extract_tokens(self) -> Dict[str, Any]:
        """
        Extract Facebook tokens from page with retry logic.

        Returns:
            Dict[str, Any]: Extracted tokens and session data
        """
        if not self.page:
            return {}

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # Wait a bit before extraction to ensure page is fully loaded
                if attempt > 0:
                    await asyncio.sleep(1.0 * attempt)  # Exponential backoff

                # Execute JavaScript to extract tokens
                tokens = await self.page.evaluate("""
                () => {
                    const scripts = document.querySelectorAll('script');
                    let tokens = {};

                    for (const script of scripts) {
                        const text = script.textContent || '';

                        // Extract fb_dtsg
                        const dtsgMatch = text.match(/"DTSGInitialData".*?"token":"([^"]+)"/);
                        if (dtsgMatch) tokens.fb_dtsg = dtsgMatch[1];

                        // Extract lsd
                        const lsdMatch = text.match(/"LSD".*?{[^}]*"token":"([^"]+)"/);
                        if (lsdMatch) tokens.lsd = lsdMatch[1];

                        // Extract jazoest
                        const jazoestMatch = text.match(/jazoest=(\\d+)/);
                        if (jazoestMatch) tokens.jazoest = jazoestMatch[1];

                        // Extract spin_r and spin_b
                        const spinRMatch = text.match(/"spin_r":(\\d+)/);
                        if (spinRMatch) tokens.spin_r = spinRMatch[1];

                        const spinBMatch = text.match(/"spin_b":"([^"]+)"/);
                        if (spinBMatch) tokens.spin_b = spinBMatch[1];
                    }

                    // Get additional page info
                    tokens.page_url = window.location.href;
                    tokens.user_agent = navigator.userAgent;

                    return tokens;
                }
            """)

                self.logger.debug(f"Extracted tokens: {list(tokens.keys())}")
                return tokens

            except Exception as e:
                if attempt < max_attempts - 1:
                    self.logger.warning(f"Token extraction attempt {attempt + 1} failed: {str(e)}. Retrying...")
                    continue
                else:
                    self.logger.error(f"Failed to extract tokens after {max_attempts} attempts: {str(e)}")
                    return {}

    async def get_session_data(self) -> Dict[str, Any]:
        """
        Get current session data including tokens.

        Returns:
            Dict[str, Any]: Session data
        """
        # Check if session needs refresh
        if self._should_refresh_session():
            await self.refresh_session()

        # Update last activity time
        self.last_activity_time = time.time()

        return self.session_data.copy()

    def _should_refresh_session(self) -> bool:
        """
        Check if session should be refreshed.

        Returns:
            bool: True if session should be refreshed
        """
        if not self.session_start_time or not self._is_session_valid:
            return True

        elapsed = time.time() - self.session_start_time
        time_until_expiry = self.max_session_duration - elapsed

        return time_until_expiry <= self.refresh_before_expiry

    async def refresh_session(self) -> bool:
        """
        Refresh the browser session.

        Returns:
            bool: True if session refresh was successful
        """
        self.logger.info("Refreshing Camoufox session")

        # Check minimum session duration
        if self.session_start_time:
            elapsed = time.time() - self.session_start_time
            if elapsed < self.min_session_duration:
                wait_time = self.min_session_duration - elapsed
                self.logger.info(f"Waiting {wait_time:.1f}s before refreshing session")
                await asyncio.sleep(wait_time)

        # Create new session
        return await self.create_new_session()

    def get_session_headers(self) -> Dict[str, str]:
        """
        Get headers for API requests.

        Returns:
            Dict[str, str]: HTTP headers
        """
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
        }

        # Add user agent if available
        if self.session_data.get('user_agent'):
            headers['User-Agent'] = self.session_data['user_agent']

        # Add referer
        if self.session_data.get('page_url'):
            headers['Referer'] = self.session_data['page_url']

        return headers

    def is_session_valid(self) -> bool:
        """
        Enhanced session health validation with browser context and network checks.

        Returns:
            bool: True if session is valid and healthy
        """
        # Check basic session state
        if not self._is_session_valid or not self.session_data.get('fb_dtsg'):
            self.logger.debug("Session invalid: Missing session state or fb_dtsg token")
            return False

        # Check if browser and page instances exist
        if not self.browser or not self.page:
            self.logger.debug("Session invalid: Missing browser or page instance")
            self._is_session_valid = False
            return False

        # Enhanced browser context health check (basic checks)
        try:
            # Check if browser is still connected
            if hasattr(self.browser, 'is_connected') and not self.browser.is_connected():
                self.logger.debug("Session invalid: Browser is not connected")
                self._is_session_valid = False
                return False

            # Check if page is still accessible
            if hasattr(self.page, 'is_closed'):
                if self.page.is_closed():
                    self.logger.debug("Session invalid: Page is closed")
                    self._is_session_valid = False
                    return False

            # Basic context validation (non-async)
            if self.context:
                try:
                    # Try to access pages property as a basic health check
                    if hasattr(self.context, 'pages'):
                        pages = self.context.pages
                        if not pages or (self.page and self.page not in pages):
                            self.logger.debug("Session invalid: Page not in browser context")
                            self._is_session_valid = False
                            return False
                except Exception as e:
                    self.logger.debug(f"Session invalid: Context pages check failed: {e}")
                    self._is_session_valid = False
                    return False

        except Exception as e:
            self.logger.debug(f"Session invalid: Browser health check failed: {e}")
            self._is_session_valid = False
            return False

        # Check if session has expired
        if self.session_start_time:
            elapsed = time.time() - self.session_start_time
            if elapsed > self.max_session_duration:
                self.logger.debug(f"Session invalid: Expired after {elapsed:.1f}s (max: {self.max_session_duration}s)")
                self._is_session_valid = False
                return False

        self.logger.debug("Session validation passed - session is valid and healthy")
        return True

    async def is_session_valid_async(self) -> bool:
        """
        Async version of session validation with comprehensive browser context health checks.

        Returns:
            bool: True if session is valid and healthy
        """
        # Run basic synchronous checks first
        if not self.is_session_valid():
            return False

        # Now run comprehensive async browser context health validation
        try:
            context_healthy = await self._validate_browser_context_health()
            if not context_healthy:
                self.logger.debug("Async session validation: Browser context health check failed")
                self._is_session_valid = False
                return False

            self.logger.debug("Async session validation passed - session is valid and context is healthy")
            return True

        except Exception as e:
            self.logger.debug(f"Async session validation failed: Browser context health validation error: {e}")
            self._is_session_valid = False
            return False

    async def ensure_session_with_context_health(self) -> bool:
        """
        Ensure session is valid with comprehensive context health validation and recovery.

        Returns:
            bool: True if session is valid and context is healthy
        """
        # First check if current session is valid (async version)
        if await self.is_session_valid_async():
            return True

        self.logger.info("🔄 Session or context invalid, attempting recovery...")

        # Try to ensure healthy browser context
        if await self.ensure_healthy_browser_context():
            # Re-extract tokens after context recreation with operation tracking
            async with self._track_operation("extract_tokens_recovery"):
                self.session_data = await self._extract_tokens()

            # Validate session after recovery
            if self.session_data.get('fb_dtsg'):
                self._is_session_valid = True
                self.last_activity_time = time.time()
                self.logger.info("✅ Session recovery with context health successful")
                return True
            else:
                self.logger.error("❌ Session recovery failed: No fb_dtsg token after context recreation")
                self._is_session_valid = False
                return False
        else:
            self.logger.error("❌ Failed to ensure healthy browser context during session recovery")
            self._is_session_valid = False
            return False

    async def cleanup(self):
        """Cleanup browser resources and image cache with atomic state management."""
        start_time = time.time()
        self.logger.info("🧹 Starting Camoufox session cleanup")

        try:
            # Atomically set closing state
            async with self._operation_lock:
                if self._is_closing:
                    self.logger.debug("Cleanup already in progress, skipping")
                    return
                self._is_closing = True
                pending_count = len(self._pending_operations)

            # Wait for pending operations to complete (with timeout)
            if pending_count > 0:
                self.logger.info(f"⏳ Waiting for {pending_count} pending operations to complete...")
                try:
                    await asyncio.wait_for(
                        self._wait_for_pending_operations(),
                        timeout=15.0  # Extended timeout for stability
                    )
                    self.logger.info("✅ All pending operations completed")
                except asyncio.TimeoutError:
                    async with self._operation_lock:
                        remaining_ops = len(self._pending_operations)
                    self.logger.warning(f"⚠️ Timeout waiting for {remaining_ops} pending operations - proceeding with cleanup")

            # Clear image cache first
            if self.image_cache_extractor:
                try:
                    await self.image_cache_extractor.clear_cache()
                    self.logger.info("🧹 Image cache cleared during session cleanup")
                except Exception as e:
                    self.logger.warning(f"Failed to clear image cache during cleanup: {e}")

            # Sequential resource cleanup with proper error isolation
            cleanup_results = {}

            # Step 1: Close page
            if self.page:
                try:
                    if hasattr(self.page, 'is_closed') and not self.page.is_closed():
                        await asyncio.wait_for(self.page.close(), timeout=5.0)
                    cleanup_results['page'] = 'success'
                    self.logger.debug("✅ Page closed successfully")
                except Exception as e:
                    cleanup_results['page'] = f'error: {e}'
                    self.logger.warning(f"⚠️ Error closing page: {e}")
                finally:
                    self.page = None

            # Step 2: Close context
            if self.context:
                try:
                    await asyncio.wait_for(self.context.close(), timeout=5.0)
                    cleanup_results['context'] = 'success'
                    self.logger.debug("✅ Context closed successfully")
                except Exception as e:
                    cleanup_results['context'] = f'error: {e}'
                    self.logger.warning(f"⚠️ Error closing context: {e}")
                finally:
                    self.context = None

            # Step 3: Close browser with proper cleanup
            if self.browser:
                try:
                    # Close browser instance
                    if hasattr(self.browser, 'browser') and self.browser.browser:
                        await asyncio.wait_for(self.browser.browser.close(), timeout=10.0)

                    # Handle context manager cleanup if exists
                    if self.browser_cm:
                        try:
                            await self.browser_cm.__aexit__(None, None, None)
                        except Exception as cm_e:
                            self.logger.debug(f"Browser context manager cleanup: {cm_e}")

                    cleanup_results['browser'] = 'success'
                    self.logger.debug("✅ Browser closed successfully")
                except Exception as e:
                    cleanup_results['browser'] = f'error: {e}'
                    self.logger.warning(f"⚠️ Error closing browser: {e}")
                finally:
                    self.browser = None
                    self.browser_cm = None

            # Atomically reset session state
            async with self._operation_lock:
                self.session_data = {}
                self.session_start_time = None
                self.last_activity_time = None
                self._is_session_valid = False
                self.current_proxy = None  # Clear current proxy on cleanup

                # Reset interception state
                self._response_interception_active = False
                self._graphql_responses = []
                self._accumulated_ndjson = ""
                self._all_responses = []
                self._response_sequence_counter = 0

                # Clear any remaining pending operations
                self._pending_operations.clear()

            # Clean up unique browser profile if needed
            self._cleanup_profile_if_needed()
            self._profile_path = None
            self._firm_id = None

            cleanup_time = time.time() - start_time
            success_count = sum(1 for result in cleanup_results.values() if result == 'success')
            error_count = len(cleanup_results) - success_count

            self.logger.info(
                f"🧹 Camoufox session cleanup completed in {cleanup_time:.2f}s "
                f"(✅ {success_count} success, ❌ {error_count} errors)"
            )

            # Reset closing flag last
            self._is_closing = False

        except Exception as e:
            self.logger.warning(f"Error during Camoufox cleanup: {str(e)}")
            self._is_closing = False

    def get_browser_instance(self):
        """Get the browser instance for direct access."""
        return self.browser if self.browser else None

    async def _validate_browser_context_health(self) -> bool:
        """
        Validate browser context lifecycle and health.

        Returns:
            bool: True if browser context is healthy
        """
        try:
            # Check if browser exists and is connected
            if not self.browser:
                self.logger.debug("Browser context health: No browser instance")
                return False

            # Check browser connection if possible
            if hasattr(self.browser, 'is_connected'):
                if not self.browser.is_connected():
                    self.logger.debug("Browser context health: Browser not connected")
                    return False

            # Check if context exists and is valid
            if not self.context:
                self.logger.debug("Browser context health: No browser context")
                return False

            # Validate context by checking pages
            try:
                pages = self.context.pages
                if not pages:
                    self.logger.debug("Browser context health: No pages in context")
                    return False

                # Check if our page is still in the context
                if self.page and self.page not in pages:
                    self.logger.debug("Browser context health: Current page not in context")
                    return False
            except Exception as e:
                self.logger.debug(f"Browser context health: Error accessing pages: {e}")
                return False

            # Check page health if it exists
            if self.page:
                try:
                    if hasattr(self.page, 'is_closed') and self.page.is_closed():
                        self.logger.debug("Browser context health: Page is closed")
                        return False

                    # Try to get page URL as a basic health check
                    url = self.page.url
                    if not url:
                        self.logger.debug("Browser context health: Page has no URL")
                        return False

                except Exception as e:
                    self.logger.debug(f"Browser context health: Error accessing page: {e}")
                    return False

            self.logger.debug("Browser context health: All checks passed")
            return True

        except Exception as e:
            self.logger.debug(f"Browser context health: Validation failed: {e}")
            return False

    async def _recreate_browser_context_if_needed(self) -> bool:
        """
        Recreate browser context if current one is unhealthy.

        Returns:
            bool: True if context is healthy or was successfully recreated
        """
        # First check if current context is healthy
        if await self._validate_browser_context_health():
            self.logger.debug("Browser context is healthy, no recreation needed")
            return True

        self.logger.info("🔄 Browser context unhealthy, attempting recreation...")

        try:
            # Close existing resources safely
            if self.page:
                try:
                    if hasattr(self.page, 'is_closed') and not self.page.is_closed():
                        await self.page.close()
                except Exception as e:
                    self.logger.debug(f"Error closing old page: {e}")
                finally:
                    self.page = None

            if self.context:
                try:
                    await self.context.close()
                except Exception as e:
                    self.logger.debug(f"Error closing old context: {e}")
                finally:
                    self.context = None

            # Attempt to create new browser and page
            if await self._create_browser_and_page():
                self.logger.info("✅ Browser context successfully recreated")
                return True
            else:
                self.logger.error("❌ Failed to recreate browser context")
                return False

        except Exception as e:
            self.logger.error(f"Error during browser context recreation: {e}")
            return False

    async def ensure_healthy_browser_context(self) -> bool:
        """
        Ensure browser context is healthy, recreating if necessary.

        Returns:
            bool: True if browser context is healthy and ready for use
        """
        # Check if we have basic browser infrastructure
        if not self.browser:
            self.logger.info("No browser instance, creating new session...")
            return await self.create_new_session()

        # Validate and potentially recreate context
        if not await self._recreate_browser_context_if_needed():
            self.logger.error("Failed to ensure healthy browser context")
            self._is_session_valid = False
            return False

        # Final validation
        if await self._validate_browser_context_health():
            self.logger.debug("Browser context health confirmed")
            return True
        else:
            self.logger.error("Browser context still unhealthy after recreation attempt")
            self._is_session_valid = False
            return False

    def set_advertiser_bandwidth_logger(self, bandwidth_logger):
        """
        Set the advertiser bandwidth logger for tracking network requests.

        Args:
            bandwidth_logger: Instance of AdvertiserBandwidthLogger
        """
        self.advertiser_bandwidth_logger = bandwidth_logger

        # Also set it on the image cache extractor if available
        if self.image_cache_extractor and hasattr(self.image_cache_extractor, 'bandwidth_logger'):
            self.image_cache_extractor.bandwidth_logger = bandwidth_logger

    async def _wait_for_pending_operations(self):
        """Wait for all pending operations to complete with proper locking."""
        while True:
            async with self._operation_lock:
                if not self._pending_operations:
                    break
                operation_count = len(self._pending_operations)

            self.logger.debug(f"Waiting for {operation_count} pending operations...")
            await asyncio.sleep(0.1)

    @asynccontextmanager
    async def _track_operation(self, operation_id: str):
        """Context manager to track pending operations."""
        async with self._operation_lock:
            if self._is_closing:
                raise RuntimeError("Session is closing, cannot start new operations")
            self._pending_operations.add(operation_id)

        try:
            yield
        finally:
            async with self._operation_lock:
                self._pending_operations.discard(operation_id)

    # ==================== Protected Browser Operation Wrappers ====================
    # These wrappers ensure ALL browser operations are tracked to prevent race conditions

    async def _safe_goto(self, url: str, **kwargs):
        """Navigate with operation tracking to prevent race conditions."""
        async with self._track_operation("navigate"):
            return await self.page.goto(url, **kwargs)

    async def _safe_wait_for_selector(self, selector: str, **kwargs):
        """Wait for selector with operation tracking."""
        async with self._track_operation("wait_for_selector"):
            return await self.page.wait_for_selector(selector, **kwargs)

    async def _safe_wait_for_load_state(self, state: str = "load", **kwargs):
        """Wait for load state with operation tracking."""
        async with self._track_operation("wait_for_load_state"):
            return await self.page.wait_for_load_state(state, **kwargs)

    async def _safe_evaluate(self, expression: str, *args):
        """Evaluate JavaScript with operation tracking."""
        async with self._track_operation("evaluate"):
            return await self.page.evaluate(expression, *args)

    async def _safe_click(self, selector: str, **kwargs):
        """Click element with operation tracking."""
        async with self._track_operation("click"):
            return await self.page.click(selector, **kwargs)

    async def _safe_fill(self, selector: str, value: str, **kwargs):
        """Fill input with operation tracking."""
        async with self._track_operation("fill"):
            return await self.page.fill(selector, value, **kwargs)

    async def _safe_type(self, selector: str, text: str, **kwargs):
        """Type text with operation tracking."""
        async with self._track_operation("type"):
            return await self.page.type(selector, text, **kwargs)

    async def _safe_press(self, selector: str, key: str, **kwargs):
        """Press key with operation tracking."""
        async with self._track_operation("press"):
            return await self.page.press(selector, key, **kwargs)

    async def _safe_keyboard_type(self, text: str, **kwargs):
        """Type with keyboard operation tracking."""
        async with self._track_operation("keyboard_type"):
            return await self.page.keyboard.type(text, **kwargs)

    async def _safe_keyboard_press(self, key: str, **kwargs):
        """Press key with keyboard operation tracking."""
        async with self._track_operation("keyboard_press"):
            return await self.page.keyboard.press(key, **kwargs)

    async def _safe_screenshot(self, **kwargs):
        """Take screenshot with operation tracking."""
        async with self._track_operation("screenshot"):
            return await self.page.screenshot(**kwargs)

    async def _safe_query_selector(self, selector: str):
        """Query selector with operation tracking."""
        async with self._track_operation("query_selector"):
            return await self.page.query_selector(selector)

    async def _safe_query_selector_all(self, selector: str):
        """Query selector all with operation tracking."""
        async with self._track_operation("query_selector_all"):
            return await self.page.query_selector_all(selector)

    def _setup_bandwidth_logging(self):
        """Set up bandwidth logging for all network requests."""
        if not self.page or not self.advertiser_bandwidth_logger:
            return

        async def log_all_responses(response):
            """Log all responses for bandwidth tracking."""
            if not self.advertiser_bandwidth_logger:
                return

            try:
                # Calculate request size estimate
                request_size = len(response.request.method) + len(response.request.url) + 200

                # Get response size (cache body to avoid multiple reads)
                response_size = 0
                body_bytes = None
                try:
                    body_bytes = await response.body()
                    response_size = len(body_bytes) if body_bytes else 0
                except:
                    content_length = response.headers.get('content-length')
                    if content_length:
                        response_size = int(content_length)

                # Log the request
                self.advertiser_bandwidth_logger.log_request(
                    url=response.url,
                    method=response.request.method,
                    request_size=request_size,
                    response_size=response_size,
                    status_code=response.status,
                    headers=dict(response.headers)
                )
            except Exception as e:
                self.logger.debug(f"Bandwidth logging error: {e}")

        # Add listener for all responses
        # Note: Event listeners work differently with sync API - need to handle in sync context
        # For now, bandwidth logging is disabled with sync API
        self.logger.warning("⚠️ Bandwidth logging temporarily disabled with sync API")
        self.logger.info("📊 Bandwidth logging enabled for all network requests")

    def get_page_instance(self):
        """Get the page instance for direct access."""
        return self.page

    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics and health information.

        Returns:
            Dict[str, Any]: Session statistics
        """
        current_time = time.time()

        stats = {
            'is_valid': self.is_session_valid(),
            'has_browser': self.browser is not None,
            'has_page': self.page is not None,
            'has_tokens': bool(self.session_data.get('fb_dtsg')),
            'last_error': self._last_error
        }

        if self.session_start_time:
            elapsed = current_time - self.session_start_time
            stats.update({
                'session_age_seconds': elapsed,
                'time_until_expiry': max(0, self.max_session_duration - elapsed),
                'should_refresh': self._should_refresh_session()
            })

        if self.last_activity_time:
            stats['idle_time_seconds'] = current_time - self.last_activity_time

        return stats

    async def capture_graphql_responses(self, page_id: str) -> Dict[str, Any]:
        """
        Capture GraphQL responses using page name search flow.
        GraphQL capture happens AFTER navigating to the advertiser page.

        Args:
            page_id: Facebook page ID to load

        Returns:
            Dict containing:
            - 'responses': List of captured GraphQL response texts
            - 'page_info': Basic page information
            - 'total_responses': Count of responses captured
            - 'success': Whether capture was successful
        """
        # CRITICAL: Log the raw page_id to trace quote issues
        self.logger.info(f"🔍 CRITICAL DEBUG: capture_graphql_responses called with page_id: '{page_id}' (type: {type(page_id)})")

        # Set firm context for unique browser profiles
        if page_id and not self._firm_id:
            self._set_firm_context(page_id)

        if not self._is_session_valid or not self.page:
            raise Exception("No active session available for GraphQL capture")

        # CRITICAL FIX: Strip quotes from page_id before processing
        # Config may pass firm IDs with quotes like "66401254358"
        original_page_id = page_id
        for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
            page_id = page_id.strip(quote_char)

        if original_page_id != page_id:
            self.logger.info(f"🧹 CRITICAL FIX: Stripped quotes from page_id: '{original_page_id}' -> '{page_id}'")

        # Get page name from page_id (need to implement lookup)
        page_name = await self._get_page_name_from_id(page_id)

        # CRITICAL FIX: Clear image cache at START of each advertiser processing
        # This ensures no cross-advertiser cache contamination
        if self.image_cache_extractor:
            try:
                await self.image_cache_extractor.clear_cache()
                self.logger.info(f"🧹 CACHE CLEARED: Starting fresh cache for advertiser {page_id} ('{page_name}')")
            except Exception as e:
                self.logger.warning(f"Failed to clear image cache for advertiser {page_id}: {e}")

        self._graphql_responses = []

        try:
            self.logger.info(f"🚀 Starting GraphQL capture for page {page_id} ('{page_name}')")

            # Step 1: Setup Ad Library search with Google referrer
            if not await self._setup_ad_library_search():
                raise Exception("Failed to setup Ad Library search")

            # Step 2: Setup GraphQL interception BEFORE searching
            self.logger.info("📡 Setting up GraphQL interception BEFORE search...")
            await self._setup_graphql_interception()
            self.logger.info("✅ GraphQL interception is now active")

            # Step 3: Search for advertiser by page name or ID
            # If no page name, use the ID instead
            # CRITICAL DEBUG: Log the decision process
            self.logger.info("="*60)
            self.logger.info(f"🔍 CRITICAL DEBUG: Search term decision")
            self.logger.info(f"   - page_name: '{page_name}'")
            self.logger.info(f"   - page_name.strip() if page_name else '': '{page_name.strip() if page_name else ''}'")
            self.logger.info(f"   - page_id: '{page_id}'")
            self.logger.info(f"   - Decision: {'USE PAGE_NAME' if page_name and page_name.strip() else 'USE PAGE_ID'}")
            self.logger.info("="*60)

            search_term = page_name if page_name and page_name.strip() else page_id
            self.logger.info(f"🔍 Search term: '{search_term}' (using {'page_name' if page_name and page_name.strip() else 'page_id as fallback'})")

            # Use the no_capture variant but GraphQL is already intercepting
            success, discovered_page_name = await self._search_advertiser_no_capture(search_term)
            if not success:
                raise Exception(f"Failed to search and select advertiser: {search_term}")

            # CRITICAL: If we discovered a page name during ID search, use it!
            if discovered_page_name and not page_name:
                self.logger.info(f"🎯 Using discovered page name: '{discovered_page_name}' (was searching by ID: {page_id})")
                page_name = discovered_page_name

            # Step 4: Verify URL contains page ID
            if not await self._verify_advertiser_page(page_id):
                raise Exception(f"Page ID {page_id} not found in URL after selection")

            # Step 5: Wait for initial page load
            await asyncio.sleep(3)  # Allow initial page content to load

            # Step 6: Trigger ad loading by scrolling
            self.logger.info("📜 Scrolling to trigger ad library data loading...")

            # Scroll down multiple times to trigger lazy loading of ads
            for i in range(3):
                async with self._track_operation("scroll_for_lazy_load"):
                    await self.page.evaluate("window.scrollBy(0, window.innerHeight)")
                await asyncio.sleep(1.5)  # Wait for GraphQL responses after each scroll

                # Check if we've captured any ad_library_main responses
                if self._graphql_responses:
                    # Process captured responses to check for ad_library_main
                    ad_library_responses = await self._process_captured_graphql_responses()
                    if ad_library_responses:
                        self.logger.info(f"✅ Captured {len(ad_library_responses)} ad_library_main responses after {i+1} scrolls")
                        break

            # Step 7: Final wait for any remaining responses
            await asyncio.sleep(2)

            # Step 8: Process final captured responses
            await self._process_captured_graphql_responses()

            await self._disable_graphql_interception()

            self.logger.info(f"🎉 Successfully captured {len(self._graphql_responses)} GraphQL responses")

            # CRITICAL: Raise exception if no GraphQL responses were captured
            if not self._graphql_responses:
                self.logger.error("❌ CRITICAL: No GraphQL responses captured - raising exception")
                self.logger.error("❌ CRITICAL: GraphQL interception failed completely")
                raise RuntimeError("No GraphQL responses captured - GraphQL interception failed completely")

            return {
                'responses': self._graphql_responses.copy(),
                'page_info': {'page_id': page_id, 'page_name': page_name},
                'total_responses': len(self._graphql_responses),
                'success': True
            }

        except Exception as e:
            self.logger.error(f"Error capturing GraphQL responses: {e}")
            await self._disable_graphql_interception()
            return {
                'responses': [],
                'page_info': {'page_id': page_id, 'page_name': page_name if 'page_name' in locals() else 'Unknown'},
                'total_responses': 0,
                'success': False,
                'error': str(e)
            }

    async def _setup_graphql_interception(self):
        """Setup network interception to capture GraphQL NDJSON responses and images."""
        if self._response_interception_active:
            return

        # CRITICAL: Validate that we have a page to intercept responses on
        if not self.page:
            self.logger.error("❌ CRITICAL: No page available for GraphQL interception - raising exception")
            self.logger.error("❌ CRITICAL: Session manager not properly initialized")
            raise RuntimeError("No page available for GraphQL interception - session manager not properly initialized")

        # Reset sequence counter and all responses for new capture session
        self._response_sequence_counter = 0
        self._all_responses = []
        self._graphql_responses = []
        self._accumulated_ndjson = ""

        # Image cache extraction will be setup later when advertiser is matched
        # This prevents caching images for the wrong advertiser pages

        async def handle_response(response):
            body_bytes = None  # Initialize at function start to avoid NameError
            try:
                # Increment sequence counter for ALL responses
                self._response_sequence_counter += 1
                seq_num = self._response_sequence_counter

                # Capture ALL response info for debugging
                response_info = {
                    'sequence': seq_num,
                    'url': response.url,
                    'status': response.status,
                    'content_type': response.headers.get('content-type', 'unknown'),
                    'is_facebook': 'facebook.com' in response.url,
                    'timestamp': time.time()
                }
                self._all_responses.append(response_info)

                # Skip logging image responses to reduce noise
                content_type = response.headers.get('content-type', 'unknown')
                if any(img_type in content_type.lower() for img_type in ['image/', 'img/', 'png', 'jpg', 'jpeg', 'gif', 'webp']):
                    return  # Skip image responses entirely

                # Enhanced logging with sequence number
                self.logger.info(f"🌐 RESPONSE #{seq_num}: {response.status} {response.url}")
                self.logger.info(f"   Content-Type: {content_type}")
                self.logger.info(f"   Facebook domain: {'facebook.com' in response.url}")

                # Try to get response text for Facebook responses
                response_text = ""
                if 'facebook.com' in response.url:
                    try:
                        # Reuse cached body_bytes if available, otherwise fetch it
                        if not body_bytes:
                            body_bytes = await response.body()
                        response_text = body_bytes.decode('utf-8', errors='ignore')
                        response_info['response_size'] = len(response_text)
                        self.logger.info(f"   Response size: {len(response_text)} characters")

                        # Log snippet of response content for debugging
                        if response_text:
                            snippet = response_text[:200].replace('\n', ' ').replace('\r', ' ')
                            self.logger.info(f"   Content snippet: {snippet}...")
                    except Exception as e:
                        self.logger.info(f"   Could not read response text: {e}")

                # Check if this passes our current GraphQL filter
                passes_filter = self._is_graphql_response(response)
                response_info['passes_current_filter'] = passes_filter
                self.logger.info(f"   Passes current filter: {passes_filter}")

                if passes_filter:
                    # Only process non-empty responses
                    if response_text:
                        # For streaming NDJSON, accumulate all response text
                        if hasattr(self, '_accumulated_ndjson'):
                            self._accumulated_ndjson += response_text
                        else:
                            self._accumulated_ndjson = response_text

                        # Also keep individual responses for debugging
                        self._graphql_responses.append(response_text)
                        self.logger.info(f"📡 ✅ CAPTURED GraphQL response #{seq_num}: {len(response_text)} characters")
                        self.logger.info(f"🔄 Total accumulated: {len(self._accumulated_ndjson)} characters")
                    else:
                        self.logger.warning(f"📡 ⚠️ Empty GraphQL response #{seq_num} - skipping")

                    # Check if this contains ad_library_main
                    if 'ad_library_main' in response_text:
                        self.logger.info(f"🎯 RESPONSE #{seq_num} CONTAINS ad_library_main!")

                else:
                    self.logger.debug(f"⏭️ Response #{seq_num} skipped (not GraphQL)")

                self.logger.info(f"   {'='*60}")

            except Exception as e:
                self.logger.warning(f"Error processing response #{seq_num}: {e}")

            # Log to advertiser bandwidth logger if available
            finally:
                if self.advertiser_bandwidth_logger:
                    try:
                        # Calculate request size estimate (headers + method + URL)
                        request_size = len(response.request.method) + len(response.request.url) + 200  # 200 bytes for headers estimate

                        # Get actual response size from body (cache to avoid multiple reads)
                        response_size = 0
                        body_bytes = None
                        try:
                            body_bytes = await response.body()
                            response_size = len(body_bytes) if body_bytes else 0
                        except:
                            # If we can't get body, estimate from headers
                            content_length = response.headers.get('content-length')
                            if content_length:
                                response_size = int(content_length)

                        # Log the request
                        self.advertiser_bandwidth_logger.log_request(
                            url=response.url,
                            method=response.request.method,
                            request_size=request_size,
                            response_size=response_size,
                            status_code=response.status,
                            headers=dict(response.headers),
                            duration_ms=None  # Playwright doesn't provide timing info easily
                        )
                    except Exception as e:
                        self.logger.debug(f"Failed to log bandwidth for response: {e}")

        # Set up response listener
        # CRITICAL FIX: Actually register the response handler
        try:
            # Register the response handler with the page
            self.page.on("response", handle_response)
            self._response_interception_active = True
            self.logger.info("✅ Response handler registered successfully")
            self.logger.info("📡 Enhanced GraphQL response interception with sequence tracking and image cache setup complete")
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Failed to register response handler: {e}")
            self.logger.error("❌ CRITICAL: GraphQL interception cannot work without response handler - raising exception")
            raise RuntimeError(f"Failed to register response handler: {e}")

    def _parse_ndjson_stream(self, ndjson_text: str) -> list:
        """Parse accumulated NDJSON stream and extract ad_library_main responses."""
        ad_library_responses = []

        try:
            # Handle different line endings and clean up the text
            ndjson_text = ndjson_text.replace('\r\n', '\n').replace('\r', '\n')

            # Split by newlines and process each potential JSON line
            lines = ndjson_text.split('\n')

            self.logger.info(f"🔍 Parsing {len(lines)} lines from NDJSON stream")

            for line_num, line in enumerate(lines):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue

                try:
                    # Try to parse as JSON
                    json_data = json.loads(line)

                    # Pretty print for logging
                    formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)
                    self.logger.info(f"📋 NDJSON Line {line_num + 1}:")
                    self.logger.info(formatted_json[:1000] + "..." if len(formatted_json) > 1000 else formatted_json)

                    # Check if this is an ad_library_main response
                    if self._is_ad_library_main_response(json_data):
                        ad_library_responses.append(json_data)
                        self.logger.info(f"🎯 ^^^ THIS IS ad_library_main response (Line {line_num + 1}) ^^^")

                except json.JSONDecodeError as e:
                    # Handle partial JSON lines or malformed JSON
                    self.logger.debug(f"❌ Invalid JSON on line {line_num + 1}: {e}")
                    self.logger.debug(f"❌ Raw line content: {line[:200]}...")
                    continue
                except Exception as e:
                    self.logger.debug(f"❌ Error processing line {line_num + 1}: {e}")
                    continue

            self.logger.info(f"✅ Extracted {len(ad_library_responses)} ad_library_main responses from NDJSON stream")

        except Exception as e:
            self.logger.error(f"❌ Error parsing NDJSON stream: {e}")

        return ad_library_responses

    async def _disable_graphql_interception(self):
        """Disable GraphQL response interception."""
        if self._response_interception_active:
            # Note: Playwright doesn't have a direct way to remove specific listeners
            # The listener will be cleaned up when the page is closed
            self._response_interception_active = False
            self.logger.debug("GraphQL response interception disabled")

    def _is_graphql_response(self, response) -> bool:
        """Check if response is a GraphQL NDJSON response."""
        try:
            url = response.url
            content_type = response.headers.get('content-type', '')

            # Check for Facebook domain first
            is_facebook_domain = 'facebook.com' in url
            if not is_facebook_domain:
                return False

            # Check for GraphQL endpoint patterns
            graphql_patterns = [
                '/graphql',
                'graphql',
                '/api/graphql',
                'batched_graphql',
                'batch_graphql',
                '/nw/',  # New Facebook endpoint for batched requests
                'ad_library_main'  # Look for specific ad library query
            ]
            is_graphql_url = any(pattern in url.lower() for pattern in graphql_patterns)

            # Check for successful response
            is_success = response.status == 200

            # More inclusive content type check - Facebook GraphQL can return various content types
            json_content_types = [
                'application/json',
                'text/plain',
                'application/x-ndjson',
                'text/json',
                'application/x-javascript',
                'text/javascript'
            ]
            is_json_content = any(ct in content_type.lower() for ct in json_content_types)

            # Special handling for text/html - check if it contains JSON data
            is_html_with_json = False
            if 'text/html' in content_type.lower():
                # For text/html, we need to check if it actually contains JSON
                # This is a heuristic check - we'll validate by checking if content looks like JSON
                is_html_with_json = True  # Assume yes for GraphQL endpoints
                is_json_content = True  # Allow text/html for GraphQL endpoints

            # Log detailed info for debugging
            if is_facebook_domain:
                self.logger.info(f"🔍 Facebook URL detected: {url}")
                self.logger.info(f"   GraphQL URL check: {is_graphql_url}")
                self.logger.info(f"   JSON content check: {is_json_content} (type: {content_type})")
                if is_html_with_json:
                    self.logger.info(f"   Special case: text/html on GraphQL endpoint (likely JSON content)")
                self.logger.info(f"   Success status: {is_success}")
                result = is_graphql_url and is_json_content and is_success
                self.logger.info(f"   FINAL RESULT: {result}")
                return result
            else:
                self.logger.info(f"🔍 Non-Facebook domain: {url}")

            return is_graphql_url and is_json_content and is_success

        except Exception as e:
            self.logger.debug(f"Error checking GraphQL response: {e}")
            return False

    def get_captured_responses(self) -> list[str]:
        """Get all captured GraphQL responses."""
        return self._graphql_responses.copy()

    def clear_captured_responses(self):
        """Clear the captured GraphQL responses."""
        self._graphql_responses = []
        self._all_responses = []
        self._response_sequence_counter = 0
        self._accumulated_ndjson = ""

    def get_response_sequence_summary(self) -> dict:
        """Get summary of all responses captured in sequence for debugging."""
        summary = {
            'total_responses': len(self._all_responses),
            'facebook_responses': [r for r in self._all_responses if r['is_facebook']],
            'graphql_responses': len(self._graphql_responses),
            'sequence_analysis': []
        }

        # Analyze response sequence
        for i, resp in enumerate(self._all_responses, 1):
            analysis = {
                'sequence': i,
                'url': resp['url'],
                'status': resp['status'],
                'content_type': resp['content_type'],
                'is_facebook': resp['is_facebook'],
                'passes_filter': resp.get('passes_current_filter', False),
                'size': resp.get('response_size', 0)
            }
            summary['sequence_analysis'].append(analysis)

        return summary

    async def _process_captured_graphql_responses(self):
        """Process captured GraphQL responses and return ad_library_main responses."""
        try:
            self.logger.info(f"🔍 Processing {len(self._graphql_responses)} captured GraphQL responses")
            self.logger.info(f"🔄 Total accumulated NDJSON: {len(self._accumulated_ndjson)} characters")

            if len(self._graphql_responses) == 0:
                self.logger.warning("⚠️ No GraphQL responses captured - check if interception is working")
                return []

            ad_library_responses = []

            # Process accumulated NDJSON first (this is the main fix)
            if self._accumulated_ndjson:
                self.logger.info("📋 PROCESSING ACCUMULATED NDJSON STREAM:")
                self.logger.info("=" * 80)

                ad_library_responses.extend(self._parse_ndjson_stream(self._accumulated_ndjson))

            # Also log individual responses for debugging
            self.logger.info("📋 INDIVIDUAL RESPONSE DEBUG:")
            self.logger.info("=" * 80)

            for i, response_text in enumerate(self._graphql_responses):
                try:
                    self.logger.info(f"📋 Response {i+1} ({len(response_text)} characters):")

                    # Try to parse and pretty-print the JSON
                    try:
                        # Check if this is NDJSON (newline-delimited JSON)
                        if '\n' in response_text:
                            # Split by newlines and process each JSON object
                            json_lines = response_text.strip().split('\n')
                            for line_num, line in enumerate(json_lines):
                                if line.strip():
                                    try:
                                        json_data = json.loads(line)
                                        formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)
                                        self.logger.info(f"📋 Response {i+1}, Line {line_num+1}:")
                                        self.logger.info(formatted_json)

                                        # Check for ad_library_main
                                        if self._is_ad_library_main_response(json_data):
                                            ad_library_responses.append(json_data)
                                            self.logger.info(f"🎯 ^^^ THIS IS ad_library_main response ^^^")

                                    except Exception as e:
                                        self.logger.info(f"❌ Raw line {line_num+1} (not JSON): {line}")
                        else:
                            # Single JSON object
                            try:
                                json_data = json.loads(response_text)
                                formatted_json = json.dumps(json_data, indent=2, ensure_ascii=False)
                                self.logger.info(formatted_json)

                                # Check for ad_library_main
                                if self._is_ad_library_main_response(json_data):
                                    ad_library_responses.append(json_data)
                                    self.logger.info(f"🎯 ^^^ THIS IS ad_library_main response ^^^")

                            except Exception as e:
                                self.logger.info(f"❌ Raw response {i+1} (not JSON): {response_text}")

                    except Exception as e:
                        self.logger.info(f"❌ Raw response {i+1}: {response_text}")

                    self.logger.info("-" * 40)

                except Exception as e:
                    self.logger.error(f"❌ Error processing response {i+1}: {e}")

            self.logger.info("=" * 80)

            # Summary and filtering
            if ad_library_responses:
                self.logger.info(f"✅ Found {len(ad_library_responses)} ad_library_main responses out of {len(self._graphql_responses)} total")

                # Filter and save ads with matching page_id
                filtered_ads = self._filter_ads_by_page_id(ad_library_responses, self._target_page_id)
                self._save_filtered_ads_to_json(filtered_ads)

            else:
                self.logger.warning(f"⚠️ No ad_library_main responses found in {len(self._graphql_responses)} captured responses")

            return ad_library_responses

        except Exception as e:
            self.logger.error(f"❌ Error processing captured GraphQL responses: {e}")
            return []

    def _filter_ads_by_page_id(self, ad_library_responses: list, target_page_id: str) -> list:
        """Filter ads to only include those where page_id matches the target page ID."""
        filtered_ads = []

        if not target_page_id:
            self.logger.warning("⚠️ No target page ID provided for filtering")
            return []

        try:
            self.logger.info(f"🎯 Filtering ads for target page ID: {target_page_id}")

            for response in ad_library_responses:
                # Navigate through the GraphQL response structure to find ads
                data = response.get('data', {})

                # Look for ad_library_main structure
                ad_library_main = data.get('ad_library_main', {})
                if not ad_library_main:
                    self.logger.debug("No ad_library_main found in response")
                    continue

                # Navigate to search_results_connection.edges (Facebook's actual structure)
                search_results = ad_library_main.get('search_results_connection', {})
                edges = search_results.get('edges', [])

                if not edges:
                    self.logger.debug("No edges found in search_results_connection")
                    continue

                self.logger.info(f"📊 Found {len(edges)} edges in response, extracting collated_results for page_id={target_page_id}")

                # Extract ads from collated_results within each node
                for edge_num, edge in enumerate(edges):
                    node = edge.get('node', {})
                    collated_results = node.get('collated_results', [])

                    if not collated_results:
                        self.logger.debug(f"Edge {edge_num} has no collated_results")
                        continue

                    # Process each ad in collated_results (usually just one)
                    for result_num, ad_data in enumerate(collated_results):
                        if not isinstance(ad_data, dict):
                            continue

                        # Extract ad info
                        page_id = ad_data.get('page_id')
                        ad_archive_id = ad_data.get('ad_archive_id')
                        snapshot = ad_data.get('snapshot', {})

                        # Log for debugging
                        self.logger.debug(f"🔍 Edge {edge_num}.{result_num}: page_id={page_id}, ad_archive_id={ad_archive_id}")

                        # Validate required fields
                        if not ad_archive_id or not snapshot:
                            self.logger.debug(f"❌ Invalid ad data: missing ad_archive_id or snapshot")
                            continue

                        # Filter by page_id matching target
                        if page_id == target_page_id:
                            filtered_ads.append(ad_data)
                            self.logger.info(f"✅ Matched ad: page_id={page_id}, ad_archive_id={ad_archive_id}")
                        else:
                            self.logger.debug(f"❌ Filtered out ad: page_id={page_id} != target={target_page_id}")

            self.logger.info(f"🔍 Filtered {len(filtered_ads)} ads with matching page_id={target_page_id} from {len(ad_library_responses)} responses")
            return filtered_ads

        except Exception as e:
            self.logger.error(f"❌ Error filtering ads: {e}")
            return []

    def _matches_law_firm(self, text: str, target_firm: str) -> bool:
        """Check if text contains the EXACT target law firm name."""
        # Simple exact match - you're giving me the exact name to find
        return target_firm.lower() in text.lower()

    def _save_filtered_ads_to_json(self, filtered_ads: list):
        """Save filtered ads to JSON file in project root."""
        try:
            if not filtered_ads:
                self.logger.warning("⚠️ No filtered ads to save")
                return

            # Save to project root (go up from src/services/fb_ads/camoufox/)
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, '..', '..', '..', '..')
            project_root = os.path.abspath(project_root)

            output_file = os.path.join(project_root, 'filtered_facebook_ads.json')

            # Create output data with metadata
            output_data = {
                'metadata': {
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_ads': len(filtered_ads),
                    'filter_criteria': 'page_id matches id',
                    'source': 'Facebook Ad Library GraphQL'
                },
                'ads': filtered_ads
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"💾 Saved {len(filtered_ads)} filtered ads to: {output_file}")

        except Exception as e:
            self.logger.error(f"❌ Error saving filtered ads: {e}")

    def _is_ad_library_main_response(self, json_data: dict) -> bool:
        """Check if the JSON response contains ad_library_main data."""
        try:
            # Check for ad_library_main in the data structure
            data = json_data.get('data', {})

            # Common patterns for ad library responses
            ad_library_patterns = [
                'ad_library_main',
                'adLibraryMain',
                'ad_library',
                'adLibrary'
            ]

            # Check top-level data keys
            for pattern in ad_library_patterns:
                if pattern in data:
                    return True

            # Check nested structures
            for key, value in data.items():
                if isinstance(value, dict):
                    for pattern in ad_library_patterns:
                        if pattern in str(value):
                            return True

            # Check if response contains ad-related data structures
            ad_indicators = ['ads', 'edges', 'pageInfo', 'totalCount']
            found_indicators = sum(1 for indicator in ad_indicators if indicator in str(json_data))

            # If we find multiple ad indicators, likely an ad library response
            return found_indicators >= 2

        except Exception as e:
            self.logger.debug(f"Error checking ad_library_main response: {e}")
            return False

    def _build_google_referrer(self, search_query: str) -> str:
        """Build a realistic Google search referrer URL."""
        if not search_query:
            return 'https://www.google.com/'

        encoded_query = urllib.parse.quote_plus(search_query)

        google_referrers = [
            f'https://www.google.com/search?q={encoded_query}&oq={encoded_query}&aqs=chrome..69i57j0l7.{random.randint(1000,9999)}j0j7&sourceid=chrome&ie=UTF-8',
            f'https://www.google.com/search?q={encoded_query}&source=hp&ei={random.randint(100000,999999)}&iflsig=random&ved=random',
            f'https://www.google.com/search?q={encoded_query}&rlz=1C1CHBF_enUS{random.randint(100,999)}US{random.randint(100,999)}&oq={encoded_query}',
            f'https://www.google.com/search?q={encoded_query}&sourceid=chrome&ie=UTF-8&rlz=1C1CHBF_enUS{random.randint(100,999)}US{random.randint(100,999)}',
        ]

        return random.choice(google_referrers)

    async def _setup_ad_library_search(self):
        """Setup Facebook Ad Library search following updated flow1.md specification."""
        try:
            # Check current page state to avoid unnecessary operations
            setup_state = await self._check_setup_state()

            # STEP 1: Navigate to Ad Library page only if not already there
            if not setup_state['on_ad_library_page']:
                self.logger.info("🌐 Navigating to Ad Library page")

                # Set realistic Google referrer
                search_queries = ["meta ads library", "facebook ads library", "fb ad library", "facebook ad transparency"]
                selected_query = random.choice(search_queries)
                referrer_url = self._build_google_referrer(selected_query)

                await self.page.set_extra_http_headers({'Referer': referrer_url})
                self.logger.info(f"🔍 Set Google referrer: {selected_query}")

                # Navigate to Facebook Ad Library home
                try:
                    async with self._track_operation("navigate_ads_library"):
                        await self.page.goto('https://www.facebook.com/ads/library/',
                                           wait_until='domcontentloaded', timeout=60000)
                except Exception as nav_error:
                    if "Target closed" in str(nav_error) or "Target page, context or browser has been closed" in str(nav_error):
                        self.logger.error("❌ Browser context closed during navigation - attempting recovery")
                        await self.cleanup()
                        if await self._create_browser_and_page():
                            # Retry navigation after recreating browser
                            async with self._track_operation("navigate_ads_library_retry"):
                                await self.page.goto('https://www.facebook.com/ads/library/',
                                                   wait_until='domcontentloaded', timeout=60000)
                        else:
                            raise Exception("Failed to recover from browser context closure")
                    else:
                        raise

                # Add a more robust modal dismissal after navigation
                await asyncio.sleep(2)  # Give modal time to appear

                try:
                    # Check for any visible buttons that might be in a modal
                    modal_selectors = [
                        'button:has-text("OK"):visible',
                        'button:has-text("Continue"):visible',
                        'button:has-text("Accept"):visible',
                        'button:has-text("Dismiss"):visible',
                        'button:has-text("Got it"):visible',
                        'button:has-text("Close"):visible',
                        'button[aria-label*="Close"]:visible',
                        '[data-testid*="close"]:visible'
                    ]

                    for selector in modal_selectors:
                        modal_button = await self.page.locator(selector).first
                        if await modal_button.count() > 0:
                            button_text = await modal_button.text_content() or selector
                            self.logger.info(f"🚨 Found modal button '{button_text}' - clicking to dismiss")
                            await modal_button.click(force=True)
                            await asyncio.sleep(1)
                            break
                except Exception as e:
                    self.logger.debug(f"No modal button found or error: {e}")

                # Add continuous modal checking to handle delayed modals
                self.logger.info("🔍 Starting continuous modal check for 5 seconds...")
                for i in range(5):  # Check 5 times over 5 seconds
                    await asyncio.sleep(1)
                    try:
                        modal_selectors = [
                            'button:has-text("OK"):visible',
                            'button:has-text("Continue"):visible',
                            'button:has-text("Accept"):visible',
                            'button:has-text("Dismiss"):visible',
                            'button:has-text("Got it"):visible',
                            'button:has-text("Close"):visible',
                            'button[aria-label*="Close"]:visible',
                            '[data-testid*="close"]:visible'
                        ]

                        found_button = False
                        for selector in modal_selectors:
                            modal_button = await self.page.locator(selector).first
                            if await modal_button.count() > 0:
                                button_text = await modal_button.text_content() or selector
                                self.logger.info(f"🚨 Found modal button '{button_text}' on attempt {i+1} - clicking to dismiss")
                                await modal_button.click(force=True)
                                await asyncio.sleep(0.5)  # Brief pause after clicking
                                found_button = True
                                break

                        if found_button:
                            break

                    except Exception as e:
                        self.logger.debug(f"Modal check {i+1}: No modal button found")

                # Wait for page to load and React app to initialize with robust selectors
                page_loaded = await self._wait_for_page_load()
                if page_loaded:
                    self.logger.info("📄 Ad Library page loaded successfully")
                else:
                    self.logger.warning("📄 Page load detection incomplete, but continuing")

                # Update state after navigation
                setup_state = await self._check_setup_state()
            else:
                self.logger.info("📄 Already on Ad Library page - skipping navigation")

            # STEP 2: Select Country - "United States" (always conditional)
            self.logger.info("🌍 Step 2: Selecting country dropdown")
            # Add a wait to ensure page is ready
            await asyncio.sleep(2)
            self.logger.info("⏳ Waiting for page to be ready...")
            await self._select_country_dropdown()

            # STEP 3: Select Ad Category - "All Ads" (always conditional)
            self.logger.info("📂 Step 3: Selecting ad category dropdown")
            await self._select_ad_category_dropdown()

            # STEP 4: Verify search input is enabled
            self.logger.info("🔍 Step 4: Verifying search input is enabled")
            if setup_state['search_input_visible'] and setup_state['search_input_enabled']:
                self.logger.info("✅ Search input already enabled - skipping verification")
            else:
                await self.page.wait_for_selector('input[placeholder*="Search"][disabled]', state='hidden')
                self.logger.info("✅ Search input enabled after setup")

            self.logger.info("✅ Ad Library search setup complete")
            return True

        except Exception as e:
            self.logger.error(f"Failed to setup Ad Library search: {str(e)}")
            return False

    async def _is_on_ad_library_page(self) -> bool:
        """Check if currently on the Facebook Ad Library page."""
        try:
            if not self.page:
                return False

            current_url = self.page.url
            is_ad_library = 'facebook.com/ads/library' in current_url

            if is_ad_library:
                self.logger.debug(f"✅ Already on Ad Library page: {current_url}")
            else:
                self.logger.debug(f"❌ Not on Ad Library page: {current_url}")

            return is_ad_library

        except Exception as e:
            self.logger.debug(f"Could not determine current page: {str(e)}")
            return False

    async def _wait_for_page_load(self) -> bool:
        """
        Wait for Facebook Ad Library page to load with multiple fallback selectors.

        Returns:
            bool: True if page appears loaded, False otherwise
        """
        # Try multiple selectors for page load detection
        selectors_to_try = [
            # Modern Facebook selectors
            '[role="main"]',
            '[data-pagelet="root"]',
            '[data-pagelet*="AdLibrary"]',
            # Generic Facebook containers
            '.facebook_container',
            '#facebook',
            '[class*="facebook"]',
            # Legacy selectors
            'div#js_o',
            # Last resort - basic DOM structure
            'body[class*="facebook"], body[class*="fb"]'
        ]

        for i, selector in enumerate(selectors_to_try):
            try:
                self.logger.debug(f"🔍 Trying page load selector {i+1}/{len(selectors_to_try)}: {selector}")
                await self.page.wait_for_selector(selector, state='visible', timeout=5000)
                self.logger.info(f"✅ Page loaded detected with selector: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"❌ Selector {selector} failed: {e}")
                continue

        self.logger.warning("⚠️ No page load selectors matched, but page may still be functional")
        return False

    async def _find_country_dropdown(self):
        """
        Find the country dropdown element using multiple fallback selectors.

        Returns:
            Element or None if not found
        """
        # Try multiple selectors for country dropdown
        country_selectors = [
            # Legacy selector (works in both languages)
            'div#js_o',
            # Modern Facebook selectors - look for dropdowns with country-related content
            '[role="button"]:has-text("United States")',
            '[role="button"]:has-text("États-Unis")',  # French
            '[role="button"]:has-text("Country")',
            '[role="button"]:has-text("Pays")',  # French
            '[aria-label*="Country"]',
            '[aria-label*="country"]',
            '[aria-label*="Pays"]',  # French
            '[aria-label*="pays"]',  # French
            # Generic dropdown selectors
            '[role="combobox"]',
            'select[name*="country"], select[id*="country"]',
            # Look for dropdowns that might contain "United States"
            '[role="button"]:has-text("US")',
            '[role="button"]:has-text("USA")',
            # Generic button that might be a dropdown
            'button[data-testid*="country"]',
            'div[data-testid*="country"]'
        ]

        for selector in country_selectors:
            try:
                self.logger.debug(f"🔍 Trying country dropdown selector: {selector}")
                element = await self.page.wait_for_selector(selector, state='visible', timeout=2000)
                if element:
                    self.logger.info(f"✅ Found country dropdown with selector: {selector}")
                    return element
            except Exception as e:
                self.logger.debug(f"❌ Country selector {selector} failed: {e}")
                continue

        self.logger.warning("⚠️ No country dropdown selectors matched")
        return None

    async def _check_setup_state(self) -> dict:
        """Check the current state of Ad Library page setup."""
        try:
            state = {
                'on_ad_library_page': await self._is_on_ad_library_page(),
                'country_dropdown_visible': False,
                'category_dropdown_visible': False,
                'search_input_visible': False,
                'search_input_enabled': False
            }

            # Check if country dropdown is visible with robust selectors
            state['country_dropdown_visible'] = await self._find_country_dropdown() is not None

            try:
                await self.page.wait_for_selector('div#js_p', state='visible', timeout=2000)
                state['category_dropdown_visible'] = True
            except:
                pass

            try:
                search_input = await self.page.wait_for_selector('input[placeholder*="Search"]', state='visible', timeout=2000)
                state['search_input_visible'] = True

                # Check if search input is enabled
                is_disabled = await search_input.is_disabled()
                state['search_input_enabled'] = not is_disabled

            except:
                pass

            self.logger.debug(f"📊 Page setup state: {state}")
            return state

        except Exception as e:
            self.logger.debug(f"Could not check setup state: {str(e)}")
            return {'on_ad_library_page': False, 'country_dropdown_visible': False, 'category_dropdown_visible': False, 'search_input_visible': False, 'search_input_enabled': False}

    async def _check_current_country(self) -> str:
        """Check the currently selected country in the dropdown."""
        try:
            # Look for the country dropdown element using robust selectors
            country_dropdown = await self._find_country_dropdown()
            if not country_dropdown:
                self.logger.debug("Could not find country dropdown to check current selection")
                return "Unknown"

            # Get the current text content of the dropdown
            current_text = await country_dropdown.inner_text()

            # The dropdown might show "United States" or just "US" or other variations
            # Also check for French text
            if "United States" in current_text or "US" in current_text or "États-Unis" in current_text:
                return "United States"
            else:
                self.logger.debug(f"Current country dropdown text: '{current_text}'")
                return current_text.strip()

        except Exception as e:
            self.logger.debug(f"Could not determine current country: {str(e)}")
            return "Unknown"

    async def _check_current_ad_category(self) -> str:
        """Check the currently selected ad category in the dropdown."""
        try:
            # Look for the ad category dropdown element
            category_dropdown = await self.page.wait_for_selector('div#js_p', state='visible')

            # Get the current text content of the dropdown
            current_text = await category_dropdown.inner_text()

            # The dropdown might show "All ads" or other variations
            # Also check for French text
            if "All ads" in current_text or "Toutes les annonces" in current_text or "Toutes les publicités" in current_text:
                return "All ads"
            else:
                self.logger.debug(f"Current ad category dropdown text: '{current_text}'")
                return current_text.strip()

        except Exception as e:
            self.logger.debug(f"Could not determine current ad category: {str(e)}")
            return "Unknown"

    async def _select_country_dropdown(self):
        """Select 'United States' from country dropdown following flow1.md specification."""
        try:
            # DEBUG: Check if page is ready
            self.logger.info(f"🔍 DEBUG: Current URL: {self.page.url}")
            self.logger.info(f"🔍 DEBUG: Page title: {await self.page.title()}")

            # Take a screenshot for debugging
            await self.page.screenshot(path="debug_country_dropdown.png")
            self.logger.info("📸 DEBUG: Screenshot saved as debug_country_dropdown.png")

            # Step 1: Check if country is already "United States"
            current_country = await self._check_current_country()
            if current_country == "United States":
                self.logger.info("🇺🇸 Country already set to 'United States' - skipping selection")
                return

            # Step 2: Click country dropdown to open the list
            self.logger.info("👆 Clicking country dropdown")
            country_dropdown = await self._find_country_dropdown()
            if not country_dropdown:
                # DEBUG: List all visible elements that might be dropdowns
                self.logger.info("🔍 DEBUG: Looking for any dropdown-like elements...")
                async with self._track_operation("query_selector_dropdowns"):
                    potential_dropdowns = await self.page.query_selector_all('[role="button"], [role="combobox"], select, div[id^="js_"]')
                self.logger.info(f"🔍 DEBUG: Found {len(potential_dropdowns)} potential dropdown elements")
                for i, elem in enumerate(potential_dropdowns[:5]):  # Check first 5
                    try:
                        text = await elem.inner_text()
                        self.logger.info(f"🔍 DEBUG: Element {i}: {text[:50]}...")
                    except:
                        pass
                raise Exception("Could not find country dropdown to click")

            await country_dropdown.scroll_into_view_if_needed()
            await country_dropdown.click()

            # Step 3: Wait for country list popup to become visible
            self.logger.info("⏳ Waiting for country grid to appear")
            await self.page.wait_for_selector('div[role="grid"]', state='visible', timeout=10000)

            # Step 4: Click "United States" option with specific selector
            self.logger.info("🇺🇸 Selecting 'United States' option")
            us_option = await self.page.wait_for_selector('div[role="gridcell"]:has-text("United States")', state='visible')
            await us_option.click()

            # Step 4.5: Check if we're in mobile mode and need to click Apply button
            # Mobile detection based on viewport width
            is_mobile = self.viewport.get('width', 1920) < 768
            if is_mobile:
                self.logger.info("📱 Mobile mode detected - looking for Apply button")
                try:
                    # Wait briefly for Apply button to appear
                    await asyncio.sleep(0.5)

                    # Try multiple selectors for the Apply button
                    apply_selectors = [
                        'button:has-text("Apply"):visible',
                        '[role="button"]:has-text("Apply"):visible',
                        'button[aria-label*="Apply"]:visible',
                        'button[aria-label*="apply"]:visible',
                        # Also try "Done" as some mobile UIs use this
                        'button:has-text("Done"):visible',
                        '[role="button"]:has-text("Done"):visible',
                    ]

                    apply_clicked = False
                    for selector in apply_selectors:
                        try:
                            apply_button = await self.page.wait_for_selector(selector, state='visible', timeout=2000)
                            if apply_button:
                                self.logger.info(f"✅ Found Apply/Done button with selector: {selector}")
                                await apply_button.click()
                                apply_clicked = True
                                self.logger.info("👆 Clicked Apply/Done button")
                                # Wait for modal to close
                                await asyncio.sleep(1)
                                break
                        except Exception:
                            continue

                    if not apply_clicked:
                        self.logger.warning("⚠️ Apply button not found in mobile mode - continuing anyway")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error handling mobile Apply button: {e} - continuing")
            else:
                self.logger.debug("💻 Desktop mode - no Apply button needed")

            # Step 5: Wait for dropdown to close (country dropdown should no longer show grid)
            self.logger.info("⏳ Waiting for country dropdown to close")
            await asyncio.sleep(1)  # Brief pause for dropdown animation

            self.logger.info("✅ Country selection completed")

        except Exception as e:
            self.logger.error(f"Failed to select country dropdown: {str(e)}")
            raise

    async def _select_ad_category_dropdown(self):
        """Select 'All ads' from ad category dropdown following flow1.md specification."""
        try:
            # Step 1: Check if ad category is already "All ads"
            current_category = await self._check_current_ad_category()
            if current_category == "All ads":
                self.logger.info("📂 Ad category already set to 'All ads' - skipping selection")
                return

            # Step 2: Click ad category dropdown to open the list
            self.logger.info("👆 Clicking ad category dropdown (div#js_p)")
            category_dropdown = await self.page.wait_for_selector('div#js_p', state='visible')
            await category_dropdown.scroll_into_view_if_needed()

            # Try click with force option first to bypass element interception
            try:
                await category_dropdown.click(force=True)
                self.logger.debug("✅ Dropdown clicked successfully with force=True")
            except Exception as e:
                self.logger.warning(f"⚠️ Force click failed, trying JavaScript click: {e}")
                # Fallback to JavaScript click to bypass interception
                await self.page.evaluate('(element) => element.click()', category_dropdown)
                self.logger.debug("✅ Dropdown clicked using JavaScript fallback")

            # Step 3: Wait for category selection popup to become visible
            # Use more specific selector to avoid ambiguity - wait for the dropdown that appears after js_p click
            self.logger.info("⏳ Waiting for category grid to appear")
            await asyncio.sleep(0.5)  # Brief pause for dropdown animation to start

            # Try multiple strategies to find the correct dropdown
            grid_found = False

            # Strategy 1: Look for grid that contains "All ads" text
            try:
                await self.page.wait_for_selector('div[role="grid"]:has-text("All ads")', state='visible', timeout=5000)
                grid_found = True
                self.logger.debug("✅ Found grid using 'All ads' text selector")
            except:
                self.logger.debug("❌ Grid with 'All ads' text not found, trying alternative")

            # Strategy 2: Look for grid near the category dropdown
            if not grid_found:
                try:
                    await self.page.wait_for_selector('div#js_p ~ div[role="grid"], div#js_p + div[role="grid"]', state='visible', timeout=5000)
                    grid_found = True
                    self.logger.debug("✅ Found grid using sibling selector")
                except:
                    self.logger.debug("❌ Grid with sibling selector not found, trying generic")

            # Strategy 3: Fallback to generic grid selector
            if not grid_found:
                await self.page.wait_for_selector('div[role="grid"]', state='visible', timeout=5000)
                self.logger.debug("✅ Found grid using generic selector")

            # Step 4: Click "All ads" option with specific selector
            self.logger.info("📂 Selecting 'All ads' option")

            # Use more specific selector for "All ads" option
            all_ads_selectors = [
                'div[role="gridcell"]:has-text("All ads")',
                'div[role="gridcell"]:has-text("Toutes les annonces")',  # French
                'div[role="gridcell"]:has-text("Toutes les publicités")',  # French alternative
                'div[role="gridcell"] >> text="All ads"',
                '[role="gridcell"]:has-text("All ads")',
                'div:has(span:text("All ads"))',
                'div:has(span:text("Toutes les annonces"))',  # French
            ]

            all_ads_option = None
            for selector in all_ads_selectors:
                try:
                    all_ads_option = await self.page.wait_for_selector(selector, state='visible', timeout=3000)
                    self.logger.debug(f"✅ Found 'All ads' option using: {selector}")
                    break
                except:
                    self.logger.debug(f"❌ 'All ads' option not found with: {selector}")
                    continue

            if not all_ads_option:
                raise Exception("Could not find 'All ads' option in dropdown")

            # Try click with force option first to bypass element interception
            try:
                await all_ads_option.click(force=True)
                self.logger.debug("✅ 'All ads' option clicked successfully with force=True")
            except Exception as e:
                self.logger.warning(f"⚠️ Force click failed on 'All ads' option, trying JavaScript click: {e}")
                # Fallback to JavaScript click to bypass interception
                await self.page.evaluate('(element) => element.click()', all_ads_option)
                self.logger.debug("✅ 'All ads' option clicked using JavaScript fallback")

            # Step 4.5: Check if we're in mobile mode and need to click Apply button
            # Mobile detection based on viewport width
            is_mobile = self.viewport.get('width', 1920) < 768
            if is_mobile:
                self.logger.info("📱 Mobile mode detected - looking for Apply button for ad category")
                try:
                    # Wait briefly for Apply button to appear
                    await asyncio.sleep(0.5)

                    # Try multiple selectors for the Apply button
                    apply_selectors = [
                        'button:has-text("Apply"):visible',
                        '[role="button"]:has-text("Apply"):visible',
                        'button[aria-label*="Apply"]:visible',
                        'button[aria-label*="apply"]:visible',
                        # Also try "Done" as some mobile UIs use this
                        'button:has-text("Done"):visible',
                        '[role="button"]:has-text("Done"):visible',
                    ]

                    apply_clicked = False
                    for selector in apply_selectors:
                        try:
                            apply_button = await self.page.wait_for_selector(selector, state='visible', timeout=2000)
                            if apply_button:
                                self.logger.info(f"✅ Found Apply/Done button with selector: {selector}")
                                await apply_button.click()
                                apply_clicked = True
                                self.logger.info("👆 Clicked Apply/Done button for ad category")
                                # Wait for modal to close
                                await asyncio.sleep(1)
                                break
                        except Exception:
                            continue

                    if not apply_clicked:
                        self.logger.warning("⚠️ Apply button not found in mobile mode for ad category - continuing anyway")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error handling mobile Apply button for ad category: {e} - continuing")
            else:
                self.logger.debug("💻 Desktop mode - no Apply button needed for ad category")

            # Step 5: Wait for dropdown to close and search input to become enabled
            self.logger.info("⏳ Waiting for category dropdown to close")
            await asyncio.sleep(1)  # Brief pause for dropdown animation

            self.logger.info("✅ Ad category selection completed")

        except Exception as e:
            self.logger.error(f"Failed to select ad category dropdown: {str(e)}")
            raise

    async def _search_advertiser(self, page_name: str):
        """Search for advertiser by page name with improved element interaction and error handling."""
        try:
            # Validate inputs
            if not page_name or not page_name.strip():
                raise ValueError("Page name cannot be empty")

            page_name = page_name.strip()

            # Locate search input with better selector
            search_input_selector = 'input[placeholder="Search by keyword or advertiser"]'
            self.logger.info(f"🔍 Locating search input: {search_input_selector}")

            try:
                search_input = await self.page.wait_for_selector(search_input_selector, state='visible', timeout=10000)
                await search_input.scroll_into_view_if_needed()
            except Exception as e:
                self.logger.error(f"❌ Search input not found or not visible: {e}")
                raise Exception(f"Search input not available: {e}")

            # Clear any existing text and type page name with human-like delays
            try:
                await search_input.click()
                await search_input.fill('')  # Clear any existing text

                # Type with human-like delays
                typing_delay = self.config.get('camoufox', {}).get('search', {}).get('typing_delay', 120)
                await search_input.type(page_name, delay=typing_delay)

                self.logger.info(f"⌨️ Typed advertiser name: {page_name}")

            except Exception as e:
                self.logger.error(f"❌ Failed to type into search input: {e}")
                raise Exception(f"Failed to enter search text: {e}")

            # Wait briefly for suggestions dropdown to appear, then navigate with arrows
            self.logger.info("⏳ Waiting 0.25 seconds for suggestions to load")
            await asyncio.sleep(0.25)

            # Use your proven working pattern for Arrow Down navigation
            self.logger.info("⌨️ Using proven working pattern: Arrow Down + specific selector")

            advertiser_selected = False
            max_attempts = 15  # Reduced from 30 to prevent browser exhaustion

            # Add browser health check variables
            health_check_interval = 10  # Check browser health every 10 iterations
            last_health_check = 0

            for i in range(max_attempts):
                try:
                    # Perform browser health check every 10 iterations
                    if i > 0 and i % health_check_interval == 0:
                        self.logger.info(f"🏥 Performing browser health check at iteration {i}")
                        try:
                            # Check if browser context is still valid
                            await self.page.evaluate("() => window.location.href")
                            self.logger.info("✅ Browser health check passed")
                        except Exception as health_error:
                            self.logger.error(f"❌ Browser health check failed: {health_error}")
                            self.logger.info("🔄 Attempting to recover browser context...")
                            # Try to recover by refreshing the context
                            try:
                                await self.page.reload(wait_until='domcontentloaded', timeout=10000)
                                await asyncio.sleep(2)
                                # Re-enter search term
                                search_input = await self.page.wait_for_selector(search_input_selector, state='visible', timeout=5000)
                                await search_input.click()
                                await search_input.fill('')
                                await search_input.type(page_name, delay=typing_delay)
                                await asyncio.sleep(0.5)
                                self.logger.info("✅ Browser context recovered successfully")
                            except:
                                self.logger.error("❌ Failed to recover browser context")
                                return False

                    # Step A: Get the currently highlighted list item (your exact selector)
                    highlighted_element = await self.page.query_selector('li[role="option"][aria-selected="true"]')

                    if highlighted_element:
                        # Step B: Get its ID and the text from the heading inside it
                        item_id = await highlighted_element.get_attribute("id")

                        # Look for heading inside the highlighted element
                        async with self._track_operation("query_selector_heading"):
                            heading_element = await highlighted_element.query_selector('div[role="heading"]')
                        heading_text = ""
                        if heading_element:
                            heading_text = await heading_element.inner_text()
                        else:
                            # Fallback to full text if no heading found
                            heading_text = await highlighted_element.inner_text()

                        self.logger.info(f"🎯 Attempt {i + 1}: Highlighted item is '{heading_text.strip()}' with ID '{item_id}'")

                        # Step C: Check if this is our target
                        # Skip "Search this exact phrase" options
                        if heading_text and "search this exact phrase" in heading_text.lower():
                            self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' option with ID '{item_id}' - pressing ArrowDown")
                            await self.page.keyboard.press("ArrowDown")
                            await asyncio.sleep(0.2)
                            continue

                        # Check if this is our target (BOTH conditions must be true)
                        # More flexible matching: exact match OR contains match
                        text_matches = False
                        if heading_text:
                            # Remove ALL quotes (including smart quotes) and normalize
                            normalized_heading = heading_text.strip()
                            # Remove various quote types
                            for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
                                normalized_heading = normalized_heading.strip(quote_char)

                            normalized_page_name = page_name.strip()
                            # Remove various quote types from page name too
                            for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
                                normalized_page_name = normalized_page_name.strip(quote_char)

                            # Check for exact match or contains match
                            text_matches = (normalized_page_name.lower() == normalized_heading.lower() or
                                          normalized_page_name.lower() in normalized_heading.lower())

                            if text_matches:
                                self.logger.info(f"✅ Text match found: '{normalized_heading}' matches '{normalized_page_name}'")

                        if text_matches:
                            self.logger.info(f"📝 Checking ID: '{item_id}' - starts with pageID: {item_id.startswith('pageID:') if item_id else False}")

                        if (text_matches and item_id and item_id.startswith("pageID:")):

                            self.logger.info(f"🎯 TARGET FOUND! Text contains '{page_name}' and ID is '{item_id}'")

                            # Extract and store the page ID for filtering
                            if item_id.startswith("pageID:"):
                                self._target_page_id = item_id.replace("pageID:", "")
                                self.logger.info(f"📋 Extracted target page ID: {self._target_page_id}")

                            # Ensure the element is properly focused before pressing Enter
                            self.logger.info("🎯 Ensuring element is focused before selection...")

                            # Method 1: Click on the highlighted element to ensure focus
                            try:
                                await highlighted_element.click()
                                self.logger.info("✅ Clicked on highlighted element to ensure focus")
                                await asyncio.sleep(0.5)  # Small delay to ensure focus is set
                            except Exception as click_error:
                                self.logger.warning(f"⚠️ Could not click highlighted element: {click_error}")

                            # Setup GraphQL capture before pressing Enter
                            self.logger.info("📡 Setting up GraphQL capture before Enter")
                            self.clear_captured_responses()
                            await self._setup_graphql_interception()

                            # Press Enter to select (with retry logic)
                            self.logger.info("⌨️ Target advertiser found. Attempting to select...")

                            # Try pressing Enter first
                            await self.page.keyboard.press("Enter")
                            self.logger.info("⌨️ Pressed Enter key")

                            # Alternative: If Enter doesn't work, try clicking the element directly
                            await asyncio.sleep(0.5)
                            current_url_before = self.page.url

                            # Check if URL changed (indicating successful selection)
                            await asyncio.sleep(1)
                            current_url_after = self.page.url

                            if current_url_before == current_url_after:
                                self.logger.warning("⚠️ URL didn't change after Enter, trying direct click...")
                                try:
                                    # Try clicking the heading element directly
                                    if heading_element:
                                        await heading_element.click()
                                        self.logger.info("✅ Clicked heading element directly")
                                    else:
                                        await highlighted_element.click()
                                        self.logger.info("✅ Clicked highlighted element directly")
                                except Exception as fallback_error:
                                    self.logger.error(f"❌ Fallback click failed: {fallback_error}")

                            # Listen for JSON responses (not just wait fixed time)
                            self.logger.info("👂 Listening for GraphQL responses after Enter...")

                            # Wait for page to load and start receiving responses
                            await asyncio.sleep(2)

                            # CRITICAL: Setup image cache extraction AFTER advertiser page loads
                            self.logger.info(f"🎯 PERFECT TIMING: Setting up image cache extraction for {item_text} advertiser page!")
                            if self.image_cache_extractor:
                                try:
                                    await self.image_cache_extractor.setup_image_interception(self.page)
                                    self.logger.info(f"🖼️ CRITICAL: Image cache extraction setup complete AFTER advertiser page loaded for {item_text}")
                                    self.logger.info(f"📊 Cache currently has {len(self.image_cache_extractor._image_cache)} images ready to use")
                                except Exception as e:
                                    self.logger.warning(f"Failed to setup image cache extraction after page load: {e}")

                            # Monitor for responses with timeout
                            max_wait_time = 15  # Total time to wait for responses
                            check_interval = 1  # Check every 1 second
                            waited_time = 0

                            while waited_time < max_wait_time:
                                if len(self._graphql_responses) > 0:
                                    self.logger.info(f"📡 Received {len(self._graphql_responses)} GraphQL responses so far")
                                    self.logger.info(f"🔄 Accumulated NDJSON: {len(self._accumulated_ndjson)} characters")

                                    # Check accumulated NDJSON for ad_library_main responses
                                    has_ad_library = False
                                    if self._accumulated_ndjson:
                                        try:
                                            # Parse the accumulated NDJSON stream
                                            lines = self._accumulated_ndjson.replace('\r\n', '\n').replace('\r', '\n').split('\n')
                                            for line in lines:
                                                line = line.strip()
                                                if line:
                                                    try:
                                                        json_data = json.loads(line)
                                                        if self._is_ad_library_main_response(json_data):
                                                            has_ad_library = True
                                                            self.logger.info(f"🎯 Found ad_library_main in accumulated stream!")
                                                            break
                                                    except:
                                                        continue
                                        except Exception as e:
                                            self.logger.debug(f"Error checking accumulated NDJSON: {e}")

                                    if has_ad_library:
                                        self.logger.info("🎯 Found ad_library_main responses! Processing...")
                                        break

                                await asyncio.sleep(check_interval)
                                waited_time += check_interval

                                if waited_time % 3 == 0:  # Log every 3 seconds
                                    self.logger.info(f"⏳ Still listening... ({waited_time}s elapsed, {len(self._graphql_responses)} responses)")

                            # Process responses and save to JSON
                            await self._process_captured_graphql_responses()
                            await self._disable_graphql_interception()

                            advertiser_selected = True
                            break  # Exit loop successfully

                    # Step D: If not the target, press ArrowDown
                    if not advertiser_selected:
                        # Log what we're looking for vs what we found
                        self.logger.info(f"❌ Not a match - Looking for: '{page_name}' with pageID:")
                        self.logger.info(f"   Found text: '{heading_text.strip() if heading_text else 'None'}'")
                        self.logger.info(f"   Found ID: '{item_id if item_id else 'None'}'")

                        self.logger.info(f"⌨️ Pressing ArrowDown to move to next item (attempt {i + 1}/{max_attempts})")
                        await self.page.keyboard.press("ArrowDown")

                        # Step E: Wait a brief, human-like moment for UI to update
                        await asyncio.sleep(0.3)  # Slightly longer delay for UI update

                except Exception as e:
                    self.logger.debug(f"Error in attempt {i + 1}: {e}")
                    # Continue to next attempt
                    await self.page.keyboard.press("ArrowDown")
                    await asyncio.sleep(0.2)

            if not advertiser_selected:
                self.logger.error(f"❌ Failed to find '{page_name}' after {max_attempts} attempts (reduced from 30 to prevent browser exhaustion)")

                # Take a screenshot for debugging
                try:
                    await self.page.screenshot(path="debug_dropdown_failure.png")
                    self.logger.info("📸 Screenshot saved as debug_dropdown_failure.png")
                except:
                    pass

                # Log all available options we saw
                self.logger.info("📋 Available options found during search:")
                try:
                    all_options = await self.page.query_selector_all('li[role="option"]')
                    for idx, option in enumerate(all_options[:5]):  # Show first 5
                        try:
                            opt_text = await option.inner_text()
                            opt_id = await option.get_attribute("id")
                            self.logger.info(f"   Option {idx + 1}: '{opt_text.strip()}' (ID: {opt_id})")
                        except:
                            pass
                except:
                    pass

                return False

            self.logger.info(f"✅ Successfully selected '{page_name}' with pageID")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to search for advertiser {page_name}: {str(e)}")
            return False

    async def _search_advertiser_no_capture(self, search_term: str) -> tuple[bool, Optional[str]]:
        """
        Simplified search for advertiser by page name or ID WITHOUT GraphQL capture.
        This version just searches and selects, leaving GraphQL capture for later.

        Args:
            search_term: Either page name or page ID to search for

        Returns:
            Tuple of (success, discovered_page_name)
            - success: True if advertiser was found and selected
            - discovered_page_name: The page name discovered during ID search (None for name searches)
        """
        try:
            # Track discovered page name (only for ID searches)
            discovered_page_name: Optional[str] = None

            # Validate inputs
            if not search_term or not search_term.strip():
                raise ValueError("Search term cannot be empty")

            search_term = search_term.strip()

            # CRITICAL FIX: Strip quotes from search term
            # Config may pass firm IDs with quotes like "66401254358" but dropdown has pageID:66401254358
            original_search_term = search_term
            for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
                search_term = search_term.strip(quote_char)

            if original_search_term != search_term:
                self.logger.info(f"🧹 CRITICAL FIX: Stripped quotes from search term: '{original_search_term}' -> '{search_term}'")

            # CRITICAL DEBUG: Log exact search term details
            self.logger.info("="*60)
            self.logger.info(f"🔍 CRITICAL DEBUG: Search term analysis")
            self.logger.info(f"   - search_term: '{search_term}'")
            self.logger.info(f"   - search_term.isdigit(): {search_term.isdigit()}")
            self.logger.info(f"   - len(search_term): {len(search_term)}")
            self.logger.info(f"   - search_term chars: {[c for c in search_term[:20]]}")  # Show first 20 chars
            self.logger.info("="*60)

            # CRITICAL FIX: Detect if search term is an ID (all digits, 10+ characters)
            # Facebook page IDs can be as short as 10-12 digits, not necessarily 15+
            is_id_search = search_term.isdigit() and len(search_term) >= 10
            self.logger.info(f"🔍 Search mode: {'ID-based' if is_id_search else 'Name-based'} for term: '{search_term}'")

            # Locate search input with better selector
            search_input_selector = 'input[placeholder="Search by keyword or advertiser"]'
            self.logger.info(f"🔍 Locating search input: {search_input_selector}")

            try:
                search_input = await self.page.wait_for_selector(search_input_selector, state='visible', timeout=10000)
                await search_input.scroll_into_view_if_needed()
            except Exception as e:
                self.logger.error(f"❌ Search input not found or not visible: {e}")
                raise Exception(f"Search input not available: {e}")

            # Clear any existing text and type search term with human-like delays
            try:
                await search_input.click()
                await search_input.fill('')  # Clear any existing text

                # Type with human-like delays
                typing_delay = self.config.get('camoufox', {}).get('search', {}).get('typing_delay', 120)
                await search_input.type(search_term, delay=typing_delay)

                self.logger.info(f"⌨️ Typed search term: {search_term}")

            except Exception as e:
                self.logger.error(f"❌ Failed to type into search input: {e}")
                raise Exception(f"Failed to enter search text: {e}")

            # Wait briefly for suggestions dropdown to appear
            self.logger.info("⏳ Waiting 0.5 seconds for suggestions to load")
            await asyncio.sleep(0.5)

            # Use Arrow Down navigation to find and select advertiser
            self.logger.info("⌨️ Navigating dropdown to find advertiser")

            advertiser_selected = False
            max_attempts = 15  # Reduced from 30 to prevent browser exhaustion

            # Add browser health check variables
            health_check_interval = 10  # Check browser health every 10 iterations

            for i in range(max_attempts):
                try:
                    # Get the currently highlighted list item
                    highlighted_element = await self.page.query_selector('li[role="option"][aria-selected="true"]')

                    if highlighted_element:
                        # Get its ID and the text from the heading inside it
                        item_id = await highlighted_element.get_attribute("id")

                        # Look for heading inside the highlighted element
                        async with self._track_operation("query_selector_heading"):
                            heading_element = await highlighted_element.query_selector('div[role="heading"]')
                        heading_text = ""
                        if heading_element:
                            heading_text = await heading_element.inner_text()
                        else:
                            # Fallback to full text if no heading found
                            heading_text = await highlighted_element.inner_text()

                        self.logger.info(f"🎯 Attempt {i + 1}: Highlighted item is '{heading_text.strip()}' with ID '{item_id}'")

                        # Skip "Search this exact phrase" options
                        if heading_text and "search this exact phrase" in heading_text.lower():
                            self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' option with ID '{item_id}' - pressing ArrowDown")
                            await self.page.keyboard.press("ArrowDown")
                            await asyncio.sleep(0.2)
                            continue

                        # Check if this is our target - different logic for ID vs name search
                        is_match = False
                        extracted_page_name = None

                        if is_id_search:
                            # ID-based search: look for pageID matches
                            if item_id and item_id.startswith("pageID:"):
                                page_id_from_item = item_id.replace("pageID:", "")
                                # CRITICAL DEBUG: Log the comparison
                                self.logger.info(f"🔍 ID COMPARISON: page_id_from_item='{page_id_from_item}' vs search_term='{search_term}'")
                                self.logger.info(f"🔍 ID COMPARISON RESULT: {page_id_from_item == search_term}")
                                if page_id_from_item == search_term:
                                    is_match = True
                                    # Extract page name from heading text for database update
                                    # SIMPLIFIED: Just use the heading text as-is, don't do aggressive cleanup
                                    extracted_page_name = heading_text.strip() if heading_text else None
                                    self.logger.info(f"🎯 ID MATCH! pageID '{page_id_from_item}' matches search term '{search_term}'")
                                    if extracted_page_name:
                                        self.logger.info(f"📝 Extracted page name: '{extracted_page_name}'")
                                        # Store for return value
                                        discovered_page_name = extracted_page_name
                        else:
                            # Name-based search: look for text matches (original logic)
                            if heading_text:
                                # Remove ALL quotes (including smart quotes) and normalize
                                normalized_heading = heading_text.strip()
                                # Remove various quote types
                                for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
                                    normalized_heading = normalized_heading.strip(quote_char)

                                normalized_search_term = search_term.strip()
                                # Remove various quote types from search term too
                                for quote_char in ['"', "'", '"', '"', ''', ''', '`']:
                                    normalized_search_term = normalized_search_term.strip(quote_char)

                                # Check for exact match or contains match
                                is_match = (normalized_search_term.lower() == normalized_heading.lower() or
                                          normalized_search_term.lower() in normalized_heading.lower())

                                if is_match:
                                    self.logger.info(f"📝 Text match found for '{normalized_search_term}' in '{normalized_heading}'")
                                    if item_id:
                                        self.logger.info(f"📝 Checking ID: '{item_id}' - starts with pageID: {item_id.startswith('pageID:')}")
                                    else:
                                        self.logger.info(f"📝 No ID found for this item")

                        if is_match and item_id and item_id.startswith("pageID:"):
                            if is_id_search:
                                self.logger.info(f"🎯 TARGET FOUND! ID '{search_term}' matches pageID '{item_id}'")
                            else:
                                self.logger.info(f"🎯 TARGET FOUND! Text contains '{search_term}' and ID is '{item_id}'")

                            # Extract and store the page ID for filtering
                            if item_id.startswith("pageID:"):
                                self._target_page_id = item_id.replace("pageID:", "")
                                self.logger.info(f"📋 Extracted target page ID: {self._target_page_id}")

                            # If this was an ID-based search, ALWAYS update the database with whatever text we found
                            if is_id_search:
                                self.logger.info("="*60)
                                self.logger.info(f"🔍 PAGE NAME UPDATE CHECK:")
                                self.logger.info(f"  - is_id_search: {is_id_search}")
                                self.logger.info(f"  - extracted_page_name: '{extracted_page_name}'")
                                self.logger.info(f"  - heading_text: '{heading_text}'")

                                # CRITICAL: Use heading_text if extracted_page_name is None
                                page_name_to_update = extracted_page_name if extracted_page_name else heading_text.strip()
                                self.logger.info(f"  - self.law_firms_repository: {self.law_firms_repository}")
                                self.logger.info(f"  - repository type: {type(self.law_firms_repository).__name__ if self.law_firms_repository else 'None'}")

                                if self.law_firms_repository:
                                    try:
                                        self.logger.info("="*60)
                                        self.logger.info(f"🔥 PAGE NAME UPDATE TRIGGERED! 🔥")
                                        self.logger.info(f"💾 Updating law firm database with page name: '{page_name_to_update}'")
                                        self.logger.info(f"🆔 Firm ID: {search_term}")
                                        self.logger.info("="*60)

                                        # Get the firm name to use for the update
                                        results = await self.law_firms_repository.get_by_id(search_term)
                                        if results and len(results) > 0:
                                            firm_name = results[0].get('name', '')
                                            if firm_name:
                                                self.logger.info(f"🏢 Firm Name: {firm_name}")
                                                self.logger.info(f"📝 Updating page_name to: '{page_name_to_update}'")
                                                self.logger.info(f"📝 Update parameters:")
                                                self.logger.info(f"    - firm_id: {search_term}")
                                                self.logger.info(f"    - name: {firm_name}")
                                                self.logger.info(f"    - updates: {{'page_name': '{page_name_to_update}'}}")

                                                # Perform the update
                                                update_result = await self.law_firms_repository.update_attributes(
                                                    firm_id=search_term,
                                                    name=firm_name,
                                                    updates={'page_name': page_name_to_update}
                                                )

                                                # 🔍 DIAGNOSTIC: Log the actual update result details
                                                self.logger.info("="*60)
                                                self.logger.info(f"📊 Update result: {update_result}")
                                                self.logger.info(f"🔍 DIAGNOSTIC - update_result type: {type(update_result)}")
                                                self.logger.info(f"🔍 DIAGNOSTIC - update_result is True: {update_result is True}")
                                                self.logger.info(f"🔍 DIAGNOSTIC - update_result == True: {update_result == True}")
                                                self.logger.info(f"🔍 DIAGNOSTIC - bool(update_result): {bool(update_result)}")

                                                # 🔍 VERIFICATION: Read back the record to verify the update
                                                self.logger.info("🔍 VERIFICATION - Reading back record to verify update...")
                                                verify_results = await self.law_firms_repository.get_by_id(search_term)
                                                if verify_results and len(verify_results) > 0:
                                                    verify_record = verify_results[0]
                                                    actual_page_name = verify_record.get('page_name', '')
                                                    self.logger.info(f"🔍 VERIFICATION - Current page_name in DB: '{actual_page_name}'")
                                                    self.logger.info(f"🔍 VERIFICATION - Expected page_name: '{page_name_to_update}'")
                                                    self.logger.info(f"🔍 VERIFICATION - Match: {actual_page_name == page_name_to_update}")
                                                else:
                                                    self.logger.error("🔍 VERIFICATION - Failed to read back record for verification!")

                                                if update_result:
                                                    self.logger.info(f"✅ DATABASE UPDATE SUCCESSFUL! ✅")
                                                    self.logger.info(f"✅ Firm: {firm_name}")
                                                    self.logger.info(f"✅ New page_name: '{page_name_to_update}'")
                                                else:
                                                    self.logger.error(f"❌ DATABASE UPDATE RETURNED FALSE!")
                                                    self.logger.error(f"❌ The update_attributes method returned: {update_result}")
                                                    self.logger.error(f"❌ CRITICAL: Law firm repository update FAILED - raising exception")
                                                    raise RuntimeError("Law firm repository update FAILED")
                                                self.logger.info("="*60)
                                            else:
                                                self.logger.warning(f"⚠️ No firm name found for ID {search_term}, skipping database update")
                                        else:
                                            self.logger.warning(f"⚠️ No firm found for ID {search_term}, skipping database update")
                                    except Exception as e:
                                        import traceback
                                        self.logger.error("="*60)
                                        self.logger.error(f"❌ DATABASE UPDATE FAILED! ❌")
                                        self.logger.error(f"❌ Error: {e}")
                                        self.logger.error(f"❌ Exception type: {type(e).__name__}")
                                        self.logger.error(f"❌ Full traceback:")
                                        self.logger.error(traceback.format_exc())
                                        self.logger.error("="*60)
                                        self.logger.error(f"❌ CRITICAL: Law firm repository update FAILED with exception - raising exception")
                                        raise RuntimeError(f"Law firm repository update FAILED with exception: {e}")
                                else:
                                    self.logger.error("="*60)
                                    self.logger.error(f"❌ CANNOT UPDATE PAGE NAME - REPOSITORY IS NULL! ❌")
                                    self.logger.error(f"❌ law_firms_repository was not passed to session manager")
                                    self.logger.error("="*60)
                                    self.logger.error(f"❌ CRITICAL: law_firms_repository is None - raising exception")
                                    raise RuntimeError("law_firms_repository is None - cannot update page name")

                            # GraphQL interception should already be set up by caller
                            self.logger.info("📡 GraphQL interception should already be active")

                            # Click on the highlighted element or press Enter
                            try:
                                await highlighted_element.click()
                                self.logger.info("✅ Clicked on highlighted element")
                            except:
                                # Fallback to Enter key
                                await self.page.keyboard.press("Enter")
                                self.logger.info("⌨️ Pressed Enter key")

                            # Wait for navigation
                            await asyncio.sleep(2)

                            advertiser_selected = True
                            break

                    # If not the target, press ArrowDown
                    if not advertiser_selected:
                        self.logger.info(f"⬇️ Not our target, pressing ArrowDown to continue searching...")
                        await self.page.keyboard.press("ArrowDown")
                        await asyncio.sleep(0.2)

                except Exception as e:
                    self.logger.error(f"❌ Error during advertiser search at attempt {i + 1}: {e}")
                    # Continue to next attempt

            if not advertiser_selected:
                search_type = "ID" if is_id_search else "Name"
                self.logger.critical(f"❌ Advertiser with {search_type} '{search_term}' not found after {max_attempts} attempts (reduced from 30 to prevent browser exhaustion)")
                return False, None

            search_type = "ID" if is_id_search else "Name"
            self.logger.info(f"✅ Successfully selected advertiser with {search_type}: {search_term}")
            # Log if we discovered a page name
            if discovered_page_name:
                self.logger.info(f"📋 Discovered page name during search: '{discovered_page_name}'")
            return True, discovered_page_name

        except Exception as e:
            search_type = "ID" if is_id_search else "Name" if 'is_id_search' in locals() else "Unknown"
            self.logger.error(f"❌ Failed to search advertiser with {search_type} '{search_term}': {e}")
            return False, None

    async def _select_advertiser_from_suggestions(self, law_firm_name: str):
        """
        Select advertiser from suggestions dropdown using robust iteration method.

        This method implements the robust iteration strategy from flow1.md specification:
        - Fetch all suggestion rows from the listbox
        - Iterate through each row to find the one with matching heading text
        - Click the target row when found

        Args:
            law_firm_name: The exact name of the law firm to find and select

        Returns:
            bool: True if advertiser was found and selected, False otherwise
        """
        try:
            # Validate inputs
            if not law_firm_name or not law_firm_name.strip():
                raise ValueError("Law firm name cannot be empty")

            law_firm_name = law_firm_name.strip()
            self.logger.info(f"🔍 Searching for advertiser in suggestions: {law_firm_name}")

            # Wait for suggestions listbox to appear and contain at least one item - flow1.md specification with fallbacks
            self.logger.info("⏳ Waiting for suggestions listbox to appear")

            # Try multiple selectors to find the suggestions
            dropdown_selectors = [
                'div[role="listbox"] > div',  # Primary from flow1.md
                '[role="listbox"] > div',     # Alternative without div prefix
                'div[role="listbox"]',        # Parent container
                '[role="listbox"]',           # Alternative parent
            ]

            working_selector = None
            for selector in dropdown_selectors:
                try:
                    self.logger.debug(f"🔍 Trying listbox selector: {selector}")
                    await self.page.wait_for_selector(selector, state='visible', timeout=2500)
                    working_selector = selector
                    self.logger.debug(f"✅ Found listbox with selector: {selector}")
                    break
                except Exception as e:
                    self.logger.debug(f"❌ Selector {selector} failed: {e}")
                    continue

            if not working_selector:
                self.logger.error("❌ No suggestions listbox found with any selector")
                raise Exception("Suggestions dropdown not available")

            # FIRST: MOVE MOUSE TO DROPDOWN IMMEDIATELY - BEFORE ANY SEARCHING
            self.logger.info("🖱️ MOVING MOUSE TO DROPDOWN FIRST!")
            try:
                # Use the SAME selector that actually worked to find the dropdown
                if '> div' in working_selector:
                    # Extract parent selector (remove > div part)
                    dropdown_selector = working_selector.split(' > div')[0]
                else:
                    dropdown_selector = working_selector

                self.logger.info(f"🖱️ Using dropdown selector: {dropdown_selector}")
                dropdown_container = await self.page.query_selector(dropdown_selector)
                if dropdown_container:
                    dropdown_box = await dropdown_container.bounding_box()
                    if dropdown_box:
                        center_x = dropdown_box['x'] + dropdown_box['width'] / 2
                        center_y = dropdown_box['y'] + dropdown_box['height'] / 2

                        self.logger.info(f"🖱️ Moving mouse FROM search box TO dropdown center: ({center_x}, {center_y})")
                        await self.page.mouse.move(center_x, center_y)
                        await asyncio.sleep(0.5)  # Let mouse settle
                        self.logger.info("✅ Mouse is now positioned over the dropdown!")
                    else:
                        self.logger.error("❌ Could not get dropdown bounding box")
                else:
                    self.logger.error(f"❌ Could not find dropdown container with selector: {dropdown_selector}")
            except Exception as e:
                self.logger.error(f"❌ Failed to move mouse to dropdown: {e}")

            # Fetch all suggestion rows - get all direct div children of the listbox
            try:
                # Determine the correct selector for rows based on what worked
                if '> div' in working_selector:
                    row_selector = working_selector
                else:
                    row_selector = f'{working_selector} > div'

                suggestion_rows = await self.page.query_selector_all(row_selector)
                if not suggestion_rows:
                    # Try alternative row selectors
                    alternative_selectors = [
                        'div[role="listbox"] div',  # All divs inside listbox
                        '[role="listbox"] div',     # Alternative all divs
                        'div[role="listbox"] > *',  # All direct children
                    ]

                    for alt_selector in alternative_selectors:
                        try:
                            suggestion_rows = await self.page.query_selector_all(alt_selector)
                            if suggestion_rows:
                                self.logger.debug(f"✅ Found rows with alternative selector: {alt_selector}")
                                break
                        except:
                            continue

                if not suggestion_rows:
                    raise Exception("No suggestion rows found in listbox")

                self.logger.info(f"📊 Found {len(suggestion_rows)} suggestion rows")
            except Exception as e:
                self.logger.error(f"❌ Failed to fetch suggestion rows: {e}")
                raise Exception(f"Could not access suggestion rows: {e}")

            # Iterate through rows to find the correct one - with scrolling support
            target_row = None
            all_found_options = []  # Track all options for better debugging
            max_scroll_attempts = 5  # Limit scrolling attempts
            scroll_attempt = 0

            # First pass: check currently visible items
            self.logger.info("🔍 First pass: Checking currently visible suggestions")

            for i, row in enumerate(suggestion_rows):
                try:
                    # Scroll row into view to ensure it's visible
                    await row.scroll_into_view_if_needed()
                    await asyncio.sleep(0.1)  # Brief pause for rendering

                    # Find the heading element within the current row - try multiple selectors
                    heading_element = await row.query_selector('div[role="heading"]')

                    # Debug: log the full row content to see what we're missing
                    row_html = await self.page.evaluate('el => el.innerHTML', row)
                    self.logger.debug(f"🔍 Row {i+1} HTML: {row_html[:200]}...")

                    if heading_element:
                        # Extract text and compare
                        heading_text = await self.page.evaluate('el => el.textContent', heading_element)
                        clean_heading = heading_text.strip()
                        all_found_options.append(clean_heading)

                        self.logger.info(f"📋 Row {i+1}: '{clean_heading}'")

                        # Skip ALL "Search this exact phrase" options - we want the actual Facebook page
                        if "search this exact phrase" in clean_heading.lower():
                            self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' option: '{clean_heading}'")
                            continue

                        if clean_heading == law_firm_name:
                            target_row = row
                            self.logger.info(f"🎯 Found exact match at row {i+1}: '{clean_heading}'")
                            break
                        elif self._matches_law_firm(clean_heading, law_firm_name):
                            target_row = row
                            self.logger.info(f"🎯 Found flexible match at row {i+1}: '{clean_heading}' matches '{law_firm_name}'")
                            break
                    else:
                        # Try alternative selectors for text extraction
                        try:
                            # Try getting all text from the row
                            row_text = await self.page.evaluate('el => el.textContent', row)
                            clean_row_text = row_text.strip()
                            if clean_row_text:
                                all_found_options.append(f"[No heading] {clean_row_text}")
                                self.logger.info(f"📋 Row {i+1} (no heading): '{clean_row_text}'")

                                # Skip ALL "Search this exact phrase" options - we want the actual Facebook page
                                if "search this exact phrase" in clean_row_text.lower():
                                    self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' in row text: '{clean_row_text}'")
                                    continue

                                if self._matches_law_firm(clean_row_text, law_firm_name):
                                    target_row = row
                                    self.logger.info(f"🎯 Found flexible match in row text at row {i+1}: '{clean_row_text}'")
                                    break
                        except Exception as text_error:
                            self.logger.info(f"❌ Row {i+1}: Failed to extract text - {text_error}")

                        # ALWAYS try to get HTML content for debugging
                        try:
                            inner_html = await self.page.evaluate('el => el.innerHTML', row)
                            self.logger.info(f"📋 Row {i+1} HTML: {inner_html[:300]}...")  # First 300 chars

                            # Look for MORGAN & MORGAN law firm specifically - NOT Morgan Freeman!
                            # Check all clickable elements in this row
                            clickable_elements = await row.query_selector_all('div[role="option"], li[role="option"], [aria-selected="false"], a, button, [onclick]')

                            for element in clickable_elements:
                                try:
                                    element_text = await element.inner_text()
                                    self.logger.info(f"🔍 Row {i+1} element text: '{element_text.strip()}'")

                                    # EXACT check for Morgan & Morgan (NOT Morgan Freeman)
                                    text_lower = element_text.lower().strip()

                                    # Must contain "morgan" AND "&" AND NOT "freeman"
                                    has_morgan_and_ampersand = 'morgan' in text_lower and '&' in text_lower
                                    not_freeman = 'freeman' not in text_lower

                                    if has_morgan_and_ampersand and not_freeman:
                                        self.logger.info(f"🎯 FOUND MORGAN & MORGAN (not Freeman): '{element_text.strip()}'")
                                        target_row = element
                                        break
                                    elif text_lower == 'morgan & morgan':
                                        self.logger.info(f"🎯 EXACT MATCH for Morgan & Morgan: '{element_text.strip()}'")
                                        target_row = element
                                        break
                                    elif 'morgan freeman' in text_lower:
                                        self.logger.info(f"⏭️ SKIPPING Morgan Freeman: '{element_text.strip()}'")
                                        continue
                                    elif 'morgan' in text_lower:
                                        self.logger.info(f"🔍 Found Morgan but checking: '{element_text.strip()}'")

                                except Exception as e:
                                    self.logger.debug(f"Error reading element text: {e}")
                                    continue

                            if target_row:
                                break

                        except Exception as html_error:
                            self.logger.info(f"📋 Row {i+1}: Failed to get HTML - {html_error}")

                        self.logger.info(f"❌ Row {i+1}: No heading element found and no readable text")

                except Exception as e:
                    self.logger.info(f"❌ Error processing row {i+1}: {e}")
                    continue

            # ALWAYS try scrolling if not found - there might be more items below
            if not target_row:
                self.logger.info(f"🔄 Target '{law_firm_name}' not found in visible items. SCROLLING dropdown for more options...")

                # Try to find the dropdown container for scrolling - USE THE SAME SELECTOR THAT WORKED EARLIER
                try:
                    # Use the same dropdown_selector that successfully found the dropdown
                    if '> div' in working_selector:
                        # Extract parent selector (remove > div part)
                        dropdown_selector_for_scroll = working_selector.split(' > div')[0]
                    else:
                        dropdown_selector_for_scroll = working_selector

                    self.logger.info(f"🔍 Using dropdown selector for scrolling: {dropdown_selector_for_scroll}")
                    dropdown_container = await self.page.query_selector(dropdown_selector_for_scroll)
                    if dropdown_container:
                        self.logger.info(f"✅ Found dropdown container for scrolling")

                        # CRITICAL: Give dropdown focus BEFORE scrolling - try multiple methods
                        self.logger.info("🎯 Setting focus on dropdown for scrolling")
                        focus_achieved = False

                        # Method 1: Try to focus the dropdown container directly
                        try:
                            await self.page.evaluate('el => el.focus()', dropdown_container)
                            await asyncio.sleep(0.2)

                            # Check if it worked
                            has_focus = await self.page.evaluate('el => document.activeElement === el', dropdown_container)
                            if has_focus:
                                self.logger.info("✅ Method 1: Successfully focused dropdown container")
                                focus_achieved = True
                        except Exception as e:
                            self.logger.info(f"❌ Method 1 failed: {e}")

                        # Method 2: Try to set tabindex and focus
                        if not focus_achieved:
                            try:
                                await self.page.evaluate('el => { el.tabIndex = 0; el.focus(); }', dropdown_container)
                                await asyncio.sleep(0.2)

                                has_focus = await self.page.evaluate('el => document.activeElement === el', dropdown_container)
                                if has_focus:
                                    self.logger.info("✅ Method 2: Successfully focused dropdown with tabIndex")
                                    focus_achieved = True
                            except Exception as e:
                                self.logger.info(f"❌ Method 2 failed: {e}")

                        # Method 3: Find and focus a focusable element inside the dropdown
                        if not focus_achieved:
                            try:
                                # Look for focusable elements inside dropdown
                                focusable_element = await dropdown_container.query_selector('input, button, [tabindex], [role="option"]')
                                if focusable_element:
                                    await focusable_element.focus()
                                    await asyncio.sleep(0.2)

                                    has_focus = await self.page.evaluate('el => document.activeElement === el', focusable_element)
                                    if has_focus:
                                        self.logger.info("✅ Method 3: Successfully focused element inside dropdown")
                                        focus_achieved = True
                                else:
                                    self.logger.info("❌ Method 3: No focusable elements found inside dropdown")
                            except Exception as e:
                                self.logger.info(f"❌ Method 3 failed: {e}")

                        # Method 4: Force focus by clicking on dropdown and setting focus via JS
                        if not focus_achieved:
                            try:
                                await dropdown_container.click()
                                await self.page.evaluate('''(el) => {
                                    el.tabIndex = 0;
                                    el.focus();
                                    el.scrollTop = 0; // Reset scroll position
                                }''', dropdown_container)
                                await asyncio.sleep(0.3)

                                has_focus = await self.page.evaluate('el => document.activeElement === el', dropdown_container)
                                if has_focus:
                                    self.logger.info("✅ Method 4: Successfully focused dropdown via click + JS")
                                    focus_achieved = True
                            except Exception as e:
                                self.logger.info(f"❌ Method 4 failed: {e}")

                        if not focus_achieved:
                            self.logger.info("❌ All focus methods failed - scrolling may not work properly")

                        # FINAL VALIDATION: Double-check focus status
                        try:
                            # Check what element actually has focus now
                            active_info = await self.page.evaluate('''() => {
                                const active = document.activeElement;
                                return {
                                    tag: active.tagName,
                                    className: active.className,
                                    id: active.id,
                                    isDropdown: active.getAttribute('role') === 'listbox'
                                };
                            }''')

                            if focus_achieved or active_info['isDropdown']:
                                self.logger.info("✅ FOCUS CONFIRMED: Dropdown or focusable element has focus - scroll events should work")
                            else:
                                self.logger.info("❌ FOCUS WARNING: Focus may not be on dropdown - scroll events might not work")
                                self.logger.info(f"🔍 Current focus is on: <{active_info['tag']}> id='{active_info['id']}' class='{active_info['className'][:50]}'")

                        except Exception as focus_check_error:
                            self.logger.info(f"❌ Could not validate final focus: {focus_check_error}")

                        # Force scrolling attempts - don't rely on scroll_attempt counter
                        for scroll_attempt in range(1, 6):  # Try up to 5 times
                            self.logger.info(f"⌨️ KEYBOARD NAVIGATION attempt {scroll_attempt}/5")

                            # Get current scroll position before scrolling
                            current_scroll = await self.page.evaluate('el => el.scrollTop', dropdown_container)
                            self.logger.info(f"📍 Current scroll position: {current_scroll}")

                            # NAVIGATE WITH KEYBOARD ARROWS (much more reliable for dropdowns)
                            self.logger.info("⌨️ Navigating dropdown with keyboard arrow keys")

                            # Use keyboard navigation to reveal more options
                            arrow_presses = 8  # Try more arrow presses to reveal more options
                            for arrow_round in range(arrow_presses):
                                self.logger.info(f"⌨️ Pressing Arrow Down key (press {arrow_round + 1}/{arrow_presses})")
                                await self.page.keyboard.press('ArrowDown')
                                await asyncio.sleep(0.5)  # Wait for navigation and loading

                                # Check if new items appeared - try multiple selectors in case structure changed
                                new_row_count = 0
                                for selector_to_try in [row_selector, 'div[role="listbox"] > div', '[role="listbox"] > div', 'div[role="listbox"] div']:
                                    try:
                                        count = len(await self.page.query_selector_all(selector_to_try))
                                        if count > new_row_count:
                                            new_row_count = count
                                            if selector_to_try != row_selector:
                                                self.logger.info(f"🔄 Row selector changed after scroll - now using: {selector_to_try}")
                                                row_selector = selector_to_try  # Update selector for subsequent operations
                                    except:
                                        continue

                                self.logger.info(f"📊 After arrow press {arrow_round + 1}: {new_row_count} rows total")

                                if new_row_count > len(suggestion_rows):
                                    self.logger.info(f"✅ New items appeared! Was {len(suggestion_rows)}, now {new_row_count}")
                                    break

                            # Check new scroll position
                            new_scroll = await self.page.evaluate('el => el.scrollTop', dropdown_container)
                            self.logger.info(f"📍 New scroll position: {new_scroll}")

                            await asyncio.sleep(1)  # Wait longer for new items to load

                            # CRITICAL: Re-fetch suggestion rows after scrolling and validate - try multiple selectors
                            new_suggestion_rows = []
                            new_count = 0

                            # Try multiple selectors in case DOM structure changed after scrolling
                            for selector_to_try in [row_selector, 'div[role="listbox"] > div', '[role="listbox"] > div', 'div[role="listbox"] div']:
                                try:
                                    rows = await self.page.query_selector_all(selector_to_try)
                                    if len(rows) > new_count:
                                        new_suggestion_rows = rows
                                        new_count = len(rows)
                                        if selector_to_try != row_selector:
                                            self.logger.info(f"🔄 Using different selector after scroll: {selector_to_try}")
                                            row_selector = selector_to_try  # Update for subsequent operations
                                except:
                                    continue

                            original_count = len(suggestion_rows)
                            new_rows_found = new_count - original_count

                            self.logger.info(f"📊 SCROLL VALIDATION: Before={original_count}, After={new_count}, New={new_rows_found}")

                            if new_rows_found > 0:
                                self.logger.info(f"✅ SUCCESS: Found {new_rows_found} new rows after scrolling!")
                            elif new_scroll > current_scroll:
                                self.logger.info(f"📜 Scroll position changed ({current_scroll} → {new_scroll}) but no new rows visible yet")
                            else:
                                self.logger.info(f"❌ SCROLL BLOCKED: No scroll movement ({current_scroll} → {new_scroll}) - might be at bottom")
                                # Try alternative scrolling method
                                self.logger.info(f"🔄 Trying alternative scroll method: page.evaluate scrollTop += 300")
                                await self.page.evaluate(f'document.querySelector(\'{dropdown_selector_for_scroll}\').scrollTop += 300')
                                await asyncio.sleep(1)

                                # Check if alternative method worked
                                alt_scroll = await self.page.evaluate('el => el.scrollTop', dropdown_container)
                                alt_suggestion_rows = await self.page.query_selector_all(row_selector)
                                alt_new_count = len(alt_suggestion_rows)

                                self.logger.info(f"📊 ALTERNATIVE SCROLL: Position={alt_scroll}, Rows={alt_new_count}")

                                if alt_new_count > original_count:
                                    self.logger.info(f"✅ Alternative scroll worked! Found {alt_new_count - original_count} new rows")
                                    new_suggestion_rows = alt_suggestion_rows
                                else:
                                    self.logger.info(f"❌ Alternative scroll failed - might be at bottom of dropdown")
                                    break

                            # Check ALL rows again (not just new ones, in case ordering changed)
                            for i, row in enumerate(new_suggestion_rows):

                                try:
                                    await row.scroll_into_view_if_needed()
                                    await asyncio.sleep(0.1)

                                    heading_element = await row.query_selector('div[role="heading"]')
                                    if heading_element:
                                        heading_text = await self.page.evaluate('el => el.textContent', heading_element)
                                        clean_heading = heading_text.strip()

                                        self.logger.info(f"📋 Checking row {i+1} after scroll: '{clean_heading}'")

                                        # Skip ALL "Search this exact phrase" options - we want the actual Facebook page
                                        if "search this exact phrase" in clean_heading.lower():
                                            self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' after scroll: '{clean_heading}'")
                                            continue

                                        if clean_heading == law_firm_name:
                                            target_row = row
                                            self.logger.info(f"🎯 FOUND EXACT MATCH after scrolling: '{clean_heading}'")
                                            break
                                        elif self._matches_law_firm(clean_heading, law_firm_name):
                                            target_row = row
                                            self.logger.info(f"🎯 FOUND FLEXIBLE MATCH after scrolling: '{clean_heading}'")
                                            break
                                    else:
                                        # Try alternative text extraction
                                        row_text = await self.page.evaluate('el => el.textContent', row)
                                        clean_row_text = row_text.strip()

                                        # Skip ALL "Search this exact phrase" options - we want the actual Facebook page
                                        if clean_row_text and "search this exact phrase" in clean_row_text.lower():
                                            self.logger.info(f"⏭️ SKIPPING 'Search this exact phrase' in row text after scroll: '{clean_row_text}'")
                                            continue

                                        if clean_row_text and self._matches_law_firm(clean_row_text, law_firm_name):
                                            target_row = row
                                            self.logger.info(f"🎯 FOUND FLEXIBLE MATCH in row text after scrolling: '{clean_row_text}'")
                                            break
                                except Exception as e:
                                    self.logger.debug(f"❌ Error processing new row after scroll: {e}")
                                    continue

                            # Update suggestion_rows list with new rows
                            suggestion_rows = new_suggestion_rows

                            if target_row:
                                break
                    else:
                        self.logger.info("❌ Could not find dropdown container for scrolling")

                except Exception as e:
                    self.logger.info(f"❌ Error during dropdown scrolling: {e}")

            # Log all found options for debugging
            self.logger.info(f"📊 All available suggestions found: {all_found_options}")

            # Click the target row or throw error
            if target_row:
                self.logger.info(f"👆 About to click target row for: {law_firm_name}")
                try:
                    # Clear any previous captures first
                    self.clear_captured_responses()

                    # Setup GraphQL interception RIGHT BEFORE clicking
                    await self._setup_graphql_interception()
                    self.logger.info("📡 GraphQL capture ready - clicking NOW")

                    # Click and immediately start monitoring
                    await target_row.click()
                    self.logger.info(f"✅ CLICKED advertiser: {law_firm_name} - monitoring responses...")

                    # Wait for GraphQL responses to stream in
                    capture_wait_time = self.config.get('camoufox', {}).get('search', {}).get('capture_wait', 5)
                    self.logger.info(f"⏳ Waiting {capture_wait_time}s for post-click GraphQL responses")
                    await asyncio.sleep(capture_wait_time)

                    # Process responses immediately after the wait
                    await self._process_captured_graphql_responses()

                    # Disable interception to avoid capturing navigation responses
                    await self._disable_graphql_interception()

                    # Stop capture
                    await self._disable_graphql_interception()

                    # Verify the click worked by checking if suggestions dropdown disappeared
                    try:
                        await self.page.wait_for_selector('div[role="listbox"]', state='hidden', timeout=5000)
                        self.logger.debug("✅ Suggestions dropdown closed after selection")
                    except:
                        self.logger.debug("⚠️ Suggestions dropdown may still be visible")

                    return True

                except Exception as e:
                    self.logger.error(f"❌ Failed to click target row: {e}")
                    await self._disable_graphql_interception()  # Cleanup on error
                    raise Exception(f"Failed to click advertiser '{law_firm_name}': {e}")
            else:
                self.logger.error(f"❌ Could not find advertiser '{law_firm_name}' in suggestions list")
                self.logger.error(f"📋 Available options: {all_found_options}")
                raise Exception(f"Advertiser '{law_firm_name}' not found in suggestions. Available: {all_found_options}")

        except Exception as e:
            self.logger.error(f"❌ Failed to select advertiser from suggestions: {str(e)}")
            return False

    async def _select_advertiser_with_capture(self, page_name: str):
        """Select advertiser from dropdown and capture GraphQL responses with improved interaction."""
        try:
            # Setup GraphQL interception RIGHT BEFORE clicking
            await self._setup_graphql_interception()
            self.logger.info("📡 Started GraphQL capture")

            # Use robust iteration method to select advertiser from suggestions
            # This triggers GraphQL responses when the advertiser is clicked
            selection_success = await self._select_advertiser_from_suggestions(page_name)
            if not selection_success:
                raise Exception(f"Failed to select advertiser '{page_name}' from suggestions")

            # Wait for GraphQL responses to stream in
            capture_wait_time = self.config.get('camoufox', {}).get('search', {}).get('capture_wait', 5)
            self.logger.info(f"⏳ Waiting {capture_wait_time}s for GraphQL responses")
            await asyncio.sleep(capture_wait_time)

            # Stop capture
            await self._disable_graphql_interception()
            self.logger.info(f"📊 Captured {len(self._graphql_responses)} GraphQL responses")
            return True

        except Exception as e:
            self.logger.error(f"Failed to select advertiser {page_name}: {str(e)}")
            await self._disable_graphql_interception()
            return False

    async def _get_page_name_from_id(self, page_id: str) -> str:
        """Get page name from page ID - lookup from database or config."""
        self.logger.info(f"🔍 Looking up page name for ID: {page_id}")

        # Try database lookup first if repository is available
        if self.law_firms_repository:
            try:
                # Query by ID field (page_id is stored as ID in law_firms table)
                # get_by_id returns a list of records
                results = await self.law_firms_repository.get_by_id(page_id)
                if results and isinstance(results, list) and len(results) > 0:
                    # Take the first result - law_firms_repository returns snake_case fields
                    result = results[0]
                    page_name = result.get('page_name')
                    if page_name:
                        # CRITICAL: Strip any quotes from the page name
                        # Database may have names stored with quotes which break Facebook search
                        page_name_clean = page_name.strip().strip('"').strip("'")
                        if page_name_clean != page_name:
                            self.logger.info(f"🧹 Cleaned page name: '{page_name}' -> '{page_name_clean}'")
                        self.logger.info(f"✅ Found page name in database: '{page_name_clean}' for ID: {page_id}")
                        return page_name_clean
                    else:
                        self.logger.warning(f"⚠️ Law firm found but no page_name for ID: {page_id}")
                        self.logger.debug(f"Record fields: {list(result.keys())}")

                        # Log to error file for manual update
                        error_log_path = Path(self.config.get('DATA_DIR', 'data')) / self.config.get('iso_date', '') / 'logs' / 'missing_page_names.log'
                        error_log_path.parent.mkdir(parents=True, exist_ok=True)

                        with open(error_log_path, 'a') as f:
                            f.write(f"{datetime.now().isoformat()} - Missing page_name for ID: {page_id}, Name: {result.get('name', 'Unknown')}, PageAlias: {result.get('page_alias', 'N/A')}\n")

                        self.logger.info(f"📝 Logged missing page_name to: {error_log_path}")
                else:
                    self.logger.warning(f"⚠️ No law firm found in database for ID: {page_id}")
            except Exception as e:
                self.logger.error(f"❌ Error querying law firms repository: {e}")
        else:
            self.logger.warning("⚠️ No law firms repository available for lookup")

        # Fallback to hardcoded mapping for known firms
        page_id_to_name = {
            "295549338384": "Wisner Baum",
            "168495366537322": "Morgan & Morgan",
            # Add more mappings as needed
        }

        if page_id in page_id_to_name:
            page_name = page_id_to_name[page_id]
            self.logger.info(f"📋 Using hardcoded mapping: '{page_name}' for ID: {page_id}")
            return page_name

        # No page name found - return None to trigger ID-based search in main workflow
        self.logger.warning(f"⚠️ No page name found for ID: {page_id}")
        return None

    async def _resolve_page_name_by_id_search(self, page_id: str) -> Optional[str]:
        """
        Resolve page name by searching Facebook ads library using the page ID.

        This implements the fallback behavior:
        1. Navigate to Facebook ads library
        2. Enter page_id in search box instead of page_name
        3. Navigate dropdown suggestions with down arrow
        4. Find matching pageID and extract the page_name

        Args:
            page_id: Facebook page ID to search for

        Returns:
            Resolved page name if found, None otherwise
        """
        try:
            self.logger.info(f"🔍 Attempting to resolve page name for ID: {page_id}")

            # Ensure we have a valid session
            if not self._is_session_valid or not self.page:
                self.logger.warning("No active session for ID search, creating new session...")
                if not await self.create_new_session():
                    return None

            # Navigate to Facebook ads library
            ads_library_url = "https://www.facebook.com/ads/library/"
            async with self._track_operation("navigate_to_ads_library"):
                await self.page.goto(ads_library_url, wait_until='networkidle', timeout=30000)
            await asyncio.sleep(2)  # Let page settle

            # Find and clear search box - be specific to avoid country/filter inputs
            search_input_selector = None
            selectors_to_try = [
                'input[placeholder*="Search ads"]',
                'input[placeholder*="Search by advertiser"]',
                'input[placeholder*="Enter advertiser name"]',
                '#content input[type="text"]:not([placeholder*="country"])',
                'div[role="main"] input[type="text"]',
                'input[type="search"]'
            ]

            for selector in selectors_to_try:
                try:
                    await self.page.wait_for_selector(selector, timeout=2000)
                    # Verify this is the right input by checking if it's not the country selector
                    is_country_input = await self.page.evaluate(f"""
                        () => {{
                            const input = document.querySelector('{selector}');
                            return input ? input.placeholder.toLowerCase().includes('country') : false;
                        }}
                    """)
                    if not is_country_input:
                        search_input_selector = selector
                        self.logger.debug(f"✅ Found main search input with selector: {selector}")
                        break
                except:
                    continue

            if not search_input_selector:
                self.logger.critical("❌ Could not find main search input box")
                return None

            # Clear any existing text
            await self.page.fill(search_input_selector, '')
            await asyncio.sleep(0.5)

            # Type the page ID
            self.logger.info(f"📝 Entering page ID: {page_id} into selector: {search_input_selector}")
            await self.page.fill(search_input_selector, page_id)
            await asyncio.sleep(1)  # Wait for suggestions to appear

            # Log what we typed to verify
            typed_value = await self.page.evaluate(f"""
                () => {{
                    const input = document.querySelector('{search_input_selector}');
                    return input ? input.value : null;
                }}
            """)
            self.logger.debug(f"📋 Verified input value: '{typed_value}'")

            # Wait for dropdown suggestions - look for advertiser suggestions specifically
            suggestions_visible = False
            suggestion_selectors = [
                '[role="listbox"]',
                '.uiContextualLayerBelowLeft',
                '[data-testid="search-suggestions"]',
                'ul[role="listbox"]',
                'div[aria-label*="suggestions"]'
            ]

            for selector in suggestion_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=3000)
                    suggestions_visible = True
                    self.logger.debug(f"✅ Found suggestions with selector: {selector}")
                    break
                except:
                    continue

            if not suggestions_visible:
                self.logger.warning("No search suggestions appeared")
                # Try clicking on the search box to trigger suggestions
                await self.page.click(search_input_selector)
                await asyncio.sleep(1)

                # Try one more time
                for selector in suggestion_selectors:
                    try:
                        await self.page.wait_for_selector(selector, timeout=2000)
                        suggestions_visible = True
                        break
                    except:
                        continue

                if not suggestions_visible:
                    return None

            # Navigate through suggestions using down arrow
            max_attempts = 20
            for i in range(max_attempts):
                # Press down arrow
                await self.page.keyboard.press('ArrowDown')
                await asyncio.sleep(0.5)

                # Extract current suggestion data
                suggestion_data = await self.page.evaluate("""
                    () => {
                        // Find the currently highlighted/selected suggestion
                        const selectors = [
                            '[aria-selected="true"]',
                            '.highlighted',
                            '.selected',
                            ':focus'
                        ];

                        let selectedItem = null;
                        for (const selector of selectors) {
                            selectedItem = document.querySelector(selector);
                            if (selectedItem) break;
                        }

                        // If no explicitly selected item, check active element
                        if (!selectedItem) {
                            selectedItem = document.activeElement;
                        }

                        if (selectedItem && selectedItem.textContent) {
                            const text = selectedItem.textContent.trim();

                            // Try to extract page ID from the element
                            let foundPageId = null;

                            // Check data attributes
                            foundPageId = selectedItem.getAttribute('data-page-id') ||
                                         selectedItem.getAttribute('data-id') ||
                                         selectedItem.getAttribute('value');

                            // Check for ID in text (Facebook IDs are typically 15+ digits)
                            if (!foundPageId && text) {
                                const idMatch = text.match(/\\b\\d{15,}\\b/);
                                if (idMatch) foundPageId = idMatch[0];
                            }

                            // Check href if it's a link
                            const link = selectedItem.querySelector('a') || (selectedItem.tagName === 'A' ? selectedItem : null);
                            if (link && !foundPageId) {
                                const href = link.href || '';
                                const idMatch = href.match(/(?:page_id|id)=(\\d+)/);
                                if (idMatch) foundPageId = idMatch[1];
                            }

                            return {
                                text: text,
                                pageId: foundPageId,
                                found: true
                            };
                        }

                        return { text: '', pageId: null, found: false };
                    }
                """)

                if suggestion_data['found'] and suggestion_data['pageId']:
                    self.logger.debug(f"Checking suggestion #{i+1}: text='{suggestion_data['text']}', pageId='{suggestion_data['pageId']}'")

                    # Check if this pageID matches our target
                    if suggestion_data['pageId'] == page_id:
                        # Extract the page name from the text
                        page_name = suggestion_data['text']

                        # Clean up the page name (remove ID if present)
                        page_name = page_name.replace(page_id, '').strip()
                        # Remove common separators
                        for sep in [' - ', ' | ', ' · ', '•']:
                            if sep in page_name:
                                parts = page_name.split(sep)
                                page_name = parts[0].strip()
                                break

                        if page_name:
                            self.logger.info(f"🎯 Found matching page name: '{page_name}' for ID: {page_id}")
                            return page_name

                # Check if we've reached the end
                if not suggestion_data['found']:
                    self.logger.debug(f"Reached end of suggestions after {i+1} attempts")
                    break

            self.logger.warning(f"⚠️ No matching page found for ID: {page_id} after checking {max_attempts} suggestions")
            return None

        except Exception as e:
            self.logger.error(f"❌ Error during ID-based page name resolution: {e}")
            return None

    async def _verify_advertiser_page(self, page_id: str) -> bool:
        """Verify that the page ID appears in the current URL."""
        try:
            await asyncio.sleep(2)  # Allow page to load
            current_url = self.page.url

            # Enhanced debugging - log the actual URL structure
            self.logger.info(f"🔍 URL Verification Debug:")
            self.logger.info(f"   Expected page ID: {page_id}")
            self.logger.info(f"   Current URL: {current_url}")

            # Check if page ID is in URL (original check)
            if page_id in current_url:
                self.logger.info(f"✅ Successfully navigated to page {page_id}")
                return True
            else:
                self.logger.warning(f"⚠️  Page ID {page_id} not found in URL, but selection may still be successful")

                # Alternative checks - Facebook might use different URL patterns
                if 'facebook.com/ads/library' in current_url and ('search_type=page' in current_url or 'advertiser_id=' in current_url):
                    self.logger.info(f"✅ Alternative verification: Ad Library page with advertiser filter detected")
                    return True

                # Log warning but don't fail - GraphQL capture might still work
                self.logger.warning(f"❌ Page ID {page_id} not found in URL: {current_url}")
                self.logger.info(f"🔄 Continuing with GraphQL capture attempt despite URL mismatch")
                return True  # Changed from False to True to continue with GraphQL capture

        except Exception as e:
            self.logger.error(f"Failed to verify advertiser page {page_id}: {str(e)}")
            return False

    async def _create_browser_and_page(self) -> bool:
        """
        Create browser instance and page without navigation.

        Returns:
            bool: True if browser creation was successful
        """
        browser_start_time = time.time()
        try:
            # Get proxy configuration
            proxy_config = None
            if self.proxy_manager:
                proxy = await self.proxy_manager.get_next_proxy()
                if proxy:
                    self.current_proxy = proxy  # Store the current proxy
                    # Don't URL-encode the password - Camoufox handles it internally
                    proxy_config = {
                        'server': f"{proxy.protocol}://{proxy.host}:{proxy.port}",
                        'username': proxy.username,
                        'password': proxy.password  # Use raw password
                    }
                    self.logger.info(f"🌐 Using proxy: {proxy.host}:{proxy.port}")
                    self.logger.debug(f"Proxy auth: username={proxy.username[:20]}... (truncated)")
                    self.logger.debug(f"Proxy protocol: {proxy.protocol}")

                    # Add proxy warmup delay to allow session initialization
                    proxy_warmup_delay = 0.3  # 300ms delay for proxy session to initialize
                    self.logger.debug(f"🕐 Proxy warmup delay: {proxy_warmup_delay}s")
                    await asyncio.sleep(proxy_warmup_delay)
                else:
                    self.current_proxy = None  # Clear current proxy if none available
                    self.logger.warning("⚠️ No proxy available from proxy manager")

            # Get fingerprint configuration
            fingerprint_config = {}
            if self.fingerprint_manager:
                fingerprint = await self.fingerprint_manager.get_next_fingerprint()
                if fingerprint:
                    fingerprint_config = fingerprint.get('config', {})
                    self.logger.debug("Applied fingerprint configuration")

            # Import Camoufox here to avoid import errors if not installed
            try:
                from camoufox.async_api import AsyncCamoufox
                from camoufox import DefaultAddons
            except ImportError:
                self.logger.error("Camoufox not installed. Install with: pip install camoufox")
                return False

            # Pre-browser-launch profile verification
            if self._profile_path:
                profile_path_obj = Path(self._profile_path)
                if profile_path_obj.exists():
                    # List any remaining files in the profile directory
                    remaining_files = list(profile_path_obj.rglob('*'))
                    if remaining_files:
                        self.logger.warning(f"⚠️ Profile directory not empty before browser launch!")
                        self.logger.warning(f"📁 Found {len(remaining_files)} files/dirs:")
                        for f in remaining_files[:10]:  # Show first 10
                            self.logger.warning(f"  - {f.relative_to(profile_path_obj)}")

                        # Force another cleanup attempt
                        force_clean = self.config.get('camoufox', {}).get('force_clean_profile', self.config.get('force_clean_profile', True))
                        if force_clean:
                            self.logger.warning(f"🔄 Forcing profile cleanup before browser launch...")
                            try:
                                shutil.rmtree(profile_path_obj)
                                time.sleep(0.5)
                                profile_path_obj.mkdir(parents=True, exist_ok=True)
                                self.logger.info(f"✅ Profile directory cleaned and recreated")
                            except Exception as e:
                                self.logger.error(f"❌ Failed to force clean profile: {e}")
                    else:
                        self.logger.info(f"✅ Profile directory is clean before browser launch")
                else:
                    self.logger.info(f"📁 Profile directory doesn't exist yet, will be created by browser")

            # Launch Camoufox browser with NO ADDONS AT ALL - most explicit approach
            browser_args = {
                'headless': self.headless,
                'humanize': self.humanize,
                'geoip': True,
                'addons': [],  # Empty addon list
                'exclude_addons': [DefaultAddons.UBO],  # MUST exclude default uBlock Origin
            }

            # Add Firefox preferences to prevent addon auto-installation
            browser_args['firefox_user_prefs'] = {
                'extensions.autoDisableScopes': 0,
                'extensions.enabledScopes': 0,
                'xpinstall.signatures.required': False,
                'extensions.blocklist.enabled': False,
            }

            # Add unique browser profile if available
            if self._profile_path:
                browser_args['profile'] = self._profile_path
                self.logger.info(f"🗂️ Using unique browser profile: {self._profile_path}")
            else:
                self.logger.debug("🗂️ No custom profile path set, using default profile")

            # Log ad blocker configuration for debugging
            self.logger.info("🚫 Camoufox configured: NO ADDONS + exclude_addons=[UBO] + Firefox prefs disabled")

            # Add proxy if available
            if proxy_config:
                # Log proxy details for debugging
                self.logger.info(f"🔐 Proxy config: server={proxy_config.get('server', 'N/A')}")
                self.logger.debug(f"Proxy username length: {len(proxy_config.get('username', ''))}")

                # Use proxy config directly - it's already in the correct format
                # The proxy manager provides the server with protocol included
                browser_args['proxy'] = proxy_config
                self.logger.debug(f"Using proxy: server={proxy_config.get('server', 'N/A')}, username={proxy_config.get('username', 'N/A')[:30]}...")

            # Launch browser using async API with retry logic
            self.logger.info("🔧 BROWSER CONFIGURATION VALIDATION:")
            self.logger.info(f"  ✓ Headless: {browser_args.get('headless', 'NOT SET')}")
            self.logger.info(f"  ✓ Humanize: {browser_args.get('humanize', 'NOT SET')}")
            self.logger.info(f"  ✓ Addons: {browser_args.get('addons', 'NOT SET')}")
            self.logger.info(f"  ✓ Exclude addons: {browser_args.get('exclude_addons', 'NOT SET')}")
            self.logger.info(f"  ✓ Firefox prefs: {list(browser_args.get('firefox_user_prefs', {}).keys())}")
            self.logger.info(f"  ✓ Profile path: {browser_args.get('profile', 'DEFAULT')}")
            self.logger.info(f"  ✓ Proxy configured: {'YES' if browser_args.get('proxy') else 'NO'}")

            # Validate critical anti-ad-blocker settings
            if 'exclude_addons' not in browser_args or DefaultAddons.UBO not in browser_args['exclude_addons']:
                self.logger.error("❌ CRITICAL: exclude_addons not properly configured!")
            if browser_args.get('addons') != []:
                self.logger.error("❌ CRITICAL: addons should be empty list!")

            self.logger.info(f"🔧 Full Camoufox browser args: {browser_args}")

            max_retries = 3
            base_retry_delay = 1.0  # Start with 1 second base delay

            for attempt in range(max_retries):
                try:
                    self.logger.info(f"🚀 Attempting to start Camoufox browser (attempt {attempt + 1}/{max_retries})")

                    # Use class-level lock to prevent concurrent browser starts
                    async with CamoufoxSessionManager._browser_start_lock:
                        self.logger.debug("🔒 Acquired browser start lock")

                        # Create browser instance using sync API with context manager
                        self.logger.debug("Creating AsyncCamoufox instance...")

                        # Create AsyncCamoufox instance
                        self.browser = AsyncCamoufox(**browser_args)
                        self.logger.info("✅ AsyncCamoufox instance created")

                        # Start the browser
                        await self.browser.start()
                        self.logger.info("✅ Browser started successfully")

                        # CRITICAL FIX: Wait for browser to be fully ready
                        browser_ready = False
                        max_ready_attempts = 10
                        for ready_attempt in range(max_ready_attempts):
                            try:
                                # Check if browser.browser property exists and is accessible
                                if hasattr(self.browser, 'browser') and self.browser.browser is not None:
                                    # Try to access browser contexts to ensure it's really ready
                                    _ = self.browser.browser.contexts
                                    browser_ready = True
                                    self.logger.info(f"✅ Browser internal state verified ready (attempt {ready_attempt + 1})")
                                    break
                            except Exception as e:
                                if ready_attempt < max_ready_attempts - 1:
                                    self.logger.debug(f"Browser not ready yet (attempt {ready_attempt + 1}): {e}")
                                    await asyncio.sleep(0.5)  # Wait 500ms before retry
                                else:
                                    raise Exception(f"Browser failed to become ready after {max_ready_attempts} attempts")

                        if not browser_ready:
                            raise Exception("Browser instance not properly initialized - internal browser object not accessible")

                        # Verify browser is actually running
                        if not self.browser:
                            raise Exception("Browser instance not properly initialized")

                        self.logger.debug("🔓 Released browser start lock")

                    # If we get here, browser started successfully
                    break

                except Exception as e:
                    self.logger.error(f"❌ Browser start failed (attempt {attempt + 1}): {str(e)}")

                    # Check if this is a proxy-related failure
                    error_str = str(e)
                    is_proxy_error = any(indicator in error_str.lower() for indicator in [
                        'failed to connect to proxy',
                        'proxy authentication required',
                        'traffic limit reached',
                        'access denied: traffic limit reached',
                        'unable to connect to proxy',
                        'invalidproxy',
                        'proxy error'
                    ])

                    # If it's a proxy error, record the failure and potentially trigger fallback
                    if is_proxy_error and self.proxy_manager:
                        current_proxy = self.proxy_manager.get_current_proxy()
                        if current_proxy:
                            self.logger.warning(f"🚨 Proxy failure detected: {error_str}")
                            self.proxy_manager.record_proxy_failure(current_proxy, error_msg=error_str)

                            # Check if proxy manager triggered fallback
                            if hasattr(self.proxy_manager, 'fallback_attempts') and self.proxy_manager.fallback_attempts > 0:
                                self.logger.info(f"🔄 Proxy fallback triggered, retrying with {self.proxy_manager.current_proxy_type} proxy")
                                # Update browser args with new proxy
                                browser_args = self._get_browser_args()

                    # Clean up any partial browser instance
                    if self.browser_cm:
                        try:
                            # Exit context manager if it exists
                            await self.browser_cm.__exit__(None, None, None)
                        except:
                            pass
                        self.browser_cm = None
                        self.browser = None

                    # If this was the last attempt, re-raise the error
                    if attempt == max_retries - 1:
                        raise Exception(f"Failed to start browser after {max_retries} attempts: {str(e)}")

                    # Exponential backoff: 1s, 2s, 4s
                    retry_delay = base_retry_delay * (2 ** attempt)
                    self.logger.info(f"⏳ Exponential backoff: waiting {retry_delay}s before retry...")
                    await asyncio.sleep(retry_delay)

            if not self.browser:
                self.logger.error("Failed to launch Camoufox browser after all retries")
                return False

            # Log comprehensive anti-ad-blocker measures
            self.logger.info("🛡️ ANTI-AD-BLOCKER MEASURES APPLIED:")
            self.logger.info("  ✓ exclude_addons=[DefaultAddons.UBO] - Prevents uBlock Origin")
            self.logger.info("  ✓ addons=[] - No addons loaded")
            self.logger.info("  ✓ Firefox prefs disabled addon auto-installation")
            self.logger.info("  ✓ Profile cleared to remove cached addon data")
            self.logger.info("  ✓ JavaScript explicitly enabled")
            self.logger.info("  ✓ Cookies cleared on context creation")

            # Create browser context with English language configuration
            context_args = {
                'locale': 'en-US',
                'java_script_enabled': True,  # Explicitly enable JavaScript
                'bypass_csp': False,  # Don't bypass content security policy
                'extra_http_headers': {
                    'Accept-Language': 'en-US,en;q=0.9'
                }
            }

            # Add viewport configuration
            context_args['viewport'] = self.viewport

            # CRITICAL FIX: Add retry logic for context creation
            context_created = False
            max_context_retries = 3
            for context_attempt in range(max_context_retries):
                try:
                    self.context = await self.browser.browser.new_context(**context_args)
                    context_created = True
                    self.logger.info(f"✅ Browser context created successfully (attempt {context_attempt + 1})")
                    break
                except Exception as e:
                    if "Target closed" in str(e) or "Target page, context or browser has been closed" in str(e):
                        self.logger.warning(f"⚠️ TargetClosedError during context creation (attempt {context_attempt + 1}): {e}")
                        if context_attempt < max_context_retries - 1:
                            await asyncio.sleep(1)  # Wait before retry
                            # Re-verify browser is still valid
                            try:
                                _ = self.browser.browser.contexts
                            except:
                                self.logger.error("❌ Browser became invalid, need full restart")
                                return False
                        else:
                            raise
                    else:
                        raise

            if not context_created:
                self.logger.error("Failed to create browser context after retries")
                return False

            # Clear all browser storage to ensure clean state
            try:
                await self.context.clear_cookies()
                self.logger.info("🍪 Cleared all cookies from browser context")
            except Exception as e:
                self.logger.warning(f"Failed to clear cookies: {e}")

            # CRITICAL FIX: Add retry logic for page creation
            page_created = False
            max_page_retries = 3
            for page_attempt in range(max_page_retries):
                try:
                    self.page = await self.context.new_page()
                    page_created = True
                    self.logger.info(f"✅ Browser page created successfully (attempt {page_attempt + 1})")
                    break
                except Exception as e:
                    if "Target closed" in str(e) or "Target page, context or browser has been closed" in str(e):
                        self.logger.warning(f"⚠️ TargetClosedError during page creation (attempt {page_attempt + 1}): {e}")
                        if page_attempt < max_page_retries - 1:
                            await asyncio.sleep(1)  # Wait before retry
                            # Re-verify context is still valid
                            try:
                                # Try to perform a simple operation on the context
                                _ = await self.context.cookies()
                            except:
                                self.logger.error("❌ Browser context became invalid, need full restart")
                                return False
                        else:
                            raise
                    else:
                        raise

            if not page_created:
                self.logger.error("Failed to create browser page after retries")
                return False

                # Image cache extraction will be setup later when advertiser page loads
                # This prevents caching images from the wrong pages before advertiser selection
                self.logger.info("🖼️ Image cache extraction will be setup AFTER advertiser page loads to cache the right images")
                self.logger.exception("Full exception details:")
                # Continue without image cache - don't fail the entire page creation
            else:
                self.logger.warning("🔍 DEBUG: image_cache_extractor is falsy - skipping setup")

            # Pre-navigation addon check
            self.logger.info("🔍 Running pre-navigation addon check...")
            try:
                addon_check_result = await self.page.evaluate("""
                    () => {
                        // Check for common addon indicators
                        const checks = {
                            hasUBlockOrigin: typeof window.uBlock !== 'undefined',
                            hasAdBlockPlus: typeof window.adblockplus !== 'undefined',
                            hasAdGuard: typeof window.AG !== 'undefined',
                            hasGhostery: typeof window.ghostery !== 'undefined',
                            hasPrivacyBadger: typeof window.privacyBadgerSurrogate !== 'undefined',
                            // Check for browser addon APIs
                            hasBrowserAddons: typeof browser !== 'undefined' && browser.runtime,
                            hasWebExtensions: typeof chrome !== 'undefined' && chrome.runtime,
                            // Check for addon injected elements
                            hasAddonElements: document.querySelector('[id*="ublock"], [id*="adblock"], [class*="adblock"]') !== null,
                            // Check navigator properties
                            navigatorPlugins: navigator.plugins.length,
                            navigatorMimeTypes: navigator.mimeTypes.length
                        };

                        return {
                            checks: checks,
                            hasAnyAddon: Object.values(checks).some(v => v === true),
                            summary: Object.entries(checks).filter(([k, v]) => v === true).map(([k]) => k)
                        };
                    }
                """)

                self.logger.info(f"🔍 Addon check results: {addon_check_result}")

                if addon_check_result.get('hasAnyAddon'):
                    self.logger.error(f"❌ CRITICAL: Addons detected! {addon_check_result.get('summary')}")
                    self.logger.error("❌ Browser may have cached addon data. Force restart recommended.")
                else:
                    self.logger.info("✅ No addons detected - browser is clean")

            except Exception as e:
                self.logger.warning(f"⚠️ Could not perform addon check: {e}")

            # Inject modal auto-click script immediately after page creation
            self.logger.info("🎯 Injecting early modal detection script")
            await self.page.add_init_script("""
                // Early modal detection and auto-click for ad blocker warnings
                const modalObserver = new MutationObserver((mutations) => {
                    for (const mutation of mutations) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === 1) { // Element node
                                // Check for dialog/modal elements
                                if (node.getAttribute && (node.getAttribute('role') === 'dialog' ||
                                    node.getAttribute('aria-modal') === 'true' ||
                                    node.classList && node.classList.contains('modal'))) {
                                    // Look for OK/Continue/Dismiss buttons
                                    const buttons = node.querySelectorAll('button');
                                    for (const button of buttons) {
                                        const text = button.textContent || button.innerText || '';
                                        if (text.match(/OK|Continue|Accept|Dismiss|Got it|Close/i)) {
                                            console.log('[EARLY MODAL] Found button, auto-clicking:', text);
                                            setTimeout(() => button.click(), 100);
                                            break;
                                        }
                                    }
                                    // Also check for ad blocker specific text
                                    const textContent = node.textContent || '';
                                    if (textContent.toLowerCase().includes('ad blocker')) {
                                        console.log('[EARLY MODAL] Detected ad blocker modal');
                                        // Try to find any clickable button
                                        const anyButton = node.querySelector('button');
                                        if (anyButton) {
                                            console.log('[EARLY MODAL] Clicking first available button');
                                            setTimeout(() => anyButton.click(), 100);
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

                // Start observing immediately
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        modalObserver.observe(document.body, { childList: true, subtree: true });
                        console.log('[EARLY MODAL] Observer activated on DOMContentLoaded');
                    });
                } else {
                    modalObserver.observe(document.body, { childList: true, subtree: true });
                    console.log('[EARLY MODAL] Observer activated immediately');
                }
            """)

            # Add dialog handler to automatically dismiss any modals/dialogs
            async def handle_dialog(dialog):
                self.logger.info("="*60)
                self.logger.info(f"🚨 DIALOG/MODAL DETECTED! 🚨")
                self.logger.info(f"📍 Type: {dialog.type}")
                self.logger.info(f"📝 Message: {dialog.message}")
                self.logger.info(f"🔘 Default value: {dialog.default_value}")

                # Accept/dismiss the dialog
                await dialog.accept()
                self.logger.info("✅ DIALOG DISMISSED SUCCESSFULLY! ✅")
                self.logger.info("="*60)

            # Register the dialog handler
            # Note: Dialog handling works differently with sync API
            # For now, dialog handling is disabled with sync API
            self.logger.warning("⚠️ Dialog auto-dismissal temporarily disabled with sync API")
            self.logger.info("📡 Dialog handler registered - will auto-dismiss any popups")

            self.logger.info("🌐 Browser context configured with English language (en-US)")

            # Additional ad blocker evasion: Remove any ad block detection flags
            if self.disable_ad_blocker_detection:
                self.logger.debug("Applying enhanced ad blocker detection evasion scripts")
                await self.page.add_init_script("""
                    // Override common ad blocker detection methods
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    // Hide ad block detection scripts
                    delete window.chrome;
                    delete window.navigator.webdriver;

                    // Override common ad blocker detection properties
                    Object.defineProperty(window, 'outerHeight', {
                        get: () => window.innerHeight,
                    });
                    Object.defineProperty(window, 'outerWidth', {
                        get: () => window.innerWidth,
                    });

                    // Intercept and auto-dismiss Facebook modals
                    const originalAddEventListener = EventTarget.prototype.addEventListener;
                    EventTarget.prototype.addEventListener = function(type, listener, options) {
                        // Skip modal/dialog related event listeners
                        if (type === 'click' && this.tagName === 'BUTTON') {
                            const buttonText = this.textContent || this.innerText || '';
                            if (buttonText.match(/OK|Continue|Accept|Dismiss|Got it/i)) {
                                console.log('Auto-clicking modal button:', buttonText);
                                setTimeout(() => this.click(), 100);
                            }
                        }
                        return originalAddEventListener.call(this, type, listener, options);
                    };

                    // Override fetch to prevent ad blocker detection calls
                    const originalFetch = window.fetch;
                    window.fetch = function(...args) {
                        const url = args[0];
                        if (typeof url === 'string' && url.includes('adblock')) {
                            console.log('Blocked ad blocker detection request:', url);
                            return Promise.resolve(new Response('{}', { status: 200 }));
                        }
                        return originalFetch.apply(this, args);
                    };

                    // Monitor for modal elements and auto-click OK buttons
                    const observer = new MutationObserver((mutations) => {
                        for (const mutation of mutations) {
                            for (const node of mutation.addedNodes) {
                                if (node.nodeType === 1) { // Element node
                                    // Check for dialog/modal elements
                                    if (node.getAttribute && (node.getAttribute('role') === 'dialog' ||
                                        node.getAttribute('aria-modal') === 'true')) {
                                        // Look for OK/Continue buttons
                                        const buttons = node.querySelectorAll('button');
                                        for (const button of buttons) {
                                            const text = button.textContent || button.innerText || '';
                                            if (text.match(/OK|Continue|Accept|Dismiss|Got it/i)) {
                                                console.log('Found modal button, auto-clicking:', text);
                                                setTimeout(() => button.click(), 500);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });

                    // Start observing when DOM is ready
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', () => {
                            observer.observe(document.body, { childList: true, subtree: true });
                        });
                    } else {
                        observer.observe(document.body, { childList: true, subtree: true });
                    }
                """)
            # Configure page for Facebook scraping
            await self._configure_page()

            # Log timing metrics
            browser_creation_duration = time.time() - browser_start_time
            self.logger.info(f"✅ Browser and page created successfully in {browser_creation_duration:.2f}s")
            if self.current_proxy:
                self.logger.info(f"📊 Proxy used: {self.current_proxy.host}:{self.current_proxy.port} (session: {self.current_proxy.session_id})")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create browser and page: {str(e)}")
            self.logger.error(f"Error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    async def _navigate_to_ads_library_home(self) -> bool:
        """
        Navigate to the Facebook ads library home page (simplified - just for session setup).

        Returns:
            bool: True if navigation was successful
        """
        max_nav_retries = 3
        for attempt in range(max_nav_retries):
            try:
                if not self.page:
                    self.logger.error("No page instance available for navigation")
                    return False

                self.logger.info(f"🔄 Attempting to navigate to Facebook ads library (attempt {attempt + 1}/{max_nav_retries})")

                # Try with shorter timeout first, then longer
                nav_timeout = 30000 if attempt == 0 else 60000

                # Navigate to Facebook ads library for session setup
                async with self._track_operation("navigate_session_setup"):
                    await self.page.goto(
                        'https://www.facebook.com/ads/library/',
                        wait_until='domcontentloaded',
                    timeout=nav_timeout
                )

                # Wait for page to load and extract tokens
                await self.page.wait_for_load_state('networkidle', timeout=10000)
                await asyncio.sleep(2)  # Additional wait for JS execution


                self.logger.info("✅ Successfully navigated to Facebook ads library home")
                return True

            except Exception as e:
                self.logger.error(f"Failed to navigate to ads library home (attempt {attempt + 1}/{max_nav_retries}): {str(e)}")

                # Check if it's a TargetClosedError
                if "Target closed" in str(e) or "Target page, context or browser has been closed" in str(e):
                    self.logger.error("❌ Browser context was closed unexpectedly - will attempt to recreate")
                    await self.cleanup()
                    if not await self._create_browser_and_page():
                        self.logger.error("Failed to recreate browser after TargetClosedError")
                        return False
                    # Continue with next attempt
                    await asyncio.sleep(2)
                    continue

                if attempt < max_nav_retries - 1:
                    # Check if it's a proxy issue
                    if "Timeout" in str(e) and self.proxy_manager:
                        self.logger.warning("🔄 Navigation timeout - possibly proxy-related. Rotating proxy...")
                        self.proxy_manager.record_proxy_failure(self.current_proxy)
                        # Try to get a new browser with rotated proxy
                        await self.cleanup()
                        if not await self._create_browser_and_page():
                            self.logger.error("Failed to recreate browser with new proxy")
                            return False
                    else:
                        # Just wait before retry
                        retry_delay = 2 ** attempt  # Exponential backoff
                        self.logger.info(f"⏳ Waiting {retry_delay}s before retry...")
                        await asyncio.sleep(retry_delay)
                else:
                    # Final attempt failed
                    return False

        return False

    async def navigate_to_advertiser_page(self, page_id: str) -> bool:
        """
        DEPRECATED: Navigate to a specific advertiser page using direct URL.

        This method is deprecated in favor of the new page name search flow.
        Use capture_graphql_responses() instead, which implements the proper
        Facebook Ad Library search flow with Google referrer and dropdown selection.

        Args:
            page_id: Facebook page ID to navigate to

        Returns:
            bool: True if navigation was successful
        """
        self.logger.warning("⚠️ DEPRECATED: navigate_to_advertiser_page() is deprecated. Use capture_graphql_responses() instead.")

        try:
            if not self._is_session_valid or not self.page:
                self.logger.error("No valid session available for navigation")
                return False

            # Construct the ad library URL for the specific advertiser
            ad_library_url = f"https://www.facebook.com/ads/library/?active_status=all&ad_type=political_and_issue_ads&country=US&media_type=all&search_type=page&view_all_page_id={page_id}"

            self.logger.info(f"🔄 Navigating to advertiser page: {page_id}")

            # Navigate to the advertiser page using the existing browser/page
            async with self._track_operation("navigate_advertiser_page"):
                await self.page.goto(ad_library_url, wait_until='domcontentloaded', timeout=self.browser_timeout)

            # Wait for page to load completely
            await self.page.wait_for_load_state('networkidle', timeout=10000)

            # Update last activity time
            self.last_activity_time = time.time()

            self.logger.info(f"✅ Successfully navigated to advertiser page: {page_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to navigate to advertiser page {page_id}: {str(e)}")
            return False

    async def download_image(self, image_url: str, timeout: int = 30) -> Optional[bytes]:
        """
        Download an image using Playwright browser context or fallback to HTTP.

        This method provides compatibility for ImageHandler which expects
        to download images through the session manager. It will use the browser
        if available, but falls back to regular HTTP requests for CDN images.

        Args:
            image_url: URL of the image to download
            timeout: Download timeout in seconds

        Returns:
            bytes: Image content as bytes if successful, None otherwise
        """
        # Check if this is a Facebook CDN image that doesn't need authentication
        is_cdn_image = any(cdn in image_url for cdn in ['fbcdn.net', 'facebook.com/tr', 'fbsbx.com'])

        # If browser is available and it's not a CDN image, use Playwright
        if self._is_session_valid and self.page and self.browser and not is_cdn_image:
            try:
                self.logger.debug(f"🖼️ Downloading image via Playwright: {image_url}")

                # Create a new page for isolated download to avoid affecting main page state
                # Create a new page using sync API for isolated download
                download_page = await self.browser.browser.new_page()

                try:
                    # Navigate to the image URL
                    response = await download_page.goto(
                        image_url,
                        wait_until='load',
                        timeout=timeout * 1000  # Convert to milliseconds
                    )

                    if response is None:
                        self.logger.error(f"No response received for image URL: {image_url}")
                        return None

                    # Check response status
                    if response.status != 200:
                        self.logger.warning(f"Image download returned status {response.status} for URL: {image_url}")

                        # Check for blocks
                        if response.status in [403, 429, 503]:
                            self.logger.warning(f"Blocked/Rate limited ({response.status}) downloading image: {image_url}")
                            # Fall back to HTTP request below
                        else:
                            return None
                    else:
                        # Success - get the image content
                        image_content = await response.body()

                        if image_content:
                            self.logger.debug(f"✅ Successfully downloaded {len(image_content)} bytes via Playwright")
                            return image_content

                finally:
                    # Always close the download page
                    await download_page.close()

            except Exception as e:
                self.logger.warning(f"Playwright download failed, falling back to HTTP: {str(e)}")

        # Fallback to regular HTTP request (for CDN images or when browser unavailable)
        return await self._download_image_http_with_retry(image_url, timeout)

    async def download_image_from_cache(self, image_url: str, timeout: int = 30) -> Optional[bytes]:
        """
        Download image with cache-first approach.

        This method provides the optimal bandwidth usage by:
        1. First checking browser image cache for the image
        2. If cache miss, falling back to existing download_image method
        3. Logging bandwidth savings for cache hits

        Args:
            image_url: URL of the image to download
            timeout: Download timeout in seconds

        Returns:
            bytes: Image content as bytes if successful, None otherwise
        """
        # Try cache first if image cache is enabled
        if self.image_cache_extractor:
            try:
                cached_image = await self.image_cache_extractor.get_cached_image(image_url)
                if cached_image:
                    self.logger.info(f"📦 CACHE HIT: Using cached image {len(cached_image.content)} bytes from {image_url}")

                    # Note: Cache hits cost ZERO bandwidth - the image was already downloaded
                    # Bandwidth was logged when it was first intercepted by the cache extractor

                    return cached_image.content
                else:
                    self.logger.debug(f"🌐 CACHE MISS: Image not in cache, downloading from network: {image_url}")
                    # Note: Bandwidth will be logged when the actual download happens

            except Exception as e:
                self.logger.warning(f"Cache lookup failed for {image_url}, falling back to network download: {e}")

        # Fallback to existing download_image method
        return await self.download_image(image_url, timeout)

    async def setup_with_image_cache_extraction(self) -> None:
        """
        Setup image cache extraction on the current page.

        This should be called after the page is created and before navigation
        to ensure images are captured during the browsing session.
        """
        if not self.image_cache_extractor:
            self.logger.info("🚫 Image cache extraction not enabled")
            return

        if not self.page:
            self.logger.error("❌ Cannot setup image extraction: page is None")
            return

        try:
            await self.image_cache_extractor.setup_image_interception(self.page)
            self.logger.info("🎯 Image cache extraction setup complete")
        except Exception as e:
            self.logger.error(f"Failed to setup image cache extraction: {e}")
            # Don't raise exception - continue without cache extraction

    def get_image_cache_stats(self) -> Dict[str, Any]:
        """
        Get image cache statistics.

        Returns:
            Dictionary containing cache performance metrics
        """
        if self.image_cache_extractor:
            return self.image_cache_extractor.get_cache_stats()
        return {
            'cache_enabled': False,
            'cached_images': 0,
            'cache_size_mb': 0.0,
            'hit_rate_percent': 0.0
        }

    @retry_async(
        max_attempts=3,
        delay=1.0,
        backoff=2.0,
        exceptions=(ClientError, asyncio.TimeoutError),  # Covers ClientConnectionResetError
        logger=None  # Will use self.logger
    )
    async def _download_image_http_with_retry(self, image_url: str, timeout: int = 30) -> Optional[bytes]:
        """
        Download image via HTTP with retry logic for connection errors.

        This method is decorated with retry logic to handle transient connection errors
        like ClientConnectionResetError. It uses exponential backoff to avoid overwhelming
        the server.

        Args:
            image_url: URL of the image to download
            timeout: Download timeout in seconds

        Returns:
            bytes: Image content if successful, None otherwise
        """
        self.logger.debug(f"📥 Downloading image via HTTP request: {image_url}")

        # Set up headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }

        # Get proxy settings if available
        proxy = None
        if self.proxy_manager and self.current_proxy:
            proxy = self.current_proxy.to_url()  # Use the to_url method with proper encoding

        # Configure connection with pooling limits
        timeout_config = aiohttp.ClientTimeout(
            total=timeout,
            connect=10,  # Connection timeout
            sock_connect=10,  # Socket connection timeout
            sock_read=timeout - 10  # Read timeout (leaving buffer for connection)
        )

        # Create connector with connection pooling limits
        connector = aiohttp.TCPConnector(
            ssl=False,  # Disable SSL verification for CDN images
            limit=30,  # Connection pool limit per host
            limit_per_host=10,  # Limit connections per host
            force_close=False,  # Keep connections alive for reuse
            enable_cleanup_closed=True  # Clean up closed connections
        )

        try:
            async with aiohttp.ClientSession(
                headers=headers,
                timeout=timeout_config,
                connector=connector,
                connector_owner=True  # Session owns the connector
            ) as session:
                async with session.get(image_url, proxy=proxy) as response:
                    # Check if this is a permanent failure (don't retry these)
                    if response.status in [404, 410]:  # Not Found, Gone
                        self.logger.warning(f"Image not found (status {response.status}): {image_url}")
                        return None

                    # Check for blocks/rate limits (these should be retried)
                    if response.status in [403, 429, 503]:
                        self.logger.warning(f"Rate limited/blocked (status {response.status}) for image: {image_url}")
                        # Raise to trigger retry
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status
                        )

                    # Success
                    if response.status == 200:
                        content = await response.read()
                        self.logger.debug(f"✅ Successfully downloaded {len(content)} bytes via HTTP")
                        return content
                    else:
                        self.logger.warning(f"HTTP download failed with status {response.status}")
                        return None

        except ClientConnectionResetError as e:
            # This error will be caught by the retry decorator
            self.logger.warning(f"Connection reset during image download: {str(e)}")
            raise  # Re-raise to trigger retry
        except asyncio.TimeoutError as e:
            # This error will also be caught by the retry decorator
            self.logger.warning(f"Timeout during image download: {str(e)}")
            raise  # Re-raise to trigger retry
        except ClientError as e:
            # Other client errors will be retried
            self.logger.warning(f"Client error during image download: {type(e).__name__}: {str(e)}")
            raise  # Re-raise to trigger retry
        except Exception as e:
            # Non-client errors won't be retried
            self.logger.error(f"Unexpected error during image download: {type(e).__name__}: {str(e)}")
            return None

    def get_session(self):
        """
        Compatibility method for ImageHandler.

        Returns a session-like object that ImageHandler can use.
        Since Camoufox uses Playwright instead of requests, we return
        self as a proxy object.
        """
        if not self._is_session_valid:
            self.logger.warning("get_session() called but browser session is not valid")
            return None
        return self

    def get_current_proxy_settings(self) -> Optional[Dict[str, str]]:
        """
        Get current proxy settings in requests-compatible format.

        Returns proxy settings dict for compatibility with legacy code.

        Returns:
            Dict with 'http' and 'https' keys if proxy is active, None otherwise
        """
        if not self.current_proxy:
            return None

        try:
            # Build proxy URL in requests format with proper URL encoding
            proxy_url = self.current_proxy.to_url()  # Use the to_url method with proper encoding

            return {
                'http': proxy_url,
                'https': proxy_url
            }
        except Exception as e:
            self.logger.error(f"Error building proxy settings: {e}")
            return None

    def handle_block_or_rate_limit(self, reason: str = "block/rate limit"):
        """
        Handle block or rate limit detection.

        This is a compatibility method for ImageHandler. In Camoufox,
        we may want to refresh the session or rotate proxy.

        Args:
            reason: Description of why this was triggered
        """
        self.logger.warning(f"🚫 {reason} detected in Camoufox session")

        # Record the block
        if hasattr(self, 'block_count'):
            self.block_count += 1
        else:
            self.block_count = 1

        # If we have proxy rotation capability, trigger it
        if self.proxy_manager and self.current_proxy:
            self.logger.info("🔄 Attempting to rotate proxy due to block/rate limit")
            # Note: Proxy rotation would require recreating the browser with new proxy
            # For now, just log the event

        # Consider refreshing the session
        if self.block_count >= 3:
            self.logger.warning("⚠️ Multiple blocks detected, consider refreshing session")
            self._is_session_valid = False
