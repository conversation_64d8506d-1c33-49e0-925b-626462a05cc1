"""
Facebook Ads Container Module

Provides Facebook Ads service providers.
"""

from dependency_injector import containers, providers

# Import AI services and clients for FB ads self-contained container
from src.infrastructure.external.deepseek_client import DeepSeekClient
from src.infrastructure.external.openai_client import OpenAIClient
from src.services.ai.ai_orchestrator import AIOrchestrator
from src.services.ai.deepseek_service import DeepSeekService
from src.services.ai.prompt_manager import PromptManager

# Import FB Ads services
from src.services.fb_ads.ad_db_service import AdDBService
from src.services.fb_ads.ad_ner_processor import AdNerProcessor
from src.services.fb_ads.ad_processing_service import AdProcessingService
# from src.services.fb_ads.api_client import FacebookAPIClient  # Removed - legacy implementation
from src.services.fb_ads.bandwidth_logger import BandwidthLogger
from src.services.fb_ads.browser_image_cache_extractor import BrowserImageCacheExtractor
from src.services.fb_ads.categorizer import FBAdCategorizer
from src.services.fb_ads.classifier import LegalAdAnalyzer
from src.services.fb_ads.concurrent_workflow_service import ConcurrentWorkflowService
from src.services.fb_ads.data_validation_service import DataValidationService
from src.services.fb_ads.disk_cache import DiskCache
from src.services.fb_ads.error_handling_service import ErrorHandlingService
from src.services.fb_ads.image_handler import ImageHandler
from src.services.fb_ads.image_utils import FBImageHashService
from src.services.fb_ads.interactive_service import InteractiveService
from src.services.fb_ads.local_image_queue import LocalImageQueue
from src.services.fb_ads.ner_rule_analyzer import NerRuleAnalyzer
from src.services.fb_ads.orchestrator import FacebookAdsOrchestrator
from src.services.fb_ads.processing_tracker import ProcessingTracker
# from src.services.fb_ads.processor import AdProcessor  # DEPRECATED
# from src.services.fb_ads.session_manager import FacebookSessionManager  # DEPRECATED - Camoufox only
from src.services.fb_ads.workflow_service import WorkflowService
from src.services.fb_ads.jobs.job_orchestration_service import JobOrchestrationService
from src.services.fb_ads.jobs.job_runner_service import JobRunnerService
from src.services.fb_ads.failed_firms_manager import FailedFirmsManager
from src.services.fb_ads.skip_term_service import SkipTermService
from src.services.fb_ads.helpers.dropdown_search_helper import DropdownSearchHelper

# Import factory pattern components for Camoufox integration
from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
from src.services.fb_ads.factories.api_client_factory import APIClientFactory
from src.services.fb_ads.graphql_response_parser import GraphQLResponseParser
from src.utils.pdf_utils import PDFExtractor
from src.services.fb_archive.data_conversion_service import (
    FBArchiveDataConversionService,
)
from src.services.fb_archive.delete_service import FBArchiveDeleteService

# Import query services
from src.services.fb_archive.query_service import FBArchiveQueryService
from src.services.fb_archive.update_service import FBArchiveUpdateService


def _create_fingerprint_manager(config, logger):
    """Create fingerprint manager with conditional import."""
    try:
        from src.infrastructure.browser import FingerprintManager
        return FingerprintManager(config=config, logger=logger)
    except ImportError as e:
        logger.warning(f"FingerprintManager not available: {e}")
        return None


def _create_proxy_manager(config, logger):
    """Create proxy manager with conditional import."""
    try:
        from src.infrastructure.browser import ProxyManager
        return ProxyManager(config=config, logger=logger, proxy_provider=None)
    except ImportError as e:
        logger.warning(f"ProxyManager not available: {e}")
        return None


class FbAdsContainer(containers.DeclarativeContainer):
    """Container for Facebook Ads services."""

    # Configuration and dependencies
    config = providers.Configuration()
    logger = providers.Dependency()
    storage_container = providers.DependenciesContainer()
    http_session = providers.Dependency()

    # AI services - FB ads container is self-contained!
    deepseek_client = providers.Singleton(
        DeepSeekClient,
        api_key=config.deepseek_api_key,
        config=config,
    )

    openai_client = providers.Singleton(
        OpenAIClient,
        api_key=config.openai_api_key,
    )

    prompt_manager = providers.Singleton(PromptManager, logger=logger, config=config)

    deepseek_service = providers.Singleton(
        DeepSeekService,
        logger=logger,
        config=config,
        client=deepseek_client,
        prompt_manager=prompt_manager,
    )

    # Camoufox dependencies (optional, created only if needed)
    fingerprint_manager = providers.Factory(
        lambda config, logger: _create_fingerprint_manager(config, logger),
        config=config,
        logger=logger
    )

    proxy_manager = providers.Factory(
        lambda config, logger: _create_proxy_manager(config, logger),
        config=config,
        logger=logger
    )

    # Session management using factory pattern - runtime creation per firm
    # SessionManagerFactory is a static factory, so we provide the class itself
    session_manager_factory = providers.Object(SessionManagerFactory)

    # Cache services - MOVED UP TO DEFINE BEFORE USE
    disk_cache = providers.Singleton(DiskCache, logger=logger, config=config)

    # MULTIPROCESSING CACHE SUPPORT: Create shared cache manager
    # This must be initialized at the application entry point before creating workers
    shared_cache_dict = providers.Configuration().shared_cache_dict

    # UNIFIED CACHE SOLUTION: Single shared BrowserImageCacheExtractor instance
    # This eliminates multiple cache instantiations and enables cache sharing
    # CRITICAL FIX: Disk persistence DISABLED to prevent cross-advertiser cache contamination
    browser_image_cache_extractor = providers.Singleton(
        BrowserImageCacheExtractor,
        logger=logger,
        config=config,
        max_cache_size_mb=500,  # Default 500MB
        cache_ttl_minutes=30,   # REDUCED: 30 minutes TTL for per-advertiser sessions
        bandwidth_logger=None,  # Will be set later with advertiser bandwidth logger
        enable_disk_persistence=False,  # DISABLED: Prevents cross-advertiser contamination
        shared_cache_dict=shared_cache_dict  # Process-safe shared cache
    )

    # Session manager created using factory pattern
    # This creates a real session manager instead of None placeholder
    # NOW WITH UNIFIED CACHE INJECTION
    session_manager = providers.Factory(
        SessionManagerFactory.create,
        config=config,
        logger=logger,
        firm_id=None,  # Default firm_id, can be overridden per-job
        fingerprint_manager=fingerprint_manager,
        proxy_manager=proxy_manager,
        law_firms_repository=storage_container.law_firms_repository,
        browser_image_cache_extractor=browser_image_cache_extractor,  # UNIFIED CACHE INJECTION
    )

    # API client with dummy session manager - won't create browser
    api_client = providers.Factory(
        APIClientFactory.create,
        session_manager=session_manager,
        config=config,
        logger=logger
    )

    # Cache services moved up above session_manager

    # Bandwidth logging
    bandwidth_logger = providers.Singleton(BandwidthLogger, logger=logger, config=config)

    # Dropdown search helper for interactive workflows
    dropdown_search_helper = providers.Singleton(
        DropdownSearchHelper,
        config=config,
        logger=logger
    )

    # Image services
    image_hash_service = providers.Singleton(
        FBImageHashService,
        logger=logger,
        repository=storage_container.fb_image_hash_repository,
    )

    local_image_queue = providers.Singleton(
        LocalImageQueue, logger=logger, config=config, data_dir=config.DATA_DIR
    )

    image_handler = providers.Singleton(
        ImageHandler,
        logger=logger,
        config=config,
        s3_manager=storage_container.s3_async_storage,
        session_manager=session_manager,  # Uses dummy session manager
        hash_manager=image_hash_service,
        bandwidth_logger=bandwidth_logger,
        browser_image_cache_extractor=browser_image_cache_extractor,  # UNIFIED CACHE INJECTION
    )

    # Processing services
    processing_tracker = providers.Singleton(
        ProcessingTracker,
        file_path=config.DATA_DIR,
        config=config,
        logger=logger
    )

    error_handling_service = providers.Singleton(
        ErrorHandlingService, logger=logger, config=config, processing_tracker=processing_tracker
    )

    data_validation_service = providers.Singleton(
        DataValidationService,
        logger=logger,
        config=config,
        processing_tracker=processing_tracker
    )

    # NER and classification services
    ner_rule_analyzer = providers.Singleton(
        NerRuleAnalyzer,
        config=config,
        session=http_session,
        ner_model_name=config.ner_model_name,
        spacy_pipe_batch_size=config.spacy_pipe_batch_size,
        use_local_dynamodb=config.use_local_dynamodb,
        dynamodb_scan_workers=config.dynamodb_scan_workers,
        ner_processing_workers=config.ner_processing_workers
    )

    ad_ner_processor = providers.Singleton(
        AdNerProcessor,
        config=config,
        session=http_session,
        text_fields=config.text_fields,
        ner_model_name=config.ner_model_name,
        spacy_pipe_batch_size=config.spacy_pipe_batch_size,
        use_local_dynamodb=config.use_local_dynamodb,
        dynamodb_scan_workers=config.dynamodb_scan_workers,
        ner_processing_workers=config.ner_processing_workers,
        cluster_min_k=config.cluster_min_k,
        cluster_max_k=config.cluster_max_k,
        cluster_k_step=config.cluster_k_step,
        cluster_output_enabled=config.cluster_output_enabled
    )

    legal_ad_analyzer = providers.Singleton(
        LegalAdAnalyzer, logger=logger, config=config
    )

    ad_categorizer = providers.Singleton(
        FBAdCategorizer,
        config=config,
        session=http_session,
        deepseek_service=deepseek_service,
        use_local=config.use_local_dynamodb,
        scan_workers=config.dynamodb_scan_workers,
        update_workers=config.dynamodb_update_workers,
        fb_archive_repository=storage_container.fb_archive_repository,
    )

    # Database service
    ad_db_service = providers.Singleton(
        AdDBService,
        logger=logger,
        config=config,
        fb_archive_repository=storage_container.fb_archive_repository,
        law_firms_repository=storage_container.law_firms_repository,
    )

    # PDFExtractor is not currently used in Facebook Ads processing
    # It requires pdf_source or s3_link at initialization, which are not available at container creation time
    # If needed, it should be created on-demand with specific PDF sources

    ai_orchestrator = providers.Singleton(
        AIOrchestrator,
        logger=logger,
        config=config,
        # Dependencies from self-contained FB ads container
        deepseek=deepseek_service,
        gpt4=openai_client,
        # Optional dependencies - the service should handle missing ones gracefully
        s3_storage=storage_container.s3_async_storage,
        image_queue=local_image_queue,
    )

    failed_firms_manager = providers.Singleton(
        FailedFirmsManager,
        data_dir=config.DATA_DIR,
        logger=logger,
    )

    skip_term_service = providers.Singleton(
        SkipTermService,
        logger=logger,
        config=config,
    )

    # GraphQL response parser for NDJSON responses
    graphql_response_parser = providers.Singleton(
        GraphQLResponseParser,
        logger=logger,
        config=config,
    )

    job_runner_service = providers.Singleton(
        JobRunnerService,
        logger=logger,
        config=config,
        ai_orchestrator=ai_orchestrator,
        graphql_parser=graphql_response_parser,
    )

    job_orchestration_service = providers.Singleton(
        JobOrchestrationService,
        logger=logger,
        config=config,
        job_runner_service=job_runner_service,
        failed_firms_manager=failed_firms_manager,
    )

    # Processing services
    ad_processing_service = providers.Singleton(
        AdProcessingService,
        logger=logger,
        config=config,
        ad_db_service=ad_db_service,
        ad_ner_processor=ad_ner_processor,
        ad_categorizer=ad_categorizer,
        image_handler=image_handler,
        data_validation_service=data_validation_service,
        law_firms_repo=storage_container.law_firms_repository,
        fb_archive_repo=storage_container.fb_archive_repository,
    )

    # AdProcessor DEPRECATED - removed from container
    # Logic moved to JobRunnerService in job-based architecture

    # Workflow services
    workflow_service = providers.Singleton(
        WorkflowService,
        logger=logger,
        config=config,
        ad_db_service=ad_db_service,
        error_handling_service=error_handling_service,
        session_manager=session_manager,
        job_orchestration_service=job_orchestration_service,
        data_validation_service=data_validation_service,
        ad_processing_service=ad_processing_service,
    )

    concurrent_workflow_service = providers.Singleton(
        ConcurrentWorkflowService,
        config=config,
        logger=logger,
        ad_db_service=ad_db_service,
        error_handling_service=error_handling_service,
        session_manager=session_manager,
        job_orchestration_service=job_orchestration_service,
        data_validation_service=data_validation_service,
        ad_processing_service=ad_processing_service,
    )

    # Query and data services
    fb_archive_query_service = providers.Singleton(
        FBArchiveQueryService,
        logger=logger,
        config=config,
        repository=storage_container.fb_archive_repository,
    )

    fb_archive_data_conversion_service = providers.Singleton(
        FBArchiveDataConversionService, logger=logger, config=config
    )

    fb_archive_delete_service = providers.Singleton(
        FBArchiveDeleteService,
        logger=logger,
        config=config,
        fb_archive_repository=storage_container.fb_archive_repository,
    )

    fb_archive_update_service = providers.Singleton(
        FBArchiveUpdateService,
        logger=logger,
        config=config,
        fb_archive_repository=storage_container.fb_archive_repository,
    )

    # Interactive service (defined before orchestrator)
    interactive_service = providers.Singleton(
        InteractiveService,
        logger=logger,
        config=config,
    )

    # Main orchestrator
    facebook_ads_orchestrator = providers.Singleton(
        FacebookAdsOrchestrator,
        logger=logger,
        config=config,
        session=http_session,
        s3_manager=storage_container.s3_async_storage,
        async_storage=storage_container.async_dynamodb_storage,
        law_firms_repository=storage_container.law_firms_repository,
        fb_archive_repository=storage_container.fb_archive_repository,
        processing_tracker=processing_tracker,
        session_manager=session_manager,
        session_manager_factory=session_manager_factory,  # Add factory for firm-specific session managers
        api_client=api_client,
        image_handler=image_handler,
        bandwidth_logger=bandwidth_logger,
        pdf_extractor=None,  # Not used in current FB ads processing
        local_image_queue=local_image_queue,
        ad_processing_service=ad_processing_service,
        data_validation_service=data_validation_service,
        error_handling_service=error_handling_service,
        interactive_service=interactive_service,
        workflow_service=workflow_service,
        job_orchestration_service=job_orchestration_service,
        browser_image_cache_extractor=browser_image_cache_extractor,  # CRITICAL: Pass cache extractor for image caching
        job_runner_service=job_runner_service,
        failed_firms_manager=failed_firms_manager,
        ad_db_service=ad_db_service,
        ai_orchestrator=ai_orchestrator,
        concurrent_workflow_service=concurrent_workflow_service,
        skip_term_service=skip_term_service,
        fingerprint_manager=fingerprint_manager,
        proxy_manager=proxy_manager,
        dropdown_search_helper=dropdown_search_helper,
    )
