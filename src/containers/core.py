"""
Core Container Module

Provides the main application container and core infrastructure services.
"""

import asyncio
import logging
from typing import Any

import aiohttp
from dependency_injector import containers, providers

from . import fb_ads, pacer, reports, storage, transformer
from src.infrastructure.protocols.logger import LoggerProtocol


class LoggerWrapper:
    """Simple wrapper to make standard logger compatible with LoggerProtocol."""
    
    def __init__(self, logger: logging.Logger):
        self._logger = logger
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.debug(message, extra=extra)
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.info(message, extra=extra)
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.warning(message, extra=extra)
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        self._logger.error(message, extra=extra, exc_info=exc_info)
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.exception(message, extra=extra)
    
    def critical(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.critical(message, extra=extra)
    
    def isEnabledFor(self, level: int) -> bool:
        """Check if logger is enabled for the given level."""
        return self._logger.isEnabledFor(level)


class CoreContainer(containers.DeclarativeContainer):
    """Core container for application-wide resources."""

    # Configuration provider
    config = providers.Configuration()

    # Logger factory - wrapped to provide LoggerProtocol interface
    # CRITICAL FIX: Use Singleton instead of Factory to ensure all services get the same logger instance
    logger = providers.Singleton(
        lambda: LoggerWrapper(logging.getLogger("src.services.fb_ads"))
    )

    # HTTP Session resource - using Resource provider for proper lifecycle management
    # Configure connection pooling and timeout settings
    @providers.Resource
    async def http_session():
        """Create and manage aiohttp session with proper connection pooling."""
        # Configure connection pooling with reasonable limits
        connector = aiohttp.TCPConnector(
            limit=100,  # Total connection pool limit
            limit_per_host=30,  # Per-host connection limit
            ttl_dns_cache=300,  # DNS cache timeout
            enable_cleanup_closed=True,  # Clean up closed connections
            force_close=False,  # Keep connections alive for reuse
            keepalive_timeout=30,  # Keep connections alive for 30 seconds
        )
        
        # Configure timeouts
        timeout = aiohttp.ClientTimeout(
            total=300,  # Total timeout for request
            connect=10,  # Connection timeout
            sock_connect=10,  # Socket connection timeout
            sock_read=60,  # Socket read timeout
        )
        
        # Create session with connector and timeout
        session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            connector_owner=True,  # Session owns the connector
        )
        
        try:
            yield session
        finally:
            # Ensure proper cleanup
            await session.close()
            # Wait a bit for connector cleanup
            await asyncio.sleep(0.1)


class MainContainer(containers.DeclarativeContainer):
    """Main application container that wires everything together."""

    # Configuration from environment and config files
    config = providers.Configuration()

    # Core services container
    core = providers.Container(CoreContainer, config=config)

    # Storage and repositories container
    storage = providers.Container(
        storage.StorageContainer,
        config=config.storage,
        logger=core.logger,
        aws_region=config.aws_region,
        aws_access_key=config.aws_access_key_id,
        aws_secret_key=config.aws_secret_access_key,
        dynamodb_endpoint=config.dynamodb_endpoint,
        s3_bucket_name=config.s3_bucket_name,
    )

    # PACER services container
    pacer = providers.Container(
        pacer.PacerContainer,
        config=config,  # Pass full config instead of just config.pacer for AI services
        logger=core.logger,
        storage_container=storage,
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )

    # Facebook Ads services container
    fb_ads = providers.Container(
        fb_ads.FbAdsContainer,
        config=config,  # Pass full config instead of just config.fb_ads
        logger=core.logger,
        storage_container=storage,
        http_session=core.http_session,
    )

    # Reports services container
    reports = providers.Container(
        reports.ReportsContainer,
        config=config,  # Pass full config instead of just config.reports
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
    )

    # Transformer services container
    transformer = providers.Container(
        transformer.TransformerContainer,
        config=config,  # Pass full config instead of just config.transformer
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )


def create_container(config_dict: dict[str, Any]) -> MainContainer:
    """
    Create and configure the main DI container.

    Args:
        config_dict: Configuration dictionary

    Returns:
        Configured MainContainer instance
    """
    container = MainContainer()

    # Set default configuration values
    defaults = {
        "aws_region": "us-west-2",
        "dynamodb_endpoint": None,
        "s3_bucket_name": "lexgenius-data",
        "aws_access_key_id": "",
        "aws_secret_access_key": "",
        "deepseek_api_key": "",
        "openai_api_key": "",
        "llava_base_url": "http://localhost:11434",
        "llava_model": "llava",
        "llava_timeout": 60,
        "fb_ciphers": "TLS_AES_128_GCM_SHA256",
        "headless": False,
        "run_parallel": True,
        "timeout_ms": 60000,
        # FB Ads NLP Configuration defaults
        "ner_model_name": "en_core_web_sm",
        "spacy_pipe_batch_size": 1000,
        "dynamodb_scan_workers": None,
        "ner_processing_workers": None,
        "text_fields": ["Title", "Body"],
        "cluster_min_k": 2,
        "cluster_max_k": 10,
        "cluster_k_step": 1,
        "cluster_output_enabled": True,
        "use_local_dynamodb": False,
        "dynamodb_update_workers": None,
        "storage": {},
        "pacer": {"headless": False, "run_parallel": True, "timeout_ms": 60000},
        "fb_ads": {},
        "transformer": {},
        "reports": {},
    }

    # Merge defaults with provided config
    final_config = {**defaults, **config_dict}

    # Configure the container
    container.config.from_dict(final_config)
    
    # Note: PACER container now uses its own PacerTransferService instead of transformer's TransferHandler

    return container


def create_container_with_multiprocessing(config_dict: dict[str, Any]) -> MainContainer:
    """
    Create and configure the main DI container with multiprocessing support.
    
    This function initializes shared resources using the bootstrap mechanism
    before creating the container, ensuring that multiprocessing.Manager
    resources are available to all worker processes.
    
    Args:
        config_dict: Configuration dictionary
        
    Returns:
        MainContainer with shared resources configured
    """
    from src.infrastructure.bootstrap import FbAdsBootstrap
    
    logger = logging.getLogger(__name__)
    logger.info("🚀 Creating container with multiprocessing support...")
    
    # Create bootstrap and initialize shared resources  
    bootstrap = FbAdsBootstrap(config_dict, logger)
    shared_resources = bootstrap.initialize_shared_resources()
    
    # Create main container using standard method
    container = create_container(config_dict)
    
    # Inject shared resources into fb_ads container configuration
    if hasattr(container, 'fb_ads'):
        # Get current fb_ads config or create empty dict if not set
        try:
            # Try to get the fb_ads config as a dict
            fb_ads_config = dict(container.config.fb_ads) if container.config.fb_ads else {}
        except (TypeError, AttributeError):
            # If fb_ads is not a dict-like object, create new dict
            fb_ads_config = {}
            
        # Update with shared resources
        fb_ads_config.update(shared_resources)
        
        # Override the fb_ads configuration
        container.config.fb_ads.override(fb_ads_config)
        logger.info(f"✅ Injected shared cache into fb_ads container (type: {type(shared_resources.get('shared_cache_dict')).__name__})")
    
    # Store bootstrap reference for cleanup
    container._bootstrap = bootstrap
    
    logger.info("✅ Container created with multiprocessing support")
    
    return container


def wire_container(container: MainContainer, modules: list):
    """
    Wire the container to specified modules.

    Args:
        container: The container to wire
        modules: List of modules to wire
    """
    container.wire(modules=modules)


def unwire_container(container: MainContainer):
    """Unwire the container."""
    container.unwire()


# Export all key items
__all__ = [
    "CoreContainer",
    "MainContainer",
    "create_container",
    "create_container_with_multiprocessing",
    "wire_container",
    "unwire_container",
]
