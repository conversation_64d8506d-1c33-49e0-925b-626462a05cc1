"""
DynamoDB Utilities - Helper Functions and Data Conversions
"""
import ast
import json
import logging
import re
from decimal import Decimal, InvalidOperation
from typing import Dict, List, Any, Tuple, Optional

import pandas as pd


# Module-level utility functions for easy import
def sanitize_for_dynamodb(data: Any) -> Any:
    """Sanitize data for DynamoDB storage"""
    return DynamoDBUtils.sanitize_for_dynamodb(data)


def pascal_case_dict(data: Dict[str, Any]) -> Dict[str, Any]:
    """Convert dictionary keys to PascalCase"""
    return DynamoDBUtils.convert_dict_to_pascal_case(data)


def replace_decimals(obj: Any) -> Any:
    """Replace Decimal objects with Python numeric types"""
    if isinstance(obj, list):
        return [replace_decimals(x) for x in obj]
    elif isinstance(obj, dict):
        return {k: replace_decimals(v) for k, v in obj.items()}
    elif isinstance(obj, Decimal):
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    else:
        return obj


class DynamoDBUtils:
    """Utility functions for DynamoDB operations"""

    @staticmethod
    def snake_to_pascal_case(snake_str: str) -> str:
        """Convert snake_case to PascalCase"""
        if not isinstance(snake_str, str) or not snake_str:
            return snake_str

        components = snake_str.split('_')
        return ''.join(x.title() for x in components)

    @staticmethod
    def pascal_to_snake_case(pascal_str: str) -> str:
        """Convert PascalCase to snake_case"""
        if not isinstance(pascal_str, str) or not pascal_str:
            return pascal_str

        # Insert underscore before uppercase letters that follow lowercase
        s1 = re.sub(r'(?<=[a-z0-9])([A-Z])', r'_\1', pascal_str)
        # Handle consecutive uppercase letters
        s2 = re.sub(r'([A-Z]+)([A-Z][a-z])', r'\1_\2', s1)
        return s2.lower()

    @staticmethod
    def convert_dict_to_pascal_case(snake_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Convert all keys in dictionary from snake_case to PascalCase"""
        if not isinstance(snake_dict, dict):
            return snake_dict

        pascal_dict = {}
        for key, value in snake_dict.items():
            pascal_key = DynamoDBUtils.snake_to_pascal_case(key)
            pascal_dict[pascal_key] = value

        return pascal_dict

    @staticmethod
    def convert_dict_to_snake_case(pascal_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Convert all keys in dictionary from PascalCase to snake_case"""
        if not isinstance(pascal_dict, dict):
            return pascal_dict

        return {
            DynamoDBUtils.pascal_to_snake_case(k): v
            for k, v in pascal_dict.items()
        }

    @staticmethod
    def sanitize_for_dynamodb(
            record: Dict[str, Any],
            convert_booleans: bool = True
    ) -> Dict[str, Any]:
        """
        Sanitize record values for DynamoDB compatibility
        
        Args:
            record: Record to sanitize
            convert_booleans: Whether to convert 0/1 to boolean
            
        Returns:
            Sanitized record
        """
        logger = logging.getLogger(__name__)
        sanitized = {}

        if not isinstance(record, dict):
            logger.warning(f"Input not dict: {type(record)}")
            return record

        for key, value in record.items():
            # Skip None or empty string values
            if value is None or value == '':
                continue

            # Convert 0/1 to boolean if enabled
            if convert_booleans and value in [0, 1, '0', '1', 0.0, 1.0, '0.0', '1.0', Decimal('0'), Decimal('1')]:
                sanitized[key] = value in [1, '1', 1.0, '1.0', Decimal('1')]
                continue

            # Parse string representations of lists/dicts
            if isinstance(value, str) and value.strip():
                stripped = value.strip()
                if (stripped.startswith('[') and stripped.endswith(']')) or \
                        (stripped.startswith('{') and stripped.endswith('}')):
                    try:
                        parsed = ast.literal_eval(stripped)
                        if isinstance(parsed, (list, dict)):
                            sanitized[key] = parsed
                            continue
                    except (ValueError, SyntaxError):
                        # Try JSON parsing
                        try:
                            parsed = json.loads(stripped)
                            if isinstance(parsed, (list, dict)):
                                sanitized[key] = parsed
                                continue
                        except json.JSONDecodeError:
                            pass

            # Handle floats
            if isinstance(value, float):
                if pd.isna(value) or value == float('inf') or value == float('-inf'):
                    continue
                try:
                    sanitized[key] = Decimal(str(value))
                except InvalidOperation:
                    sanitized[key] = str(value)

            # Handle integers
            elif isinstance(value, int):
                sanitized[key] = Decimal(value)

            # Keep compatible types
            elif isinstance(value, (str, bool, list, dict, set, bytes, bytearray, Decimal)):
                sanitized[key] = value

            # Convert others to string
            else:
                try:
                    sanitized[key] = str(value)
                except Exception as e:
                    logger.warning(f"Could not convert key '{key}' to string: {e}")
                    continue

        return sanitized

    @staticmethod
    def compare_records(
            dynamodb_record: Dict[str, Any],
            local_record: Dict[str, Any],
            remove_missing_fields: bool = False,
            preserved_fields: Optional[set] = None
    ) -> Tuple[Dict[str, Any], List[str]]:
        """
        Compare DynamoDB and local records
        
        Args:
            dynamodb_record: Record from DynamoDB
            local_record: Local record to compare
            remove_missing_fields: Whether to remove fields not in local record
            preserved_fields: Fields to preserve even if missing locally
            
        Returns:
            Tuple of (fields_to_update, fields_to_remove)
        """
        update_data = {}
        remove_data = []

        # Default preserved fields
        if preserved_fields is None:
            preserved_fields = {'AddedOn', 'UpdatedOn', 'LastModifiedDate', 'CreatedAt', 'UpdatedAt'}

        # Find differences
        for key, local_val in local_record.items():
            db_val = dynamodb_record.get(key)
            if db_val != local_val:
                update_data[key] = local_val

        # Find fields to remove
        if remove_missing_fields:
            for key in dynamodb_record:
                if key not in local_record and key not in preserved_fields:
                    remove_data.append(key)

        return update_data, remove_data

    @staticmethod
    def batch_items(items: List[Any], batch_size: int) -> List[List[Any]]:
        """Split items into batches"""
        return [items[i:i + batch_size] for i in range(0, len(items), batch_size)]

    @staticmethod
    def format_key_string(key_dict: Dict[str, Any]) -> str:
        """Format key dictionary as string for logging"""
        if not key_dict:
            return "NO_KEY"

        parts = []
        for k, v in sorted(key_dict.items()):
            parts.append(f"{k}={v}")

        return "/".join(parts)

    @staticmethod
    def extract_s3_bucket_key(s3_url: str) -> Optional[Tuple[str, str]]:
        """
        Extract bucket and key from S3 URL
        
        Args:
            s3_url: S3 URL (https:// or s3://)
            
        Returns:
            Tuple of (bucket, key) or None
        """
        if not s3_url:
            return None

        # Handle s3:// URLs
        if s3_url.startswith('s3://'):
            parts = s3_url[5:].split('/', 1)
            if len(parts) == 2:
                return parts[0], parts[1]

        # Handle https:// URLs
        elif s3_url.startswith('https://'):
            # Remove protocol
            url_parts = s3_url[8:].split('/')

            # Extract bucket from hostname
            hostname = url_parts[0]

            # Handle virtual-hosted-style URLs
            if '.s3.' in hostname:
                bucket = hostname.split('.')[0]
                key = '/'.join(url_parts[1:])
                return bucket, key

            # Handle path-style URLs
            elif hostname == 's3.amazonaws.com' and len(url_parts) > 1:
                bucket = url_parts[1]
                key = '/'.join(url_parts[2:])
                return bucket, key

        return None

    @staticmethod
    def merge_attribute_updates(
            existing_updates: Dict[str, Any],
            new_updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Merge attribute updates, with new updates taking precedence
        
        Args:
            existing_updates: Existing update dictionary
            new_updates: New updates to merge
            
        Returns:
            Merged update dictionary
        """
        merged = existing_updates.copy()
        merged.update(new_updates)
        return merged

    @staticmethod
    def validate_key_attributes(
            record: Dict[str, Any],
            key_attributes: List[str]
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate that all key attributes are present and non-empty
        
        Args:
            record: Record to validate
            key_attributes: List of required key attributes
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        for attr in key_attributes:
            value = record.get(attr)

            if value is None:
                return False, f"Key attribute '{attr}' is missing"

            if value == '':
                return False, f"Key attribute '{attr}' is empty string"

            if isinstance(value, float) and pd.isna(value):
                return False, f"Key attribute '{attr}' is NaN"

        return True, None

    @staticmethod
    def create_update_expression(
            updates: Dict[str, Any],
            removals: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, str], Dict[str, Any]]:
        """
        Create DynamoDB update expression components
        
        Args:
            updates: Dictionary of attributes to update
            removals: List of attributes to remove
            
        Returns:
            Tuple of (update_expression, expression_names, expression_values)
        """
        update_parts = []
        remove_parts = []
        expr_names = {}
        expr_values = {}

        # Build SET expressions
        for idx, (key, value) in enumerate(updates.items()):
            name_placeholder = f"#n{idx}"
            value_placeholder = f":v{idx}"

            expr_names[name_placeholder] = key
            expr_values[value_placeholder] = value
            update_parts.append(f"{name_placeholder} = {value_placeholder}")

        # Build REMOVE expressions
        if removals:
            for idx, key in enumerate(removals):
                name_placeholder = f"#r{idx}"
                expr_names[name_placeholder] = key
                remove_parts.append(name_placeholder)

        # Combine expressions
        expressions = []
        if update_parts:
            expressions.append("SET " + ", ".join(update_parts))
        if remove_parts:
            expressions.append("REMOVE " + ", ".join(remove_parts))

        update_expression = " ".join(expressions)

        return update_expression, expr_names, expr_values
