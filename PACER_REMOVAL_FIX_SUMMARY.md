# PACER Removal Case Fix Summary

## Overview
This document summarizes the changes made to fix the removal case processing for `nysd_25_06053_Ande_et_al_v_3M_Company_et_al`, where the system was incorrectly clicking on the removal notice link (2nd TD) instead of the complaint attachment link (3rd TD).

## Root Cause Analysis

### Issue 1: Duplicate Court ID Extraction
- **Problem**: The transferor court ID was being extracted twice with different results
  - First extraction: "Supreme Court, New York County" → "NY" (correct)
  - Second extraction: "Supreme Court" → "SC" (incorrect)
- **Cause**: String was split incorrectly, losing the county information

### Issue 2: Wrong Link Clicking for Removal Cases
- **Problem**: For removal cases, the system clicked the link "1" in the 2nd TD (removal notice) instead of the attachment "1" in the 3rd TD (complaint)
- **HTML Structure**:
  ```html
  <!-- 2nd TD: Main document link (WRONG) -->
  <td><a href=".../doc1/127037913580">1</a></td>
  
  <!-- 3rd TD: Attachment link (CORRECT) -->
  <td>NOTICE OF REMOVAL... Attachments: # <a href=".../doc1/127137913581">1</a> Exhibit Summons and Complaint</td>
  ```

## Changes Made

### 1. Fixed Court Name Parsing (`transfer_service.py`)

**File**: `/src/services/pacer/transfer_service.py`

**Line 883** - Changed string splitting method:
```python
# BEFORE:
parts = case_in_other_court_str.split(",", 1)  # Split only once

# AFTER:
parts = case_in_other_court_str.rsplit(",", 1)  # Split from right to keep court name intact
```

**Impact**: Preserves full court name "Supreme Court, New York County" instead of truncating to "Supreme Court"

### 2. Removed Duplicate Court ID Determination (`transfer_service.py`)

**File**: `/src/services/pacer/transfer_service.py`

**Lines 901-921** - Added logic to reuse existing court ID:
```python
# BEFORE: Always called _determine_court_type which internally called AI again
court_type, determined_court_id = await self._determine_court_type(
    potential_transferor_name,
    potential_transferor_docket,
    federal_lookup,
)

# AFTER: Check for existing court ID first
transferor_court_id = case_details.get("transferor_court_id")

if transferor_court_id and transferor_court_id not in self.NULL_CONDITIONS:
    # Determine court type based on existing court ID
    court_type = "FEDERAL" if self._is_federal_court(transferor_court_id) else "STATE"
    determined_court_id = transferor_court_id
    self.log_info(
        f"{log_prefix} Using existing transferor_court_id='{transferor_court_id}', court_type='{court_type}'"
    )
else:
    # Fall back to determining court type (which will call AI)
    court_type, determined_court_id = await self._determine_court_type(
        potential_transferor_name,
        potential_transferor_docket,
        federal_lookup,
    )
```

**Impact**: Prevents duplicate AI calls and inconsistent court ID results

### 3. Removed Duplicate Removal Handling (`download_orchestration_service.py`)

**File**: `/src/services/pacer/download_orchestration_service.py`

**Lines 780-865** - Deleted entire duplicate removal handling block:
```python
# DELETED: This entire block that was duplicating removal logic
if is_removal:
    self.log_info(
        f"{log_prefix} 🚨 REMOVAL CASE - Using specialized removal handling"
    )
    # ... 85 lines of duplicate removal handling ...
```

**Impact**: Ensures removal cases use only the specialized `_trigger_download_action_removal` method

### 4. Skip Complaint Link Click for Removal Cases (`download_orchestration_service.py`)

**File**: `/src/services/pacer/download_orchestration_service.py`

**Lines 2763-2781** - Added conditional logic to skip complaint click for removals:
```python
# BEFORE: Always clicked complaint link
complaint_clicked = await self._click_complaint_link(
    case_details_with_updated_removal
)

# AFTER: Skip for removal cases
if is_removal_updated:
    court_logger.info(
        f"{log_prefix} 🚨 REMOVAL CASE - Skipping complaint link click (will be handled by removal-specific logic)"
    )
    complaint_clicked = False
else:
    # Pass the updated removal status to the complaint click method
    case_details_with_updated_removal = case_details.copy()
    case_details_with_updated_removal["is_removal"] = is_removal_updated

    complaint_clicked = await self._click_complaint_link(
        case_details_with_updated_removal
    )
    court_logger.info(
        f"{log_prefix} 🎯 Complaint link click result: {complaint_clicked}"
    )
```

**Impact**: Prevents clicking the wrong link (2nd TD) for removal cases, allowing the removal handler to click the correct attachment (3rd TD)

### 5. Fixed Prompt Template (`extract_removal_attachment_user.md`)

**File**: `/src/config/prompts/post_processor/extract_removal_attachment_user.md`

**Lines 13-15** - Fixed JSON format in prompt:
```markdown
# BEFORE:
{
     "number": "{an integer number or NA}"
}

# AFTER:
{{
     "number": "an integer number or NA"
}}
```

**Impact**: Prevents "Missing variable in prompt template" error by properly escaping curly braces

## Verification

The removal case handling now follows this correct flow:

1. **Detection**: Case is properly identified as removal when transferor court is STATE
2. **Routing**: Removal cases skip the standard complaint link click
3. **Special Handling**: `_trigger_download_action_removal` executes for removal cases
4. **Correct Clicking**: The method clicks attachment links in the 3rd TD using:
   ```python
   # Get the 3rd TD (index 2) - docket text column
   third_td = row.locator("td").nth(2)
   # Find links within the 3rd TD
   links_in_td = third_td.locator("a")
   ```

## Key Learnings

1. **Court Name Parsing**: Always use `rsplit` when preserving the left portion of a string is critical
2. **AI Call Optimization**: Reuse existing extracted data instead of making duplicate AI calls
3. **Specialized Workflows**: Removal cases require different handling than standard cases
4. **HTML Structure Awareness**: PACER places main documents in 2nd TD and attachments in 3rd TD
5. **Template Escaping**: Double curly braces `{{` are needed to escape literal braces in format strings