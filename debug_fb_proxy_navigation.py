#!/usr/bin/env python3
"""
Debug script to test Facebook Ads Library navigation with proxy.
"""
import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_navigation_with_proxy():
    """Test Facebook navigation with different strategies."""
    try:
        # Import necessary modules
        from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
        from src.services.scraping.proxy.proxy_manager import ProxyManager
        from src.services.scraping.browser.fingerprint_manager import FingerprintManager
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Load configuration
        config_dict = {
            'name': 'fb_proxy_navigation_test',  # Required field
            'date': datetime.now().strftime('%m/%d/%y'),
            'iso_date': datetime.now().strftime('%Y%m%d'),
            'fb_ads': True,
            'use_proxy': True,
            'mobile_proxy': True,  # Use mobile proxy
            'use_graphql_capture': True,
            'feature_flags': {
                'graphql_enabled': True,
                'use_camoufox': True
            },
            'headless': False,  # Use non-headless for debugging
            'log_level': 'DEBUG',
            'browser_timeout': 60000,  # Increase timeout to 60 seconds
            'DATA_DIR': os.environ.get('LEXGENIUS_DATA_DIR', 'data'),
            # Load proxy credentials from environment
            'oxy_labs_mobile_username': os.getenv('OXY_LABS_MOBILE_USERNAME'),
            'oxy_labs_mobile_password': os.getenv('OXY_LABS_MOBILE_PASSWORD'),
            'oxy_labs_residential_username': os.getenv('OXY_LABS_RESIDENTIAL_USERNAME'),
            'oxy_labs_residential_password': os.getenv('OXY_LABS_RESIDENTIAL_PASSWORD'),
        }
        
        # Create proxy manager if proxy is enabled
        proxy_manager = None
        if config_dict.get('use_proxy'):
            proxy_manager = ProxyManager(config=config_dict, logger=logger, proxy_provider=None)
        
        # Create fingerprint manager
        fingerprint_manager = FingerprintManager(config=config_dict, logger=logger)
        
        # Create session manager directly
        session_manager = CamoufoxSessionManager(
            config=config_dict,
            logger=logger,
            proxy_manager=proxy_manager,
            fingerprint_manager=fingerprint_manager,
        )
        
        logger.info("🧪 Starting navigation test with proxy")
        logger.info(f"📅 Testing date: {config_dict['iso_date']}")
        
        # Test 1: Try to create session (which includes navigation)
        logger.info("\n=== TEST 1: Create session with navigation ===")
        success = await session_manager.create_new_session()
        
        if success:
            logger.info("✅ TEST 1 PASSED: Successfully navigated to Facebook Ads Library")
            
            # Get session stats
            stats = session_manager.get_session_stats()
            logger.info(f"Session stats: {stats}")
            
            # Test 2: Check if we can interact with the page
            logger.info("\n=== TEST 2: Check page interaction ===")
            try:
                # Check if the search box is present
                search_box = await session_manager.page.query_selector('input[placeholder*="Search"]')
                if search_box:
                    logger.info("✅ TEST 2 PASSED: Found search box - page loaded correctly")
                else:
                    logger.error("❌ TEST 2 FAILED: Could not find search box")
                    
            except Exception as e:
                logger.error(f"❌ TEST 2 FAILED with exception: {e}")
            
            # Keep browser open for 10 seconds for manual inspection
            if not config_dict.get('headless', True):
                logger.info("🔍 Keeping browser open for 10 seconds for inspection...")
                await asyncio.sleep(10)
            
        else:
            logger.error("❌ TEST 1 FAILED: Could not create session/navigate to Facebook")
            
            # Check if proxy is working by trying without proxy
            logger.info("\n=== FALLBACK TEST: Try without proxy ===")
            
            # Create new session manager without proxy
            session_manager_no_proxy = CamoufoxSessionManager(
                config=config_dict,
                logger=logger,
                proxy_manager=None,  # No proxy manager
                fingerprint_manager=fingerprint_manager,
            )
            
            success_no_proxy = await session_manager_no_proxy.create_new_session()
            if success_no_proxy:
                logger.warning("⚠️ Navigation works WITHOUT proxy - proxy may be blocked")
            else:
                logger.error("❌ Navigation fails even without proxy - Facebook may be blocking")
        
        # Cleanup
        await session_manager.cleanup()
        if 'session_manager_no_proxy' in locals():
            await session_manager_no_proxy.cleanup()
            
        return success
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}", exc_info=True)
        return False


async def main():
    """Main test runner."""
    logger.info("=" * 80)
    logger.info("Facebook Ads Proxy Navigation Debug Test")
    logger.info("=" * 80)
    logger.info("This test checks if we can navigate to Facebook Ads Library with proxy.")
    logger.info("=" * 80)
    
    # Run the test
    success = await test_navigation_with_proxy()
    
    logger.info("=" * 80)
    if success:
        logger.info("🎉 NAVIGATION TEST PASSED!")
    else:
        logger.error("❌ NAVIGATION TEST FAILED!")
        logger.info("\nPossible issues:")
        logger.info("1. Proxy is blocked by Facebook")
        logger.info("2. Proxy credentials are incorrect")
        logger.info("3. Network connectivity issues")
        logger.info("4. Facebook is detecting automation")
    logger.info("=" * 80)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)