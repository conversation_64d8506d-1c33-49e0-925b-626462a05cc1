#!/usr/bin/env python3
"""
Comprehensive test to validate ALL fixes for the AsyncCamoufox lock corruption issue.

This test validates:
1. ThreadSafeSingleton in dependency injection container
2. CamoufoxSessionManager class-level lock fix
3. SharedBrowserManager simplified lock management
4. Fingerprint config fix (not passing custom config to Camoufox)

Usage:
    python test_all_fixes_comprehensive.py
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any
from unittest.mock import MagicMock, patch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_complete_fix():
    """
    Test all fixes comprehensively.
    """
    logger.info("🎯 Testing Complete AsyncCamoufox Lock Corruption Fix")
    
    # Mock environment variables
    test_env = {
        'OXYLABS_MOBILE_PASSWORD': 'test_mobile_password',
        'OXYLABS_PASSWORD': 'test_password',
        'AWS_ACCESS_KEY_ID': 'test_key_id',
        'AWS_SECRET_ACCESS_KEY': 'test_secret',
        'S3_BUCKET_NAME': 'test-bucket',
    }
    
    for key, value in test_env.items():
        os.environ[key] = value
    
    logger.info("📋 Test 1: CamoufoxSessionManager class-level lock")
    
    try:
        from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
        
        # Test that the class-level lock is None initially
        assert CamoufoxSessionManager._browser_start_lock is None
        logger.info("✅ Class-level lock is None (not created at import time)")
        
        # Test that _get_browser_start_lock creates the lock lazily
        lock = CamoufoxSessionManager._get_browser_start_lock()
        assert lock is not None
        assert isinstance(lock, asyncio.Lock)
        logger.info("✅ Browser start lock created lazily when needed")
        
    except Exception as e:
        logger.error(f"❌ Test 1 FAILED: {e}")
        return False
    
    logger.info("📋 Test 2: SharedBrowserManager simplified locks")
    
    try:
        from src.services.fb_ads.camoufox.shared_browser_manager import get_shared_browser_manager
        
        # Get shared browser manager
        manager = get_shared_browser_manager()
        
        # Check that it has simple lock structure
        assert hasattr(manager, '_browser_lock')
        assert hasattr(manager, '_context_lock')
        logger.info("✅ SharedBrowserManager has simple lock structure")
        
        # Check that complex attributes are removed
        complex_attrs = ['_circuit_breaker_open_time', '_max_consecutive_failures']
        for attr in complex_attrs:
            assert not hasattr(manager, attr), f"Complex attribute {attr} still present"
        logger.info("✅ Complex circuit breaker attributes removed")
        
    except Exception as e:
        logger.error(f"❌ Test 2 FAILED: {e}")
        return False
    
    logger.info("📋 Test 3: Fingerprint config fix")
    
    try:
        # Create mock fingerprint manager
        mock_fingerprint_manager = MagicMock()
        mock_fingerprint_manager.get_next_fingerprint = MagicMock(return_value={
            'name': 'test_fingerprint',
            'config': {
                'screen': {'width': 1920, 'height': 1080}  # This would cause the error
            }
        })
        
        # Create session manager
        test_config = {
            'headless': False,
            'humanize': True,
            'use_proxy': True,
        }
        
        session_manager = CamoufoxSessionManager(
            config=test_config,
            logger=logger,
            fingerprint_manager=mock_fingerprint_manager,
            proxy_manager=None,
            law_firms_repository=None,
            job_id="test_job"
        )
        
        # Mock SharedBrowserManager
        mock_shared_manager = MagicMock()
        mock_context = MagicMock()
        mock_page = MagicMock()
        mock_shared_manager.get_browser_context = MagicMock(
            return_value=asyncio.coroutine(lambda: (mock_context, mock_page))()
        )
        session_manager.shared_browser_manager = mock_shared_manager
        
        # Start session - this should NOT pass the fingerprint config
        await session_manager.start()
        
        # Verify that fingerprint config was NOT passed (should be None)
        call_args = mock_shared_manager.get_browser_context.call_args
        assert call_args is not None
        _, kwargs = call_args
        assert kwargs.get('fingerprint_config') is None
        logger.info("✅ Fingerprint config NOT passed to SharedBrowserManager")
        logger.info("✅ Camoufox will use automatic fingerprinting")
        
    except Exception as e:
        logger.error(f"❌ Test 3 FAILED: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    logger.info("📋 Test 4: Full integration test")
    
    try:
        from src.containers.fb_ads import FbAdsContainer
        
        # Create container
        container = FbAdsContainer()
        
        # Mock storage
        mock_storage = MagicMock()
        mock_storage.law_firms_repository = MagicMock()
        container.storage_container.override(mock_storage)
        
        # Override config
        container.config.from_dict(test_config)
        container.logger.override(logger)
        container.http_session.override(MagicMock())
        
        # Get session manager - this triggers creation
        session_manager = container.session_manager()
        
        assert session_manager is not None
        assert hasattr(session_manager, 'shared_browser_manager')
        logger.info("✅ Container creates session manager successfully")
        logger.info("✅ No asyncio lock corruption during creation")
        
    except Exception as e:
        if "'dict' object has no attribute 'is_set'" in str(e):
            logger.error(f"❌ Test 4 FAILED: Lock corruption still present: {e}")
        else:
            logger.error(f"❌ Test 4 FAILED: {e}")
        return False
    
    logger.info("🎉 ALL TESTS PASSED!")
    return True

async def main():
    """Main test runner."""
    logger.info("🎯 Starting Comprehensive Fix Validation")
    logger.info("=" * 80)
    
    success = await test_complete_fix()
    
    logger.info("\n" + "=" * 80)
    logger.info("FINAL RESULTS")
    logger.info("=" * 80)
    
    if success:
        logger.info("🎉 ALL FIXES VALIDATED!")
        logger.info("")
        logger.info("✅ Fixed Issues:")
        logger.info("  1. CamoufoxSessionManager class-level lock → Lazy initialization")
        logger.info("  2. SharedBrowserManager complexity → Simplified locks")
        logger.info("  3. Fingerprint config format → Let Camoufox handle it")
        logger.info("  4. Dependency injection timing → ThreadSafeSingleton")
        logger.info("")
        logger.info("🚀 Production FB ads processing should now work!")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED")
        logger.error("Check the logs above for specific failures")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)