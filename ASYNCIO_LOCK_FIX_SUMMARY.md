# AsyncIO Lock Initialization Fix Summary

## Issue
Facebook ads processing was hanging during initialization when creating the SharedBrowserManager singleton. The logs showed the system stuck at asyncio lock creation.

## Root Cause
The SharedBrowserManager was attempting to create asyncio.Lock() objects during `__init__` outside of an event loop context, causing a RuntimeError that was being silently caught and creating initialization issues.

## Solution
Implemented deferred lock initialization pattern:

1. **Deferred Lock Creation**: Initialize locks as None in `__init__` and create them only when first needed inside an event loop context
2. **Added `_ensure_locks()` method**: Creates locks when called from within an async context
3. **Improved Error Handling**: Added descriptive error messages for better debugging

## Technical Details

### Original Code (Problematic)
```python
def __init__(self):
    # Simple asyncio locks - create once, use forever
    try:
        self._browser_lock = asyncio.Lock()
        self._context_lock = asyncio.Lock()
        self.logger.info("✅ Simple asyncio locks created successfully")
    except RuntimeError:
        # No event loop running yet - will be created when first used
        self._browser_lock = None
        self._context_lock = None
        self.logger.info("⏳ Asyncio locks will be created when first used")
```

### Fixed Code
```python
def __init__(self):
    # Initialize locks as None - they will be created when needed
    self._browser_lock = None
    self._context_lock = None
    self.logger.info("⏳ Asyncio locks will be created when first used (deferred initialization)")

def _ensure_locks(self):
    """Ensure locks exist, create them if needed."""
    if self._browser_lock is None or self._context_lock is None:
        try:
            # Check if we're in an event loop context
            loop = asyncio.get_running_loop()
            self._browser_lock = asyncio.Lock()
            self._context_lock = asyncio.Lock()
            self.logger.info(f"✅ Created asyncio locks in event loop {id(loop)}")
        except RuntimeError as e:
            # If no event loop is running, we can't create locks yet
            self.logger.error(f"❌ Cannot create locks - no event loop running: {e}")
            raise RuntimeError(
                "SharedBrowserManager._ensure_locks() called outside of asyncio context. "
                "This method must be called from within an async function."
            )
```

## Files Modified
- `src/services/fb_ads/camoufox/shared_browser_manager.py`

## Testing
Tested with `python src/main.py --params config/fb_ads_test.yml` and confirmed:
1. No more hanging during initialization
2. SharedBrowserManager singleton creates successfully
3. Facebook ads processing completes without errors

## Commit
```
fix(fb-ads): Fix SharedBrowserManager asyncio lock initialization

- Defer asyncio.Lock() creation until inside event loop context
- Add _ensure_locks() method to create locks when first needed
- Fix RuntimeError when creating locks outside of asyncio context
- Improve error messages for better debugging

This resolves the hang during Facebook ads processing initialization
where asyncio locks were being created during __init__ outside of
an event loop context.
```