#!/usr/bin/env python3
"""
Debug proxy format differences between curl and <PERSON><PERSON>
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scripts.analysis.talc.oxylabs_proxy_helper import OxylabsProxyHelper

async def test_proxy_formats():
    """Test different proxy formats to understand the issue."""
    
    # Initialize helper
    helper = OxylabsProxyHelper(mobile_proxy=True)
    
    # Generate a single proxy
    proxy_list = helper.generate_proxy_list(1)
    
    if not proxy_list:
        print("❌ Failed to generate proxy - check credentials")
        return
        
    original_proxy = proxy_list[0]
    print(f"Original proxy URL: {original_proxy}")
    
    # Parse the proxy URL
    if '@' in original_proxy:
        auth_part, server_part = original_proxy.split('@')
        protocol = auth_part.split('://')[0]
        credentials = auth_part.split('://')[1]
        
        print(f"Protocol: {protocol}")
        print(f"Credentials: {credentials}")
        print(f"Server: {server_part}")
        
        # Test different formats
        formats = [
            # Original format
            original_proxy,
            
            # HTTPS instead of HTTP
            original_proxy.replace('http://', 'https://'),
            
            # Without protocol
            f"{credentials}@{server_part}",
            
            # Just server with separate auth
            server_part,
        ]
        
        for i, format_url in enumerate(formats):
            print(f"\nFormat {i+1}: {format_url}")
            
            # Test with curl equivalent
            if i == 0:
                curl_equiv = f'curl -x "{format_url}" -s --max-time 10 https://httpbin.org/ip'
                print(f"Curl equivalent: {curl_equiv}")
                
                # Test if this works
                try:
                    import subprocess
                    result = subprocess.run(curl_equiv, shell=True, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0 and result.stdout:
                        print("✅ Curl works with this format")
                        print(f"Result: {result.stdout.strip()}")
                    else:
                        print("❌ Curl failed with this format")
                        print(f"Error: {result.stderr}")
                except Exception as e:
                    print(f"❌ Curl test failed: {e}")
            
            # Test with Playwright format
            playwright_format = {'server': format_url}
            print(f"Playwright format: {playwright_format}")

if __name__ == "__main__":
    asyncio.run(test_proxy_formats())