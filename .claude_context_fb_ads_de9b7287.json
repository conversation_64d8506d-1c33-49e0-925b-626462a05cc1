{"task_id": "fb_ads_de9b7287", "config_type": "fb_ads", "error_history": [{"timestamp": "2025-07-13T15:10:02.038819", "error_type": "generic_error", "details": {"error_type": "generic_error", "config_type": "fb_ads", "full_output": "Running pipeline step: fb_ads.yml with config /Users/<USER>/PycharmProjects/lexgenius/config/fb_ads.yml\nStarting main.py with Python 3.11.13 | packaged by conda-forge | (main, Jun  4 2025, 14:52:34) [Clang 18.1.8 ]\nSet multiprocessing start method to 'spawn'.\n[07/13/25 15:10:00] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and                                                \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/pacer.log                \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/pacer.log                \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/logging_confi            \n                             g_module.log                                       \n                    INFO     Initial logging setup complete. Log    main.py:1942\n                             directory:                                         \n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data/20250713/logs                         \n                    INFO     Base data directory:                   main.py:1945\n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data                                       \n                    INFO     No end_date specified, defaulting to   main.py:2034\n                             target date: 07/13/25                              \n                    INFO     Preparing for single date run:         main.py:2083\n                             07/13/25                                           \n                    INFO     DEBUG: Merging params into config     loader.py:170\n                             data for fb_ads                                    \n                    INFO     DEBUG: process_single_court in YAML:  loader.py:171\n                             NOT_SET                                            \n                    INFO     DEBUG: process_single_court in        loader.py:172\n                             params: []                                         \n                    INFO     DEBUG: process_single_court after     loader.py:174\n                             merge: []                                          \n                    WARNING  🔍 DEBUG MAIN.PY: Pydantic             main.py:2092\n                             final_config.run_parallel = True                   \n                    WARNING  🔍 DEBUG MAIN.PY: Pydantic             main.py:2093\n                             final_config.headless = True                       \n                    WARNING  🔍 DEBUG MAIN.PY: config_name =        main.py:2094\n                             fb_ads, params keys = ['date',                     \n                             'start_date', 'end_date',                          \n                             'llm_provider', 'scraper', 'headless',             \n                             'reset_chrome', 'docket_num',                      \n                             'process_single_court',                            \n                             'docket_list_for_orchestrator',                    \n                             'skip_courts', 'start_after_court',                \n                             'start_at_court', 'html_only',                     \n                             'run_parallel', 'post_process',                    \n                             'reprocess_files',                                 \n                             'start_from_incomplete', 'skip_files',             \n                             'reprocess_md',                                    \n                             'force_openrouter_paid', 'upload',                 \n                             'upload_types', 'force_upload',                    \n                             'num_workers', 'fb_ads',                           \n                             'report_generator', 'skip_ads',                    \n                             'skip_invalidate', 'weekly',                       \n                             'process_review_cases',                            \n                             'process_review_cases_legacy',                     \n                             'reprocess_failed', 'step_name',                   \n                             'input_source', 'output_location',                 \n                             'mistral_api_key', 'deepseek_api_key',             \n                             'openai_api_key', 'testing',                       \n                             'defer_image_processing', 'use_local',             \n                             'use_proxy', 'mobile_proxy',                       \n                             'render_html', 'airplane_mode',                    \n                             'default_date_range_days',                         \n                             'single_firm_date_range_days',                     \n                             'ignore_list_date_range_days',                     \n                             'session_refresh_interval',                        \n                             'skip_firms_updated_today',                        \n                             'shuffle_firm_order', 'max_ad_pages',              \n                             'api_retries', 'api_backoff_base',                 \n                             'payload_retries',                                 \n                             'oxylabs_num_proxies',                             \n                             'proxy_ban_duration',                              \n                             'max_proxy_failures',                              \n                             'rotate_proxy_between_firms',                      \n                             'rotate_proxy_per_page',                           \n                             'image_download_timeout',                          \n                             'temp_image_dir',                                  \n                             's3_ad_archive_prefix',                            \n                             's3_cdn_base_url', 'image_queue_dir',              \n                             'image_queue', 'disable_llava',                    \n                             'disable_gpt', 'disable_deepseek',                 \n                             'llava_model_name', 'llava_timeout',               \n                             'llava_temperature',                               \n                             'llava_num_gpu_layers',                            \n                             'llava_use_semaphore',                             \n                             'llava_semaphore_count', 'use_tqdm',               \n                             'max_concurrent_firms',                            \n                             'enable_concurrent_processing',                    \n                             'session_pool_size',                               \n                             'concurrent_batch_size',                           \n                             'bucket_name',                                     \n                             'processing_results_dir',                          \n                             'law_firm_data_dir', 'dynamodb',                   \n                             'fb_ad_archive_table_name',                        \n                             'fb_image_hash_table_name',                        \n                             'law_firms_table_name',                            \n                             'use_local_dynamodb',                              \n                             'local_dynamodb_port',                             \n                             'dynamodb_endpoint', 'ner_model_name',             \n                             'spacy_pipe_batch_size',                           \n                             'dynamodb_scan_workers',                           \n                             'ner_processing_workers',                          \n                             'text_fields', 'cluster_min_k',                    \n                             'cluster_max_k', 'cluster_k_step',                 \n                             'cluster_output_enabled',                          \n                             'dynamodb_update_workers',                         \n                             'disable_bandwidth_periodic_logging',              \n                             'verbose', 'max_retries',                          \n                             'fb_ad_categorizer',                               \n                             'vector_clusterer',                                \n                             'ignore_firms_file',                               \n                             'login_required_file',                             \n                             'deferred_processing_file',                        \n                             'feature_flags', 'performance',                    \n                             'monitoring', 'development',                       \n                             'cleanup']                                         \n                    WARNING  🔍 DEBUG MAIN.PY:                      main.py:2096\n                             params['run_parallel'] = True                      \n[07/13/25 15:10:00] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and data/20250713/logs/pacer.log                   \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             data/20250713/logs/pacer.log                       \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             data/20250713/logs/reports_services.log            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             data/20250713/logs/reports_services.log            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             data/20250713/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             data/20250713/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             data/20250713/logs/logging_config_modul            \n                             e.log                                              \n                    INFO     Logging re-configured for target date  main.py:2104\n                             07/13/25. Log dir: data/20250713/logs              \n                    INFO     --- Initializing Main Orchestration    main.py:2118\n                             for Single Date Run ---                            \n                    INFO     MainServiceFactory initialized   main_factory.py:27\n                             with config: Unknown config                        \n                    INFO     🚀 Using dependency-injector     main_factory.py:30\n                             framework                                          \n                    INFO     Entering MainServiceFactory     main_factory.py:112\n                             async context.                                     \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:72\n                             self.config.headless = True                        \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:73\n                             self.config.run_parallel = True                    \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:74\n                             config_dict['headless'] = True                     \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:75\n                             config_dict['run_parallel'] =                      \n                             True                                               \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:93\n                             config_dict['pacer'] =                             \n                             {'workflow': {}, 'browser':                        \n                             {'headless': True},                                \n                             'username_prod': 'gratefuldave',                   \n                             'password_prod':                                   \n                             'Sr2ket592521p!', 'headless':                      \n                             True, 'run_parallel': True,                        \n                             'timeout_ms': 60000}                               \n                    INFO     DI Container created and wired  main_factory.py:139\n                             successfully                                       \n                    INFO     MainOrchestrator initialized   component_base.py:92\n                    INFO     MainOrchestrator run started   component_base.py:92\n                    INFO     Scraping phase skipped by      component_base.py:92\n                             configuration                                      \n                    INFO     Post-processing phase skipped  component_base.py:92\n                             by configuration                                   \n                    INFO     Upload phase skipped by        component_base.py:92\n                             configuration                                      \n                    INFO     Facebook Ads processing phase  component_base.py:92\n                             initiated                                          \n                    INFO     Creating FbAdsOrchestrator via  main_factory.py:280\n                             DI Container...                                    \n                    INFO     Loaded 26 prompts              component_base.py:92\n                    WARNING  S3 storage not properly        component_base.py:99\n                             injected - S3 operations will                      \n                             be disabled                                        \n                    INFO     🔍 VALIDATION: Checking AI     component_base.py:92\n                             service requirements...                            \n                    INFO       - Available API key sources: component_base.py:92\n                             4/4                                                \n                    INFO       - AI services validation: ✅ component_base.py:92\n                             PASSED                                             \n                    INFO     FbAdsOrchestrator initialized  component_base.py:92\n                             with DI services                                   \n                    INFO     ✅ AI Orchestrator injected    component_base.py:92\n                             via DI                                             \n                    INFO     ✅ DeepSeek service injected   component_base.py:92\n                             via DI                                             \n                    INFO     ✅ Prompt Manager injected via component_base.py:92\n                             DI                                                 \n                    INFO     ✅ FbAdsOrchestrator created    main_factory.py:301\n                    INFO     Executing Facebook Ads tasks   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                             for session manager with                           \n                             periodic logging disabled                          \n                    INFO     Session manager initialized    component_base.py:92\n                             with use_proxy=True,                               \n                             mobile_proxy=False                                 \n                    INFO     Generating proxy list for      component_base.py:92\n                             RESIDENTIAL proxies                                \n                    INFO     Attempting to load credentials component_base.py:92\n                             for RESIDENTIAL proxy...                           \n                    INFO     Using specific RESIDENTIAL     component_base.py:92\n                             credentials (from config or                        \n                             env).                                              \n                    INFO     Using username:                component_base.py:92\n                             lexgenius20250612_wzykM for                        \n                             RESIDENTIAL proxy list                             \n                             generation.                                        \n                    DEBUG    Generating RESIDENTIAL proxy   component_base.py:85\n                             list for user:                                     \n                             lexgenius20250612_wzykM                            \n                    INFO     Using configured proxy count:  component_base.py:92\n                             10000 RESIDENTIAL proxies                          \n                    DEBUG    Sample RESIDENTIAL proxy       component_base.py:85\n                             format: http@...                                   \n                    INFO     Generated 10000 RESIDENTIAL    component_base.py:92\n                             proxies with random session                        \n                             IDs.                                               \n                    INFO     Generated and shuffled 10000   component_base.py:92\n                             proxy URLs.                                        \n                    INFO     Successfully generated 10000   component_base.py:92\n                             proxies                                            \n                    DEBUG    Set randomized headers based   component_base.py:85\n                             on profile (X11). UA: ...L,                        \n                             like Gecko)                                        \n                             Chrome/118.0.5100.175                              \n                             Safari/577.36                                      \n                    INFO     Setting up proxy from list of  component_base.py:92\n                             10000 proxies                                      \n                    INFO     Setting proxy index 0:         component_base.py:92\n                             <EMAIL>.i                     \n                             o:7777                                             \n[07/13/25 15:10:01] INFO     Current proxy reports IP:      component_base.py:92\n                             ************                                       \n                    INFO     Successfully set proxy:        component_base.py:92\n                             pr.oxylabs.io:7777                                 \n                    DEBUG    Using session manager's        component_base.py:85\n                             bandwidth logger instance                          \n                    INFO     Using injected                 component_base.py:92\n                             FBImageHashManager                                 \n                    DEBUG    Using injected bandwidth       component_base.py:85\n                             logger instance                                    \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue.db                                \n                    INFO     FBAdArchiveManager initialized component_base.py:92\n                             for table                                          \n                             '[cyan]FBAdArchive[/cyan]'                         \n                             (local=[yellow]False[/yellow])                     \n                    INFO     Using spaCy model:             component_base.py:92\n                             [bright_blue]en_core_web_sm[/b                     \n                             right_blue]                                        \n                    INFO     Text fields for NER:           component_base.py:92\n                             [magenta]['Title',                                 \n                             'Body'][/magenta]                                  \n                    INFO     spaCy nlp.pipe batch size:     component_base.py:92\n                             [blue]1000[/blue]                                  \n                    INFO     DynamoDB scan workers:         component_base.py:92\n                             [blue]16[/blue]                                    \n                    INFO     NER processing workers:        component_base.py:92\n                             [blue]16[/blue]                                    \n                    INFO     Clustering based on NER        component_base.py:92\n                             results is                                         \n                             [green]enabled[/green].                            \n                    INFO     Clustering k-range: [2-10],    component_base.py:92\n                             step: 1                                            \n                    WARNING  Campaign slip terms file not   component_base.py:99\n                             found at                                           \n                             /Users/<USER>/PycharmPro                     \n                             jects/lexgenius/src/config/dat                     \n                             a/fb_ads/campaign_skip_terms.j                     \n                             son or                                             \n                             /Users/<USER>/PycharmPro                     \n                             jects/lexgenius/src/src/config                     \n                             /fb_ad_categorizer/campaign_sk                     \n                             ip_terms.json                                      \n╭──────────────────────────────────────────────────────────────────────────────╮\n│ FBAdCategorizer Initializing                                                 │\n╰──────────────────────────────────────────────────────────────────────────────╯\n                    INFO     Initializing                   component_base.py:92\n                             FBAdCategorizer...                                 \n                    INFO     Local Mode: False              component_base.py:92\n                    INFO     FBArchive repository injected  component_base.py:92\n                             via DI (local=False)                               \n                    INFO     DeepSeekService injected. GPT  component_base.py:92\n                             client also injected (if                           \n                             provided).                                         \n                    INFO     Attempting to load ignored     component_base.py:92\n                             firms                                              \n                    INFO     Successfully loaded firms from component_base.py:92\n                             ignore file                                        \n                    INFO     ✅ Image handler available for component_base.py:92\n                             FB ads processing service                          \n                    INFO     ✅ LLM availability validated  component_base.py:92\n                             for FB ads processor                               \n                             (DEPRECATED)                                       \n                    WARNING  AdProcessor class is           component_base.py:99\n                             DEPRECATED and its logic has                       \n                             moved to JobRunnerService.                         \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue/image_queue.d                     \n                             b                                                  \n                    ERROR    ❌ CRITICAL: No AI job_orchestration_service.py:312\n                             orchestrator                                       \n                             available in job                                   \n                             runner for FB ads                                  \n                             processing                                         \n                    INFO     📌 Tracked aiohttp session  resource_tracker.py:127\n                             13339313040 created at                             \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137 in                              \n                             execute                                            \n                    INFO     ✅ Untracked aiohttp        resource_tracker.py:141\n                             session 13339313040 (was                           \n                             created at                                         \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137)                                \n                    INFO     Exiting MainServiceFactory      main_factory.py:149\n                             async context.                                     \n                    INFO     DI Container cleaned up         main_factory.py:158\n                             successfully.                                      \nExiting program (Code: 1).\n                    WARNING  Exiting program (Code: 1).             main.py:2366\n                    INFO     Entering final cleanup phase...        main.py:2376\n                    INFO     Closing event loop...                  main.py:2521\n                    INFO     Event loop closed.                     main.py:2523\nMain script exit.\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    ERROR    Task exception was never        base_events.py:1785\n                             retrieved                                          \n                             future: <Task finished                             \n                             name='Task-1' coro=<wait_for()                     \n                             done, defined at                                   \n                             /Users/<USER>/miniconda3/                    \n                             envs/lexgenius/lib/python3.11/a                    \n                             syncio/tasks.py:436>                               \n                             exception=SystemExit(1)>                           \n                             ╭─ Traceback (most recent cal─╮                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2592 in <module>        │                    \n                             │                             │                    \n                             │   2589 │   │   │   print(\"W │                    \n                             │   2590 │   │                │                    \n                             │   2591 │   │   # Call safe_ │                    \n                             │ ❱ 2592 │   │   exit_code =  │                    \n                             │   2593 │                    │                    \n                             │   2594 │   except Exception │                    \n                             │   2595 │   │   # Catch erro │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2386 in safe_run_main   │                    \n                             │                             │                    \n                             │   2383 │   │   │   │   │    │                    \n                             │   2384 │   │   │   │   │    │                    \n                             │   2385 │   │   │   │   │    │                    \n                             │        retrieved) is raised │                    \n                             │ ❱ 2386 │   │   │   │   │    │                    \n                             │   2387 │   │   │   │   │    │                    \n                             │   2388 │   │   │   │   │    │                    \n                             │   2389 │   │   │   │   │    │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:641 in                   │                    \n                             │ run_until_complete          │                    \n                             │                             │                    \n                             │    638 │   │                │                    \n                             │    639 │   │   future.add_d │                    \n                             │    640 │   │   try:         │                    \n                             │ ❱  641 │   │   │   self.run │                    \n                             │    642 │   │   except:      │                    \n                             │    643 │   │   │   if new_t │                    \n                             │    644 │   │   │   │   # Th │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:608 in run_forever       │                    \n                             │                             │                    \n                             │    605 │   │   │            │                    \n                             │    606 │   │   │   events._ │                    \n                             │    607 │   │   │   while Tr │                    \n                             │ ❱  608 │   │   │   │   self │                    \n                             │    609 │   │   │   │   if s │                    \n                             │    610 │   │   │   │   │    │                    \n                             │    611 │   │   finally:     │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:1936 in _run_once        │                    \n                             │                             │                    \n                             │   1933 │   │   │   │   fina │                    \n                             │   1934 │   │   │   │   │    │                    \n                             │   1935 │   │   │   else:    │                    \n                             │ ❱ 1936 │   │   │   │   hand │                    \n                             │   1937 │   │   handle = Non │                    \n                             │   1938 │                    │                    \n                             │   1939 │   def _set_corouti │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/events.py:84 │                    \n                             │  in _run                    │                    \n                             │                             │                    \n                             │    81 │                     │                    \n                             │    82 │   def _run(self):   │                    \n                             │    83 │   │   try:          │                    \n                             │ ❱  84 │   │   │   self._con │                    \n                             │    85 │   │   except (Syste │                    \n                             │    86 │   │   │   raise     │                    \n                             │    87 │   │   except BaseEx │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/tasks.py:489 │                    \n                             │  in wait_for                │                    \n                             │                             │                    \n                             │   486 │   │   │   │   raise │                    \n                             │   487 │   │                 │                    \n                             │   488 │   │   if fut.done() │                    \n                             │ ❱ 489 │   │   │   return fu │                    \n                             │   490 │   │   else:         │                    \n                             │   491 │   │   │   fut.remov │                    \n                             │   492 │   │   │   # We must │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2124 in main            │                    \n                             │                             │                    \n                             │   2121 │   │   │   │   main │                    \n                             │   2122 │   │   │   │   │    │                    \n                             │   2123 │   │   │   │   )    │                    \n                             │ ❱ 2124 │   │   │   │   awai │                    \n                             │   2125 │   │   │   logger.i │                    \n                             │   2126 │   │   except Excep │                    \n                             │   2127 │   │   │   logger.c │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/orchestration/main_orc │                    \n                             │ hestrator.py:103 in run     │                    \n                             │                             │                    \n                             │   100 │   │   │   │   │   r │                    \n                             │   101 │   │   │   │   self. │                    \n                             │   102 │   │   │   │   fb_ad │                    \n                             │ ❱ 103 │   │   │   │   await │                    \n                             │   104 │   │   │   │   self. │                    \n                             │   105 │   │   │   else:     │                    \n                             │   106 │   │   │   │   self. │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/orchestration/fb_ads_o │                    \n                             │ rchestrator.py:188 in       │                    \n                             │ execute                     │                    \n                             │                             │                    \n                             │   185 │   │   │   │   fb_ad │                    \n                             │   186 │   │   │   │         │                    \n                             │   187 │   │   │   │   # Get │                    \n                             │ ❱ 188 │   │   │   │   fb_or │                    \n                             │   189 │   │   │   │   await │                    \n                             │   190 │   │   │   self.log_ │                    \n                             │   191 │   │   except TypeEr │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:573               │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__provide_keyword_args:4 │                    \n                             │ 41                          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__get_value:361          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:573               │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__provide_keyword_args:4 │                    \n                             │ 41                          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__get_value:361          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:604               │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/fb_ads/jobs/job_orches │                    \n                             │ tration_service.py:49 in    │                    \n                             │ __init__                    │                    \n                             │                             │                    \n                             │    46 │   │   self.failed_f │                    \n                             │    47 │   │                 │                    \n                             │    48 │   │   # CRITICAL: V │                    \n                             │ ❱  49 │   │   self._validat │                    \n                             │    50 │   │   self.num_work │                    \n                             │       concurrent jobs       │                    \n                             │    51 │   │   if self.num_w │                    \n                             │    52 │   │   │   self._log │                    \n                             │       Setting to 1.\")       │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/fb_ads/jobs/job_orches │                    \n                             │ tration_service.py:314 in   │                    \n                             │ _validate_llm_availability  │                    \n                             │                             │                    \n                             │   311 │   │   │   if self._ │                    \n                             │   312 │   │   │   │   self. │                    \n                             │       runner for FB ads pro │                    \n                             │   313 │   │   │   print(\"❌ │                    \n                             │       processing\", file=sys │                    \n                             │ ❱ 314 │   │   │   sys.exit( │                    \n                             │   315 │   │                 │                    \n                             │   316 │   │   # Check if AI │                    \n                             │   317 │   │   dependencies  │                    \n                             ╰─────────────────────────────╯                    \n                             SystemExit: 1                                      \nAll pipeline steps completed.\n❌ CRITICAL: No AI orchestrator available in job runner for FB ads processing\n", "clean_output": "Running pipeline step: fb_ads.yml with config /Users/<USER>/PycharmProjects/lexgenius/config/fb_ads.yml\nStarting main.py with Python 3.11.13 | packaged by conda-forge | (main, Jun  4 2025, 14:52:34) [Clang 18.1.8 ]\nSet multiprocessing start method to 'spawn'.\n[07/13/25 15:10:00] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and                                                \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/pacer.log                \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/pacer.log                \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250713/logs/logging_confi            \n                             g_module.log                                       \n                    INFO     Initial logging setup complete. Log    main.py:1942\n                             directory:                                         \n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data/20250713/logs                         \n                    INFO     Base data directory:                   main.py:1945\n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data                                       \n                    INFO     No end_date specified, defaulting to   main.py:2034\n                             target date: 07/13/25                              \n                    INFO     Preparing for single date run:         main.py:2083\n                             07/13/25                                           \n                    INFO     DEBUG: Merging params into config     loader.py:170\n                             data for fb_ads                                    \n                    INFO     DEBUG: process_single_court in YAML:  loader.py:171\n                             NOT_SET                                            \n                    INFO     DEBUG: process_single_court in        loader.py:172\n                             params: []                                         \n                    INFO     DEBUG: process_single_court after     loader.py:174\n                             merge: []                                          \n                    WARNING  🔍 DEBUG MAIN.PY: Pydantic             main.py:2092\n                             final_config.run_parallel = True                   \n                    WARNING  🔍 DEBUG MAIN.PY: Pydantic             main.py:2093\n                             final_config.headless = True                       \n                    WARNING  🔍 DEBUG MAIN.PY: config_name =        main.py:2094\n                             fb_ads, params keys = ['date',                     \n                             'start_date', 'end_date',                          \n                             'llm_provider', 'scraper', 'headless',             \n                             'reset_chrome', 'docket_num',                      \n                             'process_single_court',                            \n                             'docket_list_for_orchestrator',                    \n                             'skip_courts', 'start_after_court',                \n                             'start_at_court', 'html_only',                     \n                             'run_parallel', 'post_process',                    \n                             'reprocess_files',                                 \n                             'start_from_incomplete', 'skip_files',             \n                             'reprocess_md',                                    \n                             'force_openrouter_paid', 'upload',                 \n                             'upload_types', 'force_upload',                    \n                             'num_workers', 'fb_ads',                           \n                             'report_generator', 'skip_ads',                    \n                             'skip_invalidate', 'weekly',                       \n                             'process_review_cases',                            \n                             'process_review_cases_legacy',                     \n                             'reprocess_failed', 'step_name',                   \n                             'input_source', 'output_location',                 \n                             'mistral_api_key', 'deepseek_api_key',             \n                             'openai_api_key', 'testing',                       \n                             'defer_image_processing', 'use_local',             \n                             'use_proxy', 'mobile_proxy',                       \n                             'render_html', 'airplane_mode',                    \n                             'default_date_range_days',                         \n                             'single_firm_date_range_days',                     \n                             'ignore_list_date_range_days',                     \n                             'session_refresh_interval',                        \n                             'skip_firms_updated_today',                        \n                             'shuffle_firm_order', 'max_ad_pages',              \n                             'api_retries', 'api_backoff_base',                 \n                             'payload_retries',                                 \n                             'oxylabs_num_proxies',                             \n                             'proxy_ban_duration',                              \n                             'max_proxy_failures',                              \n                             'rotate_proxy_between_firms',                      \n                             'rotate_proxy_per_page',                           \n                             'image_download_timeout',                          \n                             'temp_image_dir',                                  \n                             's3_ad_archive_prefix',                            \n                             's3_cdn_base_url', 'image_queue_dir',              \n                             'image_queue', 'disable_llava',                    \n                             'disable_gpt', 'disable_deepseek',                 \n                             'llava_model_name', 'llava_timeout',               \n                             'llava_temperature',                               \n                             'llava_num_gpu_layers',                            \n                             'llava_use_semaphore',                             \n                             'llava_semaphore_count', 'use_tqdm',               \n                             'max_concurrent_firms',                            \n                             'enable_concurrent_processing',                    \n                             'session_pool_size',                               \n                             'concurrent_batch_size',                           \n                             'bucket_name',                                     \n                             'processing_results_dir',                          \n                             'law_firm_data_dir', 'dynamodb',                   \n                             'fb_ad_archive_table_name',                        \n                             'fb_image_hash_table_name',                        \n                             'law_firms_table_name',                            \n                             'use_local_dynamodb',                              \n                             'local_dynamodb_port',                             \n                             'dynamodb_endpoint', 'ner_model_name',             \n                             'spacy_pipe_batch_size',                           \n                             'dynamodb_scan_workers',                           \n                             'ner_processing_workers',                          \n                             'text_fields', 'cluster_min_k',                    \n                             'cluster_max_k', 'cluster_k_step',                 \n                             'cluster_output_enabled',                          \n                             'dynamodb_update_workers',                         \n                             'disable_bandwidth_periodic_logging',              \n                             'verbose', 'max_retries',                          \n                             'fb_ad_categorizer',                               \n                             'vector_clusterer',                                \n                             'ignore_firms_file',                               \n                             'login_required_file',                             \n                             'deferred_processing_file',                        \n                             'feature_flags', 'performance',                    \n                             'monitoring', 'development',                       \n                             'cleanup']                                         \n                    WARNING  🔍 DEBUG MAIN.PY:                      main.py:2096\n                             params['run_parallel'] = True                      \n[07/13/25 15:10:00] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and data/20250713/logs/pacer.log                   \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             data/20250713/logs/pacer.log                       \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             data/20250713/logs/reports_services.log            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             data/20250713/logs/reports_services.log            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             data/20250713/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             data/20250713/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             data/20250713/logs/logging_config_modul            \n                             e.log                                              \n                    INFO     Logging re-configured for target date  main.py:2104\n                             07/13/25. Log dir: data/20250713/logs              \n                    INFO     --- Initializing Main Orchestration    main.py:2118\n                             for Single Date Run ---                            \n                    INFO     MainServiceFactory initialized   main_factory.py:27\n                             with config: Unknown config                        \n                    INFO     🚀 Using dependency-injector     main_factory.py:30\n                             framework                                          \n                    INFO     Entering MainServiceFactory     main_factory.py:112\n                             async context.                                     \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:72\n                             self.config.headless = True                        \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:73\n                             self.config.run_parallel = True                    \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:74\n                             config_dict['headless'] = True                     \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:75\n                             config_dict['run_parallel'] =                      \n                             True                                               \n                    WARNING  🔍 DEBUG FACTORY:                main_factory.py:93\n                             config_dict['pacer'] =                             \n                             {'workflow': {}, 'browser':                        \n                             {'headless': True},                                \n                             'username_prod': 'gratefuldave',                   \n                             'password_prod':                                   \n                             'Sr2ket592521p!', 'headless':                      \n                             True, 'run_parallel': True,                        \n                             'timeout_ms': 60000}                               \n                    INFO     DI Container created and wired  main_factory.py:139\n                             successfully                                       \n                    INFO     MainOrchestrator initialized   component_base.py:92\n                    INFO     MainOrchestrator run started   component_base.py:92\n                    INFO     Scraping phase skipped by      component_base.py:92\n                             configuration                                      \n                    INFO     Post-processing phase skipped  component_base.py:92\n                             by configuration                                   \n                    INFO     Upload phase skipped by        component_base.py:92\n                             configuration                                      \n                    INFO     Facebook Ads processing phase  component_base.py:92\n                             initiated                                          \n                    INFO     Creating FbAdsOrchestrator via  main_factory.py:280\n                             DI Container...                                    \n                    INFO     Loaded 26 prompts              component_base.py:92\n                    WARNING  S3 storage not properly        component_base.py:99\n                             injected - S3 operations will                      \n                             be disabled                                        \n                    INFO     🔍 VALIDATION: Checking AI     component_base.py:92\n                             service requirements...                            \n                    INFO       - Available API key sources: component_base.py:92\n                             4/4                                                \n                    INFO       - AI services validation: ✅ component_base.py:92\n                             PASSED                                             \n                    INFO     FbAdsOrchestrator initialized  component_base.py:92\n                             with DI services                                   \n                    INFO     ✅ AI Orchestrator injected    component_base.py:92\n                             via DI                                             \n                    INFO     ✅ DeepSeek service injected   component_base.py:92\n                             via DI                                             \n                    INFO     ✅ Prompt Manager injected via component_base.py:92\n                             DI                                                 \n                    INFO     ✅ FbAdsOrchestrator created    main_factory.py:301\n                    INFO     Executing Facebook Ads tasks   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                             for session manager with                           \n                             periodic logging disabled                          \n                    INFO     Session manager initialized    component_base.py:92\n                             with use_proxy=True,                               \n                             mobile_proxy=False                                 \n                    INFO     Generating proxy list for      component_base.py:92\n                             RESIDENTIAL proxies                                \n                    INFO     Attempting to load credentials component_base.py:92\n                             for RESIDENTIAL proxy...                           \n                    INFO     Using specific RESIDENTIAL     component_base.py:92\n                             credentials (from config or                        \n                             env).                                              \n                    INFO     Using username:                component_base.py:92\n                             lexgenius20250612_wzykM for                        \n                             RESIDENTIAL proxy list                             \n                             generation.                                        \n                    DEBUG    Generating RESIDENTIAL proxy   component_base.py:85\n                             list for user:                                     \n                             lexgenius20250612_wzykM                            \n                    INFO     Using configured proxy count:  component_base.py:92\n                             10000 RESIDENTIAL proxies                          \n                    DEBUG    Sample RESIDENTIAL proxy       component_base.py:85\n                             format: http@...                                   \n                    INFO     Generated 10000 RESIDENTIAL    component_base.py:92\n                             proxies with random session                        \n                             IDs.                                               \n                    INFO     Generated and shuffled 10000   component_base.py:92\n                             proxy URLs.                                        \n                    INFO     Successfully generated 10000   component_base.py:92\n                             proxies                                            \n                    DEBUG    Set randomized headers based   component_base.py:85\n                             on profile (X11). UA: ...L,                        \n                             like Gecko)                                        \n                             Chrome/118.0.5100.175                              \n                             Safari/577.36                                      \n                    INFO     Setting up proxy from list of  component_base.py:92\n                             10000 proxies                                      \n                    INFO     Setting proxy index 0:         component_base.py:92\n                             <EMAIL>.i                     \n                             o:7777                                             \n[07/13/25 15:10:01] INFO     Current proxy reports IP:      component_base.py:92\n                             ************                                       \n                    INFO     Successfully set proxy:        component_base.py:92\n                             pr.oxylabs.io:7777                                 \n                    DEBUG    Using session manager's        component_base.py:85\n                             bandwidth logger instance                          \n                    INFO     Using injected                 component_base.py:92\n                             FBImageHashManager                                 \n                    DEBUG    Using injected bandwidth       component_base.py:85\n                             logger instance                                    \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue.db                                \n                    INFO     FBAdArchiveManager initialized component_base.py:92\n                             for table                                          \n                             '[cyan]FBAdArchive[/cyan]'                         \n                             (local=[yellow]False[/yellow])                     \n                    INFO     Using spaCy model:             component_base.py:92\n                             [bright_blue]en_core_web_sm[/b                     \n                             right_blue]                                        \n                    INFO     Text fields for NER:           component_base.py:92\n                             [magenta]['Title',                                 \n                             'Body'][/magenta]                                  \n                    INFO     spaCy nlp.pipe batch size:     component_base.py:92\n                             [blue]1000[/blue]                                  \n                    INFO     DynamoDB scan workers:         component_base.py:92\n                             [blue]16[/blue]                                    \n                    INFO     NER processing workers:        component_base.py:92\n                             [blue]16[/blue]                                    \n                    INFO     Clustering based on NER        component_base.py:92\n                             results is                                         \n                             [green]enabled[/green].                            \n                    INFO     Clustering k-range: [2-10],    component_base.py:92\n                             step: 1                                            \n                    WARNING  Campaign slip terms file not   component_base.py:99\n                             found at                                           \n                             /Users/<USER>/PycharmPro                     \n                             jects/lexgenius/src/config/dat                     \n                             a/fb_ads/campaign_skip_terms.j                     \n                             son or                                             \n                             /Users/<USER>/PycharmPro                     \n                             jects/lexgenius/src/src/config                     \n                             /fb_ad_categorizer/campaign_sk                     \n                             ip_terms.json                                      \n╭──────────────────────────────────────────────────────────────────────────────╮\n│ FBAdCategorizer Initializing                                                 │\n╰──────────────────────────────────────────────────────────────────────────────╯\n                    INFO     Initializing                   component_base.py:92\n                             FBAdCategorizer...                                 \n                    INFO     Local Mode: False              component_base.py:92\n                    INFO     FBArchive repository injected  component_base.py:92\n                             via DI (local=False)                               \n                    INFO     DeepSeekService injected. GPT  component_base.py:92\n                             client also injected (if                           \n                             provided).                                         \n                    INFO     Attempting to load ignored     component_base.py:92\n                             firms                                              \n                    INFO     Successfully loaded firms from component_base.py:92\n                             ignore file                                        \n                    INFO     ✅ Image handler available for component_base.py:92\n                             FB ads processing service                          \n                    INFO     ✅ LLM availability validated  component_base.py:92\n                             for FB ads processor                               \n                             (DEPRECATED)                                       \n                    WARNING  AdProcessor class is           component_base.py:99\n                             DEPRECATED and its logic has                       \n                             moved to JobRunnerService.                         \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue/image_queue.d                     \n                             b                                                  \n                    ERROR    ❌ CRITICAL: No AI job_orchestration_service.py:312\n                             orchestrator                                       \n                             available in job                                   \n                             runner for FB ads                                  \n                             processing                                         \n                    INFO     📌 Tracked aiohttp session  resource_tracker.py:127\n                             13339313040 created at                             \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137 in                              \n                             execute                                            \n                    INFO     ✅ Untracked aiohttp        resource_tracker.py:141\n                             session 13339313040 (was                           \n                             created at                                         \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137)                                \n                    INFO     Exiting MainServiceFactory      main_factory.py:149\n                             async context.                                     \n                    INFO     DI Container cleaned up         main_factory.py:158\n                             successfully.                                      \nExiting program (Code: 1).\n                    WARNING  Exiting program (Code: 1).             main.py:2366\n                    INFO     Entering final cleanup phase...        main.py:2376\n                    INFO     Closing event loop...                  main.py:2521\n                    INFO     Event loop closed.                     main.py:2523\nMain script exit.\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    ERROR    Task exception was never        base_events.py:1785\n                             retrieved                                          \n                             future: <Task finished                             \n                             name='Task-1' coro=<wait_for()                     \n                             done, defined at                                   \n                             /Users/<USER>/miniconda3/                    \n                             envs/lexgenius/lib/python3.11/a                    \n                             syncio/tasks.py:436>                               \n                             exception=SystemExit(1)>                           \n                             ╭─ Traceback (most recent cal─╮                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2592 in <module>        │                    \n                             │                             │                    \n                             │   2589 │   │   │   print(\"W │                    \n                             │   2590 │   │                │                    \n                             │   2591 │   │   # Call safe_ │                    \n                             │ ❱ 2592 │   │   exit_code =  │                    \n                             │   2593 │                    │                    \n                             │   2594 │   except Exception │                    \n                             │   2595 │   │   # Catch erro │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2386 in safe_run_main   │                    \n                             │                             │                    \n                             │   2383 │   │   │   │   │    │                    \n                             │   2384 │   │   │   │   │    │                    \n                             │   2385 │   │   │   │   │    │                    \n                             │        retrieved) is raised │                    \n                             │ ❱ 2386 │   │   │   │   │    │                    \n                             │   2387 │   │   │   │   │    │                    \n                             │   2388 │   │   │   │   │    │                    \n                             │   2389 │   │   │   │   │    │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:641 in                   │                    \n                             │ run_until_complete          │                    \n                             │                             │                    \n                             │    638 │   │                │                    \n                             │    639 │   │   future.add_d │                    \n                             │    640 │   │   try:         │                    \n                             │ ❱  641 │   │   │   self.run │                    \n                             │    642 │   │   except:      │                    \n                             │    643 │   │   │   if new_t │                    \n                             │    644 │   │   │   │   # Th │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:608 in run_forever       │                    \n                             │                             │                    \n                             │    605 │   │   │            │                    \n                             │    606 │   │   │   events._ │                    \n                             │    607 │   │   │   while Tr │                    \n                             │ ❱  608 │   │   │   │   self │                    \n                             │    609 │   │   │   │   if s │                    \n                             │    610 │   │   │   │   │    │                    \n                             │    611 │   │   finally:     │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/base_events. │                    \n                             │ py:1936 in _run_once        │                    \n                             │                             │                    \n                             │   1933 │   │   │   │   fina │                    \n                             │   1934 │   │   │   │   │    │                    \n                             │   1935 │   │   │   else:    │                    \n                             │ ❱ 1936 │   │   │   │   hand │                    \n                             │   1937 │   │   handle = Non │                    \n                             │   1938 │                    │                    \n                             │   1939 │   def _set_corouti │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/events.py:84 │                    \n                             │  in _run                    │                    \n                             │                             │                    \n                             │    81 │                     │                    \n                             │    82 │   def _run(self):   │                    \n                             │    83 │   │   try:          │                    \n                             │ ❱  84 │   │   │   self._con │                    \n                             │    85 │   │   except (Syste │                    \n                             │    86 │   │   │   raise     │                    \n                             │    87 │   │   except BaseEx │                    \n                             │                             │                    \n                             │ /Users/<USER>/minicon │                    \n                             │ da3/envs/lexgenius/lib/pyth │                    \n                             │ on3.11/asyncio/tasks.py:489 │                    \n                             │  in wait_for                │                    \n                             │                             │                    \n                             │   486 │   │   │   │   raise │                    \n                             │   487 │   │                 │                    \n                             │   488 │   │   if fut.done() │                    \n                             │ ❱ 489 │   │   │   return fu │                    \n                             │   490 │   │   else:         │                    \n                             │   491 │   │   │   fut.remov │                    \n                             │   492 │   │   │   # We must │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/main │                    \n                             │ .py:2124 in main            │                    \n                             │                             │                    \n                             │   2121 │   │   │   │   main │                    \n                             │   2122 │   │   │   │   │    │                    \n                             │   2123 │   │   │   │   )    │                    \n                             │ ❱ 2124 │   │   │   │   awai │                    \n                             │   2125 │   │   │   logger.i │                    \n                             │   2126 │   │   except Excep │                    \n                             │   2127 │   │   │   logger.c │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/orchestration/main_orc │                    \n                             │ hestrator.py:103 in run     │                    \n                             │                             │                    \n                             │   100 │   │   │   │   │   r │                    \n                             │   101 │   │   │   │   self. │                    \n                             │   102 │   │   │   │   fb_ad │                    \n                             │ ❱ 103 │   │   │   │   await │                    \n                             │   104 │   │   │   │   self. │                    \n                             │   105 │   │   │   else:     │                    \n                             │   106 │   │   │   │   self. │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/orchestration/fb_ads_o │                    \n                             │ rchestrator.py:188 in       │                    \n                             │ execute                     │                    \n                             │                             │                    \n                             │   185 │   │   │   │   fb_ad │                    \n                             │   186 │   │   │   │         │                    \n                             │   187 │   │   │   │   # Get │                    \n                             │ ❱ 188 │   │   │   │   fb_or │                    \n                             │   189 │   │   │   │   await │                    \n                             │   190 │   │   │   self.log_ │                    \n                             │   191 │   │   except TypeEr │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:573               │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__provide_keyword_args:4 │                    \n                             │ 41                          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__get_value:361          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:573               │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__provide_keyword_args:4 │                    \n                             │ 41                          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__get_value:361          │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Provider.__call__:231    │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.Singleton._provide:3021  │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__factory_call:646       │                    \n                             │                             │                    \n                             │ in                          │                    \n                             │ dependency_injector.provide │                    \n                             │ rs.__call:604               │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/fb_ads/jobs/job_orches │                    \n                             │ tration_service.py:49 in    │                    \n                             │ __init__                    │                    \n                             │                             │                    \n                             │    46 │   │   self.failed_f │                    \n                             │    47 │   │                 │                    \n                             │    48 │   │   # CRITICAL: V │                    \n                             │ ❱  49 │   │   self._validat │                    \n                             │    50 │   │   self.num_work │                    \n                             │       concurrent jobs       │                    \n                             │    51 │   │   if self.num_w │                    \n                             │    52 │   │   │   self._log │                    \n                             │       Setting to 1.\")       │                    \n                             │                             │                    \n                             │ /Users/<USER>/Pycharm │                    \n                             │ Projects/lexgenius/src/serv │                    \n                             │ ices/fb_ads/jobs/job_orches │                    \n                             │ tration_service.py:314 in   │                    \n                             │ _validate_llm_availability  │                    \n                             │                             │                    \n                             │   311 │   │   │   if self._ │                    \n                             │   312 │   │   │   │   self. │                    \n                             │       runner for FB ads pro │                    \n                             │   313 │   │   │   print(\"❌ │                    \n                             │       processing\", file=sys │                    \n                             │ ❱ 314 │   │   │   sys.exit( │                    \n                             │   315 │   │                 │                    \n                             │   316 │   │   # Check if AI │                    \n                             │   317 │   │   dependencies  │                    \n                             ╰─────────────────────────────╯                    \n                             SystemExit: 1                                      \nAll pipeline steps completed.\n❌ CRITICAL: No AI orchestrator available in job runner for FB ads processing\n", "timestamp": "2025-07-13T15:10:02.038749"}}], "last_successful_run": null, "claude_fixes_applied": []}