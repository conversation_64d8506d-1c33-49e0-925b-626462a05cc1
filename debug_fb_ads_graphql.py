#!/usr/bin/env python3
"""
Debug script to investigate why GraphQL is not being used for Facebook ads extraction.
Tests the entire flow from configuration to ad extraction.
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config_models.loader import ConfigLoader
from src.services.fb_ads.logging_setup import FBAdsLogger
from src.services.fb_ads.feature_flag_controller import FeatureFlagController
from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
from src.services.fb_ads.jobs.job_runner_service import JobRunnerService
from src.services.fb_ads.jobs.job_models import ProcessFirmJob
from src.services.fb_ads.graphql_response_parser import GraphQLResponseParser


async def main():
    """Run debug test for GraphQL flow."""
    print("=== Facebook Ads GraphQL Debug Test ===\n")
    
    # Load configuration directly from YAML
    config_path = "config/workflows/fb_ads.yml"
    import yaml
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Add computed fields
    config['iso_date'] = datetime.now().strftime("%Y%m%d")
    config['DATA_DIR'] = config.get('DATA_DIR', os.path.join(os.getcwd(), 'data'))
    
    print(f"✅ Loaded config from: {config_path}")
    
    # Check GraphQL configuration
    print("\n📋 GraphQL Configuration:")
    print(f"   use_graphql_capture: {config.get('use_graphql_capture', False)}")
    print(f"   feature_flags.graphql_enabled: {config.get('feature_flags', {}).get('graphql_enabled', False)}")
    print(f"   feature_flags.use_camoufox: {config.get('feature_flags', {}).get('use_camoufox', False)}")
    
    # Setup logging
    logger = FBAdsLogger.setup_logging(config)
    print(f"\n✅ Logging configured to: data/{config['iso_date']}/logs/fb_ads.log")
    
    # Test feature flag controller
    print("\n🎛️ Testing Feature Flag Controller:")
    flag_controller = FeatureFlagController(config, logger)
    test_firm_id = "313942829424"  # Morgan & Morgan
    
    should_use_camoufox = flag_controller.should_use_camoufox(test_firm_id)
    implementation_name = flag_controller.get_implementation_name(test_firm_id)
    print(f"   should_use_camoufox({test_firm_id}): {should_use_camoufox}")
    print(f"   implementation_name: {implementation_name}")
    
    # Test session manager factory
    print("\n🏭 Testing Session Manager Factory:")
    try:
        # Create minimal dependencies
        dependencies = {
            'fingerprint_manager': None,
            'proxy_manager': None,
            'law_firms_repository': None
        }
        
        session_manager = SessionManagerFactory.create(
            config=config,
            logger=logger,
            firm_id=test_firm_id,
            **dependencies
        )
        
        session_type = type(session_manager).__name__
        has_graphql = hasattr(session_manager, 'capture_graphql_responses')
        print(f"   Created session manager: {session_type}")
        print(f"   Has capture_graphql_responses: {has_graphql}")
        
        # Test GraphQL parser
        print("\n📊 Testing GraphQL Parser:")
        graphql_parser = GraphQLResponseParser(logger=logger, config=config)
        print(f"   GraphQL parser created: {type(graphql_parser).__name__}")
        
        # Test job runner service
        print("\n🏃 Testing Job Runner Service:")
        job_runner = JobRunnerService(
            config=config,
            logger=logger,
            ai_orchestrator=None,
            graphql_parser=graphql_parser
        )
        
        # Check if GraphQL would be used
        should_use_graphql = job_runner._should_use_graphql(session_manager)
        print(f"   _should_use_graphql result: {should_use_graphql}")
        
        # Create a test job
        print("\n📝 Creating test job for Morgan & Morgan:")
        test_job = ProcessFirmJob(
            job_id=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            firm_id=test_firm_id,
            firm_name="Morgan & Morgan",
            firm_data={"ID": test_firm_id, "Name": "Morgan & Morgan"},
            config_snapshot=config,
            current_process_date=config.get('iso_date', datetime.now().strftime('%Y%m%d'))
        )
        print(f"   Job created: {test_job.job_id}")
        
        # Test GraphQL response parsing
        print("\n🧪 Testing GraphQL Response Parser:")
        sample_response_path = "errors/morgan_morgan_response.json"
        if os.path.exists(sample_response_path):
            with open(sample_response_path, 'r') as f:
                sample_data = json.load(f)
            
            print(f"   📄 Loaded sample JSON response from: {sample_response_path}")
            
            # Parse the JSON response
            print("\n   🔍 Parsing JSON response...")
            try:
                parsed_result = await graphql_parser.parse_json_response(sample_data)
                
                ads_extracted = parsed_result.get('ads', [])
                print(f"\n   ✅ Parser Results:")
                print(f"      - Total ads extracted: {len(ads_extracted)}")
                print(f"      - Is complete: {parsed_result.get('is_complete')}")
                print(f"      - Errors: {len(parsed_result.get('errors', []))}")
                
                # Show first few ad IDs
                if ads_extracted:
                    print(f"\n   📋 First 10 ad archive IDs extracted:")
                    for i, ad in enumerate(ads_extracted[:10]):
                        ad_id = ad.get('adArchiveID', ad.get('ad_archive_id', 'Unknown'))
                        page_name = ad.get('pageName', ad.get('page_name', 'Unknown'))
                        print(f"      {i+1}. {ad_id} - {page_name}")
                    
                    if len(ads_extracted) > 10:
                        print(f"      ... and {len(ads_extracted) - 10} more ads")
                
                # Check if specific ad ID exists
                specific_ad_id = "671758975892991"
                found_specific = any(
                    ad.get('adArchiveID', ad.get('ad_archive_id')) == specific_ad_id 
                    for ad in ads_extracted
                )
                print(f"\n   🔎 Specific ad ID {specific_ad_id} found: {found_specific}")
                
            except Exception as e:
                print(f"\n   ❌ Error parsing JSON response: {e}")
                logger.error(f"JSON parsing error: {e}", exc_info=True)
        else:
            print(f"   ⚠️ Sample response file not found: {sample_response_path}")
        
        print("\n✅ Debug test completed!")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        logger.error(f"Debug test error: {e}", exc_info=True)
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)