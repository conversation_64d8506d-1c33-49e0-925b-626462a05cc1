#!/usr/bin/env python3
"""
Test script to trace dependency injection chain for law_firms_repository
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_dependency_chain():
    """Test the dependency injection chain."""
    
    print("=== Testing Dependency Injection Chain ===\n")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # 1. Create minimal config
    config = {
        'aws_region': os.getenv('AWS_REGION', 'us-west-2'),
        'aws_access_key': os.getenv('AWS_ACCESS_KEY_ID', ''),
        'aws_secret_key': os.getenv('AWS_SECRET_ACCESS_KEY', ''),
        's3_bucket_name': os.getenv('S3_BUCKET_NAME', 'lexgenius-dockets'),
        'dynamodb_endpoint': None,
        'storage': {},
        'fb_ads': {},
    }
    
    print("1. Creating main container...")
    from src.containers.core import create_container
    container = create_container(config)
    
    print("2. Checking storage container...")
    storage_container = container.storage()
    print(f"   Storage container type: {type(storage_container).__name__}")
    
    print("3. Getting law_firms_repository...")
    law_firms_repo = storage_container.law_firms_repository()
    print(f"   Law firms repository type: {type(law_firms_repo).__name__}")
    
    print("4. Testing repository functionality...")
    try:
        # Test the get_by_id method used in CamoufoxSessionManager
        test_page_id = "344397646746"
        print(f"   Testing get_by_id('{test_page_id}')...")
        results = await law_firms_repo.get_by_id(test_page_id)
        
        if results:
            print(f"   ✅ Found {len(results)} results")
            for result in results:
                print(f"      - Name: {result.get('name')}")
                print(f"      - Page Alias: {result.get('page_alias')}")
        else:
            print(f"   ❌ No results found")
            
    except Exception as e:
        print(f"   ❌ Error testing repository: {e}")
    
    print("\n5. Creating FB ads container...")
    fb_ads_container = container.fb_ads()
    print(f"   FB ads container type: {type(fb_ads_container).__name__}")
    
    print("6. Checking session manager factory dependencies...")
    # Get the session manager factory from the container
    session_manager_factory = fb_ads_container.session_manager_factory()
    print(f"   Session manager factory type: {type(session_manager_factory).__name__}")
    
    print("7. Creating session manager with factory...")
    from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
    
    # Mock dependencies
    logger = logging.getLogger("test")
    
    # This is the critical part - passing law_firms_repository
    session_manager = SessionManagerFactory.create(
        config=config,
        logger=logger,
        firm_id="test_firm",
        fingerprint_manager=None,
        proxy_manager=None,
        law_firms_repository=law_firms_repo  # This is what's missing in debug script!
    )
    
    print(f"   Session manager type: {type(session_manager).__name__}")
    
    # Check if it's CamoufoxSessionManager and has law_firms_repository
    if hasattr(session_manager, 'law_firms_repository'):
        print(f"   ✅ Session manager has law_firms_repository: {session_manager.law_firms_repository is not None}")
    else:
        print(f"   ❌ Session manager missing law_firms_repository")
    
    print("\n=== Summary ===")
    print("The production code passes law_firms_repository through this chain:")
    print("1. MainContainer → storage container → law_firms_repository")
    print("2. MainContainer → fb_ads container (gets storage_container)")
    print("3. fb_ads container → session_manager_factory (configured with law_firms_repository)")
    print("4. JobOrchestrationService calls SessionManagerFactory.create() with law_firms_repository")
    print("5. CamoufoxSessionManager receives law_firms_repository in constructor")
    
    print("\nThe debug script is missing step 4 - it doesn't pass law_firms_repository!")

if __name__ == "__main__":
    asyncio.run(test_dependency_chain())