#!/usr/bin/env python3
"""
Fix for proxy initialization performance issue.
The health tracking initialization for large numbers of proxies causes hanging.
"""

import os
import re


def fix_proxy_initialization(file_path):
    """Fix the proxy initialization to handle large numbers efficiently."""
    print(f"\nFixing proxy initialization in: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the _initialize_proxies method and fix the health tracking initialization
    # Replace the health tracking loop with lazy initialization
    old_pattern = r'''(# Initialize health tracking for both proxy types\s*
        all_proxies = self\.mobile_proxies \+ self\.residential_proxies\s*
        for proxy in all_proxies:\s*
            proxy_key = f"{proxy\.host}:{proxy\.port}"\s*
            self\.proxy_health\[proxy_key\] = \{\s*
                'failures': 0,\s*
                'last_failure': None,\s*
                'banned_until': None,\s*
                'total_requests': 0,\s*
                'success_count': 0,\s*
                'last_used': None,\s*
                'proxy_type': 'mobile' if proxy in self\.mobile_proxies else 'residential'\s*
            \})'''
    
    new_code = '''# Initialize health tracking lazily (only when proxy is used)
        # This prevents hanging with large proxy counts
        self.logger.info(f"Proxy health tracking will be initialized on-demand for {len(self.mobile_proxies) + len(self.residential_proxies)} proxies")'''
    
    content = re.sub(old_pattern, new_code, content, flags=re.DOTALL)
    
    # Add a method to get or create health entry
    get_health_method = '''
    def _get_or_create_health(self, proxy: ProxyConfig) -> Dict[str, Any]:
        """Get or create health tracking entry for a proxy."""
        proxy_key = f"{proxy.host}:{proxy.port}"
        if proxy_key not in self.proxy_health:
            self.proxy_health[proxy_key] = {
                'failures': 0,
                'last_failure': None,
                'banned_until': None,
                'total_requests': 0,
                'success_count': 0,
                'last_used': None,
                'proxy_type': 'mobile' if proxy in self.mobile_proxies else 'residential'
            }
        return self.proxy_health[proxy_key]'''
    
    # Insert the new method after the _initialize_proxies method
    insert_pos = content.find('    def _generate_mobile_proxies(')
    if insert_pos > 0:
        content = content[:insert_pos] + get_health_method + '\n\n' + content[insert_pos:]
    
    # Update all places that access proxy_health to use the new method
    # In get_next_proxy method
    content = re.sub(
        r'health = self\.proxy_health\.get\(proxy_key, \{\}\)',
        'health = self._get_or_create_health(proxy)',
        content
    )
    
    # In _is_proxy_banned method - need to handle this differently
    content = re.sub(
        r'def _is_proxy_banned\(self, health: Dict\[str, Any\]\) -> bool:',
        'def _is_proxy_banned(self, health: Dict[str, Any]) -> bool:',
        content
    )
    
    # In record_proxy_success method
    content = re.sub(
        r'health = self\.proxy_health\.get\(proxy_key, \{\}\)',
        'health = self._get_or_create_health(proxy)',
        content
    )
    
    # In record_proxy_failure method
    content = re.sub(
        r'health = self\.proxy_health\.get\(proxy_key, \{\}\)',
        'health = self._get_or_create_health(proxy)',
        content
    )
    
    # In get_healthy_proxy_count method - needs special handling
    old_healthy_count = r'''for proxy in current_pool:
            proxy_key = f"{proxy\.host}:{proxy\.port}"
            health = self\.proxy_health\.get\(proxy_key, \{\}\)
            
            if not self\._is_proxy_banned\(health\):
                healthy_count \+= 1'''
    
    new_healthy_count = '''for proxy in current_pool:
            health = self._get_or_create_health(proxy)
            
            if not self._is_proxy_banned(health):
                healthy_count += 1'''
    
    content = re.sub(old_healthy_count, new_healthy_count, content, flags=re.DOTALL)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed proxy initialization performance issue in {file_path}")


def main():
    """Apply the performance fix to proxy managers."""
    print("🔧 Proxy Initialization Performance Fix")
    print("=" * 50)
    
    proxy_manager_files = [
        "src/infrastructure/browser/proxy_manager.py",
        "src/services/scraping/proxy/proxy_manager.py"
    ]
    
    for file_path in proxy_manager_files:
        if os.path.exists(file_path):
            fix_proxy_initialization(file_path)
    
    print("\n✅ Fix applied successfully!")
    print("\nThe proxy health tracking is now initialized on-demand instead of upfront.")
    print("This prevents hanging when initializing large numbers of proxies.")


if __name__ == "__main__":
    main()