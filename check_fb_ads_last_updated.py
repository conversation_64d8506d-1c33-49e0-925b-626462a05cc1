#!/usr/bin/env python3
"""Check Facebook ads in DynamoDB for last_updated field."""

import boto3
from datetime import datetime

def check_fb_ads():
    """Check Facebook ads data focusing on last_updated field."""
    
    print("=== CHECKING FACEBOOK ADS LAST_UPDATED ===")
    
    # Initialize DynamoDB
    use_local = False
    
    if use_local:
        dynamodb = boto3.resource(
            'dynamodb',
            endpoint_url='http://localhost:8000',
            region_name='us-west-2',
            aws_access_key_id='dummy',
            aws_secret_access_key='dummy'
        )
    else:
        dynamodb = boto3.resource('dynamodb', region_name='us-west-2')
    
    table = dynamodb.Table('FBAdArchive')
    
    # Today's date
    today = datetime.now().strftime("%Y%m%d")
    print(f"Today's date: {today}")
    
    # Check specific ads from morgan_morgan_response.json
    # First, let's scan for any of these ads to find their actual StartDate
    test_ad_ids = ["624952230635857", "1137997745013785", "1261286425672639"]
    
    print("\nSearching for test ads to find their StartDate...")
    for ad_id in test_ad_ids:
        try:
            response = table.scan(
                FilterExpression='AdArchiveID = :ad_id',
                ExpressionAttributeValues={':ad_id': ad_id},
                Limit=5
            )
            
            if response['Items']:
                for item in response['Items']:
                    start_date = item.get('StartDate', 'Unknown')
                    last_updated = item.get('LastUpdated', 'NOT SET')
                    print(f"✓ Found Ad {ad_id}: StartDate={start_date}, LastUpdated={last_updated}")
            else:
                print(f"✗ Ad {ad_id} not found in table")
        except Exception as e:
            print(f"✗ Error scanning for ad {ad_id}: {e}")
    
    
    # Do a scan to count all ads with today's last_updated
    print(f"\nScanning for all ads with LastUpdated = {today}...")
    
    try:
        response = table.scan(
            FilterExpression='LastUpdated = :today',
            ExpressionAttributeValues={':today': today},
            Limit=100  # Limit for testing
        )
        
        count = response.get('Count', 0)
        print(f"\nFound {count} ads with LastUpdated = {today}")
        
        if count > 0:
            print("\nSample ads updated today:")
            for i, item in enumerate(response['Items'][:5]):
                print(f"  {i+1}. AdArchiveID: {item.get('AdArchiveID')}, "
                      f"PageName: {item.get('PageName', 'Unknown')}")
        
        # Also check ads for Morgan & Morgan specifically
        print(f"\nChecking Morgan & Morgan ads...")
        response = table.scan(
            FilterExpression='PageID = :page_id',
            ExpressionAttributeValues={':page_id': '313942829424'},
            Limit=10
        )
        
        mm_count = response.get('Count', 0)
        print(f"Found {mm_count} Morgan & Morgan ads (sample)")
        
        for item in response['Items'][:3]:
            print(f"  - AdArchiveID: {item.get('AdArchiveID')}, "
                  f"LastUpdated: {item.get('LastUpdated', 'NOT SET')}")
        
        # Check total count of ads in table
        print(f"\nChecking total ads in table...")
        response = table.scan(Limit=10)
        total_count = response.get('Count', 0)
        print(f"Found {total_count} ads in table (sample)")
        
        if total_count > 0:
            print("\nSample ads from table:")
            for i, item in enumerate(response['Items'][:3]):
                print(f"  {i+1}. AdArchiveID: {item.get('AdArchiveID')}, "
                      f"StartDate: {item.get('StartDate')}, "
                      f"LastUpdated: {item.get('LastUpdated', 'NOT SET')}")
        
    except Exception as e:
        print(f"Error scanning table: {e}")

if __name__ == "__main__":
    check_fb_ads()