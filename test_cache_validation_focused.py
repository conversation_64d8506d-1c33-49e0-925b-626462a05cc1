#!/usr/bin/env python3
"""
Focused cache validation test to verify unified cache fixes and identify specific issues.

This test specifically validates:
1. CACHE HITS are occurring for previously scraped images
2. REDUNDANT proxy downloads are eliminated  
3. "CACHE HIT" log messages appear correctly
4. URL normalization works for all Facebook CDN patterns
"""

import asyncio
import time
from unittest.mock import MagicMock
from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)

async def main():
    print("🎯 FOCUSED CACHE VALIDATION TEST")
    print("=" * 50)
    
    # Create cache extractor with validation-friendly settings
    config = {
        "camoufox": {
            "image_cache": {
                "enabled": True,
                "max_cache_size_mb": 50,
                "cache_ttl_minutes": 1440  # 24 hours
            }
        }
    }
    
    mock_logger = MagicMock()
    extractor = BrowserImageCacheExtractor(
        logger=mock_logger,
        config=config,
        max_cache_size_mb=50,
        cache_ttl_minutes=1440,
        enable_disk_persistence=False  # Disable for focus on core functionality
    )
    
    print(f"✅ Cache extractor initialized")
    
    # TEST 1: Basic Cache Hit Validation
    print("\n🧪 TEST 1: Basic Cache Hit Validation")
    
    # Cache an image with thumbnail parameters
    thumbnail_url = (
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
        "447181435_1029863199156819_7025413177941515490_n.jpg"
        "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
        "&_nc_ohc=kRJy7nRzpfMQ7kNvgGKXxGX&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
        "&_nc_gid=A2PBBJaFJQMd7CjQ9uINlWH&oh=00_AYB8n6xUH4G_X23vcQcvhZJRfBxjSQMD6UQQCqWupb2lrg"
        "&oe=6732D6F0"
    )
    
    thumbnail_content = b"thumbnail_validation_content"
    cached_resource = CachedImageResource(
        url=thumbnail_url,
        content=thumbnail_content,
        content_type="image/jpeg",
        content_length=len(thumbnail_content),
        timestamp=time.time(),
        source="validation_test"
    )
    await extractor._store_cached_image(cached_resource)
    print(f"   📦 Cached thumbnail: {len(thumbnail_content)} bytes")
    
    # Request fullsize version (should hit cache)
    fullsize_url = (
        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
        "447181435_1029863199156819_7025413177941515490_n.jpg"
        "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
        "&_nc_ohc=DifferentHashValue&_nc_zt=14&_nc_ht=scontent-dfw5-1.xx"
        "&_nc_gid=DifferentGIDValue&oh=00_DifferentOHValue&oe=DifferentOEValue"
    )
    
    cached_image = await extractor.get_cached_image(fullsize_url)
    
    if cached_image and cached_image.content == thumbnail_content:
        print(f"   ✅ CACHE HIT: Found thumbnail when requesting fullsize")
        cache_hit_success = True
    else:
        print(f"   ❌ CACHE MISS: Thumbnail not found for fullsize request")
        cache_hit_success = False
    
    # Check log messages
    cache_hit_logs = [
        str(call) for call in mock_logger.method_calls 
        if 'CACHE HIT' in str(call)
    ]
    print(f"   📝 Cache hit log messages: {len(cache_hit_logs)}")
    
    # TEST 2: URL Normalization Investigation
    print("\n🧪 TEST 2: URL Normalization Investigation")
    
    # Test different URL patterns to see which ones work
    test_urls = [
        {
            "name": "Different CDN host",
            "cached_url": "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=101",
            "lookup_url": "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/123_456_789_n.jpg?_nc_cat=104"
        },
        {
            "name": "Generic scontent host", 
            "cached_url": "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/234_567_890_n.jpg?_nc_cat=101",
            "lookup_url": "https://scontent.xx.fbcdn.net/v/t39.35426-6/234_567_890_n.jpg?_nc_cat=107"
        },
        {
            "name": "Mobile z-m-scontent host",
            "cached_url": "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/345_678_901_n.jpg?_nc_cat=101", 
            "lookup_url": "https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/345_678_901_n.jpg?_nc_cat=110"
        }
    ]
    
    normalization_results = []
    
    for i, test_case in enumerate(test_urls):
        # Cache the original
        test_content = f"test_content_{i}".encode()
        cached_resource = CachedImageResource(
            url=test_case["cached_url"],
            content=test_content,
            content_type="image/jpeg",
            content_length=len(test_content),
            timestamp=time.time(),
            source="normalization_test"
        )
        await extractor._store_cached_image(cached_resource)
        
        # Try to look up with different pattern
        lookup_result = await extractor.get_cached_image(test_case["lookup_url"])
        
        # Test normalization directly
        cached_normalized = extractor._normalize_facebook_image_url(test_case["cached_url"])
        lookup_normalized = extractor._normalize_facebook_image_url(test_case["lookup_url"])
        
        success = lookup_result is not None and lookup_result.content == test_content
        normalization_match = cached_normalized == lookup_normalized
        
        status = "✅" if success else "❌"
        print(f"   {status} {test_case['name']}: {'HIT' if success else 'MISS'}")
        print(f"      Normalized match: {normalization_match}")
        print(f"      Cached normalized: {cached_normalized[:60]}...")
        print(f"      Lookup normalized: {lookup_normalized[:60]}...")
        
        normalization_results.append({
            'name': test_case['name'],
            'success': success,
            'normalization_match': normalization_match
        })
    
    # TEST 3: Redundant Download Prevention Verification
    print("\n🧪 TEST 3: Redundant Download Prevention")
    
    # Simulate multiple requests for same image with different parameters
    base_url = "https://scontent.fbcdn.net/v/t39.35426-6/prevention_test_999_888_777_n.jpg"
    
    # Cache initial version
    initial_url = f"{base_url}?_nc_cat=101&ccb=1-7"
    initial_content = b"prevention_test_content"
    
    cached_resource = CachedImageResource(
        url=initial_url,
        content=initial_content,
        content_type="image/jpeg",
        content_length=len(initial_content),
        timestamp=time.time(),
        source="prevention_test"
    )
    await extractor._store_cached_image(cached_resource)
    
    # Test multiple variations
    variations = [
        f"{base_url}?stp=dst-jpg_s60x60_tt6&_nc_cat=999",
        f"{base_url}?stp=dst-jpg_s600x600_tt6&_nc_cat=888",
        f"{base_url}?w=400&h=400&quality=80&_nc_cat=777",
        f"{base_url}?random=params&session=different&_nc_cat=666"
    ]
    
    prevention_hits = 0
    for variation in variations:
        result = await extractor.get_cached_image(variation)
        if result and result.content == initial_content:
            prevention_hits += 1
    
    prevention_rate = (prevention_hits / len(variations)) * 100
    print(f"   📊 Download prevention: {prevention_hits}/{len(variations)} ({prevention_rate:.1f}%)")
    
    # SUMMARY
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    # Calculate overall success
    normalization_success_count = sum(1 for result in normalization_results if result['success'])
    normalization_rate = (normalization_success_count / len(normalization_results)) * 100
    
    print(f"Cache Hit Test: {'✅ PASS' if cache_hit_success else '❌ FAIL'}")
    print(f"URL Normalization: {normalization_success_count}/{len(normalization_results)} patterns work ({normalization_rate:.1f}%)")
    print(f"Download Prevention: {prevention_hits}/{len(variations)} variations prevented ({prevention_rate:.1f}%)")
    print(f"Cache Hit Logs: {len(cache_hit_logs)} messages found")
    
    # Get final cache statistics
    stats = extractor.get_cache_stats()
    print(f"\n📈 Final Cache Statistics:")
    print(f"   Images cached: {stats['cached_images']}")
    print(f"   Hit rate: {stats['hit_rate_percent']:.1f}%")
    print(f"   Cache misses: {stats['cache_misses']}")
    print(f"   Bandwidth saved: {stats['total_saved_mb']:.3f} MB")
    
    # Identify specific issues
    print(f"\n🔍 Issue Analysis:")
    
    issues_found = []
    if not cache_hit_success:
        issues_found.append("Basic cache hit failing - URL normalization issue")
    
    if normalization_rate < 100:
        failed_patterns = [r['name'] for r in normalization_results if not r['success']]
        issues_found.append(f"URL normalization failing for: {', '.join(failed_patterns)}")
    
    if prevention_rate < 80:
        issues_found.append(f"Low download prevention rate: {prevention_rate:.1f}%")
    
    if len(cache_hit_logs) == 0:
        issues_found.append("No cache hit log messages found")
    
    if issues_found:
        print("   Issues detected:")
        for issue in issues_found:
            print(f"   ❌ {issue}")
    else:
        print("   ✅ No critical issues detected")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if normalization_rate < 100:
        print("   🔧 Review URL normalization logic for mobile/CDN host patterns")
    if prevention_rate < 80:
        print("   🔧 Investigate why some URL variations aren't matching normalized cache")
    if len(cache_hit_logs) == 0:
        print("   🔧 Check logging configuration for cache hit messages")
    
    # Overall assessment
    critical_tests_passed = cache_hit_success and prevention_rate >= 75 and normalization_rate >= 66
    
    print(f"\n" + "=" * 50)
    if critical_tests_passed:
        print("🎉 UNIFIED CACHE IS WORKING!")
        print("✅ Core functionality validated")
        print("✅ Cache hits are occurring")
        print("✅ Downloads are being prevented")
        return 0
    else:
        print("⚠️  UNIFIED CACHE HAS ISSUES")
        print("❌ Some functionality needs attention")
        print("❌ Review issues and recommendations above")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)