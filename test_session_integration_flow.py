#!/usr/bin/env python3
"""
Integration Test Suite for Session Management Flow

This script tests the complete end-to-end flow of session management
including the critical fix for session persistence across job phases.
"""

import asyncio
import pytest
import time
import logging
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, Any, List

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockJobOrchestrationService:
    """Mock job orchestration service to test the session persistence fix"""
    
    def __init__(self, session_manager):
        self.session_manager = session_manager
        self.logger = logging.getLogger("MockJobOrchestration")
        
    async def _process_single_firm_with_worker(self, job):
        """Simulate the job orchestration process that was causing the bug"""
        try:
            self.logger.info(f"🚀 Starting job processing for firm {job.firm_id}")
            
            # Dependencies setup
            dependencies_with_progress = {
                'session_manager': self.session_manager,
                'job': job,
                'firm_id': job.firm_id
            }
            
            # Simulate job runner execution (Phase 1: GraphQL capture)
            self.logger.info("📊 Phase 1: GraphQL data capture")
            
            # This is where the session gets used for GraphQL requests
            if self.session_manager.is_session_valid():
                session_data = await self.session_manager.get_session_data()
                self.logger.info(f"✅ GraphQL capture completed with session data: {len(session_data)} tokens")
            else:
                raise Exception("Session not valid for GraphQL capture")
            
            # Job orchestration returns (Phase 1 complete)
            self.logger.info("✅ Job orchestration Phase 1 completed")
            
            return dependencies_with_progress
            
        except Exception as e:
            self.logger.error(f"❌ Job orchestration failed: {e}")
            raise
            
        finally:
            # CRITICAL TEST: This is where the bug was!
            # Before the fix, this cleanup would run here and invalidate the session
            # BEFORE Phase 2 (image downloads) could use it
            
            self.logger.info("🔍 Job orchestration finally block - checking fix")
            
            # THE FIX: Do NOT clean up session manager here!
            # Original buggy code (commented out):
            # if 'session_manager' in dependencies_with_progress:
            #     session_manager = dependencies_with_progress.get('session_manager')
            #     if session_manager and hasattr(session_manager, 'cleanup'):
            #         await session_manager.cleanup()
            #         self.logger.info(f"✅ Cleaned up session manager for firm {job.firm_id}")
            
            # FIXED: Session cleanup is deferred to job runner after ALL phases
            self.logger.info("✅ Job orchestration cleanup deferred (THE FIX)")


class MockJobRunnerService:
    """Mock job runner service that handles all phases"""
    
    def __init__(self, session_manager):
        self.session_manager = session_manager
        self.logger = logging.getLogger("MockJobRunner")
        
    async def run_all_phases(self, job):
        """Run all job phases with the session"""
        try:
            self.logger.info(f"🎯 Job runner starting all phases for firm {job.firm_id}")
            
            # Phase 1: GraphQL capture (already done by job orchestration)
            # Session should still be valid here
            if not self.session_manager.is_session_valid():
                raise Exception("❌ CRITICAL BUG: Session invalid at start of job runner!")
            
            # Phase 2: Image downloads (this was failing before the fix)
            self.logger.info("🖼️ Phase 2: Image downloads")
            await self._simulate_image_downloads(job)
            
            # Phase 3: Database operations
            self.logger.info("💾 Phase 3: Database operations")
            await self._simulate_database_operations(job)
            
            self.logger.info("✅ All phases completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Job runner failed: {e}")
            raise
            
        finally:
            # CORRECT PLACE for session cleanup - AFTER all phases
            self.logger.info("🧹 Job runner cleanup - after ALL phases")
            await self.session_manager.cleanup()
            self.logger.info("✅ Session cleaned up after all phases complete")
    
    async def _simulate_image_downloads(self, job):
        """Simulate image downloads using session"""
        # This is where the bug manifested: "Session not available"
        if not self.session_manager.is_session_valid():
            raise Exception("❌ BUG REPRODUCED: Session not available for image downloads!")
        
        # Get session headers for image requests
        headers = self.session_manager.get_session_headers()
        if not headers:
            raise Exception("❌ Session headers not available for image downloads!")
        
        # Simulate downloading multiple images
        image_urls = [
            "https://scontent.fxyz1-1.fna.fbcdn.net/image1.jpg",
            "https://scontent.fxyz1-1.fna.fbcdn.net/image2.jpg", 
            "https://scontent.fxyz1-1.fna.fbcdn.net/image3.jpg"
        ]
        
        for i, url in enumerate(image_urls):
            if not self.session_manager.is_session_valid():
                raise Exception(f"Session became invalid during image {i+1} download!")
            
            # Simulate image download with session
            await asyncio.sleep(0.1)  # Simulate download time
            self.logger.info(f"📥 Downloaded image {i+1}: {url}")
        
        self.logger.info(f"✅ Successfully downloaded {len(image_urls)} images")
    
    async def _simulate_database_operations(self, job):
        """Simulate database operations"""
        if not self.session_manager.is_session_valid():
            raise Exception("Session not available for database operations!")
        
        # Simulate database writes
        await asyncio.sleep(0.05)
        self.logger.info("✅ Database operations completed")


class MockJob:
    """Mock job for testing"""
    
    def __init__(self, firm_id: str):
        self.firm_id = firm_id
        self.status = "pending"


class MockSessionManager:
    """Mock session manager for integration testing"""
    
    def __init__(self):
        self.logger = logging.getLogger("MockSessionManager")
        self._is_session_valid = False
        self._session_data = {}
        self._cleaned_up = False
        
    async def create_new_session(self):
        """Create new session"""
        self.logger.info("🔧 Creating new session")
        self._is_session_valid = True
        self._session_data = {
            'fb_dtsg': 'test_token_123',
            'lsd': 'test_lsd_456',
            'jazoest': 'test_jazoest_789',
            'user_agent': 'Mozilla/5.0 Test Browser'
        }
        await asyncio.sleep(0.1)  # Simulate creation time
        self.logger.info("✅ Session created successfully")
        return True
    
    def is_session_valid(self):
        """Check if session is valid"""
        return self._is_session_valid and not self._cleaned_up
    
    async def get_session_data(self):
        """Get session data"""
        if not self.is_session_valid():
            raise Exception("Session not valid - cannot get session data")
        return self._session_data.copy()
    
    def get_session_headers(self):
        """Get session headers"""
        if not self.is_session_valid():
            return {}
        
        return {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': self._session_data.get('user_agent', ''),
            'Referer': 'https://www.facebook.com/ads/library'
        }
    
    async def cleanup(self):
        """Cleanup session"""
        self.logger.info("🧹 Session cleanup initiated")
        await asyncio.sleep(0.05)  # Simulate cleanup time
        self._is_session_valid = False
        self._session_data = {}
        self._cleaned_up = True
        self.logger.info("✅ Session cleanup completed")


async def test_session_persistence_across_phases():
    """Test the main fix: session persistence across job phases"""
    logger.info("🔍 Testing session persistence across job phases (THE MAIN FIX)")
    
    # Create session manager
    session_manager = MockSessionManager()
    
    # Create services
    job_orchestration = MockJobOrchestrationService(session_manager)
    job_runner = MockJobRunnerService(session_manager)
    
    # Create test job
    job = MockJob("test_firm_123")
    
    # Step 1: Create session
    await session_manager.create_new_session()
    assert session_manager.is_session_valid(), "Session should be valid after creation"
    
    # Step 2: Job orchestration (Phase 1) - this was calling cleanup prematurely
    dependencies = await job_orchestration._process_single_firm_with_worker(job)
    
    # CRITICAL TEST: Session should STILL be valid after job orchestration
    assert session_manager.is_session_valid(), \
        "❌ CRITICAL: Session should remain valid after job orchestration (main fix)"
    
    logger.info("✅ Session persisted through job orchestration (fix verified)")
    
    # Step 3: Job runner phases (including image downloads that were failing)
    await job_runner.run_all_phases(job)
    
    # Step 4: Verify session is cleaned up ONLY after all phases
    assert not session_manager.is_session_valid(), \
        "Session should be cleaned up after all phases complete"
    
    logger.info("✅ Session persistence across phases test passed")


async def test_image_download_flow_with_session():
    """Test the specific image download flow that was failing"""
    logger.info("🖼️ Testing image download flow with session")
    
    session_manager = MockSessionManager()
    job_runner = MockJobRunnerService(session_manager)
    job = MockJob("image_test_firm")
    
    # Create session
    await session_manager.create_new_session()
    
    # Simulate the scenario where image downloads need session headers
    logger.info("Testing image download scenario")
    
    # Before fix: this would fail with "Session not available"
    await job_runner._simulate_image_downloads(job)
    
    logger.info("✅ Image download flow test passed")


async def test_multi_phase_coordination():
    """Test coordination between multiple phases"""
    logger.info("🎯 Testing multi-phase coordination")
    
    session_manager = MockSessionManager()
    
    # Create session
    await session_manager.create_new_session()
    session_start = time.time()
    
    phases = [
        ("Phase 1: GraphQL Capture", 0.1),
        ("Phase 2: Image Downloads", 0.3), 
        ("Phase 3: Database Operations", 0.1)
    ]
    
    for phase_name, duration in phases:
        logger.info(f"Executing {phase_name}")
        
        # Session should be valid for each phase
        assert session_manager.is_session_valid(), \
            f"Session should be valid for {phase_name}"
        
        # Get session data
        session_data = await session_manager.get_session_data()
        assert session_data, f"Session data should be available for {phase_name}"
        
        # Simulate phase work
        await asyncio.sleep(duration)
        
        logger.info(f"✅ {phase_name} completed")
    
    # Cleanup after ALL phases
    await session_manager.cleanup()
    
    total_time = time.time() - session_start
    logger.info(f"All phases completed in {total_time:.2f}s")
    
    assert not session_manager.is_session_valid(), \
        "Session should be cleaned up after all phases"
    
    logger.info("✅ Multi-phase coordination test passed")


async def test_session_state_transitions():
    """Test proper session state transitions"""
    logger.info("🔄 Testing session state transitions")
    
    session_manager = MockSessionManager()
    
    # Initial state
    assert not session_manager.is_session_valid(), "Session should start invalid"
    
    # Create session
    await session_manager.create_new_session()
    assert session_manager.is_session_valid(), "Session should be valid after creation"
    
    # Use session
    headers = session_manager.get_session_headers()
    assert headers, "Session headers should be available"
    assert 'User-Agent' in headers, "Headers should contain User-Agent"
    
    session_data = await session_manager.get_session_data()
    assert 'fb_dtsg' in session_data, "Session data should contain fb_dtsg"
    
    # Cleanup session
    await session_manager.cleanup()
    assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
    
    # Try to use invalid session
    headers_after = session_manager.get_session_headers()
    assert not headers_after, "Headers should be empty after cleanup"
    
    logger.info("✅ Session state transitions test passed")


async def test_error_scenarios_during_phases():
    """Test error handling during different phases"""
    logger.info("❌ Testing error scenarios during phases")
    
    class FailingSessionManager(MockSessionManager):
        def __init__(self, fail_on_phase=None):
            super().__init__()
            self.fail_on_phase = fail_on_phase
            self.current_phase = 0
            
        async def get_session_data(self):
            self.current_phase += 1
            if self.fail_on_phase == self.current_phase:
                raise Exception(f"Simulated failure in phase {self.current_phase}")
            return await super().get_session_data()
    
    # Test failure in Phase 2 (image downloads)
    session_manager = FailingSessionManager(fail_on_phase=2)
    job_runner = MockJobRunnerService(session_manager)
    job = MockJob("error_test_firm")
    
    await session_manager.create_new_session()
    
    try:
        await job_runner.run_all_phases(job)
        assert False, "Should have failed during Phase 2"
    except Exception as e:
        assert "phase 2" in str(e), "Should fail with phase 2 error"
        logger.info(f"✅ Expected error caught: {e}")
    
    # Session should still be cleaned up even after error
    assert not session_manager.is_session_valid(), \
        "Session should be cleaned up even after error"
    
    logger.info("✅ Error scenarios test passed")


async def test_concurrent_phase_execution():
    """Test session handling with concurrent phase operations"""
    logger.info("🔀 Testing concurrent phase execution")
    
    session_manager = MockSessionManager()
    await session_manager.create_new_session()
    
    async def phase_operation(phase_id: int, duration: float):
        """Simulate a phase operation"""
        logger.info(f"Starting phase operation {phase_id}")
        
        for i in range(int(duration * 10)):
            if not session_manager.is_session_valid():
                return f"Phase {phase_id} aborted - session invalid"
            
            # Use session
            headers = session_manager.get_session_headers()
            if not headers:
                return f"Phase {phase_id} failed - no session headers"
            
            await asyncio.sleep(0.01)
        
        return f"Phase {phase_id} completed"
    
    # Start multiple concurrent phase operations
    phase_tasks = []
    for i in range(5):
        task = asyncio.create_task(phase_operation(i, 0.5))
        phase_tasks.append(task)
    
    # Let operations run
    await asyncio.sleep(0.2)
    
    # Session should remain valid during concurrent operations
    assert session_manager.is_session_valid(), \
        "Session should remain valid during concurrent operations"
    
    # Wait for operations to complete
    results = await asyncio.gather(*phase_tasks)
    
    # All operations should complete successfully
    completed_count = sum(1 for r in results if "completed" in r)
    assert completed_count == 5, f"All 5 operations should complete, got {completed_count}"
    
    # Cleanup
    await session_manager.cleanup()
    
    logger.info("✅ Concurrent phase execution test passed")


async def test_session_recovery_during_phases():
    """Test session recovery during phase execution"""
    logger.info("🔄 Testing session recovery during phases")
    
    class RecoverableSessionManager(MockSessionManager):
        def __init__(self):
            super().__init__()
            self.recovery_count = 0
            self.max_recoveries = 2
            
        async def get_session_data(self):
            # Simulate session becoming invalid
            if self.recovery_count < self.max_recoveries and self._is_session_valid:
                self.recovery_count += 1
                self.logger.info(f"⚠️ Simulating session failure {self.recovery_count}")
                self._is_session_valid = False
                
                # Auto-recovery
                await asyncio.sleep(0.1)
                await self.create_new_session()
                self.logger.info("🔄 Session recovered")
            
            return await super().get_session_data()
    
    session_manager = RecoverableSessionManager()
    await session_manager.create_new_session()
    
    # Simulate phases that trigger recovery
    for phase in range(3):
        logger.info(f"Phase {phase + 1}")
        
        # This will trigger recovery on first two calls
        session_data = await session_manager.get_session_data()
        assert session_data, f"Session data should be available for phase {phase + 1}"
        
        # Session should be valid after recovery
        assert session_manager.is_session_valid(), \
            f"Session should be valid after phase {phase + 1}"
    
    logger.info(f"✅ Session recovered {session_manager.recovery_count} times")
    
    await session_manager.cleanup()
    
    logger.info("✅ Session recovery during phases test passed")


async def run_all_integration_tests():
    """Run all integration flow tests"""
    logger.info("🚀 Starting Session Integration Flow Tests")
    
    tests = [
        test_session_persistence_across_phases,
        test_image_download_flow_with_session,
        test_multi_phase_coordination,
        test_session_state_transitions,
        test_error_scenarios_during_phases,
        test_concurrent_phase_execution,
        test_session_recovery_during_phases
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            start_time = time.time()
            await test()
            duration = time.time() - start_time
            logger.info(f"✅ {test.__name__} passed ({duration:.2f}s)")
            passed += 1
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test.__name__} failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n🏁 Integration Flow Tests Complete")
    logger.info(f"📊 Results: {passed} passed, {failed} failed, {len(tests)} total")
    
    return passed, failed


if __name__ == "__main__":
    """Main execution"""
    import sys
    
    logger.info("🔬 Session Integration Flow Test Suite")
    logger.info("=" * 60)
    
    try:
        passed, failed = asyncio.run(run_all_integration_tests())
        
        if failed > 0:
            logger.error(f"\n💥 {failed} test(s) failed!")
            sys.exit(1)
        else:
            logger.info(f"\n🎉 All {passed} tests passed!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Test suite failed: {e}")
        sys.exit(1)