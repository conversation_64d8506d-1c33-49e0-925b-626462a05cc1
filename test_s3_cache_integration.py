#!/usr/bin/env python3
"""
Test script for S3 cache integration in LexGenius reports.

This script validates the S3 existence cache system performance improvements
by simulating the ad page generation process with and without caching.
"""

import asyncio
import os
import sys
import time
from typing import Dict, List, Any

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.infrastructure.protocols.logger import LoggerProtocol
from src.services.reports.s3_cache_service import S3ExistenceCacheService


class MockLogger:
    """Mock logger for testing."""
    
    def debug(self, msg: str, **kwargs): print(f"DEBUG: {msg}")
    def info(self, msg: str, **kwargs): print(f"INFO: {msg}")
    def warning(self, msg: str, **kwargs): print(f"WARNING: {msg}")
    def error(self, msg: str, **kwargs): print(f"ERROR: {msg}")
    def log_debug(self, msg: str, **kwargs): self.debug(msg, **kwargs)
    def log_info(self, msg: str, **kwargs): self.info(msg, **kwargs)
    def log_warning(self, msg: str, **kwargs): self.warning(msg, **kwargs)
    def log_error(self, msg: str, **kwargs): self.error(msg, **kwargs)


class MockS3Storage:
    """Mock S3 storage that simulates real S3 behavior with artificial delays."""
    
    def __init__(self, logger: LoggerProtocol):
        self.logger = logger
        # Simulate some existing images and some missing ones
        self.existing_objects = {
            "adarchive/fb/123456789/111111111.jpg": True,
            "adarchive/fb/123456789/222222222.jpg": True,
            "adarchive/fb/987654321/333333333.jpg": False,  # Missing
            "adarchive/fb/987654321/444444444.jpg": True,
            "adarchive/fb/555555555/555555555.jpg": False,  # Missing
        }
        self.call_count = 0
        
    async def file_exists(self, s3_key: str) -> bool:
        """Simulate S3 HEAD request with artificial delay."""
        self.call_count += 1
        # Simulate network latency
        await asyncio.sleep(0.1)  # 100ms per request
        
        # Default to exists for keys not in our test set
        exists = self.existing_objects.get(s3_key, True)
        self.logger.debug(f"S3 HEAD request #{self.call_count}: {s3_key} -> {exists}")
        return exists
    
    def reset_call_count(self):
        """Reset the call counter for testing."""
        self.call_count = 0


class MockReportsConfig:
    """Mock reports configuration."""
    
    def __init__(self):
        self.iso_date = "20250717"
        self.download_dir = "/tmp/lexgenius_test"
        os.makedirs(self.download_dir, exist_ok=True)


async def test_cache_performance():
    """Test S3 cache performance improvements."""
    print("=" * 60)
    print("S3 CACHE PERFORMANCE TEST")
    print("=" * 60)
    
    # Setup
    logger = MockLogger()
    mock_s3 = MockS3Storage(logger)
    mock_config = MockReportsConfig()
    
    # Test data: simulate 100 ad images
    test_ad_data = []
    for i in range(100):
        ad_archive_id = f"12345678{i:02d}"
        ad_creative_id = f"11111111{i:02d}"
        test_ad_data.append({
            'ad_archive_id': ad_archive_id,
            'ad_creative_id': ad_creative_id
        })
    
    # Configuration for cache service
    cache_config = {
        'reports_config': mock_config,
        's3_cache': {
            'ttl_hours': 24,
            'enable_file_cache': True,
            'enable_memory_cache': True,
            'batch_size': 100,
            'max_concurrent_checks': 10
        }
    }
    
    # Initialize cache service
    cache_service = S3ExistenceCacheService(
        logger=logger,
        config=cache_config,
        s3_storage=mock_s3
    )
    await cache_service.initialize()
    
    print(f"Testing with {len(test_ad_data)} ad images...")
    print()
    
    # Test 1: Without caching (individual S3 calls)
    print("TEST 1: Individual S3 existence checks (no caching)")
    print("-" * 50)
    
    mock_s3.reset_call_count()
    start_time = time.time()
    
    # Simulate the old way: individual file_exists calls
    individual_results = {}
    for ad in test_ad_data:
        s3_key = f"adarchive/fb/{ad['ad_archive_id']}/{ad['ad_creative_id']}.jpg"
        exists = await mock_s3.file_exists(s3_key)
        individual_results[s3_key] = exists
    
    individual_duration = time.time() - start_time
    individual_calls = mock_s3.call_count
    
    print(f"Duration: {individual_duration:.2f} seconds")
    print(f"S3 API calls: {individual_calls}")
    print(f"Average time per check: {individual_duration/len(test_ad_data)*1000:.1f}ms")
    print()
    
    # Test 2: With caching (bulk pre-loading)
    print("TEST 2: Bulk cached existence checks")
    print("-" * 50)
    
    mock_s3.reset_call_count()
    start_time = time.time()
    
    # Use cache service to pre-load existence data
    preload_stats = await cache_service.preload_ad_image_cache(test_ad_data)
    preload_calls = mock_s3.call_count  # Calls made during preload
    
    # Reset counter to measure cache efficiency
    mock_s3.reset_call_count()
    
    # Now "check" each image (should be instant from cache)
    cached_results = {}
    for ad in test_ad_data:
        s3_key = f"adarchive/fb/{ad['ad_archive_id']}/{ad['ad_creative_id']}.jpg"
        exists = await cache_service.check_exists(s3_key)
        cached_results[s3_key] = exists
    
    cached_duration = time.time() - start_time
    cache_lookup_calls = mock_s3.call_count  # Should be 0 (all from cache)
    total_cached_calls = preload_calls + cache_lookup_calls
    
    print(f"Duration: {cached_duration:.2f} seconds")
    print(f"S3 API calls: {total_cached_calls} (preload: {preload_calls}, cache lookups: {cache_lookup_calls})")
    print(f"Average time per check: {cached_duration/len(test_ad_data)*1000:.1f}ms")
    print(f"Preload stats: {preload_stats}")
    print()
    
    # Test 3: Cache hit ratio test
    print("TEST 3: Cache hit ratio validation")
    print("-" * 50)
    
    # Check cache stats
    cache_stats = await cache_service.get_cache_stats()
    print(f"Cache statistics: {cache_stats}")
    
    # Verify results are identical
    results_match = individual_results == cached_results
    print(f"Results consistency: {'✅ PASS' if results_match else '❌ FAIL'}")
    
    if not results_match:
        print("ERROR: Cache results don't match individual results!")
        return False
    
    print()
    
    # Performance summary
    print("PERFORMANCE SUMMARY")
    print("=" * 60)
    
    time_savings = individual_duration - cached_duration
    time_savings_pct = (time_savings / individual_duration) * 100
    api_call_reduction = individual_calls - total_cached_calls
    api_call_reduction_pct = (api_call_reduction / individual_calls) * 100
    
    print(f"Time savings: {time_savings:.2f}s ({time_savings_pct:.1f}% faster)")
    print(f"S3 API call reduction: {api_call_reduction} calls ({api_call_reduction_pct:.1f}% reduction)")
    print(f"Cache efficiency: {len(test_ad_data)} checks with only {total_cached_calls} S3 calls")
    print(f"Cache lookup efficiency: {len(test_ad_data)} lookups with {cache_lookup_calls} S3 calls (should be 0)")
    
    # Performance targets
    target_time_savings = 60  # 60% faster
    target_cache_lookup_efficiency = 95  # 95% of lookups should be from cache (cache_lookup_calls should be near 0)
    
    time_target_met = time_savings_pct >= target_time_savings
    cache_efficiency = ((len(test_ad_data) - cache_lookup_calls) / len(test_ad_data)) * 100
    cache_target_met = cache_efficiency >= target_cache_lookup_efficiency
    
    print()
    print("PERFORMANCE TARGETS")
    print("-" * 30)
    print(f"Time savings target (≥{target_time_savings}%): {'✅ PASS' if time_target_met else '❌ FAIL'}")
    print(f"Cache lookup efficiency (≥{target_cache_lookup_efficiency}%): {'✅ PASS' if cache_target_met else '❌ FAIL'}")
    print(f"  Actual cache efficiency: {cache_efficiency:.1f}%")
    
    # Test 4: File cache persistence
    print()
    print("TEST 4: File cache persistence")
    print("-" * 50)
    
    # Clear memory cache but keep file cache
    await cache_service.clear_cache('memory')
    
    # Create new cache service to test file cache loading
    cache_service2 = S3ExistenceCacheService(
        logger=logger,
        config=cache_config,
        s3_storage=mock_s3
    )
    await cache_service2.initialize()
    
    # Check if file cache was loaded
    cache_stats2 = await cache_service2.get_cache_stats()
    file_cache_loaded = cache_stats2['memory_cache_size'] > 0
    
    print(f"File cache persistence: {'✅ PASS' if file_cache_loaded else '❌ FAIL'}")
    print(f"Loaded {cache_stats2['memory_cache_size']} entries from file cache")
    
    # Cleanup
    await cache_service.clear_cache('all')
    await cache_service2.clear_cache('all')
    
    overall_success = (
        results_match and 
        time_target_met and 
        cache_target_met and 
        file_cache_loaded
    )
    
    print()
    print("OVERALL RESULT")
    print("=" * 60)
    print(f"S3 Cache Integration Test: {'✅ PASS' if overall_success else '❌ FAIL'}")
    
    return overall_success


async def main():
    """Run the test suite."""
    try:
        success = await test_cache_performance()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())