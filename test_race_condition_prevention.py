#!/usr/bin/env python3
"""
Focused Test Suite for Race Condition Prevention

This script tests the atomic state management and pending operation tracking
fixes that prevent race conditions during session cleanup and concurrent operations.
"""

import asyncio
import pytest
import time
import logging
import threading
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, Any, List, Optional
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockAsyncCamoufox:
    """Mock AsyncCamoufox for race condition testing"""
    
    def __init__(self, creation_delay=0.1, **kwargs):
        self.creation_delay = creation_delay
        self.browser = None
        self.created = False
        
    async def __aenter__(self):
        await asyncio.sleep(self.creation_delay)
        self.browser = MockBrowser()
        self.created = True
        return self.browser
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.browser:
            await self.browser.close()
        return False


class MockBrowser:
    """Mock browser with cleanup delays"""
    
    def __init__(self, cleanup_delay=0.05):
        self.cleanup_delay = cleanup_delay
        self.contexts = []
        self.closed = False
        self.browser = self
        
    async def new_context(self, **kwargs):
        context = MockContext()
        self.contexts.append(context)
        return context
    
    async def close(self):
        await asyncio.sleep(self.cleanup_delay)
        self.closed = True
        for context in self.contexts:
            await context.close()


class MockContext:
    """Mock context with cleanup delays"""
    
    def __init__(self, cleanup_delay=0.03):
        self.cleanup_delay = cleanup_delay
        self.pages = []
        self.closed = False
        
    async def new_page(self):
        page = MockPage()
        self.pages.append(page)
        return page
    
    async def close(self):
        await asyncio.sleep(self.cleanup_delay)
        self.closed = True
        for page in self.pages:
            await page.close()


class MockPage:
    """Mock page with cleanup delays"""
    
    def __init__(self, cleanup_delay=0.02):
        self.cleanup_delay = cleanup_delay
        self.closed = False
        self._url = "https://facebook.com/ads/library"
        self._title = "Facebook Ad Library"
        
    async def url(self):
        if self.closed:
            raise Exception("Page is closed")
        return self._url
    
    async def title(self):
        if self.closed:
            raise Exception("Page is closed")
        return self._title
    
    def is_closed(self):
        return self.closed
    
    async def close(self):
        await asyncio.sleep(self.cleanup_delay)
        self.closed = True
    
    async def reload(self, **kwargs):
        if self.closed:
            raise Exception("Cannot reload closed page")
        await asyncio.sleep(0.1)
    
    async def evaluate(self, script):
        if self.closed:
            raise Exception("Cannot evaluate on closed page")
        return {'ok': True, 'status': 200}
    
    async def set_viewport_size(self, **kwargs):
        pass
    
    async def set_extra_http_headers(self, headers: Dict[str, str]):
        pass


class ConcurrentSessionManager:
    """Test session manager for race condition testing"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger("ConcurrentSessionManager")
        
        # Session state
        self.browser = None
        self.browser_cm = None
        self.page = None
        self.context = None
        self.session_data = {}
        self._is_session_valid = False
        self._is_closing = False
        
        # Race condition tracking
        self.operation_count = 0
        self.cleanup_count = 0
        self.concurrent_operations = []
        self._state_lock = asyncio.Lock()
        
        # Metrics
        self.metrics = {
            'sessions_created': 0,
            'sessions_cleaned': 0,
            'race_conditions_detected': 0,
            'concurrent_operations_completed': 0
        }
    
    async def create_session(self):
        """Create session with race condition protection"""
        async with self._state_lock:
            if self._is_session_valid:
                self.logger.info("Session already valid, skipping creation")
                return True
            
            try:
                self.logger.info("Creating new session")
                
                # Simulate browser creation
                self.browser_cm = MockAsyncCamoufox(creation_delay=0.1)
                self.browser = await self.browser_cm.__aenter__()
                
                # Create context and page
                self.context = await self.browser.new_context()
                self.page = await self.context.new_page()
                
                # Configure page
                await self.page.set_viewport_size(width=1920, height=1080)
                
                # Mark as valid
                self._is_session_valid = True
                self.metrics['sessions_created'] += 1
                
                self.logger.info("✅ Session created successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"❌ Session creation failed: {e}")
                await self._cleanup_partial_resources()
                return False
    
    async def _cleanup_partial_resources(self):
        """Clean up partially created resources"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            
            if self.context:
                await self.context.close()
                self.context = None
            
            if self.browser_cm:
                await self.browser_cm.__aexit__(None, None, None)
                self.browser = None
                self.browser_cm = None
                
        except Exception as e:
            self.logger.warning(f"Error during partial cleanup: {e}")
    
    async def cleanup(self):
        """Cleanup with race condition protection"""
        async with self._state_lock:
            if self._is_closing:
                self.logger.warning("Cleanup already in progress")
                return
            
            self._is_closing = True
            
            try:
                self.logger.info("Starting session cleanup")
                
                # Wait for pending operations to complete
                await self._wait_for_pending_operations()
                
                # Clean up resources
                if self.page:
                    await self.page.close()
                    self.page = None
                
                if self.context:
                    await self.context.close()
                    self.context = None
                
                if self.browser_cm:
                    await self.browser_cm.__aexit__(None, None, None)
                    self.browser = None
                    self.browser_cm = None
                
                # Reset state
                self.session_data = {}
                self._is_session_valid = False
                self.metrics['sessions_cleaned'] += 1
                
                self.logger.info("✅ Session cleanup completed")
                
            except Exception as e:
                self.logger.error(f"❌ Cleanup failed: {e}")
            finally:
                self._is_closing = False
    
    async def _wait_for_pending_operations(self, timeout=5.0):
        """Wait for pending operations to complete"""
        start_time = time.time()
        while self.concurrent_operations and (time.time() - start_time) < timeout:
            pending_count = len([op for op in self.concurrent_operations if not op.done()])
            if pending_count == 0:
                break
            
            self.logger.debug(f"Waiting for {pending_count} pending operations")
            await asyncio.sleep(0.1)
        
        # Cancel any remaining operations
        for operation in self.concurrent_operations:
            if not operation.done():
                operation.cancel()
    
    async def simulate_operation(self, operation_id: int, duration: float = 0.2):
        """Simulate a session operation"""
        operation_task = asyncio.current_task()
        self.concurrent_operations.append(operation_task)
        
        try:
            self.operation_count += 1
            self.logger.debug(f"Starting operation {operation_id}")
            
            # Check if session is valid
            if not self._is_session_valid or self._is_closing:
                self.logger.warning(f"Operation {operation_id} aborted - session invalid")
                return f"Operation {operation_id} aborted"
            
            # Simulate work
            for i in range(int(duration * 10)):
                if self._is_closing:
                    self.logger.info(f"Operation {operation_id} interrupted by cleanup")
                    break
                
                await asyncio.sleep(0.02)
                
                # Simulate accessing session resources
                if self.page and not self.page.is_closed():
                    try:
                        url = await self.page.url()
                    except Exception:
                        # Page might be closed by cleanup
                        break
            
            self.metrics['concurrent_operations_completed'] += 1
            result = f"Operation {operation_id} completed"
            self.logger.debug(result)
            return result
            
        except asyncio.CancelledError:
            self.logger.info(f"Operation {operation_id} cancelled")
            return f"Operation {operation_id} cancelled"
        except Exception as e:
            self.logger.error(f"Operation {operation_id} failed: {e}")
            return f"Operation {operation_id} failed: {e}"
        finally:
            if operation_task in self.concurrent_operations:
                self.concurrent_operations.remove(operation_task)
    
    def is_session_valid(self):
        """Check if session is valid"""
        return self._is_session_valid and not self._is_closing


async def test_concurrent_operations_during_cleanup():
    """Test concurrent operations during cleanup don't cause race conditions"""
    logger.info("Testing concurrent operations during cleanup")
    
    session_manager = ConcurrentSessionManager({})
    
    # Create session
    success = await session_manager.create_session()
    assert success, "Session creation should succeed"
    assert session_manager.is_session_valid(), "Session should be valid"
    
    # Start multiple concurrent operations
    operation_tasks = []
    for i in range(10):
        task = asyncio.create_task(
            session_manager.simulate_operation(i, duration=0.5)
        )
        operation_tasks.append(task)
    
    # Let operations start
    await asyncio.sleep(0.1)
    
    # Start cleanup while operations are running
    cleanup_task = asyncio.create_task(session_manager.cleanup())
    
    # Wait for cleanup to complete
    await cleanup_task
    
    # Wait for operations to complete/cancel
    results = await asyncio.gather(*operation_tasks, return_exceptions=True)
    
    # Verify session is cleaned up
    assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
    assert session_manager.page is None, "Page should be cleaned up"
    assert session_manager.browser is None, "Browser should be cleaned up"
    
    # Count results
    completed = sum(1 for r in results if isinstance(r, str) and "completed" in r)
    cancelled = sum(1 for r in results if isinstance(r, str) and "cancelled" in r)
    aborted = sum(1 for r in results if isinstance(r, str) and "aborted" in r)
    
    logger.info(f"Operations: {completed} completed, {cancelled} cancelled, {aborted} aborted")
    
    # Should have some operations in each category
    assert completed + cancelled + aborted == 10, "All operations should be accounted for"
    
    logger.info("✅ Concurrent operations during cleanup test passed")


async def test_atomic_state_management():
    """Test atomic state management during critical operations"""
    logger.info("Testing atomic state management")
    
    session_manager = ConcurrentSessionManager({})
    
    # Test concurrent session creation
    create_tasks = []
    for i in range(5):
        task = asyncio.create_task(session_manager.create_session())
        create_tasks.append(task)
    
    results = await asyncio.gather(*create_tasks, return_exceptions=True)
    
    # Only one session should be created (others should see existing session)
    success_count = sum(1 for r in results if r is True)
    assert success_count >= 1, "At least one session creation should succeed"
    assert session_manager.metrics['sessions_created'] == 1, "Only one session should be created"
    
    # Test concurrent cleanup
    cleanup_tasks = []
    for i in range(3):
        task = asyncio.create_task(session_manager.cleanup())
        cleanup_tasks.append(task)
    
    await asyncio.gather(*cleanup_tasks, return_exceptions=True)
    
    # Session should be properly cleaned up
    assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
    assert session_manager.metrics['sessions_cleaned'] == 1, "Only one cleanup should execute"
    
    logger.info("✅ Atomic state management test passed")


async def test_pending_operations_tracking():
    """Test tracking and handling of pending operations"""
    logger.info("Testing pending operations tracking")
    
    session_manager = ConcurrentSessionManager({})
    
    await session_manager.create_session()
    
    # Start long-running operations
    long_operations = []
    for i in range(5):
        task = asyncio.create_task(
            session_manager.simulate_operation(i, duration=2.0)  # 2 second operations
        )
        long_operations.append(task)
    
    # Let operations start
    await asyncio.sleep(0.2)
    
    # Verify operations are tracked
    assert len(session_manager.concurrent_operations) >= 5, \
        "Concurrent operations should be tracked"
    
    # Start cleanup
    cleanup_start = time.time()
    cleanup_task = asyncio.create_task(session_manager.cleanup())
    
    # Operations should be allowed to complete or be cancelled gracefully
    await cleanup_task
    cleanup_duration = time.time() - cleanup_start
    
    logger.info(f"Cleanup completed in {cleanup_duration:.2f}s")
    
    # Wait for operations to finish
    await asyncio.gather(*long_operations, return_exceptions=True)
    
    # No operations should still be tracked
    assert len(session_manager.concurrent_operations) == 0, \
        "No operations should remain tracked after cleanup"
    
    logger.info("✅ Pending operations tracking test passed")


async def test_cleanup_sequencing():
    """Test proper sequencing of cleanup operations"""
    logger.info("Testing cleanup sequencing")
    
    session_manager = ConcurrentSessionManager({})
    
    await session_manager.create_session()
    
    # Track cleanup sequence
    cleanup_sequence = []
    
    # Mock cleanup methods to track order
    original_page_close = session_manager.page.close
    original_context_close = session_manager.context.close
    original_browser_exit = session_manager.browser_cm.__aexit__
    
    async def tracked_page_close():
        cleanup_sequence.append("page_close")
        return await original_page_close()
    
    async def tracked_context_close():
        cleanup_sequence.append("context_close")
        return await original_context_close()
    
    async def tracked_browser_exit(*args):
        cleanup_sequence.append("browser_exit")
        return await original_browser_exit(*args)
    
    session_manager.page.close = tracked_page_close
    session_manager.context.close = tracked_context_close
    session_manager.browser_cm.__aexit__ = tracked_browser_exit
    
    # Perform cleanup
    await session_manager.cleanup()
    
    # Verify cleanup sequence (page -> context -> browser)
    expected_sequence = ["page_close", "context_close", "browser_exit"]
    assert cleanup_sequence == expected_sequence, \
        f"Cleanup sequence should be {expected_sequence}, got {cleanup_sequence}"
    
    logger.info("✅ Cleanup sequencing test passed")


async def test_state_consistency_under_load():
    """Test state consistency under high concurrent load"""
    logger.info("Testing state consistency under load")
    
    session_manager = ConcurrentSessionManager({})
    
    # Create session
    await session_manager.create_session()
    
    # High load test: many operations and state checks
    async def stress_operation(op_id: int):
        """Stress test operation with random delays"""
        try:
            delay = random.uniform(0.01, 0.1)
            await asyncio.sleep(delay)
            
            # Random operation type
            if random.choice([True, False]):
                return await session_manager.simulate_operation(op_id, duration=0.1)
            else:
                # State check operation
                is_valid = session_manager.is_session_valid()
                return f"State check {op_id}: {'valid' if is_valid else 'invalid'}"
                
        except Exception as e:
            return f"Operation {op_id} exception: {e}"
    
    # Start many concurrent operations
    stress_tasks = []
    for i in range(50):
        task = asyncio.create_task(stress_operation(i))
        stress_tasks.append(task)
    
    # Let operations run for a bit
    await asyncio.sleep(0.3)
    
    # Trigger cleanup during high load
    cleanup_task = asyncio.create_task(session_manager.cleanup())
    
    # Wait for everything to complete
    await cleanup_task
    results = await asyncio.gather(*stress_tasks, return_exceptions=True)
    
    # Analyze results
    exceptions = [r for r in results if isinstance(r, Exception)]
    successes = [r for r in results if isinstance(r, str)]
    
    logger.info(f"Stress test results: {len(successes)} successful, {len(exceptions)} exceptions")
    
    # Should have mostly successful operations with some cancelled/aborted
    assert len(exceptions) == 0, f"No exceptions should occur, got: {exceptions}"
    assert len(successes) > 0, "Should have some successful operations"
    
    # Session should be consistently cleaned up
    assert not session_manager.is_session_valid(), "Session should be invalid after cleanup"
    
    logger.info("✅ State consistency under load test passed")


async def test_resource_leak_prevention():
    """Test that race conditions don't cause resource leaks"""
    logger.info("Testing resource leak prevention")
    
    initial_tasks = len(asyncio.all_tasks())
    
    # Create and destroy sessions rapidly with concurrent operations
    for cycle in range(5):
        session_manager = ConcurrentSessionManager({})
        
        # Create session
        await session_manager.create_session()
        
        # Start operations
        operation_tasks = []
        for i in range(10):
            task = asyncio.create_task(
                session_manager.simulate_operation(i, duration=0.2)
            )
            operation_tasks.append(task)
        
        # Quick cleanup (might interrupt operations)
        await asyncio.sleep(0.05)
        cleanup_task = asyncio.create_task(session_manager.cleanup())
        
        # Wait for cleanup
        await cleanup_task
        
        # Cancel any remaining operations
        for task in operation_tasks:
            if not task.done():
                task.cancel()
        
        # Wait for cancellations
        await asyncio.gather(*operation_tasks, return_exceptions=True)
        
        logger.info(f"Cycle {cycle + 1} completed")
    
    # Give time for cleanup
    await asyncio.sleep(0.5)
    
    # Check task count (should not have increased significantly)
    final_tasks = len(asyncio.all_tasks())
    task_increase = final_tasks - initial_tasks
    
    logger.info(f"Task count: initial={initial_tasks}, final={final_tasks}, increase={task_increase}")
    
    # Should not have a significant increase in background tasks
    assert task_increase < 10, f"Too many leaked tasks: {task_increase}"
    
    logger.info("✅ Resource leak prevention test passed")


async def test_concurrent_session_recreation():
    """Test concurrent session recreation scenarios"""
    logger.info("Testing concurrent session recreation")
    
    session_manager = ConcurrentSessionManager({})
    
    # Test scenario: create, cleanup, and recreate concurrently
    async def create_cleanup_cycle(cycle_id: int):
        """Create and cleanup session cycle"""
        try:
            # Create
            success = await session_manager.create_session()
            if success:
                # Do some work
                await session_manager.simulate_operation(cycle_id, duration=0.1)
                # Cleanup
                await session_manager.cleanup()
                return f"Cycle {cycle_id} completed"
            else:
                return f"Cycle {cycle_id} create failed"
        except Exception as e:
            return f"Cycle {cycle_id} exception: {e}"
    
    # Run multiple create/cleanup cycles concurrently
    cycle_tasks = []
    for i in range(8):
        # Stagger start times slightly
        await asyncio.sleep(0.01)
        task = asyncio.create_task(create_cleanup_cycle(i))
        cycle_tasks.append(task)
    
    results = await asyncio.gather(*cycle_tasks, return_exceptions=True)
    
    # Analyze results
    completed = sum(1 for r in results if isinstance(r, str) and "completed" in r)
    failed = sum(1 for r in results if isinstance(r, str) and "failed" in r)
    exceptions = sum(1 for r in results if isinstance(r, Exception))
    
    logger.info(f"Recreation test: {completed} completed, {failed} failed, {exceptions} exceptions")
    
    # Should have some successful cycles
    assert completed > 0, "Should have some successful create/cleanup cycles"
    assert exceptions == 0, f"Should not have exceptions, got: {[r for r in results if isinstance(r, Exception)]}"
    
    # Final state should be clean
    assert not session_manager.is_session_valid(), "Final session should be invalid"
    
    logger.info("✅ Concurrent session recreation test passed")


async def run_all_race_condition_tests():
    """Run all race condition prevention tests"""
    logger.info("🚀 Starting Race Condition Prevention Tests")
    
    tests = [
        test_concurrent_operations_during_cleanup,
        test_atomic_state_management,
        test_pending_operations_tracking,
        test_cleanup_sequencing,
        test_state_consistency_under_load,
        test_resource_leak_prevention,
        test_concurrent_session_recreation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            start_time = time.time()
            await test()
            duration = time.time() - start_time
            logger.info(f"✅ {test.__name__} passed ({duration:.2f}s)")
            passed += 1
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test.__name__} failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n🏁 Race Condition Prevention Tests Complete")
    logger.info(f"📊 Results: {passed} passed, {failed} failed, {len(tests)} total")
    
    return passed, failed


if __name__ == "__main__":
    """Main execution"""
    import sys
    
    logger.info("🔬 Race Condition Prevention Test Suite")
    logger.info("=" * 60)
    
    try:
        passed, failed = asyncio.run(run_all_race_condition_tests())
        
        if failed > 0:
            logger.error(f"\n💥 {failed} test(s) failed!")
            sys.exit(1)
        else:
            logger.info(f"\n🎉 All {passed} tests passed!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Test suite failed: {e}")
        sys.exit(1)