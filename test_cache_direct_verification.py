#!/usr/bin/env python3
"""
Direct test of BrowserImageCacheExtractor to verify cache sharing behavior.
"""

import asyncio
import time
from unittest.mock import MagicMock
from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor, 
    CachedImageResource
)

async def test_single_instance_cache_sharing():
    """Test that a single cache instance properly shares data between async tasks."""
    print("🧪 TESTING SINGLE INSTANCE CACHE SHARING")
    print("=" * 50)
    
    # Create a single cache extractor instance
    config = {"camoufox": {"image_cache": {"enabled": True}}}
    logger = MagicMock()
    cache_extractor = BrowserImageCacheExtractor(
        logger=logger,
        config=config,
        max_cache_size_mb=10,
        cache_ttl_minutes=60,
        enable_disk_persistence=False
    )
    
    # Test URLs
    test_urls = [
        ("https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/111_222_333_n.jpg?stp=dst-jpg_s60x60", "thumbnail"),
        ("https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/111_222_333_n.jpg?stp=dst-jpg_s600x600", "fullsize"),
        ("https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/111_222_333_n.jpg?_nc_cat=999", "different_cdn"),
    ]
    
    # Store the thumbnail version
    print("📦 Storing thumbnail image...")
    thumbnail_url = test_urls[0][0]
    thumbnail_content = b"test_thumbnail_content"
    cached_resource = CachedImageResource(
        url=thumbnail_url,
        content=thumbnail_content,
        content_type="image/jpeg",
        content_length=len(thumbnail_content),
        timestamp=time.time(),
        source="test"
    )
    await cache_extractor._store_cached_image(cached_resource)
    
    # Try to retrieve different variations
    print("\n🔍 Testing cache retrieval with URL variations:")
    for url, variation_type in test_urls:
        cached_image = await cache_extractor.get_cached_image(url)
        if cached_image:
            print(f"  ✅ CACHE HIT ({variation_type}): Found {cached_image.content_length} bytes")
        else:
            print(f"  ❌ CACHE MISS ({variation_type}): Not found")
    
    # Check normalization
    print("\n🔗 Testing URL normalization:")
    for url, variation_type in test_urls:
        normalized = cache_extractor._normalize_facebook_image_url(url)
        print(f"  {variation_type}: {normalized[:80]}...")
    
    # Get cache stats
    stats = cache_extractor.get_cache_stats()
    print(f"\n📊 Cache Statistics:")
    print(f"  Images cached: {stats['cached_images']}")
    print(f"  Cache hits: {stats['cache_hits']}")
    print(f"  Cache misses: {stats['cache_misses']}")
    print(f"  Hit rate: {stats['hit_rate_percent']}%")
    
    return cache_extractor

async def test_multiworker_simulation():
    """Simulate multiple workers using the same cache instance."""
    print("\n\n🧪 TESTING MULTI-WORKER CACHE SHARING (ASYNCIO)")
    print("=" * 50)
    
    # Create shared cache instance
    config = {"camoufox": {"image_cache": {"enabled": True}}}
    logger = MagicMock()
    shared_cache = BrowserImageCacheExtractor(
        logger=logger,
        config=config,
        max_cache_size_mb=50,
        cache_ttl_minutes=60,
        enable_disk_persistence=False
    )
    
    # Worker function
    async def worker(worker_id: int, cache: BrowserImageCacheExtractor):
        """Simulate a worker processing images."""
        base_urls = [
            "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/worker_test_{}_n.jpg",
            "https://scontent.xx.fbcdn.net/v/t39.35426-6/worker_test_{}_n.jpg"
        ]
        
        if worker_id == 1:
            # Worker 1 stores images
            print(f"\nWorker {worker_id}: Storing images...")
            for i in range(3):
                url = base_urls[0].format(i) + f"?_nc_cat={100+i}"
                content = f"worker1_image_{i}".encode()
                cached_resource = CachedImageResource(
                    url=url,
                    content=content,
                    content_type="image/jpeg",
                    content_length=len(content),
                    timestamp=time.time(),
                    source=f"worker_{worker_id}"
                )
                await cache._store_cached_image(cached_resource)
                print(f"  Stored image {i}: {len(content)} bytes")
        else:
            # Other workers try to find the images
            await asyncio.sleep(0.1)  # Give worker 1 time to store
            print(f"\nWorker {worker_id}: Looking for cached images...")
            hits = 0
            for i in range(3):
                # Try with different URL variation
                url = base_urls[1].format(i) + f"?_nc_cat={200+worker_id}"
                cached_image = await cache.get_cached_image(url)
                if cached_image:
                    hits += 1
                    print(f"  ✅ Found image {i}: {cached_image.content_length} bytes")
                else:
                    print(f"  ❌ Missing image {i}")
            print(f"Worker {worker_id}: Found {hits}/3 images")
        
        return worker_id
    
    # Run multiple workers concurrently
    workers = await asyncio.gather(
        worker(1, shared_cache),
        worker(2, shared_cache),
        worker(3, shared_cache),
        worker(4, shared_cache)
    )
    
    print(f"\n✅ All {len(workers)} workers completed")
    
    # Final stats
    stats = shared_cache.get_cache_stats()
    print(f"\n📊 Final Cache Statistics:")
    print(f"  Images cached: {stats['cached_images']}")
    print(f"  Cache hits: {stats['cache_hits']}")
    print(f"  Cache misses: {stats['cache_misses']}")
    print(f"  Hit rate: {stats['hit_rate_percent']}%")
    
    # Check miss reasons
    if stats['cache_miss_reasons']:
        print(f"\n❌ Cache Miss Reasons:")
        for reason, count in stats['cache_miss_reasons'].items():
            print(f"  {reason}: {count}")

async def main():
    """Run all cache verification tests."""
    print("🎯 BROWSER IMAGE CACHE VERIFICATION SUITE")
    print("=" * 70)
    
    # Test 1: Single instance cache sharing
    cache1 = await test_single_instance_cache_sharing()
    
    # Test 2: Multi-worker simulation
    await test_multiworker_simulation()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY:")
    print("  1. Single instance cache sharing: Basic functionality tested")
    print("  2. Multi-worker simulation: Asyncio concurrency tested")
    print("\n💡 Key Findings:")
    print("  - URL normalization is critical for cache hits")
    print("  - Different CDN hosts may cause cache misses")
    print("  - Asyncio locks ensure thread-safe access")
    
    return 0

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)