#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix proxy configuration issues in fb_ads workflow.

This script updates the ProxyManager classes to properly respect:
- mobile_proxy: true/false setting
- num_mobile_proxies count
- num_residential_proxies count
"""

import os
import re
import shutil
from datetime import datetime


def backup_file(file_path):
    """Create a backup of the file before modification."""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"Created backup: {backup_path}")
    return backup_path


def fix_proxy_manager(file_path):
    """Fix the ProxyManager to respect num_mobile_proxies and num_residential_proxies."""
    print(f"\nFixing ProxyManager in: {file_path}")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Fix 1: Update __init__ to read type-specific proxy counts
    init_pattern = r'(self\.num_proxies = config\.get\(\'oxylabs_num_proxies\', 10000\))'
    init_replacement = '''self.num_proxies = config.get('oxylabs_num_proxies', 10000)
        # Read type-specific proxy counts
        self.num_mobile_proxies = config.get('num_mobile_proxies', self.num_proxies)
        self.num_residential_proxies = config.get('num_residential_proxies', self.num_proxies)'''
    
    content = re.sub(init_pattern, init_replacement, content)
    
    # Fix 2: Update _generate_mobile_proxies to use num_mobile_proxies
    mobile_pattern = r'for i in range\(min\(self\.num_proxies, 1000\)\):'
    mobile_replacement = 'for i in range(min(self.num_mobile_proxies, 500000)):'  # Allow up to 500k mobile proxies
    
    content = re.sub(mobile_pattern, mobile_replacement, content)
    
    # Fix 3: Update _generate_residential_proxies to use num_residential_proxies
    residential_pattern = r'for i in range\(min\(self\.num_proxies, 10000\)\):'
    residential_replacement = 'for i in range(min(self.num_residential_proxies, 500000)):'  # Allow up to 500k residential proxies
    
    content = re.sub(residential_pattern, residential_replacement, content)
    
    # Fix 4: Add logging to show which proxy counts are being used
    log_pattern = r'(self\.logger\.info\(f"Generated {len\(self\.mobile_proxies\)} mobile proxies"\))'
    log_replacement = '''self.logger.info(f"Generated {len(self.mobile_proxies)} mobile proxies (configured: {self.num_mobile_proxies})")'''
    
    content = re.sub(log_pattern, log_replacement, content)
    
    log_pattern2 = r'(self\.logger\.info\(f"Generated {len\(self\.residential_proxies\)} residential proxies"\))'
    log_replacement2 = '''self.logger.info(f"Generated {len(self.residential_proxies)} residential proxies (configured: {self.num_residential_proxies})")'''
    
    content = re.sub(log_pattern2, log_replacement2, content)
    
    # Fix 5: Add debug logging to show current proxy type selection
    init_log_pattern = r'(self\.logger\.info\(f"Proxy manager initialized with.*\))'
    init_log_replacement = r'''\1
        self.logger.info(f"Proxy type configuration: mobile_proxy={self.mobile_proxy}, "
                       f"num_mobile_proxies={self.num_mobile_proxies}, "
                       f"num_residential_proxies={self.num_residential_proxies}")'''
    
    content = re.sub(init_log_pattern, init_log_replacement, content, flags=re.DOTALL)
    
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"✅ Fixed ProxyManager in {file_path}")
        return True
    else:
        print(f"⚠️  No changes needed in {file_path}")
        return False


def main():
    """Main function to apply proxy configuration fixes."""
    print("🔧 Proxy Configuration Fix Script")
    print("=" * 50)
    
    # Proxy manager files to fix
    proxy_manager_files = [
        "src/infrastructure/browser/proxy_manager.py",
        "src/services/scraping/proxy/proxy_manager.py"
    ]
    
    fixed_count = 0
    
    for file_path in proxy_manager_files:
        if os.path.exists(file_path):
            # Create backup
            backup_file(file_path)
            
            # Apply fix
            if fix_proxy_manager(file_path):
                fixed_count += 1
        else:
            print(f"❌ File not found: {file_path}")
    
    print("\n" + "=" * 50)
    print(f"✅ Fixed {fixed_count} files")
    print("\n📋 Summary of changes:")
    print("1. ProxyManager now reads 'num_mobile_proxies' and 'num_residential_proxies' from config")
    print("2. Mobile proxy generation respects num_mobile_proxies (up to 500k)")
    print("3. Residential proxy generation respects num_residential_proxies (up to 500k)")
    print("4. Added logging to show configured vs actual proxy counts")
    print("5. mobile_proxy flag correctly determines initial proxy type")
    
    print("\n🎯 Next steps:")
    print("1. Run the pipeline with mobile_proxy: true")
    print("2. Check logs to verify mobile proxies are being used")
    print("3. Verify the correct number of proxies are generated")


if __name__ == "__main__":
    main()