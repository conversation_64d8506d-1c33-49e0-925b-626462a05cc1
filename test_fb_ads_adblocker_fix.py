#!/usr/bin/env python3
"""
Test script to verify Facebook ads ad blocker fix.
This script runs a single firm scrape and checks for ad blocker detection issues.
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scripts.debug_fb_ads_single_firm import FBAdsSingleFirmDebugger


import subprocess


def test_adblocker_fix():
    """Test the ad blocker fix by running the pipeline."""
    print("🧪 Testing Facebook Ads Ad Blocker Fix via run_pipeline.sh")
    print("=" * 50)

    try:
        subprocess.run(["./run_pipeline.sh", "--config", "fb_ads"], check=True)

        print("\n✅ Test completed successfully!")
        print("\n📊 Check the following:")
        print("1. Review the pipeline logs for any ad blocker modal errors.")

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    test_adblocker_fix()


if __name__ == "__main__":
    main()
