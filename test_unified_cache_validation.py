#!/usr/bin/env python3
"""
Comprehensive validation tests for the unified cache implementation.

This script validates that:
1. Cache hits occur for previously scraped images 
2. Redundant proxy downloads are eliminated
3. "CACHE HIT" messages appear in logs
4. Cache unification is working correctly
5. Performance improvements are measurable
"""

import asyncio
import time
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any
from unittest.mock import MagicMock, AsyncMock, patch
import pytest

from src.services.fb_ads.browser_image_cache_extractor import (
    BrowserImageCacheExtractor,
    CachedImageResource
)

class UnifiedCacheValidator:
    """Comprehensive validation of unified cache implementation"""
    
    def __init__(self):
        self.test_results = {}
        self.cache_extractor = None
        self.temp_dir = None
        self.bandwidth_saved = 0
        self.cache_hits_detected = 0
        self.redundant_downloads_prevented = 0
        
    async def setup_test_environment(self):
        """Set up test environment with temporary cache directory"""
        print("🔧 Setting up test environment...")
        
        # Create temporary directory for disk persistence tests
        self.temp_dir = tempfile.mkdtemp(prefix="cache_validation_")
        
        # Create cache extractor with unified cache enabled
        config = {
            "camoufox": {
                "image_cache": {
                    "enabled": True,
                    "max_cache_size_mb": 100,
                    "cache_ttl_minutes": 1440  # 24 hours
                }
            }
        }
        
        # Mock logger to capture cache hit messages
        self.mock_logger = MagicMock()
        self.cache_extractor = BrowserImageCacheExtractor(
            logger=self.mock_logger,
            config=config,
            max_cache_size_mb=100,
            cache_ttl_minutes=1440,
            enable_disk_persistence=True
        )
        
        # Override disk cache directory to use temp directory
        self.cache_extractor._disk_cache_dir = Path(self.temp_dir) / '.image_cache'
        self.cache_extractor._disk_cache_dir.mkdir(parents=True, exist_ok=True)
        (self.cache_extractor._disk_cache_dir / 'images').mkdir(exist_ok=True)
        (self.cache_extractor._disk_cache_dir / 'metadata').mkdir(exist_ok=True)
        
        print(f"✅ Test environment ready (temp dir: {self.temp_dir})")
        
    async def cleanup_test_environment(self):
        """Clean up test environment"""
        if self.temp_dir and Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
            print(f"🧹 Cleaned up temp directory: {self.temp_dir}")
    
    async def test_1_cache_hit_validation(self) -> bool:
        """Test 1: Validate cache hits occur for previously scraped images"""
        print("\n🧪 TEST 1: Cache Hit Validation")
        
        try:
            # Simulate initial scraping - cache some images
            initial_images = [
                {
                    "url": (
                        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                        "447181435_1029863199156819_7025413177941515490_n.jpg"
                        "?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
                    ),
                    "content": b"initial_thumbnail_content_1",
                    "ad_id": "ad_001"
                },
                {
                    "url": (
                        "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                        "460370463_1037577531718719_2663569665313904600_n.jpg"
                        "?stp=dst-jpg_s60x60_tt6&_nc_cat=104&ccb=1-7&_nc_sid=cf96c8"
                    ),
                    "content": b"initial_thumbnail_content_2",
                    "ad_id": "ad_002"
                },
                {
                    "url": (
                        "https://z-m-scontent.xx.fbcdn.net/v/t39.35426-6/"
                        "515043210_987654321_1122334455667788_n.jpg"
                        "?_nc_cat=110&oh=mobileHash&oe=mobileOE"
                    ),
                    "content": b"initial_mobile_content",
                    "ad_id": "ad_003"
                }
            ]
            
            # Cache the images (simulating initial scraping)
            for img in initial_images:
                cached_resource = CachedImageResource(
                    url=img["url"],
                    content=img["content"],
                    content_type="image/jpeg",
                    content_length=len(img["content"]),
                    timestamp=time.time(),
                    ad_archive_id=img["ad_id"],
                    source="validation_test"
                )
                await self.cache_extractor._store_cached_image(cached_resource)
            
            print(f"   📦 Cached {len(initial_images)} images during initial scraping")
            
            # Now simulate subsequent requests with different parameters
            subsequent_requests = [
                {
                    "url": (
                        "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                        "447181435_1029863199156819_7025413177941515490_n.jpg"
                        "?stp=dst-jpg_s600x600_tt6&_nc_cat=101&ccb=1-7&_nc_sid=cf96c8"
                        "&_nc_ohc=DifferentHash&oh=00_NewValue&oe=NewTimestamp"
                    ),
                    "expected_content": b"initial_thumbnail_content_1"
                },
                {
                    "url": (
                        "https://scontent-dfw5-2.xx.fbcdn.net/v/t39.35426-6/"
                        "460370463_1037577531718719_2663569665313904600_n.jpg"
                        "?stp=dst-jpg_s1200x1200_tt6&_nc_cat=999&ccb=2-8&_nc_sid=different"
                        "&_nc_ohc=CompletelyDifferent&oh=00_BrandNew&oe=AllNew"
                    ),
                    "expected_content": b"initial_thumbnail_content_2"
                },
                {
                    "url": (
                        "https://scontent.xx.fbcdn.net/v/t39.35426-6/"
                        "515043210_987654321_1122334455667788_n.jpg"
                        "?_nc_cat=888&oh=desktopHash&oe=desktopOE&stp=dst-jpg_tt6"
                    ),
                    "expected_content": b"initial_mobile_content"
                }
            ]
            
            # Test cache hits
            cache_hits = 0
            cache_misses = 0
            
            for request in subsequent_requests:
                cached_image = await self.cache_extractor.get_cached_image(request["url"])
                
                if cached_image:
                    cache_hits += 1
                    self.cache_hits_detected += 1
                    
                    # Validate content matches
                    if cached_image.content == request["expected_content"]:
                        print(f"   ✅ CACHE HIT: Found cached image for {request['url'][:80]}...")
                    else:
                        print(f"   ❌ CACHE HIT but wrong content for {request['url'][:80]}...")
                        return False
                else:
                    cache_misses += 1
                    print(f"   ❌ CACHE MISS: No cached image for {request['url'][:80]}...")
            
            # Validate results
            expected_hits = len(subsequent_requests)
            hit_rate = (cache_hits / expected_hits) * 100 if expected_hits > 0 else 0
            
            print(f"   📊 Cache Results: {cache_hits}/{expected_hits} hits ({hit_rate:.1f}% hit rate)")
            
            # Check for "CACHE HIT" log messages
            cache_hit_logs = [
                call for call in self.mock_logger.method_calls 
                if 'CACHE HIT' in str(call)
            ]
            
            print(f"   📝 Found {len(cache_hit_logs)} 'CACHE HIT' log messages")
            
            # Test passes if we get high cache hit rate
            test_passed = hit_rate >= 90.0 and len(cache_hit_logs) > 0
            
            if test_passed:
                print("   🎉 TEST 1 PASSED: Cache hits working correctly!")
                self.bandwidth_saved += sum(len(img["content"]) for img in initial_images) * cache_hits
            else:
                print(f"   ❌ TEST 1 FAILED: Low hit rate ({hit_rate:.1f}%) or missing logs")
            
            return test_passed
            
        except Exception as e:
            print(f"   ❌ TEST 1 ERROR: {e}")
            return False
    
    async def test_2_redundant_download_prevention(self) -> bool:
        """Test 2: Verify redundant proxy downloads are eliminated"""
        print("\n🧪 TEST 2: Redundant Download Prevention")
        
        try:
            # Create mock bandwidth logger to track downloads
            mock_bandwidth_logger = MagicMock()
            self.cache_extractor.bandwidth_logger = mock_bandwidth_logger
            
            # Test scenario: same image requested multiple times
            test_image_url = (
                "https://scontent-dfw5-1.xx.fbcdn.net/v/t39.35426-6/"
                "redundancy_test_123_456_789_n.jpg"
            )
            
            # Variations of the same image URL with different parameters
            url_variations = [
                f"{test_image_url}?stp=dst-jpg_s60x60_tt6&_nc_cat=101&ccb=1-7",
                f"{test_image_url}?stp=dst-jpg_s600x600_tt6&_nc_cat=999&ccb=2-8",
                f"{test_image_url}?stp=dst-jpg_s1200x1200_tt6&_nc_cat=555&ccb=3-9",
                f"{test_image_url}?w=400&h=400&quality=80&_nc_cat=777",
                f"{test_image_url}?random=params&session=different&_nc_cat=123"
            ]
            
            # First request - should result in cache storage
            initial_content = b"redundancy_test_content_original"
            cached_resource = CachedImageResource(
                url=url_variations[0],
                content=initial_content,
                content_type="image/jpeg",
                content_length=len(initial_content),
                timestamp=time.time(),
                source="validation_test"
            )
            await self.cache_extractor._store_cached_image(cached_resource)
            
            print(f"   📦 Cached initial version of image ({len(initial_content)} bytes)")
            
            # Subsequent requests - should hit cache, preventing downloads
            downloads_prevented = 0
            
            for i, url_variation in enumerate(url_variations[1:], 1):
                cached_image = await self.cache_extractor.get_cached_image(url_variation)
                
                if cached_image:
                    downloads_prevented += 1
                    self.redundant_downloads_prevented += 1
                    print(f"   ✅ Download #{i} prevented - cache hit for variation")
                else:
                    print(f"   ❌ Download #{i} NOT prevented - cache miss for variation")
            
            # Check bandwidth logger wasn't called for redundant requests
            # (It should only be called during initial caching, not for cache hits)
            bandwidth_logs = mock_bandwidth_logger.method_calls
            
            prevention_rate = (downloads_prevented / (len(url_variations) - 1)) * 100
            
            print(f"   📊 Prevention Results: {downloads_prevented}/{len(url_variations)-1} downloads prevented ({prevention_rate:.1f}%)")
            
            # Test passes if most downloads were prevented
            test_passed = prevention_rate >= 80.0
            
            if test_passed:
                print("   🎉 TEST 2 PASSED: Redundant downloads prevented successfully!")
                self.bandwidth_saved += len(initial_content) * downloads_prevented
            else:
                print(f"   ❌ TEST 2 FAILED: Low prevention rate ({prevention_rate:.1f}%)")
            
            return test_passed
            
        except Exception as e:
            print(f"   ❌ TEST 2 ERROR: {e}")
            return False
    
    async def test_3_cache_hit_log_messages(self) -> bool:
        """Test 3: Validate 'CACHE HIT' messages appear in logs"""
        print("\n🧪 TEST 3: Cache Hit Log Messages")
        
        try:
            # Clear previous log calls
            self.mock_logger.reset_mock()
            
            # Cache an image
            test_url = (
                "https://scontent.fbcdn.net/v/t39.35426-6/"
                "log_test_111_222_333_n.jpg?_nc_cat=101"
            )
            
            test_content = b"log_test_content"
            cached_resource = CachedImageResource(
                url=test_url,
                content=test_content,
                content_type="image/jpeg",
                content_length=len(test_content),
                timestamp=time.time(),
                source="validation_test"
            )
            await self.cache_extractor._store_cached_image(cached_resource)
            
            # Request the same image with different parameters
            lookup_url = (
                "https://scontent.fbcdn.net/v/t39.35426-6/"
                "log_test_111_222_333_n.jpg?_nc_cat=999&oh=different&oe=different"
            )
            
            cached_image = await self.cache_extractor.get_cached_image(lookup_url)
            
            # Check for cache hit log messages
            all_log_calls = [str(call) for call in self.mock_logger.method_calls]
            cache_hit_messages = [
                call for call in all_log_calls 
                if 'CACHE HIT' in call and ('normalized' in call or 'prefix' in call)
            ]
            
            print(f"   📝 Total log calls: {len(all_log_calls)}")
            print(f"   🎯 Cache hit messages found: {len(cache_hit_messages)}")
            
            # Print some example cache hit messages
            for i, message in enumerate(cache_hit_messages[:3]):
                print(f"   📄 Message {i+1}: ...{message[-60:]}")
            
            # Test specific log patterns that indicate cache hits
            log_patterns_found = {
                'cache_hit_basic': any('CACHE HIT' in msg for msg in all_log_calls),
                'cache_hit_normalized': any('normalized' in msg and 'CACHE HIT' in msg for msg in all_log_calls),
                'cache_hit_prefix': any('prefix' in msg and 'CACHE HIT' in msg for msg in all_log_calls),
                'cache_hit_with_details': any('Found' in msg and 'byte' in msg for msg in all_log_calls)
            }
            
            patterns_found = sum(log_patterns_found.values())
            print(f"   🔍 Log patterns found: {patterns_found}/4")
            
            for pattern, found in log_patterns_found.items():
                status = "✅" if found else "❌"
                print(f"   {status} {pattern.replace('_', ' ').title()}")
            
            # Test passes if we found cache hit messages
            test_passed = cached_image is not None and len(cache_hit_messages) > 0 and patterns_found >= 2
            
            if test_passed:
                print("   🎉 TEST 3 PASSED: Cache hit log messages working correctly!")
            else:
                print("   ❌ TEST 3 FAILED: Missing or insufficient cache hit log messages")
            
            return test_passed
            
        except Exception as e:
            print(f"   ❌ TEST 3 ERROR: {e}")
            return False
    
    async def test_4_disk_persistence_validation(self) -> bool:
        """Test 4: Validate disk persistence is working"""
        print("\n🧪 TEST 4: Disk Persistence Validation")
        
        try:
            # Check that disk cache directory was created
            disk_cache_dir = self.cache_extractor._disk_cache_dir
            images_dir = disk_cache_dir / 'images'
            metadata_dir = disk_cache_dir / 'metadata'
            
            dirs_exist = all([
                disk_cache_dir.exists(),
                images_dir.exists(), 
                metadata_dir.exists()
            ])
            
            print(f"   📁 Cache directories exist: {dirs_exist}")
            print(f"      Cache dir: {disk_cache_dir}")
            print(f"      Images dir: {images_dir}")
            print(f"      Metadata dir: {metadata_dir}")
            
            # Cache an image and verify it's saved to disk
            disk_test_url = (
                "https://scontent.fbcdn.net/v/t39.35426-6/"
                "disk_test_444_555_666_n.jpg?_nc_cat=101"
            )
            
            disk_test_content = b"disk_persistence_test_content"
            cached_resource = CachedImageResource(
                url=disk_test_url,
                content=disk_test_content,
                content_type="image/jpeg",
                content_length=len(disk_test_content),
                timestamp=time.time(),
                source="validation_test"
            )
            
            await self.cache_extractor._store_cached_image(cached_resource)
            
            # Wait a moment for disk write to complete
            await asyncio.sleep(0.1)
            
            # Check disk files were created
            image_files = list(images_dir.glob('*.jpg'))
            metadata_files = list(metadata_dir.glob('*.json'))
            
            print(f"   💾 Disk files created:")
            print(f"      Image files: {len(image_files)}")
            print(f"      Metadata files: {len(metadata_files)}")
            
            # Verify file contents
            disk_validation_passed = False
            if image_files and metadata_files:
                # Read back the image file
                image_file = image_files[0]
                with open(image_file, 'rb') as f:
                    saved_content = f.read()
                
                # Read metadata file
                metadata_file = metadata_files[0]
                import json
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Validate content matches
                content_matches = saved_content == disk_test_content
                metadata_valid = (
                    'url' in metadata and
                    'content_type' in metadata and
                    'content_length' in metadata and
                    metadata['content_length'] == len(disk_test_content)
                )
                
                disk_validation_passed = content_matches and metadata_valid
                
                print(f"   ✅ Content matches: {content_matches}")
                print(f"   ✅ Metadata valid: {metadata_valid}")
            
            test_passed = dirs_exist and disk_validation_passed and len(image_files) > 0
            
            if test_passed:
                print("   🎉 TEST 4 PASSED: Disk persistence working correctly!")
            else:
                print("   ❌ TEST 4 FAILED: Disk persistence issues detected")
            
            return test_passed
            
        except Exception as e:
            print(f"   ❌ TEST 4 ERROR: {e}")
            return False
    
    async def test_5_performance_metrics_validation(self) -> bool:
        """Test 5: Validate performance improvements are measurable"""
        print("\n🧪 TEST 5: Performance Metrics Validation")
        
        try:
            # Get initial cache stats
            initial_stats = self.cache_extractor.get_cache_stats()
            
            # Perform a series of cache operations
            performance_test_urls = []
            for i in range(10):
                base_url = f"https://scontent.fbcdn.net/v/t39.35426-6/perf_test_{i}_777_888_999_n.jpg"
                
                # Cache original
                original_url = f"{base_url}?_nc_cat={100+i}"
                test_content = f"performance_test_content_{i}".encode()
                
                cached_resource = CachedImageResource(
                    url=original_url,
                    content=test_content,
                    content_type="image/jpeg",
                    content_length=len(test_content),
                    timestamp=time.time(),
                    source="performance_test"
                )
                await self.cache_extractor._store_cached_image(cached_resource)
                
                # Create variation URLs for cache hit testing
                variations = [
                    f"{base_url}?_nc_cat={200+i}&oh=hash{i}&oe=timestamp{i}",
                    f"{base_url}?stp=dst-jpg_s600x600_tt6&_nc_cat={300+i}",
                    f"{base_url}?w=400&h=400&quality=80&_nc_cat={400+i}"
                ]
                performance_test_urls.extend(variations)
            
            # Time cache lookup performance
            start_time = time.time()
            
            hits = 0
            total_bytes_from_cache = 0
            
            for url in performance_test_urls:
                cached_image = await self.cache_extractor.get_cached_image(url)
                if cached_image:
                    hits += 1
                    total_bytes_from_cache += cached_image.content_length
            
            lookup_time = time.time() - start_time
            avg_lookup_time = lookup_time / len(performance_test_urls)
            
            # Get final cache stats
            final_stats = self.cache_extractor.get_cache_stats()
            
            # Calculate performance metrics
            hit_rate = (hits / len(performance_test_urls)) * 100
            throughput = len(performance_test_urls) / lookup_time  # lookups per second
            
            print(f"   📊 Performance Results:")
            print(f"      Cache hits: {hits}/{len(performance_test_urls)} ({hit_rate:.1f}%)")
            print(f"      Total lookup time: {lookup_time:.3f}s")
            print(f"      Average lookup time: {avg_lookup_time*1000:.2f}ms")
            print(f"      Lookup throughput: {throughput:.1f} lookups/sec")
            print(f"      Bytes served from cache: {total_bytes_from_cache:,}")
            
            print(f"   📈 Cache Statistics:")
            print(f"      Cached images: {final_stats['cached_images']}")
            print(f"      Cache size: {final_stats['cache_size_mb']:.2f} MB")
            print(f"      Hit rate: {final_stats['hit_rate_percent']:.1f}%")
            print(f"      Total saved: {final_stats['total_saved_mb']:.2f} MB")
            
            # Performance criteria
            performance_criteria = {
                'hit_rate_good': hit_rate >= 80.0,
                'lookup_speed_fast': avg_lookup_time < 0.010,  # Under 10ms
                'throughput_high': throughput > 50,  # Over 50 lookups/sec
                'bandwidth_saved': final_stats['total_saved_mb'] > 0
            }
            
            criteria_met = sum(performance_criteria.values())
            print(f"   ✅ Performance criteria met: {criteria_met}/4")
            
            for criterion, met in performance_criteria.items():
                status = "✅" if met else "❌"
                print(f"      {status} {criterion.replace('_', ' ').title()}")
            
            test_passed = criteria_met >= 3  # At least 3 out of 4 criteria
            
            if test_passed:
                print("   🎉 TEST 5 PASSED: Performance improvements validated!")
                self.bandwidth_saved += total_bytes_from_cache
            else:
                print(f"   ❌ TEST 5 FAILED: Performance criteria not met ({criteria_met}/4)")
            
            return test_passed
            
        except Exception as e:
            print(f"   ❌ TEST 5 ERROR: {e}")
            return False
    
    async def generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        stats = self.cache_extractor.get_cache_stats()
        
        return {
            'timestamp': time.time(),
            'validation_results': self.test_results,
            'cache_statistics': stats,
            'performance_metrics': {
                'total_bandwidth_saved_bytes': self.bandwidth_saved,
                'total_cache_hits_detected': self.cache_hits_detected,
                'redundant_downloads_prevented': self.redundant_downloads_prevented,
                'bandwidth_saved_mb': self.bandwidth_saved / (1024 * 1024),
            },
            'cache_analytics': self.cache_extractor.get_cache_analytics_dashboard()
        }
    
    async def run_all_validations(self) -> bool:
        """Run all validation tests and return overall success"""
        print("🧪 UNIFIED CACHE VALIDATION TEST SUITE")
        print("=" * 60)
        
        await self.setup_test_environment()
        
        try:
            # Run all validation tests
            tests = [
                ('cache_hit_validation', self.test_1_cache_hit_validation),
                ('redundant_download_prevention', self.test_2_redundant_download_prevention),
                ('cache_hit_log_messages', self.test_3_cache_hit_log_messages),
                ('disk_persistence_validation', self.test_4_disk_persistence_validation),
                ('performance_metrics_validation', self.test_5_performance_metrics_validation),
            ]
            
            for test_name, test_func in tests:
                try:
                    result = await test_func()
                    self.test_results[test_name] = result
                except Exception as e:
                    print(f"   ❌ {test_name} failed with error: {e}")
                    self.test_results[test_name] = False
            
            # Generate report
            report = await self.generate_validation_report()
            
            # Print summary
            print("\n" + "=" * 60)
            print("📊 VALIDATION SUMMARY")
            print("=" * 60)
            
            total_tests = len(self.test_results)
            passed_tests = sum(self.test_results.values())
            success_rate = (passed_tests / total_tests) * 100
            
            print(f"Total tests: {total_tests}")
            print(f"Passed: {passed_tests}")
            print(f"Failed: {total_tests - passed_tests}")
            print(f"Success rate: {success_rate:.1f}%")
            
            print(f"\n💰 Performance Benefits:")
            print(f"   Bandwidth saved: {report['performance_metrics']['bandwidth_saved_mb']:.2f} MB")
            print(f"   Cache hits detected: {self.cache_hits_detected}")
            print(f"   Downloads prevented: {self.redundant_downloads_prevented}")
            
            print(f"\n📈 Cache Statistics:")
            cache_stats = report['cache_statistics']
            print(f"   Images cached: {cache_stats['cached_images']}")
            print(f"   Hit rate: {cache_stats['hit_rate_percent']:.1f}%")
            print(f"   Cache efficiency: {cache_stats['cache_efficiency_score']:.1f}%")
            
            # Detailed results
            print(f"\n📋 Detailed Results:")
            for test_name, passed in self.test_results.items():
                status = "✅ PASS" if passed else "❌ FAIL"
                print(f"   {status} {test_name.replace('_', ' ').title()}")
            
            # Overall assessment
            overall_success = passed_tests >= 4  # At least 4 out of 5 tests must pass
            
            print("\n" + "=" * 60)
            if overall_success:
                print("🎉 UNIFIED CACHE VALIDATION SUCCESSFUL!")
                print("✅ Cache unification is working correctly")
                print("✅ Redundant downloads are being prevented") 
                print("✅ Performance improvements are measurable")
                print("✅ Cache hit logging is functioning")
            else:
                print("❌ UNIFIED CACHE VALIDATION FAILED!")
                print("⚠️  Some critical tests failed - review results above")
            print("=" * 60)
            
            return overall_success
            
        finally:
            await self.cleanup_test_environment()

async def main():
    """Main validation entry point"""
    validator = UnifiedCacheValidator()
    success = await validator.run_all_validations()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)