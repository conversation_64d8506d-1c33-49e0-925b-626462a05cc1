# Browser Image Cache Extractor Injection Fix

## Problem
The `CamoufoxSessionManager` was receiving `None` for `browser_image_cache_extractor`, resulting in:
- No image interception functionality
- Cache misses on all image requests  
- Redundant image downloads for every firm

## Root Cause
The dependency injection chain was incomplete:
1. `browser_image_cache_extractor` was created in the FB Ads container
2. It was NOT passed to the `FacebookAdsOrchestrator` 
3. It was NOT included in `job_global_dependencies`
4. When `JobOrchestrationService` created firm-specific session managers, it couldn't pass the cache extractor

## Solution

### 1. Updated FB Ads Container (`src/containers/fb_ads.py`)
Added `browser_image_cache_extractor` to the orchestrator's dependencies:
```python
facebook_ads_orchestrator = providers.Singleton(
    FacebookAdsOrchestrator,
    # ... other dependencies ...
    browser_image_cache_extractor=browser_image_cache_extractor,  # Unified browser image cache
)
```

### 2. Updated Orchestrator (`src/services/fb_ads/orchestrator.py`)
Added `browser_image_cache_extractor` to job global dependencies:
```python
self.job_global_dependencies = {
    # ... other dependencies ...
    "browser_image_cache_extractor": self.browser_image_cache_extractor,  # Unified browser image cache
}
```

### 3. Updated Job Orchestration Service (`src/services/fb_ads/jobs/job_orchestration_service.py`)
Pass `browser_image_cache_extractor` when creating session managers:
```python
session_manager = factory.create(
    config=merged_config,
    logger=dependencies_with_progress.get("logger"),
    firm_id=job.firm_id,
    fingerprint_manager=dependencies_with_progress.get("fingerprint_manager"),
    proxy_manager=dependencies_with_progress.get("proxy_manager"),
    law_firms_repository=dependencies_with_progress.get("law_firms_repository"),
    browser_image_cache_extractor=dependencies_with_progress.get("browser_image_cache_extractor")
)
```

## Benefits
1. **Unified Cache**: Single shared cache instance across all firm-specific session managers
2. **Image Interception**: Browser can now intercept and cache images properly
3. **Reduced Bandwidth**: Images are cached and reused across firms
4. **Better Performance**: No redundant downloads for common images

## Testing
Run `test_browser_image_cache_injection.py` to verify:
1. Cache extractor exists in container
2. Orchestrator receives it
3. It's in job_global_dependencies
4. Session managers receive it properly

## Files Modified
1. `/src/containers/fb_ads.py` - Added browser_image_cache_extractor to orchestrator
2. `/src/services/fb_ads/orchestrator.py` - Added to job_global_dependencies
3. `/src/services/fb_ads/jobs/job_orchestration_service.py` - Pass to session manager factory