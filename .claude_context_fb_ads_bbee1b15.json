{"task_id": "fb_ads_bbee1b15", "config_type": "fb_ads", "error_history": [{"timestamp": "2025-07-12T18:36:03.286756", "error_type": "dependency_injection", "details": {"error_type": "dependency_injection", "config_type": "fb_ads", "full_output": "Running pipeline step: fb_ads.yml with config /Users/<USER>/PycharmProjects/lexgenius/config/fb_ads.yml\nStarting main.py with Python 3.11.13 | packaged by conda-forge | (main, Jun  4 2025, 14:52:34) [Clang 18.1.8 ]\nSet multiprocessing start method to 'spawn'.\n[07/12/25 18:36:01] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and                                                \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/pacer.log                \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/pacer.log                \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/logging_confi            \n                             g_module.log                                       \n                    INFO     Initial logging setup complete. Log    main.py:1921\n                             directory:                                         \n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data/20250712/logs                         \n                    INFO     Base data directory:                   main.py:1924\n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data                                       \n                    INFO     No end_date specified, defaulting to   main.py:2013\n                             target date: 07/12/25                              \n                    INFO     Preparing for single date run:         main.py:2062\n                             07/12/25                                           \n                    INFO     DEBUG: Merging params into config     loader.py:170\n                             data for fb_ads                                    \n                    INFO     DEBUG: process_single_court in YAML:  loader.py:171\n                             NOT_SET                                            \n                    INFO     DEBUG: process_single_court in        loader.py:172\n                             params: []                                         \n                    INFO     DEBUG: process_single_court after     loader.py:174\n                             merge: []                                          \n[07/12/25 18:36:01] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and data/20250712/logs/pacer.log                   \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             data/20250712/logs/pacer.log                       \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             data/20250712/logs/reports_services.log            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             data/20250712/logs/reports_services.log            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             data/20250712/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             data/20250712/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             data/20250712/logs/logging_config_modul            \n                             e.log                                              \n                    INFO     Logging re-configured for target date  main.py:2074\n                             07/12/25. Log dir: data/20250712/logs              \n                    INFO     --- Initializing Main Orchestration    main.py:2088\n                             for Single Date Run ---                            \n                    INFO     MainServiceFactory initialized   main_factory.py:27\n                             with config: Unknown config                        \n                    INFO     🚀 Using dependency-injector     main_factory.py:30\n                             framework                                          \n                    INFO     Entering MainServiceFactory      main_factory.py:93\n                             async context.                                     \n                    INFO     DI Container created and wired  main_factory.py:120\n                             successfully                                       \n                    INFO     MainOrchestrator initialized   component_base.py:92\n                    INFO     MainOrchestrator run started   component_base.py:92\n                    INFO     Scraping phase skipped by      component_base.py:92\n                             configuration                                      \n                    INFO     Post-processing phase skipped  component_base.py:92\n                             by configuration                                   \n                    INFO     Upload phase skipped by        component_base.py:92\n                             configuration                                      \n                    INFO     Facebook Ads processing phase  component_base.py:92\n                             initiated                                          \n                    INFO     Creating FbAdsOrchestrator via  main_factory.py:261\n                             DI Container...                                    \n                    INFO     Loaded 26 prompts              component_base.py:92\n                    WARNING  S3 storage not properly        component_base.py:99\n                             injected - S3 operations will                      \n                             be disabled                                        \n                    INFO     🔍 VALIDATION: Checking AI     component_base.py:92\n                             service requirements...                            \n                    INFO       - Available API key sources: component_base.py:92\n                             4/4                                                \n                    INFO       - AI services validation: ✅ component_base.py:92\n                             PASSED                                             \n                    INFO     FbAdsOrchestrator initialized  component_base.py:92\n                             with DI services                                   \n                    INFO     ✅ AI Orchestrator injected    component_base.py:92\n                             via DI                                             \n                    INFO     ✅ DeepSeek service injected   component_base.py:92\n                             via DI                                             \n                    INFO     ✅ Prompt Manager injected via component_base.py:92\n                             DI                                                 \n                    INFO     ✅ FbAdsOrchestrator created    main_factory.py:282\n                    INFO     Executing Facebook Ads tasks   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                             for session manager with                           \n                             periodic logging disabled                          \n                    INFO     Session manager initialized    component_base.py:92\n                             with use_proxy=True,                               \n                             mobile_proxy=False                                 \n                    INFO     Generating proxy list for      component_base.py:92\n                             RESIDENTIAL proxies                                \n                    INFO     Attempting to load credentials component_base.py:92\n                             for RESIDENTIAL proxy...                           \n                    INFO     Using specific RESIDENTIAL     component_base.py:92\n                             credentials (from config or                        \n                             env).                                              \n                    INFO     Using username:                component_base.py:92\n                             lexgenius20250612_wzykM for                        \n                             RESIDENTIAL proxy list                             \n                             generation.                                        \n                    DEBUG    Generating RESIDENTIAL proxy   component_base.py:85\n                             list for user:                                     \n                             lexgenius20250612_wzykM                            \n                    INFO     Using configured proxy count:  component_base.py:92\n                             10000 RESIDENTIAL proxies                          \n                    DEBUG    Sample RESIDENTIAL proxy       component_base.py:85\n                             format: http@...                                   \n                    INFO     Generated 10000 RESIDENTIAL    component_base.py:92\n                             proxies with random session                        \n                             IDs.                                               \n                    INFO     Generated and shuffled 10000   component_base.py:92\n                             proxy URLs.                                        \n                    INFO     Successfully generated 10000   component_base.py:92\n                             proxies                                            \n                    DEBUG    Set randomized headers based   component_base.py:85\n                             on profile (Windows). UA:                          \n                             ...L, like Gecko)                                  \n                             Chrome/121.0.6304.105                              \n                             Safari/554.36                                      \n                    INFO     Setting up proxy from list of  component_base.py:92\n                             10000 proxies                                      \n                    INFO     Setting proxy index 0:         component_base.py:92\n                             <EMAIL>.i                     \n                             o:7777                                             \n[07/12/25 18:36:02] INFO     Current proxy reports IP:      component_base.py:92\n                             *************                                      \n                    INFO     Successfully set proxy:        component_base.py:92\n                             pr.oxylabs.io:7777                                 \n                    DEBUG    Using session manager's        component_base.py:85\n                             bandwidth logger instance                          \n                    INFO     Using injected                 component_base.py:92\n                             FBImageHashManager                                 \n                    DEBUG    Using injected bandwidth       component_base.py:85\n                             logger instance                                    \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue.db                                \n                    INFO     FBAdArchiveManager initialized component_base.py:92\n                             for table                                          \n                             '[cyan]FBAdArchive[/cyan]'                         \n                             (local=[yellow]<built-in                           \n                             method get of dict object at                       \n                             0x3418d4f80>[/yellow])                             \n                    INFO     Using spaCy model:             component_base.py:92\n                             [bright_blue]<built-in method                      \n                             get of dict object at                              \n                             0x3418d4f80>[/bright_blue]                         \n                    INFO     Text fields for NER:           component_base.py:92\n                             [magenta]<built-in method get                      \n                             of dict object at                                  \n                             0x3418d4f80>[/magenta]                             \n                    INFO     spaCy nlp.pipe batch size:     component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     DynamoDB scan workers:         component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     NER processing workers:        component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     Clustering based on NER        component_base.py:92\n                             results is                                         \n                             [green]enabled[/green].                            \n                    INFO     Clustering k-range: [<built-in component_base.py:92\n                             method get of dict object at                       \n                             0x3418d4f80>-<built-in method                      \n                             get of dict object at                              \n                             0x3418d4f80>], step: <built-in                     \n                             method get of dict object at                       \n                             0x3418d4f80>                                       \n                    INFO     📌 Tracked aiohttp session  resource_tracker.py:127\n                             13990827408 created at                             \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137 in                              \n                             execute                                            \n                    INFO     ✅ Untracked aiohttp        resource_tracker.py:141\n                             session 13990827408 (was                           \n                             created at                                         \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137)                                \n                    ERROR    TypeError in Facebook ads     component_base.py:111\n                             processing:                                        \n                             FBAdCategorizer.__init__()                         \n                             got an unexpected keyword                          \n                             argument 'logger'                                  \nFULL TRACEBACK: Traceback (most recent call last):\n  File \"/Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/fb_ads_orchestrator.py\", line 184, in execute\n    fb_orchestrator = fb_ads_container.facebook_ads_orchestrator()\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 604, in dependency_injector.providers.__call\nTypeError: FBAdCategorizer.__init__() got an unexpected keyword argument 'logger'\n\n                    ERROR    MainOrchestrator encountered  component_base.py:111\n                             an error during run                                \n                    WARNING  MainOrchestrator run completed component_base.py:99\n                             with errors                                        \n                    INFO     Exiting MainServiceFactory      main_factory.py:130\n                             async context.                                     \n                    INFO     DI Container cleaned up         main_factory.py:139\n                             successfully.                                      \n                    INFO     MainOrchestrator run completed for     main.py:2095\n                             single date.                                       \n                    INFO     Main function execution finished.      main.py:2137\n                    INFO     === Resource Tracker Report ===        main.py:2163\n                    INFO     ✅ No unclosed resources    resource_tracker.py:253\n                             detected                                           \n                    INFO     🧹 Starting comprehensive   resource_tracker.py:338\n                             resource cleanup...                                \n                    INFO     ✅ No unclosed resources    resource_tracker.py:253\n                             detected                                           \n                    INFO     🔍 Searching for orphaned   resource_tracker.py:290\n                             aiohttp sessions...                                \n                    INFO     ✅ No orphaned aiohttp      resource_tracker.py:317\n                             sessions found                                     \n                    INFO     ✅ Resource cleanup         resource_tracker.py:351\n                             complete                                           \n                    DEBUG    Found LLM client for cleanup: Client   main.py:2228\n                             ID: 13990418576, Type: DeepSeekClient              \n                    WARNING  Found 1 unclosed LLM clients -         main.py:2273\n                             attempting to close them                           \n                    DEBUG    Closed DeepSeekClient session          main.py:2279\n                    INFO     Async resources cleanup completed      main.py:2289\n                    INFO     Entering final cleanup phase...        main.py:2346\n                    INFO     Gathering all remaining tasks after    main.py:2365\n                             main task completion/cancellation...               \n                    INFO     No other pending tasks found after     main.py:2438\n                             main task.                                         \n                    INFO     Explicitly running garbage collection  main.py:2463\n                             (first pass)...                                    \n                    INFO     Explicitly running garbage collection  main.py:2474\n                             (second pass)...                                   \n                    INFO     Garbage collection finished.           main.py:2478\n                    INFO     Closing event loop...                  main.py:2491\n                    INFO     Event loop closed.                     main.py:2493\n                    INFO     Current thread's event loop policy     main.py:2498\n                             reset.                                             \n                    INFO     Running synchronous                    main.py:2500\n                             cleanup_everything...                              \n                    INFO     Starting comprehensive      resource_cleanup.py:282\n                             cleanup...                                         \n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    INFO     Comprehensive cleanup       resource_cleanup.py:297\n                             completed                                          \n[07/12/25 18:36:03] INFO     cleanup_everything completed.          main.py:2504\n                    INFO     safe_run_main finished with exit_code: main.py:2515\n                             0                                                  \nMain script exit.\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\nAll pipeline steps completed.\nFULL TRACEBACK FOR TYPEERROR: Traceback (most recent call last):\n  File \"/Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/fb_ads_orchestrator.py\", line 184, in execute\n    fb_orchestrator = fb_ads_container.facebook_ads_orchestrator()\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 604, in dependency_injector.providers.__call\nTypeError: FBAdCategorizer.__init__() got an unexpected keyword argument 'logger'\n\n", "clean_output": "Running pipeline step: fb_ads.yml with config /Users/<USER>/PycharmProjects/lexgenius/config/fb_ads.yml\nStarting main.py with Python 3.11.13 | packaged by conda-forge | (main, Jun  4 2025, 14:52:34) [Clang 18.1.8 ]\nSet multiprocessing start method to 'spawn'.\n[07/12/25 18:36:01] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and                                                \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/pacer.log                \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/pacer.log                \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/reports_servi            \n                             ces.log                                            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/transformer.l            \n                             og                                                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             /Users/<USER>/PycharmProjects/lex            \n                             genius/data/20250712/logs/logging_confi            \n                             g_module.log                                       \n                    INFO     Initial logging setup complete. Log    main.py:1921\n                             directory:                                         \n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data/20250712/logs                         \n                    INFO     Base data directory:                   main.py:1924\n                             /Users/<USER>/PycharmProjects/le             \n                             xgenius/data                                       \n                    INFO     No end_date specified, defaulting to   main.py:2013\n                             target date: 07/12/25                              \n                    INFO     Preparing for single date run:         main.py:2062\n                             07/12/25                                           \n                    INFO     DEBUG: Merging params into config     loader.py:170\n                             data for fb_ads                                    \n                    INFO     DEBUG: process_single_court in YAML:  loader.py:171\n                             NOT_SET                                            \n                    INFO     DEBUG: process_single_court in        loader.py:172\n                             params: []                                         \n                    INFO     DEBUG: process_single_court after     loader.py:174\n                             merge: []                                          \n[07/12/25 18:36:01] INFO     Log propagation ENABLED for 'src.pacer' main.py:549\n                             to root (console). Check both console              \n                             and data/20250712/logs/pacer.log                   \n                    INFO     Configured separate log file for        main.py:555\n                             'src.pacer' at:                                    \n                             data/20250712/logs/pacer.log                       \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.reports' to root                     \n                             (console). Check both console and                  \n                             data/20250712/logs/reports_services.log            \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.reports' at:                         \n                             data/20250712/logs/reports_services.log            \n                    INFO     Log propagation ENABLED for             main.py:549\n                             'src.services.transformer' to root                 \n                             (console). Check both console and                  \n                             data/20250712/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.services.transformer' at:                     \n                             data/20250712/logs/transformer.log                 \n                    INFO     Configured separate log file for        main.py:555\n                             'src.logging_config' at:                           \n                             data/20250712/logs/logging_config_modul            \n                             e.log                                              \n                    INFO     Logging re-configured for target date  main.py:2074\n                             07/12/25. Log dir: data/20250712/logs              \n                    INFO     --- Initializing Main Orchestration    main.py:2088\n                             for Single Date Run ---                            \n                    INFO     MainServiceFactory initialized   main_factory.py:27\n                             with config: Unknown config                        \n                    INFO     🚀 Using dependency-injector     main_factory.py:30\n                             framework                                          \n                    INFO     Entering MainServiceFactory      main_factory.py:93\n                             async context.                                     \n                    INFO     DI Container created and wired  main_factory.py:120\n                             successfully                                       \n                    INFO     MainOrchestrator initialized   component_base.py:92\n                    INFO     MainOrchestrator run started   component_base.py:92\n                    INFO     Scraping phase skipped by      component_base.py:92\n                             configuration                                      \n                    INFO     Post-processing phase skipped  component_base.py:92\n                             by configuration                                   \n                    INFO     Upload phase skipped by        component_base.py:92\n                             configuration                                      \n                    INFO     Facebook Ads processing phase  component_base.py:92\n                             initiated                                          \n                    INFO     Creating FbAdsOrchestrator via  main_factory.py:261\n                             DI Container...                                    \n                    INFO     Loaded 26 prompts              component_base.py:92\n                    WARNING  S3 storage not properly        component_base.py:99\n                             injected - S3 operations will                      \n                             be disabled                                        \n                    INFO     🔍 VALIDATION: Checking AI     component_base.py:92\n                             service requirements...                            \n                    INFO       - Available API key sources: component_base.py:92\n                             4/4                                                \n                    INFO       - AI services validation: ✅ component_base.py:92\n                             PASSED                                             \n                    INFO     FbAdsOrchestrator initialized  component_base.py:92\n                             with DI services                                   \n                    INFO     ✅ AI Orchestrator injected    component_base.py:92\n                             via DI                                             \n                    INFO     ✅ DeepSeek service injected   component_base.py:92\n                             via DI                                             \n                    INFO     ✅ Prompt Manager injected via component_base.py:92\n                             DI                                                 \n                    INFO     ✅ FbAdsOrchestrator created    main_factory.py:282\n                    INFO     Executing Facebook Ads tasks   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                    INFO     Bandwidth logger initialized   component_base.py:92\n                             for session manager with                           \n                             periodic logging disabled                          \n                    INFO     Session manager initialized    component_base.py:92\n                             with use_proxy=True,                               \n                             mobile_proxy=False                                 \n                    INFO     Generating proxy list for      component_base.py:92\n                             RESIDENTIAL proxies                                \n                    INFO     Attempting to load credentials component_base.py:92\n                             for RESIDENTIAL proxy...                           \n                    INFO     Using specific RESIDENTIAL     component_base.py:92\n                             credentials (from config or                        \n                             env).                                              \n                    INFO     Using username:                component_base.py:92\n                             lexgenius20250612_wzykM for                        \n                             RESIDENTIAL proxy list                             \n                             generation.                                        \n                    DEBUG    Generating RESIDENTIAL proxy   component_base.py:85\n                             list for user:                                     \n                             lexgenius20250612_wzykM                            \n                    INFO     Using configured proxy count:  component_base.py:92\n                             10000 RESIDENTIAL proxies                          \n                    DEBUG    Sample RESIDENTIAL proxy       component_base.py:85\n                             format: http@...                                   \n                    INFO     Generated 10000 RESIDENTIAL    component_base.py:92\n                             proxies with random session                        \n                             IDs.                                               \n                    INFO     Generated and shuffled 10000   component_base.py:92\n                             proxy URLs.                                        \n                    INFO     Successfully generated 10000   component_base.py:92\n                             proxies                                            \n                    DEBUG    Set randomized headers based   component_base.py:85\n                             on profile (Windows). UA:                          \n                             ...L, like Gecko)                                  \n                             Chrome/121.0.6304.105                              \n                             Safari/554.36                                      \n                    INFO     Setting up proxy from list of  component_base.py:92\n                             10000 proxies                                      \n                    INFO     Setting proxy index 0:         component_base.py:92\n                             <EMAIL>.i                     \n                             o:7777                                             \n[07/12/25 18:36:02] INFO     Current proxy reports IP:      component_base.py:92\n                             *************                                      \n                    INFO     Successfully set proxy:        component_base.py:92\n                             pr.oxylabs.io:7777                                 \n                    DEBUG    Using session manager's        component_base.py:85\n                             bandwidth logger instance                          \n                    INFO     Using injected                 component_base.py:92\n                             FBImageHashManager                                 \n                    DEBUG    Using injected bandwidth       component_base.py:85\n                             logger instance                                    \n                    DEBUG    Database schema initialized    component_base.py:85\n                    INFO     LocalImageQueue initialized at component_base.py:92\n                             data/image_queue.db                                \n                    INFO     FBAdArchiveManager initialized component_base.py:92\n                             for table                                          \n                             '[cyan]FBAdArchive[/cyan]'                         \n                             (local=[yellow]<built-in                           \n                             method get of dict object at                       \n                             0x3418d4f80>[/yellow])                             \n                    INFO     Using spaCy model:             component_base.py:92\n                             [bright_blue]<built-in method                      \n                             get of dict object at                              \n                             0x3418d4f80>[/bright_blue]                         \n                    INFO     Text fields for NER:           component_base.py:92\n                             [magenta]<built-in method get                      \n                             of dict object at                                  \n                             0x3418d4f80>[/magenta]                             \n                    INFO     spaCy nlp.pipe batch size:     component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     DynamoDB scan workers:         component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     NER processing workers:        component_base.py:92\n                             [blue]<built-in method get of                      \n                             dict object at                                     \n                             0x3418d4f80>[/blue]                                \n                    INFO     Clustering based on NER        component_base.py:92\n                             results is                                         \n                             [green]enabled[/green].                            \n                    INFO     Clustering k-range: [<built-in component_base.py:92\n                             method get of dict object at                       \n                             0x3418d4f80>-<built-in method                      \n                             get of dict object at                              \n                             0x3418d4f80>], step: <built-in                     \n                             method get of dict object at                       \n                             0x3418d4f80>                                       \n                    INFO     📌 Tracked aiohttp session  resource_tracker.py:127\n                             13990827408 created at                             \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137 in                              \n                             execute                                            \n                    INFO     ✅ Untracked aiohttp        resource_tracker.py:141\n                             session 13990827408 (was                           \n                             created at                                         \n                             /Users/<USER>/Pycharm                        \n                             Projects/lexgenius/src/serv                        \n                             ices/orchestration/fb_ads_o                        \n                             rchestrator.py:137)                                \n                    ERROR    TypeError in Facebook ads     component_base.py:111\n                             processing:                                        \n                             FBAdCategorizer.__init__()                         \n                             got an unexpected keyword                          \n                             argument 'logger'                                  \nFULL TRACEBACK: Traceback (most recent call last):\n  File \"/Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/fb_ads_orchestrator.py\", line 184, in execute\n    fb_orchestrator = fb_ads_container.facebook_ads_orchestrator()\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 604, in dependency_injector.providers.__call\nTypeError: FBAdCategorizer.__init__() got an unexpected keyword argument 'logger'\n\n                    ERROR    MainOrchestrator encountered  component_base.py:111\n                             an error during run                                \n                    WARNING  MainOrchestrator run completed component_base.py:99\n                             with errors                                        \n                    INFO     Exiting MainServiceFactory      main_factory.py:130\n                             async context.                                     \n                    INFO     DI Container cleaned up         main_factory.py:139\n                             successfully.                                      \n                    INFO     MainOrchestrator run completed for     main.py:2095\n                             single date.                                       \n                    INFO     Main function execution finished.      main.py:2137\n                    INFO     === Resource Tracker Report ===        main.py:2163\n                    INFO     ✅ No unclosed resources    resource_tracker.py:253\n                             detected                                           \n                    INFO     🧹 Starting comprehensive   resource_tracker.py:338\n                             resource cleanup...                                \n                    INFO     ✅ No unclosed resources    resource_tracker.py:253\n                             detected                                           \n                    INFO     🔍 Searching for orphaned   resource_tracker.py:290\n                             aiohttp sessions...                                \n                    INFO     ✅ No orphaned aiohttp      resource_tracker.py:317\n                             sessions found                                     \n                    INFO     ✅ Resource cleanup         resource_tracker.py:351\n                             complete                                           \n                    DEBUG    Found LLM client for cleanup: Client   main.py:2228\n                             ID: 13990418576, Type: DeepSeekClient              \n                    WARNING  Found 1 unclosed LLM clients -         main.py:2273\n                             attempting to close them                           \n                    DEBUG    Closed DeepSeekClient session          main.py:2279\n                    INFO     Async resources cleanup completed      main.py:2289\n                    INFO     Entering final cleanup phase...        main.py:2346\n                    INFO     Gathering all remaining tasks after    main.py:2365\n                             main task completion/cancellation...               \n                    INFO     No other pending tasks found after     main.py:2438\n                             main task.                                         \n                    INFO     Explicitly running garbage collection  main.py:2463\n                             (first pass)...                                    \n                    INFO     Explicitly running garbage collection  main.py:2474\n                             (second pass)...                                   \n                    INFO     Garbage collection finished.           main.py:2478\n                    INFO     Closing event loop...                  main.py:2491\n                    INFO     Event loop closed.                     main.py:2493\n                    INFO     Current thread's event loop policy     main.py:2498\n                             reset.                                             \n                    INFO     Running synchronous                    main.py:2500\n                             cleanup_everything...                              \n                    INFO     Starting comprehensive      resource_cleanup.py:282\n                             cleanup...                                         \n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    INFO     Comprehensive cleanup       resource_cleanup.py:297\n                             completed                                          \n[07/12/25 18:36:03] INFO     cleanup_everything completed.          main.py:2504\n                    INFO     safe_run_main finished with exit_code: main.py:2515\n                             0                                                  \nMain script exit.\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\n                    DEBUG    Starting comprehensive      resource_cleanup.py:136\n                             resource cleanup...                                \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-hpL2GK                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-EIZVRm                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-BFtoVN                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-6yscW5                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-5q1nLI                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-ZEJEbU                              \n                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201\n                             point or special):                                 \n                             /tmp/tmp-mount-w9oPGA                              \n                    DEBUG    Resource cleanup completed  resource_cleanup.py:145\nAll pipeline steps completed.\nFULL TRACEBACK FOR TYPEERROR: Traceback (most recent call last):\n  File \"/Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/fb_ads_orchestrator.py\", line 184, in execute\n    fb_orchestrator = fb_ads_container.facebook_ads_orchestrator()\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 573, in dependency_injector.providers.__call\n  File \"src/dependency_injector/providers.pxd\", line 441, in dependency_injector.providers.__provide_keyword_args\n  File \"src/dependency_injector/providers.pxd\", line 361, in dependency_injector.providers.__get_value\n  File \"src/dependency_injector/providers.pyx\", line 231, in dependency_injector.providers.Provider.__call__\n  File \"src/dependency_injector/providers.pyx\", line 3021, in dependency_injector.providers.Singleton._provide\n  File \"src/dependency_injector/providers.pxd\", line 646, in dependency_injector.providers.__factory_call\n  File \"src/dependency_injector/providers.pxd\", line 604, in dependency_injector.providers.__call\nTypeError: FBAdCategorizer.__init__() got an unexpected keyword argument 'logger'\n\n", "timestamp": "2025-07-12T18:36:03.286085", "service_name": "FBAdCategorizer", "invalid_param": "logger", "error_file": "/Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/fb_ads_orchestrator.py", "error_line": "184"}}], "last_successful_run": null, "claude_fixes_applied": []}