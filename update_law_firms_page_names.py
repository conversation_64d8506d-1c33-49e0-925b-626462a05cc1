#!/usr/bin/env python3
"""
Script to update law firms with page_name from FBAdArchive table.
For each law firm, it queries FBAdArchive by page_id (using the law firm's ID),
retrieves the PageName, and updates the law firm record after user approval.
"""

import asyncio
import sys
import logging
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
from rich.table import Table
from rich.prompt import Confirm
from datetime import datetime

# Add src to path to import our modules
sys.path.insert(0, 'src')

from infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from repositories.law_firms_repository import LawFirmsRepository
from repositories.fb_archive_repository import FBArchiveRepository
from config_models.storage import StorageConfig


class LawFirmsPageNameUpdater:
    """Updates law firms with page names from Facebook Ad Archive"""
    
    def __init__(self, endpoint_url: str = None):
        # Create config for DynamoDB (use AWS by default, local if endpoint_url provided)
        self.config = StorageConfig(
            dynamodb_endpoint_url=endpoint_url,
            aws_region="us-west-2"
        )
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(handler)
        
        # Rich console for pretty output
        self.console = Console()
        
        # Statistics
        self.stats = {
            'total_firms': 0,
            'firms_with_valid_id': 0,
            'page_names_found': 0,
            'updates_approved': 0,
            'updates_completed': 0,
            'errors': 0
        }
    
    async def get_page_name_from_fb_archive(self, page_id: str) -> Optional[str]:
        """
        Query FBAdArchive for a single ad by page_id and return its PageName
        
        Args:
            page_id: The Facebook page ID to look up
            
        Returns:
            PageName if found, None otherwise
        """
        try:
            # Create FB Archive repository
            fb_storage = AsyncDynamoDBStorage(self.config, self.logger)
            fb_repo = FBArchiveRepository(fb_storage, self.logger)
            
            async with fb_storage:
                # Query by page_id, limit to 1 result
                ads = await fb_repo.query_by_page_id(page_id)
                
                if ads and len(ads) > 0:
                    # Return the PageName from the first ad
                    first_ad = ads[0]
                    page_name = first_ad.get('page_name')  # Repository returns snake_case
                    return page_name
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error querying FBAdArchive for page_id {page_id}: {e}")
            return None
    
    async def scan_and_update_law_firms(self):
        """Main process to scan law firms and update with page names"""
        try:
            # Create law firms repository
            law_firms_storage = AsyncDynamoDBStorage(self.config, self.logger)
            law_firms_repo = LawFirmsRepository(law_firms_storage, self.logger)
            
            async with law_firms_storage:
                # Scan all law firms
                self.console.print("[bold blue]Scanning all law firms...[/bold blue]")
                all_firms = await law_firms_repo.scan_all()
                self.stats['total_firms'] = len(all_firms)
                
                self.console.print(f"Found {len(all_firms)} law firms total\n")
                
                # Collect firms that need updates
                firms_to_update = []
                
                for firm in all_firms:
                    firm_id = firm.get('id')
                    firm_name = firm.get('name')
                    page_alias = firm.get('page_alias')
                    existing_page_name = firm.get('page_name')
                    
                    # Skip if no firm_id
                    if not firm_id:
                        self.logger.debug(f"Skipping {firm_name} - no id")
                        continue
                    
                    self.stats['firms_with_valid_id'] += 1
                    
                    # Skip if already has page_name
                    if existing_page_name:
                        self.logger.debug(f"Skipping {firm_name} - already has page_name: {existing_page_name}")
                        continue
                    
                    # Look up page name from FBAdArchive using firm_id as page_id
                    self.console.print(f"Looking up page name for {firm_name} (using firm_id as page_id: {firm_id})...")
                    page_name = await self.get_page_name_from_fb_archive(firm_id)
                    
                    if page_name:
                        self.stats['page_names_found'] += 1
                        firms_to_update.append({
                            'id': firm_id,
                            'name': firm_name,
                            'page_alias': page_alias,
                            'page_name': page_name
                        })
                        self.console.print(f"  [green]✓ Found page name: {page_name}[/green]")
                    else:
                        self.console.print(f"  [yellow]✗ No page name found in FBAdArchive[/yellow]")
                
                # Display summary and get approval
                if not firms_to_update:
                    self.console.print("\n[yellow]No firms need page_name updates[/yellow]")
                    return
                
                self.console.print(f"\n[bold]Found {len(firms_to_update)} firms to update:[/bold]\n")
                
                # Create table showing proposed updates
                table = Table(title="Proposed Page Name Updates")
                table.add_column("Law Firm ID", style="cyan")
                table.add_column("Law Firm Name", style="green")
                table.add_column("Page Alias (FB ID)", style="yellow")
                table.add_column("→ Page Name", style="magenta")
                
                for firm in firms_to_update:
                    table.add_row(
                        firm['id'],
                        firm['name'],
                        firm['page_alias'],
                        firm['page_name']
                    )
                
                self.console.print(table)
                
                # Ask for approval
                if not Confirm.ask("\n[bold]Do you want to proceed with these updates?[/bold]"):
                    self.console.print("[red]Updates cancelled by user[/red]")
                    return
                
                self.stats['updates_approved'] = len(firms_to_update)
                
                # Perform updates
                self.console.print("\n[bold blue]Updating law firms...[/bold blue]")
                
                for firm in firms_to_update:
                    try:
                        success = await law_firms_repo.update_attributes(
                            firm_id=firm['id'],
                            name=firm['name'],
                            updates={'page_name': firm['page_name']}
                        )
                        
                        if success:
                            self.stats['updates_completed'] += 1
                            self.console.print(f"✓ Updated {firm['name']}")
                        else:
                            self.stats['errors'] += 1
                            self.console.print(f"[red]✗ Failed to update {firm['name']}[/red]")
                            
                    except Exception as e:
                        self.stats['errors'] += 1
                        self.console.print(f"[red]✗ Error updating {firm['name']}: {e}[/red]")
                
                # Print summary
                self.print_summary()
                
        except Exception as e:
            self.console.print(f"[bold red]Fatal error: {e}[/bold red]")
            self.logger.error(f"Fatal error in scan_and_update_law_firms: {e}", exc_info=True)
    
    def print_summary(self):
        """Print execution summary"""
        self.console.print("\n[bold]Execution Summary:[/bold]")
        self.console.print(f"  Total law firms scanned: {self.stats['total_firms']}")
        self.console.print(f"  Firms with valid ID: {self.stats['firms_with_valid_id']}")
        self.console.print(f"  Page names found in FBAdArchive: {self.stats['page_names_found']}")
        self.console.print(f"  Updates approved: {self.stats['updates_approved']}")
        self.console.print(f"  Updates completed: [green]{self.stats['updates_completed']}[/green]")
        if self.stats['errors'] > 0:
            self.console.print(f"  Errors: [red]{self.stats['errors']}[/red]")


async def main():
    """Main function to run the updater"""
    # Check command line arguments
    use_local = '--local' in sys.argv
    
    if use_local:
        print("Using local DynamoDB at http://localhost:8000")
        updater = LawFirmsPageNameUpdater(endpoint_url="http://localhost:8000")
    else:
        print("Using AWS DynamoDB")
        updater = LawFirmsPageNameUpdater()
    
    await updater.scan_and_update_law_firms()


if __name__ == "__main__":
    # Check if rich is available
    try:
        from rich.console import Console
        from rich.table import Table
        from rich.prompt import Confirm
    except ImportError:
        print("Error: 'rich' library is required. Install with: pip install rich")
        sys.exit(1)
    
    # Run the updater
    asyncio.run(main())