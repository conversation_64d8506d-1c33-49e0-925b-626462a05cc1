# Facebook Ads Browser Session Fix Summary

## Issue
The browser session was being invalidated between Phase 1 (GraphQL capture) and Phase 2 (image downloads), causing all image downloads to fail with the error:
```
WARNING  get_session() called but browser session is not valid
ERROR    IH_DOWNLOAD: Cannot download image adarchive/fb/xxx/xxx.jpg: Session not available.
```

## Root Cause
The session manager was being cleaned up prematurely in `job_orchestration_service.py` in the `finally` block of `_process_single_firm_with_worker()`. This cleanup happened immediately after the job runner returned from Phase 1, but BEFORE Phase 2 could use the session for image downloads.

## Fix Applied
Removed the premature session cleanup from `job_orchestration_service.py` (lines 498-507 commented out). The job runner service already handles cleanup properly in its own `finally` blocks after ALL phases complete.

### File Modified
- `/src/services/fb_ads/jobs/job_orchestration_service.py`

### Change Details
```python
# BEFORE (incorrect - cleanup too early):
finally:
    # Clean up firm-specific session manager
    if 'session_manager' in dependencies_with_progress:
        session_manager = dependencies_with_progress.get('session_manager')
        if session_manager and hasattr(session_manager, 'cleanup'):
            try:
                await session_manager.cleanup()
                self.logger.info(f"✅ Cleaned up session manager for firm {job.firm_id}")
            except Exception as e:
                self.logger.error(f"⚠️ Session cleanup failed for {job.firm_id}: {e}")

# AFTER (correct - no premature cleanup):
finally:
    # CRITICAL: Do NOT clean up session manager here - it's still needed by job runner phases!
    # The job runner will handle cleanup after all phases are complete.
    # [cleanup code commented out]
```

## Timeline of Events (Before Fix)
1. Job starts with new session manager
2. Phase 1: GraphQL capture uses session successfully  
3. Job orchestration's finally block runs, calling `session_manager.cleanup()`
4. Session is invalidated (`self.page = None`, `self._is_session_valid = False`)
5. Phase 2: Image downloads fail because session is no longer valid

## Timeline of Events (After Fix)
1. Job starts with new session manager
2. Phase 1: GraphQL capture uses session successfully
3. Phase 2: Image downloads use same valid session
4. Phase 3: Database operations complete
5. Job runner's finally block runs, calling `session_manager.cleanup()` AFTER all phases

## Verification
Run the test script to verify the fix:
```bash
python test_fb_ads_session_fix.py
```

This will process a single firm (Morgan & Morgan) and verify that:
1. Session is created successfully
2. GraphQL capture completes
3. Images can be downloaded in Phase 2
4. Session is cleaned up only after all phases complete

## Impact
This fix ensures that:
- Browser sessions remain valid throughout all job phases
- Image downloads work correctly
- Proper resource cleanup still happens, just at the right time
- No browser resources are leaked