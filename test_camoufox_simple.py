#!/usr/bin/env python3
"""
Simple test to verify Camoufox browser automation is working.
"""

import asyncio
import logging
from camoufox.async_api import AsyncCamoufox

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_camoufox():
    """Test basic Camoufox browser automation."""
    browser = None
    try:
        logger.info("Creating Camoufox browser...")
        browser = AsyncCamoufox(headless=False, viewport={'width': 1920, 'height': 1080})
        
        logger.info("Starting browser...")
        await browser.start()
        
        logger.info("Creating new context...")
        context = await browser.browser.new_context()
        
        logger.info("Creating new page...")
        page = await context.new_page()
        
        logger.info("Navigating to Facebook Ad Library...")
        await page.goto("https://www.facebook.com/ads/library/")
        
        logger.info("Waiting for page to load...")
        await page.wait_for_load_state('networkidle')
        
        logger.info("Looking for search input...")
        search_input = await page.wait_for_selector('input[placeholder*="Search"]', timeout=10000)
        
        if search_input:
            logger.info("✅ Found search input - browser automation is working!")
            
            logger.info("Clicking on search input...")
            await search_input.click()
            
            logger.info("Typing in search input...")
            await search_input.type("Wisner Baum", delay=100)
            
            logger.info("✅ Successfully typed in search input!")
        else:
            logger.error("❌ Could not find search input")
        
        logger.info("Waiting 5 seconds before closing...")
        await asyncio.sleep(5)
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if browser and browser.browser:
            logger.info("Closing browser...")
            await browser.browser.close()

if __name__ == "__main__":
    asyncio.run(test_camoufox())