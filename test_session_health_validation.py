#!/usr/bin/env python3
"""
Focused Test Suite for Session Health Validation

This script provides detailed testing of the enhanced session validation
with various session states, specifically testing the is_session_valid()
improvements and health monitoring integration.
"""

import asyncio
import pytest
import time
import logging
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, Any

# Import health monitoring components
from src.services.fb_ads.camoufox.session_health_monitor import (
    SessionHealthMonitor, HealthStatus, HealthResult,
    BrowserHealthCheck, PageHealthCheck, TokenHealthCheck, NetworkHealthCheck
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockSessionManager:
    """Mock session manager for health testing"""
    
    def __init__(self, browser=None, page=None, session_data=None):
        self.browser = browser
        self.page = page
        self.session_data = session_data or {}
        self._is_session_valid = True
        self.session_start_time = time.time()


class MockBrowser:
    """Mock browser for health testing"""
    
    def __init__(self, contexts=None, should_fail=False):
        self.contexts = contexts or []
        self.should_fail = should_fail
        self.browser = self  # AsyncCamoufox compatibility
        
    def __len__(self):
        return len(self.contexts)


class MockPage:
    """Mock page for health testing"""
    
    def __init__(self, url="https://facebook.com", title="Facebook", closed=False, should_fail=False):
        self._url = url
        self._title = title
        self.closed = closed
        self.should_fail = should_fail
    
    async def url(self):
        if self.should_fail:
            raise Exception("Failed to get URL")
        return self._url
    
    async def title(self):
        if self.should_fail:
            raise Exception("Failed to get title")
        return self._title
    
    def is_closed(self):
        return self.closed
    
    async def evaluate(self, script):
        if self.should_fail:
            raise Exception("Evaluation failed")
        if 'fetch' in script:
            return {'ok': True, 'status': 200, 'url': 'https://facebook.com/favicon.ico'}
        return {}


async def test_browser_health_check_healthy():
    """Test browser health check with healthy browser"""
    logger.info("Testing browser health check - healthy state")
    
    health_check = BrowserHealthCheck()
    session_manager = MockSessionManager(
        browser=MockBrowser(contexts=['context1', 'context2'])
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.HEALTHY
    assert result.component == "browser"
    assert "healthy with 2 contexts" in result.message
    assert result.details['context_count'] == 2
    assert result.details['browser_connected'] is True
    
    logger.info("✅ Browser health check - healthy state passed")


async def test_browser_health_check_missing():
    """Test browser health check with missing browser"""
    logger.info("Testing browser health check - missing browser")
    
    health_check = BrowserHealthCheck()
    session_manager = MockSessionManager(browser=None)
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.FAILED
    assert result.component == "browser"
    assert "not available" in result.message
    assert result.details['browser_exists'] is False
    
    logger.info("✅ Browser health check - missing browser passed")


async def test_browser_health_check_unresponsive():
    """Test browser health check with unresponsive browser"""
    logger.info("Testing browser health check - unresponsive browser")
    
    health_check = BrowserHealthCheck()
    
    # Mock browser that raises exception when accessing contexts
    class UnresponsiveBrowser:
        def __init__(self):
            self.browser = self
        
        @property
        def contexts(self):
            raise Exception("Browser unresponsive")
    
    session_manager = MockSessionManager(browser=UnresponsiveBrowser())
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.CRITICAL
    assert result.component == "browser"
    assert "unresponsive" in result.message
    
    logger.info("✅ Browser health check - unresponsive browser passed")


async def test_page_health_check_healthy():
    """Test page health check with healthy page"""
    logger.info("Testing page health check - healthy state")
    
    health_check = PageHealthCheck()
    session_manager = MockSessionManager(
        page=MockPage(url="https://facebook.com/ads/library", title="Facebook Ad Library")
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.HEALTHY
    assert result.component == "page"
    assert "Facebook Ad Library" in result.message
    assert result.details['is_facebook_page'] is True
    assert result.details['page_closed'] is False
    
    logger.info("✅ Page health check - healthy state passed")


async def test_page_health_check_closed():
    """Test page health check with closed page"""
    logger.info("Testing page health check - closed page")
    
    health_check = PageHealthCheck()
    session_manager = MockSessionManager(
        page=MockPage(closed=True)
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.FAILED
    assert result.component == "page"
    assert "closed" in result.message
    assert result.details['page_closed'] is True
    
    logger.info("✅ Page health check - closed page passed")


async def test_page_health_check_unresponsive():
    """Test page health check with unresponsive page"""
    logger.info("Testing page health check - unresponsive page")
    
    health_check = PageHealthCheck()
    session_manager = MockSessionManager(
        page=MockPage(should_fail=True)
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.CRITICAL
    assert result.component == "page"
    assert "unresponsive" in result.message
    
    logger.info("✅ Page health check - unresponsive page passed")


async def test_token_health_check_healthy():
    """Test token health check with all required tokens"""
    logger.info("Testing token health check - healthy state")
    
    health_check = TokenHealthCheck()
    session_manager = MockSessionManager(
        session_data={
            'fb_dtsg': 'test_token_123',
            'lsd': 'test_lsd_456', 
            'jazoest': 'test_jazoest_789',
            'fb_dtsg_timestamp': time.time(),
            'lsd_timestamp': time.time(),
            'jazoest_timestamp': time.time()
        }
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.HEALTHY
    assert result.component == "tokens"
    assert "3 tokens present and fresh" in result.message
    assert len(result.details['present_tokens']) == 3
    assert len(result.details['missing_tokens']) == 0
    
    logger.info("✅ Token health check - healthy state passed")


async def test_token_health_check_missing_tokens():
    """Test token health check with missing tokens"""
    logger.info("Testing token health check - missing tokens")
    
    health_check = TokenHealthCheck()
    session_manager = MockSessionManager(
        session_data={
            'fb_dtsg': 'test_token_123',
            # Missing lsd and jazoest
        }
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.CRITICAL
    assert result.component == "tokens"
    assert "Missing critical tokens" in result.message
    assert 'lsd' in result.details['missing_tokens']
    assert 'jazoest' in result.details['missing_tokens']
    
    logger.info("✅ Token health check - missing tokens passed")


async def test_token_health_check_old_tokens():
    """Test token health check with old tokens"""
    logger.info("Testing token health check - old tokens")
    
    health_check = TokenHealthCheck()
    old_timestamp = time.time() - 3700  # More than 1 hour old
    
    session_manager = MockSessionManager(
        session_data={
            'fb_dtsg': 'test_token_123',
            'lsd': 'test_lsd_456',
            'jazoest': 'test_jazoest_789',
            'fb_dtsg_timestamp': old_timestamp,
            'lsd_timestamp': old_timestamp,
            'jazoest_timestamp': old_timestamp
        }
    )
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.WARNING
    assert result.component == "tokens"
    assert "aging" in result.message
    assert len(result.details['old_tokens']) > 0
    
    logger.info("✅ Token health check - old tokens passed")


async def test_token_health_check_no_session_data():
    """Test token health check with no session data"""
    logger.info("Testing token health check - no session data")
    
    health_check = TokenHealthCheck()
    session_manager = MockSessionManager(session_data={})
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.FAILED
    assert result.component == "tokens"
    assert "No session data available" in result.message
    
    logger.info("✅ Token health check - no session data passed")


async def test_network_health_check_healthy():
    """Test network health check with healthy connection"""
    logger.info("Testing network health check - healthy state")
    
    health_check = NetworkHealthCheck()
    session_manager = MockSessionManager(page=MockPage())
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.HEALTHY
    assert result.component == "network"
    assert "healthy" in result.message
    assert result.details['response_time'] > 0
    
    logger.info("✅ Network health check - healthy state passed")


async def test_network_health_check_failed():
    """Test network health check with network failure"""
    logger.info("Testing network health check - network failure")
    
    health_check = NetworkHealthCheck()
    session_manager = MockSessionManager(page=MockPage(should_fail=True))
    
    result = await health_check.validate(session_manager)
    
    assert result.status == HealthStatus.CRITICAL
    assert result.component == "network"
    assert "failed" in result.message
    
    logger.info("✅ Network health check - network failure passed")


async def test_health_monitor_comprehensive_check():
    """Test comprehensive health monitoring"""
    logger.info("Testing comprehensive health monitoring")
    
    # Create session manager with all components
    session_manager = MockSessionManager(
        browser=MockBrowser(contexts=['context1']),
        page=MockPage(),
        session_data={
            'fb_dtsg': 'test_token',
            'lsd': 'test_lsd',
            'jazoest': 'test_jazoest'
        }
    )
    
    # Create health monitor
    health_monitor = SessionHealthMonitor(session_manager, {
        'check_interval': 10,
        'failure_threshold': 2,
        'continuous_monitoring': False
    })
    
    # Perform comprehensive health check
    result = await health_monitor.perform_health_check()
    
    assert result.component == "aggregate"
    assert result.status == HealthStatus.HEALTHY
    assert result.details['total_checks'] == 4
    assert result.details['healthy_checks'] > 0
    
    # Check health summary
    summary = health_monitor.get_health_summary()
    assert summary['current_health']['status'] == 'healthy'
    assert summary['monitoring']['total_checks'] == 1
    assert health_monitor.health_score > 0.9
    
    logger.info("✅ Comprehensive health monitoring passed")


async def test_health_result_aggregation():
    """Test health result aggregation logic"""
    logger.info("Testing health result aggregation")
    
    # Test with mixed results
    results = [
        HealthResult.healthy("component1", "All good"),
        HealthResult.warning("component2", "Minor issue"),
        HealthResult.critical("component3", "Major issue"),
        HealthResult.healthy("component4", "Working fine")
    ]
    
    aggregated = HealthResult.aggregate(results)
    
    assert aggregated.status == HealthStatus.CRITICAL  # Worst status wins
    assert aggregated.component == "aggregate"
    assert aggregated.details['total_checks'] == 4
    assert aggregated.details['healthy_checks'] == 2
    assert aggregated.details['critical_checks'] == 1
    
    # Test with all healthy
    healthy_results = [
        HealthResult.healthy("comp1", "Good"),
        HealthResult.healthy("comp2", "Good")
    ]
    
    healthy_aggregated = HealthResult.aggregate(healthy_results)
    assert healthy_aggregated.status == HealthStatus.HEALTHY
    
    # Test with no results
    empty_aggregated = HealthResult.aggregate([])
    assert empty_aggregated.status == HealthStatus.FAILED
    
    logger.info("✅ Health result aggregation passed")


async def test_health_check_timeout_handling():
    """Test health check timeout handling"""
    logger.info("Testing health check timeout handling")
    
    class SlowHealthCheck:
        def __init__(self):
            self.name = "slow_check"
            self.timeout = 1.0  # 1 second timeout
    
        async def validate(self, session_manager):
            await asyncio.sleep(2.0)  # Takes longer than timeout
            return HealthResult.healthy(self.name, "Slow but healthy")
        
        async def run_with_timeout(self, session_manager):
            start_time = time.time()
            try:
                result = await asyncio.wait_for(
                    self.validate(session_manager),
                    timeout=self.timeout
                )
                result.duration = time.time() - start_time
                return result
            except asyncio.TimeoutError:
                duration = time.time() - start_time
                return HealthResult.failed(
                    self.name,
                    f"Health check timed out after {duration:.2f}s",
                    {"timeout": self.timeout, "duration": duration}
                )
    
    slow_check = SlowHealthCheck()
    session_manager = MockSessionManager()
    
    result = await slow_check.run_with_timeout(session_manager)
    
    assert result.status == HealthStatus.FAILED
    assert "timed out" in result.message
    assert result.details['timeout'] == 1.0
    
    logger.info("✅ Health check timeout handling passed")


async def run_all_health_tests():
    """Run all health validation tests"""
    logger.info("🚀 Starting Session Health Validation Tests")
    
    tests = [
        test_browser_health_check_healthy,
        test_browser_health_check_missing,
        test_browser_health_check_unresponsive,
        test_page_health_check_healthy,
        test_page_health_check_closed,
        test_page_health_check_unresponsive,
        test_token_health_check_healthy,
        test_token_health_check_missing_tokens,
        test_token_health_check_old_tokens,
        test_token_health_check_no_session_data,
        test_network_health_check_healthy,
        test_network_health_check_failed,
        test_health_monitor_comprehensive_check,
        test_health_result_aggregation,
        test_health_check_timeout_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            await test()
            passed += 1
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test.__name__} failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n🏁 Health Validation Tests Complete")
    logger.info(f"📊 Results: {passed} passed, {failed} failed, {len(tests)} total")
    
    return passed, failed


if __name__ == "__main__":
    """Main execution"""
    import sys
    
    logger.info("🔬 Session Health Validation Test Suite")
    logger.info("=" * 60)
    
    try:
        passed, failed = asyncio.run(run_all_health_tests())
        
        if failed > 0:
            logger.error(f"\n💥 {failed} test(s) failed!")
            sys.exit(1)
        else:
            logger.info(f"\n🎉 All {passed} tests passed!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Test suite failed: {e}")
        sys.exit(1)