#!/usr/bin/env python3
"""
Test script to replicate the exact FB ads orchestration failure scenario.

This test replicates the exact conditions that cause the 'dict' object has no attribute 'is_set' error:
1. FB ads orchestrator creating session managers via factory
2. Multiple event loop contexts (orchestration environment)
3. SharedBrowserManager singleton access across event loops
4. CamoufoxSessionManager creation with job isolation

Usage:
    python test_fb_orchestration_fix.py
"""

import asyncio
import logging
import sys
import threading
import time
from typing import Dict, Any, Optional
from unittest.mock import MagicMock, AsyncMock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_fb_orchestration_scenario():
    """
    Test that replicates the exact FB ads orchestration scenario that was failing.
    """
    logger.info("🎯 Testing FB Ads Orchestration Scenario - Exact Failure Replication")
    
    try:
        # Mock the dependencies that would be present in the real orchestration
        mock_config = {
            'headless': True,
            'humanize': False,
            'feature_flags': {
                'use_camoufox': True  # Force Camoufox usage
            },
            'camoufox': {
                'browser': {
                    'startup_delay': 0.1
                }
            }
        }
        
        logger.info("📋 Phase 1: Testing SessionManagerFactory CAMOUFOX-ONLY mode")
        
        # Import the factory - this is the exact path the orchestrator uses
        from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
        
        # Create mock dependencies like the container provides
        mock_fingerprint_manager = None  # Can be None
        mock_proxy_manager = None  # Can be None
        mock_law_firms_repository = MagicMock()
        
        logger.info("🔧 Testing factory with multiple firm IDs (simulating orchestration)")
        
        # Test creating session managers for different firms (like orchestration does)
        test_firms = [
            "firm_123_test",
            "firm_456_test", 
            "firm_789_test"
        ]
        
        session_managers = []
        
        for firm_id in test_firms:
            logger.info(f"🏭 Creating session manager for firm: {firm_id}")
            
            session_manager = SessionManagerFactory.create(
                config=mock_config,
                logger=logger,
                firm_id=firm_id,
                fingerprint_manager=mock_fingerprint_manager,
                proxy_manager=mock_proxy_manager,
                law_firms_repository=mock_law_firms_repository
            )
            
            logger.info(f"✅ Created session manager: {type(session_manager).__name__} for {firm_id}")
            session_managers.append((firm_id, session_manager))
            
            # Verify it's a CamoufoxSessionManager
            from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
            if not isinstance(session_manager, CamoufoxSessionManager):
                logger.error(f"❌ CRITICAL: Factory created {type(session_manager).__name__} instead of CamoufoxSessionManager")
                return False
        
        logger.info(f"✅ Phase 1 PASSED: All {len(session_managers)} session managers are CamoufoxSessionManager")
        
        logger.info("📋 Phase 2: Testing SharedBrowserManager access in current event loop")
        
        # Test that SharedBrowserManager can be accessed without corruption
        session_manager_instances = []
        for firm_id, session_manager in session_managers:
            logger.info(f"🔍 Testing SharedBrowserManager access for {firm_id}")
            
            # Access the shared browser manager (this was causing the corruption)
            shared_browser_manager = session_manager.shared_browser_manager
            logger.info(f"✅ Successfully accessed SharedBrowserManager for {firm_id}")
            
            # Verify it's the same singleton across all instances
            session_manager_instances.append((firm_id, shared_browser_manager))
        
        # Check that all session managers share the same SharedBrowserManager
        singleton_ids = [id(sbm) for _, sbm in session_manager_instances]
        unique_singletons = len(set(singleton_ids))
        
        if unique_singletons == 1:
            logger.info(f"✅ Phase 2 PASSED: All session managers share the same SharedBrowserManager singleton")
        else:
            logger.error(f"❌ Phase 2 FAILED: Found {unique_singletons} different SharedBrowserManager instances")
            return False
        
        logger.info("📋 Phase 3: Testing browser context creation (the exact failure point)")
        
        # This is where the 'dict' object has no attribute 'is_set' error occurred
        test_config = {
            'headless': True,
            'humanize': False
        }
        
        context_results = []
        for firm_id, session_manager in session_managers:
            logger.info(f"🚀 Testing browser context creation for {firm_id}")
            
            try:
                # This is the exact operation that was failing
                shared_browser_manager = session_manager.shared_browser_manager
                
                # Test the problematic get_browser_context call
                context, page = await shared_browser_manager.get_browser_context(
                    job_id=firm_id,
                    config=test_config
                )
                
                logger.info(f"✅ Successfully created browser context for {firm_id}")
                
                # Test basic page operation
                await page.goto('about:blank')
                title = await page.title()
                logger.info(f"📄 Page title for {firm_id}: '{title}'")
                
                context_results.append((firm_id, True, None))
                
                # Clean up context
                await shared_browser_manager.cleanup_job_context(firm_id)
                logger.info(f"🧹 Cleaned up context for {firm_id}")
                
            except Exception as e:
                logger.error(f"❌ Browser context creation failed for {firm_id}: {e}")
                context_results.append((firm_id, False, str(e)))
        
        # Analyze results
        successful_contexts = [r for r in context_results if r[1]]
        failed_contexts = [r for r in context_results if not r[1]]
        
        logger.info(f"📊 Phase 3 Results:")
        logger.info(f"   ✅ Successful contexts: {len(successful_contexts)}")
        logger.info(f"   ❌ Failed contexts: {len(failed_contexts)}")
        
        if failed_contexts:
            logger.error("❌ CONTEXT CREATION FAILURES:")
            for firm_id, _, error in failed_contexts:
                logger.error(f"   {firm_id}: {error}")
        
        # Final cleanup
        logger.info("🧹 Phase 4: Final cleanup of shared resources")
        if session_managers:
            # Get any shared browser manager for final cleanup
            _, first_session_manager = session_managers[0]
            shared_browser_manager = first_session_manager.shared_browser_manager
            await shared_browser_manager.cleanup_all()
            logger.info("✅ All shared resources cleaned up")
        
        # Final validation
        if len(successful_contexts) == len(test_firms):
            logger.info("🎉 TEST PASSED: FB Ads Orchestration Scenario Working Correctly!")
            logger.info("✅ No 'dict' object has no attribute 'is_set' errors")
            logger.info("✅ CamoufoxSessionManager creation successful")
            logger.info("✅ SharedBrowserManager singleton working correctly")
            logger.info("✅ Browser context creation successful for all firms")
            return True
        else:
            logger.error("❌ TEST FAILED: Some browser contexts could not be created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with critical error: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def run_in_new_event_loop(coro):
    """Helper function to run coroutine in a completely new event loop."""
    def thread_target():
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        try:
            return new_loop.run_until_complete(coro)
        finally:
            new_loop.close()
    
    result_container = {}
    exception_container = {}
    
    def wrapper():
        try:
            result_container['result'] = thread_target()
        except Exception as e:
            exception_container['exception'] = e
    
    thread = threading.Thread(target=wrapper)
    thread.start()
    thread.join()
    
    if 'exception' in exception_container:
        raise exception_container['exception']
    
    return result_container.get('result')

async def test_multi_event_loop_orchestration():
    """
    Test the orchestration scenario across multiple event loops.
    This simulates the production environment where orchestration might create new event loops.
    """
    logger.info("🎯 Testing Multi-Event-Loop Orchestration Scenario")
    
    # Phase 1: Create session managers in first event loop
    logger.info("📋 Phase 1: Creating session managers in Event Loop A")
    
    first_loop_result = await test_fb_orchestration_scenario()
    
    if not first_loop_result:
        logger.error("❌ Phase 1 failed in first event loop")
        return False
    
    logger.info("✅ Phase 1 passed in first event loop")
    
    # Phase 2: Test in completely new event loop (simulating production environment)
    logger.info("📋 Phase 2: Testing in Event Loop B (new event loop)")
    
    try:
        second_loop_result = run_in_new_event_loop(test_fb_orchestration_scenario())
        
        if second_loop_result:
            logger.info("✅ Phase 2 passed in second event loop")
            logger.info("🎉 MULTI-EVENT-LOOP TEST PASSED!")
            logger.info("✅ SharedBrowserManager handles event loop transitions correctly")
            return True
        else:
            logger.error("❌ Phase 2 failed in second event loop")
            return False
            
    except Exception as e:
        logger.error(f"❌ Phase 2 failed with error: {e}")
        return False

async def main():
    """Main test runner for FB ads orchestration fix validation."""
    logger.info("🎯 Starting FB Ads Orchestration Fix Validation")
    logger.info("=" * 80)
    
    test_results = []
    
    # Test 1: Single Event Loop Orchestration
    logger.info("\n" + "=" * 60)
    logger.info("TEST 1: Single Event Loop FB Ads Orchestration")
    logger.info("=" * 60)
    
    test1_passed = await test_fb_orchestration_scenario()
    test_results.append(("Single Event Loop Orchestration", test1_passed))
    
    # Test 2: Multi Event Loop Orchestration
    logger.info("\n" + "=" * 60)
    logger.info("TEST 2: Multi Event Loop FB Ads Orchestration")
    logger.info("=" * 60)
    
    test2_passed = await test_multi_event_loop_orchestration()
    test_results.append(("Multi Event Loop Orchestration", test2_passed))
    
    # Final Results
    logger.info("\n" + "=" * 80)
    logger.info("FINAL TEST RESULTS")
    logger.info("=" * 80)
    
    passed_count = 0
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if passed:
            passed_count += 1
    
    logger.info(f"\nOverall: {passed_count}/{len(test_results)} tests passed")
    
    if passed_count == len(test_results):
        logger.info("🎉 ALL TESTS PASSED - FB Ads Orchestration Fix Working!")
        logger.info("✅ 'dict' object has no attribute 'is_set' error eliminated")
        logger.info("✅ CamoufoxSessionManager ONLY mode working correctly")
        logger.info("✅ SharedBrowserManager handles event loop transitions")
        logger.info("✅ Per-operation lock creation prevents corruption")
        logger.info("✅ Production orchestration scenario validated")
        return 0
    else:
        logger.error("❌ SOME TESTS FAILED - Additional fixes needed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        sys.exit(1)