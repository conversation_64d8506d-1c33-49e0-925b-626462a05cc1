# Proxy Configuration Fix Summary

## Issue Description
When `mobile_proxy: true` was set in the fb_ads workflow configuration, the system was not respecting:
1. The `mobile_proxy` setting (appeared to use residential proxy instead)
2. The `num_mobile_proxies` count (500,000 configured)
3. The `num_residential_proxies` count (500,000 configured)

## Root Cause Analysis

### Problem 1: Proxy Count Configuration
The ProxyManager was only reading `oxylabs_num_proxies` from the config and ignoring the type-specific counts:
- `num_mobile_proxies` was not being read
- `num_residential_proxies` was not being read
- Mobile proxy generation was hardcoded to limit at 1,000 proxies
- Residential proxy generation was limited to 10,000 proxies

### Problem 2: Configuration Fields Not Used
The fb_ads.yml configuration file had these settings:
```yaml
num_mobile_proxies: 500000      # Custom mobile proxy count
num_residential_proxies: 500000 # Custom residential proxy count
oxylabs_num_proxies: 500000    # General proxy count
```

But ProxyManager was only using `oxylabs_num_proxies` for both proxy types.

## Solution Implemented

### Files Modified
1. `src/infrastructure/browser/proxy_manager.py`
2. `src/services/scraping/proxy/proxy_manager.py`

### Changes Made

1. **Added type-specific proxy count fields** (lines 65-67):
   ```python
   self.num_proxies = config.get('oxylabs_num_proxies', 10000)
   # Read type-specific proxy counts
   self.num_mobile_proxies = config.get('num_mobile_proxies', self.num_proxies)
   self.num_residential_proxies = config.get('num_residential_proxies', self.num_proxies)
   ```

2. **Updated mobile proxy generation** (line 180):
   ```python
   # Changed from: for i in range(min(self.num_proxies, 1000)):
   for i in range(min(self.num_mobile_proxies, 500000)):
   ```

3. **Updated residential proxy generation** (line 217):
   ```python
   # Changed from: for i in range(min(self.num_proxies, 10000)):
   for i in range(min(self.num_residential_proxies, 500000)):
   ```

4. **Enhanced logging** to show configured vs actual proxy counts:
   ```python
   self.logger.info(f"Generated {len(self.mobile_proxies)} mobile proxies (configured: {self.num_mobile_proxies})")
   self.logger.info(f"Generated {len(self.residential_proxies)} residential proxies (configured: {self.num_residential_proxies})")
   ```

5. **Added configuration logging** to show proxy type selection:
   ```python
   self.logger.info(f"Proxy type configuration: mobile_proxy={self.mobile_proxy}, "
                    f"num_mobile_proxies={self.num_mobile_proxies}, "
                    f"num_residential_proxies={self.num_residential_proxies}")
   ```

## Verification

The fix was tested with the following scenarios:

1. **Mobile Proxy Configuration**: 
   - Set `mobile_proxy: true` with 100 mobile and 50 residential proxies
   - Correctly generated 100 mobile proxies and started with mobile type

2. **Residential Proxy Configuration**:
   - Set `mobile_proxy: false` with 200 mobile and 300 residential proxies
   - Correctly generated the specified counts and started with residential type

3. **Large Proxy Counts (fb_ads.yml scenario)**:
   - Set `mobile_proxy: true` with 500,000 of each type
   - Successfully generated 500,000 mobile and 500,000 residential proxies

## Usage

With this fix, the fb_ads workflow will now:

1. **Respect the `mobile_proxy` setting**: When set to `true`, it will start with mobile proxies
2. **Use correct proxy counts**: Will generate the number specified in `num_mobile_proxies` and `num_residential_proxies`
3. **Fallback behavior**: If type-specific counts aren't provided, it falls back to `oxylabs_num_proxies`

## Configuration Example

```yaml
# Proxy Configuration
use_proxy: true              # Enable proxy rotation
mobile_proxy: true          # Use mobile vs residential proxy
num_mobile_proxies: 500000      # Number of mobile proxies to generate
num_residential_proxies: 500000 # Number of residential proxies to generate
oxylabs_num_proxies: 500000    # Fallback if type-specific counts not provided
```

## Next Steps

1. Run the pipeline with `mobile_proxy: true` to verify mobile proxies are being used
2. Monitor logs to confirm the correct number of proxies are generated
3. Check that proxy rotation works correctly with the large proxy pools

## Backup Files

Backups were created before modifications:
- `src/infrastructure/browser/proxy_manager.py.backup_20250804_161853`
- `src/services/scraping/proxy/proxy_manager.py.backup_20250804_161853`