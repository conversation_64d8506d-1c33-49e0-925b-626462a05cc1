#!/usr/bin/env python3
"""
Test script to verify the browser initialization fix for TargetClosedError.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_browser_initialization():
    """Test browser initialization with the fix."""
    
    # Minimal config for testing
    config = {
        'camoufox': {
            'browser': {
                'headless': True,
                'timeout': 30000,
                'viewport': {
                    'width': 1920,
                    'height': 1080
                }
            },
            'session': {
                'max_duration_minutes': 5,
                'min_duration_minutes': 3
            },
            'force_clean_profile': True
        },
        'use_proxy': False
    }
    
    session_manager = None
    
    try:
        logger.info("Creating CamoufoxSessionManager...")
        session_manager = CamoufoxSessionManager(
            config=config,
            logger=logger,
            fingerprint_manager=None,
            proxy_manager=None,
            law_firms_repository=None
        )
        
        logger.info("Starting session...")
        success = await session_manager.create_new_session()
        
        if success:
            logger.info("✅ Session started successfully!")
            
            # Test browser is working
            if session_manager.page:
                logger.info("Navigating to test page...")
                await session_manager.page.goto('https://example.com')
                title = await session_manager.page.title()
                logger.info(f"Page title: {title}")
                
                if title == "Example Domain":
                    logger.info("✅ Browser is working correctly!")
                    return True
                else:
                    logger.error(f"❌ Unexpected page title: {title}")
                    return False
            else:
                logger.error("❌ No page object available")
                return False
        else:
            logger.error("❌ Failed to start session")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}", exc_info=True)
        return False
        
    finally:
        if session_manager:
            logger.info("Cleaning up...")
            await session_manager.cleanup()


async def main():
    """Run the test."""
    logger.info("Starting browser initialization test...")
    
    success = await test_browser_initialization()
    
    if success:
        logger.info("\n✅ TEST PASSED: Browser initialization fix is working!")
        return 0
    else:
        logger.error("\n❌ TEST FAILED: Browser initialization still has issues")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)